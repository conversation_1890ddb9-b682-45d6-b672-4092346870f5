import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../controller/contract/myloan.controller.dart';
import '../../themes/app_textstyle.dart';
import '../../utils/AppSvgImage.dart';

class ContractPayWidget {
  static alertQRPayment(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) =>
            GetBuilder<MyloanController>(
                builder: (myloanCtl) {
              return Container(
                height: Get.height,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          InkWell(
                            onTap: () {
                              // Get.back();
                              Navigator.pop(context);
                              print("hflhgw");
                            },
                            child: Container(
                              width: 24.w,
                              height: 24.h,
                              alignment: Alignment.topRight,
                              decoration: ShapeDecoration(
                                color: Colors.white.withOpacity(0.699999988079071),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              margin: EdgeInsets.only(
                                top: 36.h,
                                left: 14.w,
                                right: 14.w,
                              ),
                              child: Center(
                                  child:
                                  SvgPicture.string(AppSvgImage.aam_close_icon)),
                            ),
                          ),
                        ],
                      ),
                      GetBuilder<MyloanController>(
                        init: MyloanController(),
                        builder: (myloanCtl) {
                          return SingleChildScrollView(
                            child: Container(
                              width: Get.width,
                              height: Get.height,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.all(Radius.circular(20)),
                              ),
                              margin: EdgeInsets.only(left: 14.w, right: 14.w, bottom: 34.h,top: 14.h),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  Container(
                                    width: 110.w,
                                    height: 22.h,
                                    child: Text(
                                      menuGetLoanQR.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 14,
                                        fontFamily: TextStyleTheme.text_Regular
                                            .fontFamily,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 4.h,
                                  ),
                                  SizedBox(
                                    width: 242.w,
                                    height: 20.h,
                                    child: Text(
                                      menuGetLoanQRDes.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: const Color(0xBF1A1818),
                                        fontSize: 12,
                                        fontFamily: TextStyleTheme.text_Regular
                                            .fontFamily,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 5.h,
                                  ),
                                  Container(
                                    height: 1.h,
                                    alignment: Alignment.center,
                                    child: const Divider(
                                      color: Color(0xFFE5E5E5),
                                      thickness: 1,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Container(
                                    height: 24.h,
                                    margin: EdgeInsets.only(
                                      left: 32.w,
                                      right: 32.w,
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment
                                          .spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 24.h,
                                          child: Text(
                                            menuGetLoanPayAll.tr,
                                            style: TextStyle(
                                              color: const Color(0xFF1A1818),
                                              fontSize: 14,
                                              fontFamily:
                                              TextStyleTheme.text_Regular.fontFamily,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 24.h,
                                          child: Obx(() {
                                            return Text.rich(
                                              TextSpan(
                                                children: [
                                                  TextSpan(
                                                    text: myloanCtl
                                                        .qrNextpayData.value
                                                        .toString(),
                                                    style: TextStyle(
                                                      color: configTheme().colorScheme.onSecondary,
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                  TextSpan(
                                                    text: ' ',
                                                    style: TextStyle(
                                                      color: const Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                  TextSpan(
                                                    text: menuGetLoanBath.tr,
                                                    style: TextStyle(
                                                      color: const Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              textAlign: TextAlign.center,
                                            );
                                          }),
                                        ),
                                      ],
                                    ),
                                  ),

                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  //TODO QR image
                                  Obx(() {
                                    return Container(
                                      width: 283.w,
                                      height: 283.h,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                      ),
                                      child: Image.network(
                                        myloanCtl.qrNextpayUrl.value,
                                        fit: BoxFit.fill,
                                      ),
                                    );
                                  }),
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  SizedBox(
                                    width: 223.w,
                                    height: 22.h,
                                    child: Text(
                                      appConfigService.countryConfigCollection.toString() == 'aam' ? "บริษัท เอเอเอ็ม แคปปิตอล เซอร์วิส จำกัด"
                                          :appConfigService.countryConfigCollection.toString() == "rafco"?"Rafco cambidia company":"Rplc loa company",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 14,
                                        fontFamily: TextStyleTheme.text_Regular
                                            .fontFamily,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 13.h,
                                  ),
                                  Container(
                                    height: 1.h,
                                    alignment: Alignment.center,
                                    child: const Divider(
                                      color: Color(0xFFE5E5E5),
                                      thickness: 1,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 12.h,
                                  ),
                                  Container(
                                    width: 283.w,
                                    height: 65.h,
                                    margin: EdgeInsets.only(
                                      left: 32.w,
                                      right: 32.w,
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment
                                          .spaceBetween,
                                      children: [
                                        Container(
                                          height: 65.h,
                                          child: Obx(() {
                                            return Text.rich(
                                              TextSpan(
                                                children: [
                                                  TextSpan(
                                                    text: menuGetLoanPleasePay.tr,
                                                    style: TextStyle(
                                                      color: const Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                                  ),
                                                  TextSpan(
                                                    text: '${myloanCtl.count_qrExpired
                                                        .value}\n',
                                                    style: TextStyle(
                                                      color: const Color(0xFFFF3B30),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                  TextSpan(
                                                    text: '${menuGetLoanPayTimeOut.tr} ${myloanCtl
                                                        .timeQRDataExpired?.value}',
                                                    style: TextStyle(
                                                      color: const Color(0x7F1A1818),
                                                      fontSize: 12,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          }),
                                        ),
                                        InkWell(
                                          onTap: () {
                                            print('save Inage');
                                            myloanCtl.saveImageQrCode();
                                          },
                                          child: Container(
                                            width: 40.w,
                                            // height: 65.h,
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment
                                                  .start,
                                              children: [
                                                Container(
                                                  alignment: Alignment.topRight,
                                                  width: 40.w,
                                                  height: 40.h,
                                                  decoration: ShapeDecoration(
                                                    color: configTheme().colorScheme.onSecondary,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                      BorderRadius.circular(12),
                                                    ),
                                                  ),
                                                  child: Center(
                                                      child: SvgPicture.string(
                                                          AppSvgImage.save_payment)),
                                                ),
                                                SizedBox(
                                                  height: 4.h,
                                                ),
                                                Container(
                                                  alignment: Alignment.centerRight,
                                                  child: Center(
                                                    child: Text(
                                                      menuGetLoanSaveQR.tr,
                                                      textAlign: TextAlign.center,
                                                      style: TextStyle(
                                                        color: const Color(
                                                            0xFF1A1818),
                                                        fontSize: 14,
                                                        fontFamily: TextStyleTheme
                                                            .text_Regular.fontFamily,
                                                        fontWeight: FontWeight.w700,
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Container(
                                    height: 1.h,
                                    alignment: Alignment.center,
                                    child: const Divider(
                                      color: Color(0xFFE5E5E5),
                                      thickness: 1,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  //TODO guide payment
                                  Container(
                                    margin: EdgeInsets.only(
                                      left: 32.w,
                                      right: 32.w,
                                    ),
                                    alignment: Alignment.centerLeft,
                                    child: Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text: menuGetLoanStepPay.tr,
                                            style: TextStyle(
                                              color: const Color(0xBF1A1818),
                                              fontSize: 12,
                                              fontFamily:
                                              TextStyleTheme.text_Regular.fontFamily,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                            '\n${menuGetLoanStepPay1.tr}\n${menuGetLoanStepPay2.tr}\n${menuGetLoanStepPay3.tr}\n${menuGetLoanStepPay4.tr}\n',
                                            style: TextStyle(
                                              color: const Color(0xBF1A1818),
                                              fontSize: 12,
                                              fontFamily:
                                              TextStyleTheme.text_Regular.fontFamily,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          // TextSpan(
                                          //   text:
                                          //   '     การชำระเงินในแอพฯ เอเอเอ็ม อีกครั้ง หากสถานะยังไม่\n     อัพเดทกรุณาติดต่อทีมงาน',
                                          //   style: TextStyle(
                                          //     color: const Color(0xBF1A1818),
                                          //     fontSize: 12,
                                          //     fontFamily:
                                          //     TextStyleTheme.text_Regular.fontFamily,
                                          //     fontWeight: FontWeight.w400,
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          );
                        },
                      )
                    ],
                  ),
                ),
              );
            }));
  }

}