import 'package:AAMG/controller/transalation/en.dart';
import 'package:AAMG/controller/transalation/en_km.dart';
import 'package:AAMG/controller/transalation/km.dart';
import 'package:AAMG/controller/transalation/lo.dart';
import 'package:AAMG/controller/transalation/th.dart';
import 'package:get/get.dart';

class Messages extends Translations{
  @override
  Map<String,Map<String,String>> get keys =>{
    'km_KM':Km().messages,
    'en_US': US().messages,
    'th_TH': Th().messages,
    'lo_LA': LO().messages,
    'enKm_enKm':enKm().messages,
  };
}