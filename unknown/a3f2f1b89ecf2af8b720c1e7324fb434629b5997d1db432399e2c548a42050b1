import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../controller/register/register.controller.dart';
import '../../../controller/register/scan.controller.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/utils/AppSvgImage.dart';

class RegisterMRScanScreen extends StatefulWidget {
  const RegisterMRScanScreen({Key? key}) : super(key: key);

  @override
  State<RegisterMRScanScreen> createState() => _RegisterMRScanScreenState();
}

class _RegisterMRScanScreenState extends State<RegisterMRScanScreen> {
  final RegisterController registerCtl = Get.put(RegisterController());
  final ScanController cameraCtl = Get.put(ScanController());
  final MRController regisMr = Get.put(MRController());

  @override
  void initState() {
    super.initState();
    print('RegisterScanScreen');
  }

  @override
  void dispose() {
    cameraCtl.cameraController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<ScanController>(
        init: ScanController(),
        builder: (controller) {
          return Stack(
            children: [
              // Camera Preview
              controller.isCameraReady!.value
                  ? SizedBox(
                height: Get.height,
                width: Get.width,
                child: OverflowBox(
                  maxWidth: Get.height * controller.cameraController!.value.aspectRatio,
                  maxHeight: Get.height,
                  child: CameraPreview(controller.cameraController!),
                ),
              )
                  : const Center(child: CircularProgressIndicator()),

              // Transparent overlay with matching focus area
              CustomPaint(
                size: Size(Get.width, Get.height),
                painter: OverlayPainter(),
              ),

              // Custom frame for the focus area
              Positioned(
                top: 176.h,
                left: 47.w,
                child:InkWell(
                onTap: () async {
                    try {
                    AppLoading.loadingVerify(context);
                    final image =
                    await cameraCtl.cameraController!.takePicture();
                    cameraCtl.cameraController!.pausePreview();

                    cameraCtl.imgBlob.value = image.path;
                    cameraCtl.imgXFile = image;
                    cameraCtl.update();
                    await cameraCtl.getOCRData(context);

                    regisMr.enterfullnameController.text =
                    '${registerCtl.first_name.value.text} ${registerCtl.last_name.value.text}';
                    regisMr.idcardvalueoneController.text =
                    registerCtl.registerData.value.idCard.toString();
                    } catch (e) {
                    print(e);
                    }
                },
                child:
                  SizedBox(
                  width: 280.w,
                  height: 186.h,
                  child: Stack(
                    children: [
                      Align(
                        alignment: Alignment.topLeft,
                        child: SvgPicture.string(
                          AppSvgImage.scan_frame1,
                        ),
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: SvgPicture.string(
                          AppSvgImage.scan_frame2,
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomLeft,
                        child: SvgPicture.string(
                          AppSvgImage.scan_frame3,
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: SvgPicture.string(
                          AppSvgImage.scan_frame4,
                        ),
                      ),
                    ],
                  ),
                ),
                )
              ),


              Positioned(
                bottom: 50.h,
                left: 0,
                right: 0,
                child: Text(
                  'วางหน้าบัตรประชาชน ให้อยู่ในกรอบ\nเพื่อทำการสแกน',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                    fontFamily: TextStyleTheme.text_Regular.fontFamily,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // Header
              _buildHeader(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: Get.width,
      height: 106.h,
      child: Stack(
        children: [
          Container(
            width: Get.width,
            height: 106.h,
            padding: EdgeInsets.only(
              top: 71.h,
              left: 132.w,
              right: 113.w,
              bottom: 11.h,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 24.h,
                  child: Text(
                    'สแกนบัตรประชาชน',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              registerCtl.setScanIdcard2(true);
              // print("get Back");
            },
            child: Container(
              margin: EdgeInsets.only(left: 24.w, top: 60.h),
              child: SizedBox(
                  height: 24.h,
                  width: 24.w,
                  child: SvgPicture.string(AppSvgImage.back_btn)
              )
            ),
          ),
        ],
      ),
    );
  }
}

// CustomPainter for the purple overlay with a perfectly aligned hole
class OverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = AppColors.AAMPurple.withOpacity(0.7)
      ..style = PaintingStyle.fill;

    // Full-screen overlay
    Rect overlayRect = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawRect(overlayRect, paint);

    // Transparent area: Matching the container's shape and position
    Paint clearPaint = Paint()
      ..blendMode = BlendMode.clear;

    RRect focusRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        50.w,
        179.h,
        274.w,
        180.h,
      ),
      Radius.circular(18),
    );
    canvas.drawRRect(focusRect, clearPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
