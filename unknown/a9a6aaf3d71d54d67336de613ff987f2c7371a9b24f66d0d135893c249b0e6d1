import 'package:AAMG/controller/branch/branch.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../themes/app_colors.dart';
import '../../../themes/theme.dart';

class AreaPopUp {
  static AlertBranchArea(BuildContext context) {
    final BranchController branchCtl = Get.put(BranchController());
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 248.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 15.h,
                ),
                Container(
                  child: SvgPicture.string(
                      '<svg width="44" height="5" viewBox="0 0 44 5" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="44" height="5" rx="2.5" fill="#1A1818" fill-opacity="0.2"/></svg>'),
                ),
                SizedBox(
                  height: 26.h,
                ),
                SizedBox(
                    height: 128.h,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.5.w),
                      child: StaggeredGrid.count(
                        crossAxisCount: 2,
                        mainAxisSpacing: 16.h,
                        crossAxisSpacing: 16.w,
                        children: [
                          InkWell(
                              onTap: () {
                                branchCtl.setSelectedZone('กลาง');
                                branchCtl.setActivateSelectZone(false);
                                Navigator.pop(context,true);
                              },
                              child: Container(
                                width: 159.w,
                                height: 56.h,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: AppColors.textBlackColor
                                        .withOpacity(0.08),
                                    width: 1,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    'ภาคกลาง',
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium!
                                          .color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontWeight,
                                    ),
                                  ),
                                ),
                              )),
                          InkWell(
                              onTap: () {
                                branchCtl.setSelectedZone('ตะวันออกเฉียงเหนือ');
                                branchCtl.setActivateSelectZone(false);
                                Navigator.pop(context,true);
                              },
                              child: Container(
                                width: 159.w,
                                height: 56.h,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: AppColors.textBlackColor
                                        .withOpacity(0.08),
                                    width: 1,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    'ภาคอีสาน',
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium!
                                          .color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontWeight,
                                    ),
                                  ),
                                ),
                              )),
                          InkWell(
                              onTap: () {
                                branchCtl.setSelectedZone('ตะวันออก');
                                branchCtl.setActivateSelectZone(false);
                                Navigator.pop(context,true);
                              },
                              child: Container(
                                width: 159.w,
                                height: 56.h,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: AppColors.textBlackColor
                                        .withOpacity(0.08),
                                    width: 1,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    'ภาคตะวันออก',
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium!
                                          .color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontWeight,
                                    ),
                                  ),
                                ),
                              )),
                          InkWell(
                              onTap: () {
                                branchCtl.setSelectedZone('ตะวันตก');
                                branchCtl.setActivateSelectZone(false);
                                Navigator.pop(context,true);
                              },
                              child: Container(
                                width: 159.w,
                                height: 56.h,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: AppColors.textBlackColor
                                        .withOpacity(0.08),
                                    width: 1,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    'ภาคตะวันตก',
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium!
                                          .color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .titleLarge
                                          ?.fontWeight,
                                    ),
                                  ),
                                ),
                              )),
                        ],
                      ),
                    )),
              ],
            ),
          );
        });
  }
}
