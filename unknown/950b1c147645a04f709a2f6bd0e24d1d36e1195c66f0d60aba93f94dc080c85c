import 'package:flutter/material.dart';
import '../../../componance/themes/app_colors.dart';
import '../../../componance/utils/AppImageAssets.dart';
import '../../../componance/utils/constant/size.dart';
import '../../../componance/widgets/common_widgets/rounded_container.dart';
import '../../../componance/widgets/common_widgets/vertical_icon_text_button.dart';

class FeedbackSelectionList extends StatelessWidget {
  const FeedbackSelectionList({
    super.key,
    required this.text,
    this.padding,
    this.showBorder,
    this.onTapLike,
    this.onTapUnlike,
    required this.isLiked,
    required this.isDisliked,

  });

  final String text;
  final bool? showBorder;
  final EdgeInsets? padding;
  final void Function()? onTapLike,onTapUnlike;
  final bool? isLiked; // null = neither, true = liked, false = unliked
  final bool? isDisliked;

  @override
  Widget build(BuildContext context) {
    return CRoundedContainer(
      //  height: 100,
        width: double.infinity,
        padding: padding ?? const EdgeInsets.all(CSizes.sm),
        margin: const EdgeInsets.only(bottom: CSizes.space5),
        backgroundColor: Colors.white,
        showBorder: showBorder ?? true,
        borderColor: AppColors.softGrey,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(flex: 7,child: Text( text , style: Theme.of(context).textTheme.titleSmall!.apply(color: AppColors.darkerGrey,), textAlign: TextAlign.left,),),
            const SizedBox(width: CSizes.space5,),
            Flexible(
              flex: 4,
              child: Row(
                mainAxisAlignment:  MainAxisAlignment.end , // Correct comparison, no assignment
                children: [
                  // Show like button only if isLiked is true
                  if (isLiked == true )
                    CIconTextButtonWidget(
                      svgIcon: AppImageAssets.like_feedback_icon,
                      iconColor: Colors.green, // Show the like button in green
                      iconWidth: 18,
                      iconHeight: 18,
                      btnWidth: 47,
                      btnHeight: 33,
                      borderColor: AppColors.lightGrey,
                      borderRadius: CSizes.borderRadiusLg,
                      onTap: onTapLike,
                    )
                  else if(isLiked == null && isDisliked == null)
                  // Show like button with opacity when not liked
                    CIconTextButtonWidget(
                      svgIcon: AppImageAssets.like_feedback_icon,
                      iconColor: const Color(0xFF2D8E18).withOpacity(0.4),  // Opacity when not liked
                      iconWidth: 18,
                      iconHeight: 18,
                      btnWidth: 47,
                      btnHeight: 33,
                      borderColor: AppColors.lightGrey,
                      borderRadius: CSizes.borderRadiusLg,
                      onTap: onTapLike,
                    ),

                  // Show space only if dislike is active
                  if (isLiked == null && isDisliked == null)
                    const SizedBox(width: CSizes.spaceBetweenButton),

                  // Show unlike button only if isDisliked is true
                  if (isDisliked == true)
                    CIconTextButtonWidget(
                      svgIcon: AppImageAssets.unlike_feedback_icon,
                      iconColor: Colors.red,  // Show unlike button in red when disliked
                      iconWidth: 18,
                      iconHeight: 18,
                      btnWidth: 47,
                      btnHeight: 33,
                      borderColor: AppColors.lightGrey,
                      borderRadius: CSizes.borderRadiusLg,
                      onTap: onTapUnlike,
                    )
                  else if(isLiked == null )
                  // Show unlike button with opacity when not disliked
                    CIconTextButtonWidget(
                      svgIcon: AppImageAssets.unlike_feedback_icon,
                      iconColor: const Color(0xFFFF0000).withOpacity(0.4),  // Opacity when not disliked
                      iconWidth: 18,
                      iconHeight: 18,
                      btnWidth: 47,
                      btnHeight: 33,
                      borderColor: AppColors.lightGrey,
                      borderRadius: CSizes.borderRadiusLg,
                      onTap: onTapUnlike,
                    ),
                ],

              ),
            ),
          ],
        ));
  }
}