# This file is used to configure the Shorebird updater used by your app.
# Learn more at https://docs.shorebird.dev
# This file should be checked into version control.

# This is the unique identifier assigned to your app.
# Your app_id is not a secret and is just used to identify your app
# when requesting patches from Shorebird's servers.
# shorebird release android --target ./lib/main_aam_dev.dart --flavor aam_dev --flutter-version 3.13.9

app_id: e75e4f18-8702-4b74-bb46-3bba0f642a38
flavors:
  aam_dev: ccda70b7-1afb-42f9-b008-bc2a22016841
  aam_prod: 4b64f982-f0d5-43dd-bbc0-abaf909272ee
  rafco_dev: bacf5307-0089-4e44-a7b7-cfe63a4bd0e3
  rafco_prod: b57a914d-55ca-48af-847b-8d7486e5fed8
  rplc_dev: 297e10d7-2b7d-44e3-9969-af61f54045dd
  rplc_prod: 539a48f1-e347-4f82-a101-3763e9846ff0

# auto_update controls if Shorebird should automatically update in the background on launch.
# If auto_update: false, you will need to use package:shorebird_code_push to trigger updates.
# https://pub.dev/packages/shorebird_code_push
# Uncomment the following line to disable automatic updates.
auto_update: false
