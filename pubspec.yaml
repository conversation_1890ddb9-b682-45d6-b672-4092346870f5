# flutter build appbundle --build-name=<x.y.z> --build-number=x
# {lastest build} : flutter build appbundle --build-name=2.3.55 --build-number=2371
#  flutter build apk --build-number=2369 --build-name=2.3.55 --no-sound-null-safety
#  flutter build appbundle --flavor aam_prod -t lib/main_aam_prod.dart
# export PATH="/Users/<USER>/.shorebird/bin:$PATH"
# shorebird release android --flavor aam_dev -t lib/main_aam_dev.dart --flutter-version=3.22.3

#shorebird patch android --release-version=1.0.0+78 --flavor aam_dev -t lib/main_aam_dev.dart
# android เป็น 1.0.0+38
# ios เป็น 1.0.0+41
#  shorebird release android --flavor aam_prod -t lib/main_aam_prod.dart --flutter-version=3.22.3 #production 2495 2.5.0
# export PATH="/Users/<USER>/Flutterversion/flutter3.22.2/bin":$PATH

 #เปลี่ยนตรงนี้ตรงชื่อแอปตอนกดปิด AAM,RPLC,RAFCO

name: AAMG
description: AAMG AAM App

publish_to: 'none'
version: 2.5.3+2504

environment:
  sdk: '>=3.3.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  ags_authrest2: ^1.0.1
  cupertino_icons: ^1.0.2
  flutter_spinkit: ^5.2.0
  get: ^4.6.6
  get_storage: ^2.1.1
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.6
  double_back_to_close_app: ^2.1.0
  introduction_screen: ^3.1.12
  card_swiper: ^3.0.1
  pinput: ^5.0.1
  pin_code_fields: ^8.0.1
  persistent_bottom_nav_bar_v2: ^5.3.0
  buttons_tabbar: ^1.3.8
  flutter_staggered_grid_view: ^0.7.0
  percent_indicator: ^4.2.3
  carousel_slider: ^5.0.0
  flutter_advanced_switch: ^3.1.0
  flutter_dotenv: ^5.0.0
  flutter_inappwebview: ^6.0.0
  mask_text_input_formatter: ^2.9.0
  currency_text_input_formatter: ^2.2.3
  local_auth: ^2.1.8
  permission_handler: ^11.0.0
  html: ^0.15.4
  global_configuration: ^2.0.0-nullsafety.1
  uuid: ^4.2.2
  camera: ^0.10.5+2
  image_picker: ^1.0.4
  firebase_analytics: ^11.1.0
  firebase_auth: ^5.5.3
  firebase_core: ^3.4.0
#  firebase_database: ^10.0.13
  firebase_messaging: ^15.1.0
#  firebase_storage: ^11.1.1
  cloud_firestore: ^5.0.2
#  tflite_flutter: ^0.9.0
  # image_gallery_saver: ^2.0.3
  image_gallery_saver_plus: ^4.0.1
  dio: ^5.4.3+1
  google_maps_flutter: ^2.2.8
  pretty_qr_code: ^3.3.0
  intl: ^0.18.1
  flutter_local_notifications: ^17.2.2
  awesome_notifications: ^0.8.2

  shimmer: ^3.0.0
  flutter_secure_storage: ^9.2.2
  shorebird_code_push: ^1.1.4
  restart_app: ^1.3.2
  url_launcher: ^6.3.1
  google_fonts: ^6.2.1
  loading_animation_widget: ^1.2.1
  share_plus: ^11.0.0
  flutter_typeahead: ^5.2.0
  input_history_text_field: ^0.3.1
  natural_language: ^0.0.3
  lottie: ^3.1.2
  sentry: ^8.9.0
  app_badge_plus: ^1.1.5
  google_maps_flutter_web: ^0.5.10
  # qr_code_scanner: ^1.0.1
  qr_code_scanner_plus: ^2.0.10+1
  mqtt_client: ^10.3.0 #mqtt client connect aceleda RAFCO
  dart_jsonwebtoken: ^2.14.1
  open_store: ^0.5.0
  app_tutorial: ^1.0.6
  showcaseview: ^3.0.0
  flutter_tts: ^3.2.2
  flutter_facebook_auth: ^7.1.1
  google_sign_in: ^6.2.2
  flutter_line_sdk: ^2.3.8
  # flutter_jailbreak_detection: ^1.10.0
  connectivity_plus: ^6.1.1
  flutter_native_splash: ^2.4.1
  web_socket_channel: ^3.0.2
  package_info_plus: ^8.3.0
#  qr_code_scanner: ^0.4.0
#  tflite_flutter_helper:
#    git:
#      url: https://github.com/filofan1/tflite_flutter_helper.git
#      ref: 783f15e5a87126159147d8ea30b98eea9207ac70
#  tflite_v2: ^1.0.0
dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0

# dependency_overrides:
#   package_info_plus: any

#scripts:
#  splash_aam_dev: flutter pub run flutter_native_splash:create --path=flutter_native_splash_aam.yaml
#  splash_rafco_dev: flutter pub run flutter_native_splash:create --path=flutter_native_splash_rafco.yaml
#  splash_rplc_dev: flutter pub run flutter_native_splash:create --path=flutter_native_splash-rplc_dev.yaml
#  splash_aam_prod: flutter pub run flutter_native_splash:create --path=flutter_native_splash_aam.yaml
#  splash_rafco_prod: flutter pub run flutter_native_splash:create --path=flutter_native_splash_rafco.yaml
#  splash_rplc_prod: flutter pub run flutter_native_splash:create --path=flutter_native_splash-rplc_dev.yaml


flutter:

  uses-material-design: true

  assets:
    - assets/app_logo/
    - assets/app_logo/aam/
    - assets/app_logo/rplc/
    - assets/app_logo/rafco/
    - assets/svg_image/rafco/
    - assets/intro/aam/
    - assets/intro/rplc/
    - assets/intro/rafco/
    - assets/register/
    - assets/register/aam/
    - assets/register/rafco/
    - assets/register/rplc/
    - assets/register/icon/
    - assets/social/
    - assets/home/
    - assets/menu_icon/aam/
    - assets/menu_icon/rplc/
    - assets/menu_icon/rafco/
    - assets/myloan/
    - assets/loan/aam/
    - assets/loan/rplc/
    - assets/loan/rafco/
    - assets/poi/
    - assets/profile/
    - assets/notify/
    - assets/detect_object/
    - assets/mascot/
    - assets/
    - shorebird.yaml
    - assets/json/
    - assets/.env
    - assets/animation/
    - assets/pop_up_tutorial/
    - assets/tutorial/rafco/
    - assets/tutorial/aam/
    - assets/tutorial/rplc/
    - assets/branch/
    - assets/feedback/
    - assets/feedback/aam/
    - assets/feedback/rafco/
    - assets/feedback/rplc/
    - assets/feedback/svg_images/
    - assets/mr/
    - assets/bank/rplc/
    - assets/home/<USER>/
  fonts:
    - family: NotoSansThai
      fonts:
        - asset: assets/fonts/NotoSansThai-Regular.ttf
    - family: NotoSansThai-SemiBold
      fonts:
        - asset: assets/fonts/NotoSansThai-SemiBold.ttf
    - family: NotoSansThai-Bold
      fonts:
        - asset: assets/fonts/NotoSansThai-Bold.ttf
    - family: IBMPlexSansThai
      fonts:
        - asset: assets/fonts/IBMPlexSansThai-Regular.ttf
    - family: NotoSansLao
      fonts:
        - asset: assets/fonts/NotoSansLao-Regular.ttf
    - family: NotoSansLao-SemiBold
      fonts:
        - asset: assets/fonts/NotoSansLao-SemiBold.ttf
    - family: NotoSansLao-Bold
      fonts:
        - asset: assets/fonts/NotoSansLao-Bold.ttf
    - family: NotoSansKhmer
      fonts:
        - asset: assets/fonts/NotoSansKhmer-Regular.ttf
    - family: NotoSansKhmer-SemiBold
      fonts:
        - asset: assets/fonts/NotoSansKhmer-SemiBold.ttf
    - family: NotoSansKhmer-Bold
      fonts:
        - asset: assets/fonts/NotoSansKhmer-Bold.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
