// importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js');
// importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js');

// firebase.initializeApp({
//   apiKey: "AIzaSyC0Oq8PjS5TvN2Ud338uJ9T34-vxOaIkOY",
//   authDomain: "mappaam-44857.firebaseapp.com",
//   databaseURL: "https://mappaam-44857.firebaseio.com",
//   projectId: "mappaam-44857",
//   storageBucket: "mappaam-44857.appspot.com",
//   messagingSenderId: "465736603910",
//   appId: "1:465736603910:web:5c258a24c8ae77125e6687",
//   measurementId: "G-NG6XPX7MGD"
// });

// const messaging = firebase.messaging();
// // Optional: Custom background handler
// messaging.onBackgroundMessage(function(payload) {
//   console.log('[firebase-messaging-sw.js] Received background message ', payload);

//   self.registration.showNotification(payload.notification.title, {
//     body: payload.notification.body,
//     icon: '/icons/Icon-192.png' // You can customize this
//   });
// });