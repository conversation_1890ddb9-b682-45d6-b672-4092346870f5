{"project_info": {"project_number": "153559207355", "firebase_url": "https://mapprplc.firebaseio.com", "project_id": "mapprplc", "storage_bucket": "mapprplc.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:153559207355:android:5da8300ccd868a3188489e", "android_client_info": {"package_name": "com.ruampattanaleasing.rplc_app"}}, "oauth_client": [{"client_id": "153559207355-41idp3k6q7j73kdv53b241e46l16207c.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.ruampattanaleasing.rplc_app", "certificate_hash": "af2694e940627739f103a36e89726027afc0ac4d"}}, {"client_id": "153559207355-bru0lp2h4aubp60gseotcco3tv5772t8.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "153559207355-bru0lp2h4aubp60gseotcco3tv5772t8.apps.googleusercontent.com", "client_type": 3}, {"client_id": "153559207355-2489jv9e5sj9f30658m8tvoas25noenk.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.ruampattanaleasing.rplcapp"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:153559207355:android:bccb2a588d1d425088489e", "android_client_info": {"package_name": "com.ruampattanaleasing.rplc_appbeta"}}, "oauth_client": [{"client_id": "153559207355-bru0lp2h4aubp60gseotcco3tv5772t8.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "153559207355-bru0lp2h4aubp60gseotcco3tv5772t8.apps.googleusercontent.com", "client_type": 3}, {"client_id": "153559207355-2489jv9e5sj9f30658m8tvoas25noenk.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.ruampattanaleasing.rplcapp"}}]}}}], "configuration_version": "1"}