import 'dart:convert';
import 'dart:io';
import 'package:AAMG/service/endpoint.dart';
import 'package:ags_authrest2/ags_authrest.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:global_configuration/global_configuration.dart';
import 'package:http/http.dart' as http;

import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/service/AppUrl.dart';

class HttpService {
  static Map<String, String> noKeyRequestHeaders = {
    'Content-Type': 'application/json'
  };

  static Map<String, String> KeyRequestHeadersLambdaRPLC = {
    'Content-Type': 'application/json',
    'x-api-key': 'cRrMpCgqAb7FbzIVF9jfB9xNoWZsPL1O9isIJt7R'
  };

  static Map<String, String> KeyRequestHeadersLikePoint = {
    'Content-Type': 'application/json',
    'x-api-key': 'QX2uH^!DwwF%zvR8xvW6kTKF8YHgEA57'
  };

  static Map<String, String> KeyRequestHeadersLikePointV2 = {
    'Content-Type': 'application/json',
    'x-api-key': '841JEGYD93NOCDXWKx6ra8NMDw00Pzvu'
  };

  static Map<String, String> KeyRequestHeaders = {
    'Content-Type': 'application/json',
    'x-api-key': ''
  };

  static Map<String, String> KeyRequestHeadersReferral = {
    'x-api-key': '841JEGYD93NOCDXWKx6ra8NMDw00Pzvu'
  };

  static post(String url, Map jsonMap, Map<String, String> headers) async {
    var urlApi = Uri.parse(url);
    var response =
        await http.post(urlApi, headers: headers, body: jsonEncode(jsonMap));
    final statusCode = response.statusCode;
    final parsedResponse =
        await json.decode(response.body) as Map<String, dynamic>;
    parsedResponse['statusCode'] = statusCode;

    // print("parsedResponse ==> $parsedResponse");
    return parsedResponse;
  }

  static post_v2(String url, Map jsonMap, Map<String, String> headers) async {
    var urlApi = Uri.parse(url);
    var response =
        await http.post(urlApi, headers: headers, body: jsonEncode(jsonMap));
    final statusCode = response.statusCode;
    final parsedResponse = await json.decode(response.body);
    var responseMap = {"statusCode": statusCode, "result": parsedResponse};
    return responseMap;
  }

  static Future<dynamic> apiPostRequest2(Uri url, data) async {
    var response = await http.post(url, body: data);

    var xx = json.decode(response.body);
    return xx;
  }

  Future<dynamic> apiPostRequestLikePoint(Uri url, Map jsonMap) async {
    HttpClient httpClient = new HttpClient();
    HttpClientRequest request = await httpClient.postUrl(url);
    request.headers.set('content-type', 'application/json');
    request.headers.set('x-api-key', '841JEGYD93NOCDXWKx6ra8NMDw00Pzvu');
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }

  static postAssets(
      String url, Map jsonMap, Map<String, String> headers) async {
    var urlApi = Uri.parse(url);
    var response =
        await http.post(urlApi, headers: headers, body: jsonEncode(jsonMap));
    final statusCode = response.statusCode;
    final parsedResponse =
        await json.decode(response.body) as Map<String, dynamic>;
    parsedResponse['statusCode'] = statusCode;
    return parsedResponse;
  }

  static get(String url, Map<String, String> headers) async {
    var urlApi = Uri.parse(url);

    var response = await http.get(urlApi, headers: headers);
    final statusCode = response.statusCode;
    final parsedResponse =
        await json.decode(response.body) as Map<String, dynamic>;
    parsedResponse['statusCode'] = statusCode;
    return parsedResponse;
  }

  static put(String url, Map jsonMap, Map<String, String> headers) async {
    var urlApi = Uri.parse(url);
    final body = jsonMap;

    var response =
        await http.put(urlApi, headers: headers, body: jsonEncode(jsonMap));
    final statusCode = response.statusCode;
    final parsedResponse =
        await json.decode(response.body) as Map<String, dynamic>;

    // print("parsedResponse ==> $parsedResponse");
    parsedResponse['statusCode'] = statusCode;
    return parsedResponse;
  }

  static delete(String url, Map<String, String> headers) async {
    var urlApi = Uri.parse(url);

    var response = await http.delete(urlApi, headers: headers);
    final statusCode = response.statusCode;
    final parsedResponse =
        await json.decode(response.body) as Map<String, dynamic>;
    parsedResponse['statusCode'] = statusCode;
    return parsedResponse;
  }

  static getAuthEndPoint(String from_bu, String endpoint_api) async {
    try {

      debugPrint('from_bu: $from_bu');
      debugPrint('endpoint_api: $endpoint_api');

      var auth = Ags_restauth();
      auth.SECERT_JWT = 'eg5ejJaSX95pwiBCULkX15w1';
      auth.R_USER = 'aamg-uni';

      Map data = {};
      var url = '';
      if (endpoint_api.toString().contains("-UAT")) {
        //TODO UAT version
        var headers = {
          //TODO ปิดเพื่อใช้กับ API test แบบไม่มี Auth
          'Content-Type': 'application/json'
        };
        url = 'https://agilesoftgroup.com/AAMGp3-UAT/getEndpoint';
        data = {"bu": from_bu};
        var body = json.encode(data);

        var request = http.Request("POST", Uri.parse(url.toString()));
        // TODO ปิดเพื่อใช้กับ API test แบบไม่มี Auth
        request.body = body;
        request.headers.addAll(headers);
        http.StreamedResponse response = await request.send();
        var dataRaw = await response.stream.bytesToString();
        var responseData = json.decode(dataRaw);
        // print('responseData: $responseData');
        //TODO set endpoint API to GlobalConfiguration
        await GlobalConfiguration().loadFromMap(responseData['result']);
      } else {
        //TODO production version
        var headers = {
          'Authorization': auth
              .genTokenEncryp(), // TODO ปิดเพื่อใช้กับ API test แบบไม่มี Auth
          'Content-Type': 'application/json'
        };
        url = 'https://agilesoftgroup.com/AAMGp3/getEndpoint';
        data = {"bu": '${from_bu}_prod'};
        var body = json.encode(data);

        var request = http.Request("POST", Uri.parse(url.toString()));
        request.body = json.encode(auth.encrypbody(body)); // TODO Auth
        // request.body = body;
        request.headers.addAll(headers);
        http.StreamedResponse response = await request.send();
        var dataRaw = await response.stream.bytesToString();
        var responseData = json.decode(dataRaw);
        // debugPrint('responseData EndPoint : $responseData');
        //TODO set endpoint API to GlobalConfiguration
        await GlobalConfiguration().loadFromMap(responseData['result']);
      }
    } catch (e) {
      debugPrint(e.toString());
      Get.snackbar('EndPoint error', e.toString());
    }
  }

  // static Map<String, String> KeyRequestHeadersEndPoint = {
  //   'Content-Type': 'application/json',
  //   'x-api-key': getAuthEndPoint()
  // };

  static Future<dynamic> callAPIjwt(
      String method, String path, Map data) async {
    try {
      AppConfigService appConfig = Get.find<AppConfigService>();
      final environment = appConfig.environment;

      var auth = Ags_restauth();
      auth.SECERT_JWT = 'eg5ejJaSX95pwiBCULkX15w1';
      auth.R_USER = 'aamg-uni';

      //TODO Production version
      if(environment.toString().contains("_prod")){
        var headers = {
          'Authorization':
              auth.genTokenEncryp(),  // TODO มี Auth
          'Content-Type': 'application/json'
        };
        var body = json.encode(data);

        String url = path;
        ///'https://agilesoftgroup.com'
        var request = http.Request(method, Uri.parse(url.toString()));
        request.body = json.encode(auth.encrypbody(body)); // TODO มี Auth
        request.headers.addAll(headers);
        http.StreamedResponse response = await request.send();

        var dataRaw = await response.stream.bytesToString();
        var responseData = json.decode(dataRaw);
        return responseData;
      }else{
        //TODO UAT version
        var headers = {
          'Content-Type': 'application/json'
        };
        var body = json.encode(data);

        String url = path;
        var request = http.Request(method, Uri.parse(url.toString()));
        request.body = body;
        request.headers.addAll(headers);
        http.StreamedResponse response = await request.send();

        var dataRaw = await response.stream.bytesToString();
        var responseData = json.decode(dataRaw);
        return responseData;
      }

    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  static Future<dynamic> callAPIjwtLocal(
      String method, String path, Map data) async {
    try {
      AppConfigService appConfigService = Get.find<AppConfigService>();

      var auth = Ags_restauth();
      auth.SECERT_JWT = 'eg5ejJaSX95pwiBCULkX15w1';
      auth.R_USER = 'aamg-uni';
      var headers = {
        // 'Authorization':
        //     auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);

      String url =
          'https://731d-134-236-111-26.ngrok-free.app/AAMGp3-UAT/$path';

      // print('method: $method');
      // print('body: $body');
      // print('url: $url');

      ///'https://agilesoftgroup.com'
      var request = http.Request(method, Uri.parse(url.toString()));
      // request.body = json.encode(auth.encrypbody(body)); // TODO ปิดเพื่อใช้กับ API test แบบไม่มี Auth
      request.body = body;
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);
      // print('responseData: $responseData');

      return responseData;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  static Future<dynamic> callAPICloudflare(
      String method, String path, Map data) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'vhy.mxk3kfp@MPN_fuq';
      auth.R_USER = 'aam_api';
      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);
      var url = 'https://agilesoftgroup.com/AAMp/$path';
      var request = http.Request(method, Uri.parse(url.toString()));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);

      return responseData;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  static Future<dynamic> callAPIjwtRPLC(
      String method, String url, Map data) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'vhy.mxk3kfp@MPN_fuq';
      auth.R_USER = 'rplc-api';
      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);
      var request = http.Request(method, Uri.parse(url.toString()));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);

      return responseData;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  static Future<dynamic> callAPIjwtRAFCO(
      String method, String url, Map data) async {
    try {
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'vhy.mxk3kfp@MPN_fuq';
      auth.R_USER = 'rafco-api';
      var headers = {
        'Authorization': auth.genTokenEncryp(),
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);
      var request = http.Request(method, Uri.parse(url.toString()));
      request.body = json.encode(auth.encrypbody(body));
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();

      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);

      return responseData;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  static Future<dynamic> callAPIsmsService(
      String method, String url, Map data) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'vhy.mxk3kfp@MPN_fuq';
    auth.R_USER = 'sms-service';

    var headers = {
      'Authorization': auth.genTokenEncryp(), // genTokenEncryp() or genToken()
      'Content-Type': 'application/json'
    };
    // print('headers: $headers');
    var body = json.encode(data);

    ///'https://agilesoftgroup.com'
    var request = http.Request(method, Uri.parse(url));
    request.body = json.encode(auth.encrypbody(body));
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
      debugPrint("here");
      var dataRaw = await response.stream.bytesToString();
      var data = json.decode(dataRaw);

      debugPrint("data: $data");

      return data;
    } else {
      return 0;
    }
  }

  static Future<dynamic> callAPIFirebaseService(String url, Map data) async {
    var auth = Ags_restauth();
    auth.SECERT_JWT = 'XPEHsqqm0R5sO9cHkpOMaNEn';
    auth.R_USER = 'FCS_service';
    // var uniqueID = Uuid().v4();
    var headers = {
      'Authorization': auth.genTokenEncryp(), // genTokenEncryp() or genToken()
      'Content-Type': 'application/json'
    };
    var body = json.encode(data);

    ///'https://agilesoftgroup.com'
    var request = http.Request('POST', Uri.parse(url));
    request.body = json.encode(auth.encrypbody(body));

    ///encrypbody require genTokenEncryp()
    request.headers.addAll(headers);
    http.StreamedResponse response = await request.send();

    var dataRaw = await response.stream.bytesToString();
    // print(dataRaw);
    var responseData = json.decode(dataRaw);
    // print(responseData);

    return responseData;
  }

  static Future<dynamic> apiPostGetLinkACELDA(
      String url, data, String authorization) async {
    try {
      HttpClient httpClient = new HttpClient();
      HttpClientRequest request =
          await httpClient.postUrl(Uri.parse(url.toString()));
      request.headers.set('content-type', 'application/json');
      request.headers.set('path', url.toString());
      request.headers.set('Authorization', authorization.toString());
      request.add(utf8.encode(json.encode(data)));
      HttpClientResponse response = await request.close();
      // todo - you should check the response.statusCode
      String reply = await response.transform(utf8.decoder).join();
      httpClient.close();
      var xx = json.decode(reply);
      return xx;
    } catch (e) {
      debugPrint(e.toString());
      Get.snackbar('error', e.toString());
    }
  }
}
