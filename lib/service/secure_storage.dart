import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage{
  final _storage = const FlutterSecureStorage(
    // aOptions: AndroidOptions(
    //   encryptedSharedPreferences: true, // ใช้ encryptedSharedPreferences
    // ),
    // iOptions: IOSOptions(
    //   accessibility: KeychainAccessibility.first_unlock_this_device,
    // ),
  );
  Future writeSecureData(String key, String value)  async {
    var writeData = await _storage.write(key: key, value: value);
    return writeData;
  }
  Future readSecureData(String key) async {
    var readData = await _storage.read(key: key);
    return readData;
  }
  Future deleteSecureData(String key) async{
    var deleteData = await _storage.delete(key: key);
    return deleteData;
  }
  Future deleteSecureDataAll() async{
    var deleteData = await _storage.deleteAll();
    return deleteData;
  }
}