
class AppUrl {

  static String apiCenterLink = 'https://agilesoftgroup.com/';
  static String apiUploadS3_Center = 'https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/uploadS3_Center';
  static String ocrUrl = 'https://likepoint-assets-2cvcjlddla-as.a.run.app/thai-id-card-ocr';

  static Uri aamProvince = Uri.parse(
      "https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/province");
  static Uri aamAmp = Uri.parse(
      "https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/amphor");
  static Uri aamTum = Uri.parse(
      "https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/tumbol");

  static Uri getBalanceByUID = Uri.parse("https://new.likepoint.io/getBalanceByUID");
  static Uri getBalanceByPhoneNumber = Uri.parse("https://new.likepoint.io/getBalanceByphoneNumber");
  static Uri getBalanceByPhoneNumberV2 = Uri.parse('https://new.likepoint.io/newGetBalanceByphoneNumber');

  //TODO  ams4 API
  static Uri checkAAMPAY = Uri.parse("https://ams4.prachakij.com/api/v1/digitalLoanLimit");
  static Uri getCreditByGuaranteeID = Uri.parse('https://ams4.prachakij.com/api/v1/checkGuaranteeLoanLimit');
  static Uri getCreditByGuaranteeIDDigital = Uri.parse('https://ams4.prachakij.com/api/v1/digitalLoanLimit');
  static Uri QrCodePay = Uri.parse('https://byp1dympee.execute-api.ap-southeast-1.amazonaws.com/latest/qrcode_pay');


  //TODO Cloud Function API
  static Uri getKycSumsub = Uri.parse('https://us-central1-mappaam-44857.cloudfunctions.net/getKycSumsub');
  static Uri getKycBookbank = Uri.parse('https://us-central1-mappaam-44857.cloudfunctions.net/getdetailbookbank');
}
