import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';

class AnalyticsService {
  // สร้าง instance ของ FirebaseAnalytics
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  /// บันทึกการเปลี่ยนหน้า
  static Future<void> trackScreenUser({
    required String screen_name,
  }) async {
    // check screen_name
    var ScreenName = await sortScreenName(screen_name);
    var route = screen_name;

    if (ScreenName.isEmpty) {
      return;
    }

    // บันทึกหน้าที่ผู้ใช้กำลังดู
    await FirebaseAnalytics.instance.setCurrentScreen(
      screenName: screen_name,
      screenClassOverride: route,
    );

    // บันทึกเหตุการณ์เพิ่มเติม เช่น ผู้ใช้ดูโปรไฟล์ของตนเอง
    await FirebaseAnalytics.instance.logEvent(
      name: screen_name,
      parameters: {
        'user_id':
            Get.find<ProfileController>().profile.value.running.toString(),
        'phone': Get.find<ProfileController>().profile.value.phone.toString(),
      },
    );
  }

  /// ตั้งค่าผู้ใช้งาน (User Property)
  static Future<void> setUserProperty({
    required String name,
    required String value,
  }) async {
    await _analytics.setUserProperty(name: name, value: value);
  }

  /// บันทึก ID ของผู้ใช้ (User ID)
  static Future<void> setUserId(String userId) async {
    await _analytics.setUserId(id: userId);
  }

  static Future<String> sortScreenName(String screen_name) async {
    if (screen_name.isEmpty || screen_name == null) {
      return '';
    }
    // if cause by BU
    if (appConfigService.countryConfigCollection.toString().toLowerCase() ==
        "aam") {
    } else if (appConfigService.countryConfigCollection
            .toString()
            .toLowerCase() ==
        "rplc") {
    } else if (appConfigService.countryConfigCollection
            .toString()
            .toLowerCase() ==
        "rafco") {

    }

    return ""; //ยังไม่ได้กำหนดเงื่อนไข
  }
}
