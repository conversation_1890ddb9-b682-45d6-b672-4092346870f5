import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseService {
  static firebaseField(String collection, String doc, String field) async {
    return await FirebaseFirestore.instance
        .collection(collection)
        .doc(doc)
        .get()
        .then((DocumentSnapshot documentSnapshot) async {
      if (documentSnapshot.exists) {
        //print("Firebase" + documentSnapshot.get(field).toString());
        return documentSnapshot.get(field);
      } else {}
    }).catchError((error) async {
      log("Error firebaseField: $error");
      return false;
    });
  }

  static firebaseDoc(String collection, String doc) async {
    return await FirebaseFirestore.instance
        .collection(collection)
        .doc(doc)
        .get()
        .then((DocumentSnapshot documentSnapshot) async {
      if (documentSnapshot.exists) {
        //print("Firebase" + documentSnapshot.get(field).toString());
        return documentSnapshot;
      } else {}
    }).catchError((error) async {
      return false;
    });
  }

  static FirebaseCollectionMapStream(String collection) {
    return FirebaseFirestore.instance
        .collection(collection)
        .snapshots()
        .map((QuerySnapshot querySnapshot) {
      Map<String, dynamic> dataMap = {};
      querySnapshot.docs.forEach((doc) {
        dataMap[doc.id] = doc.data();
      });
      return dataMap;
    });
  }

  static Future<Map<String, dynamic>> FirebaseCollectionMap(
      String collection) async {
    try {
      Map<String, dynamic> dataMap = {};
      var res = await FirebaseFirestore.instance
          .collection(
            collection,
          )
          .get();
      int i;
      List<DocumentSnapshot> docList = res.docs;
      for (i = 0; i < docList.length; i++) {
        dataMap[docList[i].id] = docList[i].data();
      }
      return dataMap;
    } catch (e) {
      print("Error fetching collection: $e");
      return {}; // คืนค่าเป็นแผนที่ว่างในกรณีที่เกิดข้อผิดพลาด
    }
  }
}
