import 'package:flutter/material.dart';

// 1
enum Environment {
  aam_dev,
  aam_prod,
  rafco_dev,
  rafco_prod,
  rplc_dev,
  rplc_prod
}

// 2
class AppConfig extends InheritedWidget {
  // 3
  final Environment environment;
  final String appTitle;
  late String countryConfigCollection;
  final String pahtConfigCloudflareAPI;

  // 4
  AppConfig({
    Key? key,
    required Widget child,
    required this.environment,
    required this.appTitle,
    required this.countryConfigCollection,
    required this.pahtConfigCloudflareAPI,
  }) : super(
          key: key,
          child: child,
        );

  // 5
  static AppConfig of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<AppConfig>()!;
  }

  // 6
  @override
  bool updateShouldNotify(covariant InheritedWidget oldWidget) => false;
}
