import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'app_config.dart';
import 'controller/AppConfigService.dart';
import 'firebase_options.dart';
import 'main.dart';
import 'service/http_service.dart';
import 'package:firebase_core/firebase_core.dart';

void main() async {
  try {

    // print("rereerere");
    // print('test call data = > ${GlobalConfiguration().loadFromUrlIntoKey(url, key)}' );
    // await GlobalConfiguration().loadFromUrl("https://agilesoftgroup.com/AAMGp3-UAT/getEndpoint",queryParameters:{"bu" : "aam"}, headers: HttpService.KeyRequestHeadersEndPoint);
    // await GlobalConfiguration().loadFromUrl("https://ukxppovr06.execute-api.ap-southeast-1.amazonaws.com/latest/RPLC/GetGlobalConfigV3");
    await HttpService.getAuthEndPoint('aam', 'AAMGp3-UAT');
  } catch (e) {
    // something went wrong while fetching the config from the url ... do something
  }
  // define dev environment here
  var configuredApp = AppConfig(
    environment: Environment.aam_dev,
    appTitle: 'This is DEV env with AAM endpoints and code',
    countryConfigCollection: 'aam',
    pahtConfigCloudflareAPI: 'AAMGp3-UAT',
    child: MyApp(),
  );
  // run init here

  // await dotenv.load(fileName: ".env");
  await GetStorage.init();
  GetStorage box = GetStorage();
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  initScreen = box.read('initScreen') ?? 0;
  await box.write('initScreen', 1); //if already shown -> 1 else 0
  runApp(configuredApp);
}
