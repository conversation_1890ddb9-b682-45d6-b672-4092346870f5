import 'dart:async';

import 'package:AAMG/controller/notification/notify.controller.dart';
import 'package:AAMG/controller/transalation/messages.dart';
import 'package:AAMG/service/AppService.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
// import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:AAMG/view/screen/intro/intro_page.dart';
import 'package:sentry/sentry.dart';
import 'dart:developer' as developer;
// import 'package:flutter_web_plugins/flutter_web_plugins.dart'; //TODO only web เปิดใช้งานเฉพาะ Build Web

import 'appRoutes.dart';
import 'app_config.dart';
import 'controller/AppConfigService.dart';
import 'controller/bilndingsApp.dart';
import 'firebase_options.dart';
import 'service/route_observer.dart';
import 'view/componance/themes/theme.dart';

int initScreen = 0;
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Log หรือ forward เข้า controller ถ้าอยาก
  debugPrint("🌙 Background message: ${message.messageId}");
}


void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await GetStorage.init();
  GetStorage box = GetStorage();
  // box.remove('popup_shown_this_session');
  box.remove('popup_shown_this_session'); // ✅ ลบ flag ทันทีที่เปิดแอป

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  FlutterNativeSplash.remove();

 
  if(kIsWeb) {



    // setUrlStrategy(PathUrlStrategy()); //TODO เอา # ออกจาก URL บน Web  only web เปิดใช้งานเฉพาะ Build Web
  }

  initScreen = box.read('initScreen') ?? 0;
  await box.write('initScreen', 1); //if already shown -> 1 else 0

  debugPrint("## Firebase initializeApp");
  if(kIsWeb) {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.web_aam_dev,
    );
  } else {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  if (!kIsWeb) {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }
  // ✅ เคลียร์ flag ทุกรอบเปิดแอปใหม่

  FirebaseMessaging.onBackgroundMessage(
      Get.put(NotifyController()).firebaseMessagingBackgroundHandler);

  try {
    await dotenv.load(fileName: "assets/.env");
  } catch (e) {
    debugPrint("Error: $e");
  }
  await Sentry.init(
    (options) {
      options.dsn =
          'https://agilesoft-corporation-co-ltd.sentry.io/projects/aamg/?project=4508069136498688';
    },
    appRunner: () => runApp(
      MyApp(),
    ),
  );
}

final Map<String, String> languageCodes = {
  "RAFCO": "km-KH", // กัมพูชา
  "RPLC": "lo-LA", // ลาว
  "AAM": "th-TH", // ไทย
};
final FlutterTts flutterTts = FlutterTts();
Future<void> setVoice() async {
  // ตั้งค่าภาษาเป็นภาษาไทย
  await flutterTts.setLanguage('th-TH');

  // ตรวจสอบเสียงที่มีอยู่ และเลือกเสียงที่เหมาะสม
  List<dynamic> voices = await flutterTts.getVoices;
  print('Available voices: $voices');

  // ตัวอย่างการเลือกเสียง (ถ้ามีเสียงที่ตรงกันใน system TTS)
  await flutterTts.setVoice({'name': 'th-TH-x-xyz-local', 'locale': 'th-TH'});
  await flutterTts.setSpeechRate(1.20);
  await flutterTts.setPitch(1.20);
  await flutterTts.getVoices;
  await flutterTts.getEngines;
  await flutterTts.getSpeechRateValidRange;

  // await flutterTts.setSpeechRate(0.55);


}





Future<void> _messageHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('background message ${message.notification?.body.toString()}');
  }
}

class MyApp extends StatefulWidget {
  MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {

  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (!kIsWeb) {
      initPlatformState(); // ตรวจสอบ Jailbreak Detection
    }
    Future.delayed(Duration.zero, () {
      initConnectivity(); // ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต
    });
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus); // ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต
    setVoice();

  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription.cancel();
    super.dispose();
  }

  void fetchSupportedLanguages() async {
    List<dynamic> languages = await flutterTts.getLanguages;
    print("Supported languages: $languages");
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused) {
    } else {}
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    bool jailbroken;
    bool developerMode;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // try {
    //   jailbroken = await FlutterJailbreakDetection.jailbroken;
    //   developerMode = await FlutterJailbreakDetection.developerMode;
    // } on PlatformException {
    //   jailbroken = true;
    //   developerMode = true;
    // }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;
    // if (jailbroken == true || developerMode == true) {
    //   debugPrint("jailbroken");
    //   // await alertDialog
    // }
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      developer.log('Couldn\'t check connectivity status', error: e);
      return;
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) {
      return Future.value(null);
    }

    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    setState(() {
      _connectionStatus = result;
    });
    // ignore: avoid_print
    print('Connectivity changed: ${_connectionStatus[0]}');
    if(_connectionStatus[0] == ConnectivityResult.none) {
      //TODO แจ้งเตือนเมื่อไม่มีการเชื่อมต่ออินเทอร์เน็ต
      Get.snackbar(
          "No Internet Connection", "Please check your internet connection",
          snackPosition: SnackPosition.TOP,
          duration: const Duration(milliseconds: 3000));
    }
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    Get.put(AppConfigService(context: context));
    Get.lazyPut(()=>AppConfigService(context: context));
    ///เปลี่ยนภาษาตาม Config
    // fetchSupportedLanguages();
    // String selectedLanguage = AppConfig.of(context).countryConfigCollection.toString() == 'aam'
    //     ? 'AAM'
    //     : AppConfig.of(context).countryConfigCollection.toString() == 'rafco'
    //     ? 'RAFCO'
    //     : 'RPLC';
// print("selectedLanguage : "+selectedLanguage.toString());
//     setLanguageAndSpeak(selectedLanguage);
    ///
    //TODO ตัวจัดการการแจ้งเตือนทั้งหมดบนแอป
    NotifyController notifyController = Get.put(
      NotifyController(),
      permanent: true,
    );

    late final ThemeData lightTheme = configTheme(); // กำหนดธีมของแอพ

    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (_, child) {
          return AutoCloseKeyboard(
            child: GetMaterialApp(
              debugShowCheckedModeBanner: false,
              title: appConfigService.countryConfigCollection.toUpperCase(),
              theme: lightTheme,
              darkTheme: lightTheme,
              themeMode: ThemeMode.system,
              initialBinding: BilndingsApp(),
              // locale: const Locale('km', 'KM'),
              locale: AppConfig.of(context)
                          .countryConfigCollection
                          .toString() ==
                      'aam'
                  ? const Locale('th', 'Th')
                  : AppConfig.of(context).countryConfigCollection.toString() ==
                          'rafco'
                      ? const Locale('km', 'KM')
                      : AppConfig.of(context)
                                  .countryConfigCollection
                                  .toString() ==
                              'rplc'
                          ? const Locale('lo', 'LA')
                          : const Locale('en', 'US'),
              // locale: const Locale('en', 'US'),
              // locale: const Locale('th', 'TH'),
              // locale: const Locale('lo', 'LA'),
              fallbackLocale: AppConfig.of(context)
                          .countryConfigCollection
                          .toString() ==
                      'aam'
                  ? const Locale('th', 'Th')
                  : AppConfig.of(context).countryConfigCollection.toString() ==
                          'rafco'
                      ? const Locale('km', 'KM')
                      : AppConfig.of(context)
                                  .countryConfigCollection
                                  .toString() ==
                              'rplc'
                          ? const Locale('lo', 'LA')
                          : const Locale('en', 'US'),
              supportedLocales: const [
                Locale('en', 'US'),
              ],
              initialRoute: '/',
              getPages: Routes.pages, // กำหนดเส้นทางของแต่ละโมดูล
              home: const Stack(
                children: [
                  IntroPage(),
                ],
              ),
              translations: Messages(),
              navigatorKey: Get.key, // ใช้ navigatorKey ของ Get
              navigatorObservers: [
                AppRouteObserver(), // listener สำหรับการเปลี่ยนหน้า
              ],
            ),
          );
        });
  }
}

//TODO โหลดข้อมูล activity id กิจกรรม POI , likepoint2.0 จาก Firebase และเก็บไว้ใน GetX
// class AppFirebaseCofig extends StatelessWidget {
//   const AppFirebaseCofig({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     print("## AppFirebaseCofig");
//
//     //TODO checkFirebaseConnection ตรวจสอบการเชื่อมต่อ Firebase
//
//     Stream<bool> checkFirebaseConnection() async* {
//       try {
//         final appConfig = await Get.put(AppConfigService(context: context));
//         print('appConfig : ${appConfig.environment}');
//         final user = FirebaseAuth.instance.currentUser;
//         if (user != null) {
//           print('Firebase connection success: user is not null');
//           yield true;
//         } else {
//           print('Firebase connection error: user is null');
//           yield false;
//         }
//       } catch (e) {
//         debugPrint('Firebase connection error: $e');
//         yield false;
//       }
//     }
//
//     Stream<Map<String, dynamic>?> fetchDataStream() async* {
//       await for (bool isConnected in checkFirebaseConnection()) {
//         if (isConnected) {
//           print('Firebase connection success: fetching data');
//           print(Firebase.app().name);
//           yield* FirebaseService.FirebaseCollectionMapStream('config');
//         } else {
//           yield null; // เมื่อไม่มีการเชื่อมต่อหรือเกิดข้อผิดพลาด
//         }
//       }
//     }
//
//     return StreamBuilder<Map<String, dynamic>?>(
//       stream: fetchDataStream(),
//       // stream: FirebaseService.FirebaseCollectionMapStream('config'),
//       builder: (context, snapshot) {
//         print('snapshot: ${snapshot.connectionState}');
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           debugPrint('Loading Likepoint config');
//         } else if (snapshot.hasError) {
//           debugPrint('Error Likepoint config');
//         } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
//           debugPrint('No Data Found');
//         } else {
//           //TODO กำหนดค่าเริ่มต้น activity id Likepoint2.0 สำหรับ POI ของแอป
//           // Get.put(WebViewPointController()).setInitData();
//         }
//         return Container();
//       },
//     );
//   }
// }

class AutoCloseKeyboard extends StatelessWidget {
  final Widget child;
  AutoCloseKeyboard({required this.child});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        AppService.hideKeyboard();
      },
      child: child,
    );
  }
}
