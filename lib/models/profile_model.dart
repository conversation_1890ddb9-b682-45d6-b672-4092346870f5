class Profile {
  final String? running;
  final String? phone;
  final String? address;
  final String? tumbol;
  final String? amphur;
  final String? province;
  final String? zipcode;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? idcard;
  late final String? avatar;
  final String? username;
  final String? phoneFirebase;
  final String? birthday;
  final String? displayName;
  final String? app_agree_status;
  final String? privacy_agree_status;
  final String? loan_agree_status;
  final String? digital_agree_status;
  final String? ref_code;
  final String? ref_code_other;
  final String? branch_name;
  final String? aicp_agree_status;
  final String? aicp_privacy_status;
  final String? fb_uid;
  final String? line_uid;
  final String? apple_uid;
  final String? google_uid;
  final String? tg_uid;
  final String? wa_uid;
  final String? facebook_connect;
  final String? line_connect;
  final String? apple_connect;
  final String? google_connect;
  final String? tg_connect;
  final String? wa_connect;

  Profile(
      {this.running,
      this.phone,
      this.address,
      this.tumbol,
      this.amphur,
      this.province,
      this.zipcode,
      this.firstname,
      this.lastname,
      this.email,
      this.idcard,
      this.avatar,
      this.username,
      this.phoneFirebase,
      this.birthday,
      this.displayName,
      this.app_agree_status,
      this.privacy_agree_status,
      this.loan_agree_status,
      this.digital_agree_status,
      this.ref_code,
      this.ref_code_other,
      this.branch_name,
      this.aicp_agree_status,
      this.aicp_privacy_status,
      this.fb_uid,
      this.line_uid,
      this.apple_uid,
      this.google_uid,
      this.tg_uid,
      this.wa_uid,
      this.facebook_connect,
      this.line_connect,
      this.apple_connect,
      this.google_connect,
      this.tg_connect,
      this.wa_connect});

  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
        running: json['running'] as String?,
        phone: json['phone'] as String?,
        address: json['address'] as String? ?? '',
        tumbol: json['tumbol'] as String? ?? '',
        amphur: json['amphur'] as String? ?? '',
        province: json['province'] as String? ?? '',
        zipcode: json['zipcode'] as String? ?? '',
        firstname: json['firstname'] as String? ?? '',
        lastname: json['lastname'] as String? ?? '',
        email: json['email'] as String? ?? '',
        idcard: json['idcard'] as String? ?? '',
        avatar: json['avatar'] as String? ?? '',
        username: json['username'] as String?,
        phoneFirebase: json['phone_firebase'] as String?,
        birthday: json['birthday'] as String?,
        displayName: json['displayName'] as String?,
        app_agree_status: json['app_agree_status'] as String?,
        privacy_agree_status: json['privacy_agree_status'] as String?,
        loan_agree_status: json['loan_agree_status'] as String?,
        digital_agree_status: json['digital_agree_status'] as String?,
        ref_code: json['ref_coded'] as String?,
        ref_code_other: json['ref_code_other'] as String?,
        branch_name: json['branch_name'] as String?,
        aicp_agree_status: json['aicp_agree_status'] as String?,
        aicp_privacy_status: json['aicp_privacy_status'] as String?,
        fb_uid: json['fb_uid'] as String?,
        line_uid: json['line_uid'] as String?,
        apple_uid: json['apple_uid'] as String?,
        google_uid: json['google_uid'] as String?,
        tg_uid: json['tg_uid'] as String?,
        wa_uid: json['wa_uid'] as String?,
        facebook_connect: json['facebook_connect'] as String?,
        line_connect: json['line_connect'] as String?,
        apple_connect: json['apple_connect'] as String?,
        google_connect: json['google_connect'] as String?,
        tg_connect: json['tg_connect'] as String?,
        wa_connect: json['wa_connect'] as String?);
  }

  Map<String, dynamic> toJson() {
    return {
      'running': running,
      'phone': phone,
      'address': address,
      'tumbol': tumbol,
      'amphur': amphur,
      'province': province,
      'zipcode': zipcode,
      'firstname': firstname,
      'lastname': lastname,
      'email': email,
      'idcard': idcard,
      'avatar': avatar,
      'username': username,
      'phone_firebase': phoneFirebase,
      'birthday': birthday,
      'displayName': displayName,
      'app_agree_status': app_agree_status,
      'privacy_agree_status': privacy_agree_status,
      'loan_agree_status': loan_agree_status,
      'digital_agree_status': digital_agree_status,
      'ref_code': ref_code,
      'ref_code_other': ref_code_other,
      'branch_name': branch_name,
      'aicp_agree_status': aicp_agree_status,
      'aicp_privacy_status': aicp_privacy_status,
      'fb_uid': fb_uid,
      'line_uid': line_uid,
      'apple_uid': apple_uid,
      'google_uid': google_uid,
      'tg_uid': tg_uid,
      'wa_uid': wa_uid,
      'facebook_connect': facebook_connect,
      'line_connect': line_connect,
      'apple_connect': apple_connect,
      'google_connect': google_connect,
      'tg_connect': tg_connect,
      'wa_connect': wa_connect
    };
  }
}
