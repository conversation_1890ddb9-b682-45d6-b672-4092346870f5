class SocialContentModel {
  final String appname;
  final String branch;
  final String platform;
  final String title;
  final String content;
  final String url;
  final String timestamp;

  SocialContentModel({
    required this.appname,
    required this.branch,
    required this.platform,
    required this.title,
    required this.content,
    required this.url,
    required this.timestamp,
  });

  factory SocialContentModel.fromJson(Map<String, dynamic> json) {
    return SocialContentModel(
      appname: json['appname'] ?? '',
      branch: json['branch'] ?? '',
      platform: json['platform'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      url: json['url'] ?? '',
      timestamp: json['timestamp'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'appname': appname,
        'branch': branch,
        'platform': platform,
        'title': title,
        'content': content,
        'url': url,
        'timestamp': timestamp,
      };
}