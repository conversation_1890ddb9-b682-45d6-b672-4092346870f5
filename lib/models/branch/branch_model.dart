import 'package:get/get.dart';

class Branch {
  String branchName;
  String branchNameTh;
  String branchNameEn;
  String branchId;
  String province;
  String zone;
  String address;
  String addressTh;
  String addressEn;
  String addressDesc;
  String branchPhone;
  String lineId;
  String lineUrl;
  String facebookId;
  String facebookUrl;
  String telegram_id;
  String telegramUrl;
  String whatsapp_id;
  String whatsappUrl;
  String tiktokUrl;
  String mapUrl;
  String latitude;
  String longitude;

  Branch({
    required this.branchName,
    required this.branchNameTh,
    required this.branchNameEn,
    required this.branchId,
    required this.province,
    required this.zone,
    required this.address,
    required this.addressTh,
    required this.addressEn,
    required this.addressDesc,
    required this.branchPhone,
    required this.lineId,
    required this.lineUrl,
    required this.facebookId,
    required this.facebookUrl,
    required this.telegram_id,
    required this.telegramUrl,
    required this.whatsapp_id,
    required this.whatsappUrl,
    required this.tiktokUrl,
    required this.mapUrl,
    required this.latitude,
    required this.longitude,
  });

  factory Branch.fromJson(Map<String, dynamic> json) {
    return Branch(
      branchName: json['branch_name'] ?? '',
      branchNameTh: json['branch_name_th'] ?? '',
      branchNameEn: json['branch_name_en'] ?? '',
      branchId: json['branch_id']?.toString() ?? '',
      province: json['province'] ?? '',
      zone: json['zone'] ?? '',
      address: json['address'] ?? '',
      addressTh: json['address_th'] ?? '',
      addressEn: json['address_en'] ?? '',
      addressDesc: json['address_desc'] ?? '',
      branchPhone: json['branch_phone'] ?? '',
      lineId: json['line_id'] ?? '',
      lineUrl: json['line_url'] ?? '',
      facebookId: json['facebook_id'] ?? '',
      facebookUrl: json['facebook_url'] ?? '',
      telegram_id: json['telegram_id'] ?? '',
      telegramUrl: json['telegram_url'] ?? '',
      whatsapp_id: json['whatsapp_id'] ?? '',
      whatsappUrl: json['whatsapp_url'] ?? '',
      tiktokUrl: json['tiktok_url'] ?? '',
      mapUrl: json['map_url'] ?? '',
      latitude: json['latitude']?.toString() ?? '',
      longitude: json['longitude']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'branch_name': branchName,
      'branch_name_th': branchNameTh,
      'branch_name_en': branchNameEn,
      'branch_id': branchId,
      'province': province,
      'zone': zone,
      'address': address,
      'addressTh': addressTh,
      'addressEn': addressEn,
      'address_desc': addressDesc,
      'branch_phone': branchPhone,
      'line_id': lineId,
      'line_url': lineUrl,  
      'facebook_id': facebookId,
      'facebook_url': facebookUrl,
      'telegram_id': telegram_id,
      'telegram_url': telegramUrl,
      'whatsapp_id': whatsapp_id,
      'whatsapp_url': whatsappUrl,
      'tiktok_url': tiktokUrl,
      'map_url': mapUrl,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class BranchList extends GetxController {
  List<Branch> branches = <Branch>[].obs;

  void fromJsonList(List<dynamic> jsonList) {
    branches = jsonList.map((item) => Branch.fromJson(item)).toList().obs;
  }
}
