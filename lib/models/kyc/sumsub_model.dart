class SumsubModel {
  final String? sumsub_status;
  final String? sumsub_reviewAns;
  final String? sumsub_message;
  final bool? sumsub_passed;
  final bool? sumsub_caution;

  SumsubModel({
    this.sumsub_status,
    this.sumsub_reviewAns,
    this.sumsub_message,
    this.sumsub_passed,
    this.sumsub_caution,
  });

  factory SumsubModel.fromJson(Map<String, dynamic> json) {
    return SumsubModel(
      sumsub_status: json['sumsub_status'] as String?,
      sumsub_reviewAns: json['sumsub_reviewAns'] as String?,
      sumsub_message: json['sumsub_message'] as String?,
      sumsub_passed: json['sumsub_passed'] as bool?,
      sumsub_caution: json['sumsub_caution'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sumsub_status': sumsub_status,
      'sumsub_reviewAns': sumsub_reviewAns,
      'sumsub_message': sumsub_message,
      'sumsub_passed': sumsub_passed,
      'sumsub_caution': sumsub_caution,
    };
  }
}
