class ResponseAppConfig {
  ResponseAppConfig({
    this.running,
    this.update_time,
    this.update_user,
    this.create_time,
    this.create_user,
    this.row_spreadsheet,
    this.status_giftBox,
    this.status_aampay,
    this.alert_to_telegram,
    this.status_kycsumsub,
    this.status_kycbookbank,
    this.status_payathome,
    this.requestloan_withmap,
    this.FeelwellTV,
  });

  int? running;
  String? update_time;
  String? update_user;
  String? create_time;
  String? create_user;
  String? row_spreadsheet;
  String? status_giftBox;
  String? status_aampay;
  String? alert_to_telegram;
  String? status_kycsumsub;
  String? status_kycbookbank;
  String? status_payathome;
  String? requestloan_withmap;
  String? FeelwellTV;


  factory ResponseAppConfig.fromJson(Map<String, dynamic> json) =>
      ResponseAppConfig(
          running: json["running"] ?? 0 ,
          update_time: json["update_time"] ?? "",
          update_user: json["update_user"] ?? "",
          create_time: json["create_time"] ?? "",
        create_user: json["create_user"] ?? "",
        row_spreadsheet: json["row_spreadsheet"] ?? "",
        status_giftBox: json["status_giftBox"] ?? "",
        status_aampay: json["status_aampay"] ?? "",
        alert_to_telegram: json["alert_to_telegram"] ?? "",
        status_kycsumsub: json["status_kycsumsub"] ?? "",
        status_kycbookbank: json["status_kycbookbank"] ?? "",
        status_payathome: json["status_payathome"] ?? "",
        requestloan_withmap: json["requestloan_withmap"] ?? "",
        FeelwellTV: json["FeelwellTV"] ?? "",
      );

  Map<String, dynamic> toJson() => {
    "running": running,
    "update_time": update_time,
    "update_user": update_user,
    "create_time": create_time,
    "create_user": create_user,
    "row_spreadsheet": row_spreadsheet,
    "status_giftBox": status_giftBox,
    "status_aampay": status_aampay,
    "alert_to_telegram": alert_to_telegram,
    "status_kycsumsub": status_kycsumsub,
    "status_kycbookbank": status_kycbookbank,
    "status_payathome": status_payathome,
    "requestloan_withmap": requestloan_withmap,
    "FeelwellTV": FeelwellTV,
  };
}


class ResponseMobileOS {
  ResponseMobileOS({
    this.deviceOS
  });

  String? deviceOS;


  factory ResponseMobileOS.fromJson(Map<String, dynamic> json) =>
      ResponseMobileOS(
        deviceOS: json["os"] ?? ""
      );

  Map<String, dynamic> toJson() => {
    "deviceOS": deviceOS,
  };
}
