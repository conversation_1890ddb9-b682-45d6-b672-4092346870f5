import '../app_config.dart';
import 'package:get/get.dart';
class PopUpModel {
  final int popupId;
  final String notifyPic;
  final String popupType;
  final String popupPath;
  final String popupScope;


  PopUpModel({
    required this.popupId,
    required this.notifyPic,
    required this.popupType,
    required this.popupPath,
    required this.popupScope,


  });

  factory PopUpModel.fromJson(Map<String, dynamic> json) {
    final config = AppConfig.of(Get.context!).countryConfigCollection.toString();
    String notifyPic = '';

    if (config == 'aam') {
      notifyPic = json['notify_pic'] ?? '';
    } else if (config == 'rplc') {
      notifyPic = json['url_img_news'] ?? '';
    } else {
      notifyPic = json['link_ads'] ?? '';
    }

    return PopUpModel(
      popupId:json['running'] ?? '',
      notifyPic: notifyPic,
      popupType: json['popup_type'] ?? '',
      popupPath: json['popup_path'] ?? '',
        popupScope:  json['popup_scope'] ?? '',

    );
  }



}
