class MRResponse {
  final String? mr_id;
  final String? mr_fname;
  final String? mr_lname;
  final String? mr_phone;
  final String? mr_rank;

  MRResponse({
    this.mr_id,
    this.mr_fname,
    this.mr_lname,
    this.mr_phone,
    this.mr_rank,
  });

  factory MRResponse.fromJson(Map<String, dynamic> json) => MRResponse(
        mr_id: json['mr_id'] as String,
        mr_fname: json['mr_fname'] as String,
        mr_lname: json['mr_lname'] as String,
        mr_phone: json['mr_phone'] as String,
        mr_rank: json['mr_rank'] as String,
      );
  Map<String, dynamic> toJson() {
    return {
      'mr_id': mr_id,
      'mr_fname': mr_fname,
      'mr_lname': mr_lname,
      'mr_phone': mr_phone,
      'mr_rank': mr_rank,
    };
  }
}


class ReferralMR{
  // final String? transection_close;
  // final String? transection_pending;
  final String? mr_name;
  final String? cust_name;
  final String? cust_phone;
  final String? refcode;
  final String? status_grant;
  final String? status;

  ReferralMR({
    // this.transection_close,
    // this.transection_pending,
    this.mr_name,
    this.cust_name,
    this.cust_phone,
    this.refcode,
    this.status_grant,
    this.status,
  });

  factory ReferralMR.fromJson(Map<String, dynamic> json) => ReferralMR(
    // transection_close: json['transection_close'] as String,
    // transection_pending: json['transection_pending'] as String,
    mr_name: json['mr_name'] as String,
    cust_name: json['cust_name'] as String,
    cust_phone: json['cust_phone'] as String,
    refcode: json['refcode'] as String,
    status_grant: json['status_grant'] as String,
    status: json['status'] as String,
  );
  // Map<String, dynamic> toJson() {
  //   return {
  //     'transection_close': transection_close,
  //     'transection_pending': transection_pending,
  //     'mr_name': mr_name,
  //     'cust_name': cust_name,
  //     'cust_phone': cust_phone,
  //     'refcode': refcode,
  //     'status_grant': status_grant,
  //   };
  // }
}


class ReferralCode {
  final String? idReferralCode ;
  final String? referralCode;

  ReferralCode({
    this.idReferralCode,
    this.referralCode
  });

  factory ReferralCode.from(Map<String,dynamic> json){
    return ReferralCode(
      idReferralCode: json['id'] as String,
      referralCode: json['referralCode'] as String
    );
  }

  Map<String,dynamic> toJson(){
    return {
      'idReferralCode':idReferralCode,
      'referralCode':referralCode
    };
  }
}
