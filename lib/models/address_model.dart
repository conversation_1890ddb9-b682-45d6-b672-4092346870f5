class City {
   int? cityId;
   String? cityNameLocal;
   String? cityNameThai;
   String? cityNameEng;
   String? branch;

  City({
     this.cityId ,
      this.cityNameLocal,
      this.cityNameThai,
      this.cityNameEng,
      this.branch,
  });

  factory City.fromJson(Map<String, dynamic> json) => City(
    cityId: json['city_id'] as int?,
    cityNameLocal: json['city_name_local'] ?? "",
    cityNameThai: json['city_name_thai'] ?? "",
    cityNameEng: json['city_name_eng'] ?? "",
    branch: json['branch'] ?? "",
  );
}

class CityResponse {
  final List<City> cities;

  CityResponse({
    required this.cities,
  });

  factory CityResponse.fromJson(Map<String, dynamic> json) => CityResponse(
    cities: (json['cities'] as List<dynamic>)
        .map((city) => City.fromJson(city as Map<String, dynamic>))
        .toList(),
  );
}


class District {
   int? districtId;
   String? districtNameLocal;
   String? districtNameThai;
   String? districtNameEng;
   String? districtZipcode;

  District({
     this.districtId,
     this.districtNameLocal,
      this.districtNameThai,
      this.districtNameEng,
     this.districtZipcode,
  });

  factory District.fromJson(Map<String, dynamic> json) => District(
    districtId: json['district_id'] as int?,
    districtNameLocal: json['district_name'] as String,
    districtNameThai: json['district_name_thai'] as String,
    districtNameEng: json['district_name_eng'] as String,
    districtZipcode: json['district_zipcode'] as String,
  );
}

class DistrictResponse {
  final List<District> districts;

  DistrictResponse({
    required this.districts,
  });

  factory DistrictResponse.fromJson(Map<String, dynamic> json) => DistrictResponse(
    districts: (json['districts'] as List<dynamic>)
        .map((district) => District.fromJson(district as Map<String, dynamic>))
        .toList(),
  );
}

class SubDistrict {
  int? subDistrictId;
  String? subDistrictNameLocal;
  String? subDistrictNameThai;
  String? subDistrictNameEng;
  String? districtZipcode;

  SubDistrict({
    this.subDistrictId,
    this.subDistrictNameLocal,
    this.subDistrictNameThai,
    this.subDistrictNameEng,
    this.districtZipcode,
  });

  factory SubDistrict.fromJson(Map<String, dynamic> json) => SubDistrict(
    subDistrictId: json['sub_district_id'] as int?,
    subDistrictNameLocal: json['sub_district_name'] as String,
    subDistrictNameThai: json['sub_district_name_thai'] as String,
    subDistrictNameEng: json['sub_district_name_eng'] as String,
    districtZipcode: json['district_zipcode'] as String,
  );
}

class SubDistrictResponse {
  final List<SubDistrict> subDistricts;

  SubDistrictResponse({
    required this.subDistricts,
  });

  factory SubDistrictResponse.fromJson(Map<String, dynamic> json) => SubDistrictResponse(
    subDistricts: (json['sub_districts'] as List<dynamic>)
        .map((subDistrict) => SubDistrict.fromJson(subDistrict as Map<String, dynamic>))
        .toList(),
  );
}