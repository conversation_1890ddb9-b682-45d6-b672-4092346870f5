class ResponseMemberLikePoint {
  ResponseMemberLikePoint({
    this.id,
    this.createdAt,
    this.modifiedBy,
    this.documentStatus,
    this.firstName,
    this.lastName,
    this.kycStatus,
    this.phone,
    this.email,
    this.linkFacebook,
    this.linkLine,
    this.linkGoogle,
    this.firebaseUid,
    this.freezeStatus,
    this.gender,
    this.birthday,
    this.referralCode,
    this.displayPicture,
    this.displayName,
  });

  String? id;
  String? createdAt;
  dynamic modifiedBy;
  bool? documentStatus;
  dynamic firstName;
  dynamic lastName;
  bool? kycStatus;
  dynamic phone;
  String? email;
  bool? linkFacebook;
  bool? linkLine;
  bool? linkGoogle;
  String? firebaseUid;
  bool? freezeStatus;
  dynamic gender;
  dynamic birthday;
  String? referralCode;
  String? displayPicture;
  String? displayName;

  factory ResponseMemberLikePoint.fromJson(Map<String, dynamic> json) => ResponseMemberLikePoint(
    id: json["id"],
    createdAt: json["createdAt"],
    modifiedBy: json["modifiedBy"],
    documentStatus: json["documentStatus"],
    firstName: json["firstName"],
    lastName: json["lastName"],
    kycStatus: json["kycStatus"],
    phone: json["phone"],
    email: json["email"],
    linkFacebook: json["linkFacebook"],
    linkLine: json["linkLine"],
    linkGoogle: json["linkGoogle"],
    firebaseUid: json["firebaseUid"],
    freezeStatus: json["freezeStatus"],
    gender: json["gender"],
    birthday: json["birthday"],
    referralCode: json["referralCode"],
    displayPicture: json["displayPicture"],
    displayName: json["displayName"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "createdAt": createdAt,
    "modifiedBy": modifiedBy,
    "documentStatus": documentStatus,
    "firstName": firstName,
    "lastName": lastName,
    "kycStatus": kycStatus,
    "phone": phone,
    "email": email,
    "linkFacebook": linkFacebook,
    "linkLine": linkLine,
    "linkGoogle": linkGoogle,
    "firebaseUid": firebaseUid,
    "freezeStatus": freezeStatus,
    "gender": gender,
    "birthday": birthday,
    "referralCode": referralCode,
    "displayPicture": displayPicture,
    "displayName": displayName,
  };
}