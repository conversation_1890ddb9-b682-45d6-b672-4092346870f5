class ResponseContracDigi {
  ResponseContracDigi({
    this.guarantee_id,
    this.current_contract,
    this.money_approve,
    this.debt_loan,
    this.remain_loan,
  });

  String? guarantee_id;
  int? current_contract;
  int? money_approve;
  int? debt_loan;
  int? remain_loan;

  factory ResponseContracDigi.fromJson(dynamic json) => ResponseContracDigi(
        guarantee_id: json["guarantee_id"]?.toString() ?? "",
        current_contract: _toInt(json["current_contract"]),
        money_approve: _toInt(json["money_approve"]),
        debt_loan: _toInt(json["debt_loan"]),
        remain_loan: _toInt(json["remain_loan"]),
      );

  Map<String, dynamic> toJson() => {
        "guarantee_id": guarantee_id,
        "current_contract": current_contract,
        "money_approve": money_approve,
        "debt_loan": debt_loan,
        "remain_loan": remain_loan,
      };

  static int _toInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed != null) return parsed.toInt();
    }
    return 0;
  }
}
