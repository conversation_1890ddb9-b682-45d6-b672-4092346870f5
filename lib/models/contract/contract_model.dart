class ContractModel {
  final String? ctt_code;
  final String? guarantee_type;
  final String? guarantee_type_name;
  final String? loan_amount;
  final String? remain;
  final String? nextpay;
  final String? due_date;

  ContractModel({
    this.ctt_code,
    this.guarantee_type,
    this.guarantee_type_name,
    this.loan_amount,
    this.remain,
    this.nextpay,
    this.due_date,
  });

  factory ContractModel.fromJson(Map<String, dynamic> json) {
    return ContractModel(
      ctt_code: json['ctt_code'] as String?,
      guarantee_type: json['guarantee_type'] as String?,
      guarantee_type_name: json['guarantee_type_name'] as String?,
      loan_amount: json['loan_amount'] as String?,
      remain: json['remain'] as String?,
      nextpay: json['nextpay'] as String?,
      due_date: json['due_date'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'ctt_code': ctt_code,
      'guarantee_type': guarantee_type,
      'guarantee_type_name': guarantee_type_name,
      'loan_amount': loan_amount,
      'remain': remain,
      'nextpay': nextpay,
      'due_date': due_date
    };
  }
}
