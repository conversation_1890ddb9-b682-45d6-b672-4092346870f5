class Period {
  final String? ctt_code;
  final String? nextpay;
  final String? due_date;

  Period({
    this.ctt_code,
    this.nextpay,
    this.due_date,
  });

  factory Period.fromJson(Map<String, dynamic> json) {
    return Period(
      ctt_code: json['ctt_code'] as String?,
      nextpay: json['nextpay'] as String?,
      due_date: json['due_date'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'ctt_code': ctt_code, 'nextpay': nextpay, 'due_date': due_date};
  }
}

class ContractList {
  final String? ctt_code;
  final String? guarantee_type;
  final String? guarantee_type_name;
  final String? loan_amount;
  final String? remain;
  final String? nextpay;
  final String? due_date;
  final String? periods;
  final String? paid_periods;
  final String? name;
  final String? interest;

  ContractList({
    this.ctt_code,
    this.guarantee_type,
    this.guarantee_type_name,
    this.loan_amount,
    this.remain,
    this.nextpay,
    this.due_date,
    this.periods,
    this.paid_periods,
    this.name,
    this.interest,
  });

  factory ContractList.fromJson(Map<String, dynamic> json) {
    return ContractList(
      ctt_code: json['ctt_code'] as String?,
      guarantee_type: json['guarantee_type'] as String?,
      guarantee_type_name: json['guarantee_type_name'] as String?,
      loan_amount: json['loan_amount'] as String?,
      remain: json['remain'] as String?,
      nextpay: json['nextpay'] as String?,
      due_date: json['due_date'] as String?,
      periods: json['periods'] as String?,
      paid_periods: json['paid_periods'] as String?,
      name: json['name'] as String?,
      interest: json['interest'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ctt_code': ctt_code,
      'guarantee_type': guarantee_type,
      'guarantee_type_name': guarantee_type_name,
      'loan_amount': loan_amount,
      'remain': remain,
      'nextpay': nextpay,
      'due_date': due_date,
      'periods': periods,
      'paid_periods': paid_periods,
      'name': name,
      'interest': interest,
    };
  }
}

class ContractPending {
  final String? grant_id;
  final String? guarantee_type;
  final String? ctt_code;
  final String? loan_amount;
  final String? loan_periods;
  final String? loan_status;
  final String? grant_status;

  ContractPending({
    this.grant_id,
    this.guarantee_type,
    this.ctt_code,
    this.loan_amount,
    this.loan_periods,
    this.loan_status,
    this.grant_status,
  });

  factory ContractPending.fromJson(Map<String, dynamic> json) {
    return ContractPending(
      grant_id: json['grant_id'] ?? '',
      guarantee_type: json['guarantee_type'] ?? '1',
      ctt_code: json['ctt_code'] ?? '',
      loan_amount: json['loan_amount'] ??'0',
      loan_periods: json['loan_periods'] ?? '0',
      loan_status: json['loan_status'] ?? "pending",
      grant_status: json['grant_status'] ?? "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'grant_id': grant_id,
      'guarantee_type': guarantee_type,
      'ctt_code': ctt_code,
      'loan_amount': loan_amount,
      'loan_periods': loan_periods,
      'loan_status': loan_status,
      'grant_status': grant_status
    };
  }
}
