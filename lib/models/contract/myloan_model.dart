class MyLoan {
  MyLoan({
    required this.ctt_code,
  });

  String? ctt_code;

  factory MyLoan.fromJson(Map<String, dynamic> json) => MyLoan(
    ctt_code: json["ctt_code"] ?? "",
  );
  Map<String, dynamic> toJson() => {
    "ctt_code": ctt_code,
  };
}

class Credit {
  Credit({
    required this.guarantee_id,
    required this.ctt_code
  });

  String? guarantee_id;
  String? ctt_code;

  factory Credit.fromJson(Map<String, dynamic> json) => Credit(
    guarantee_id: json["guarantee_id"] ?? "",
    ctt_code: json["ctt_code"] ?? "",
  );
  Map<String, dynamic> toJson() => {
    "guarantee_id": guarantee_id,
    "ctt_code": ctt_code,
  };
}

class CreditDigi {
  CreditDigi({
    required this.cust_code,
  });

  String? cust_code;

  factory CreditDigi.fromJson(Map<String, dynamic> json) => CreditDigi(
    cust_code: json["cust_code"] ?? "",
  );
  Map<String, dynamic> toJson() => {
    "cust_code": cust_code,
  };
}

class ResponseMyLoan {
  ResponseMyLoan({
    this.ctt_code,
    this.guarantee_id,
    this.guarantee_type,
    this.guarantee_type_name,
    this.fullname,
    this.data,
    this.mn_total,
    this.mn_totalInt,
    this.payed,
    this.remain,
    this.payedInt,
    this.remainInt,
    this.nextpay,
    this.nextpayQR,
    this.due_date,
    this.periods,
    this.from,
    this.branchPICO,
    this.loanType // สินเชื่อออนไลน์
  });

  String? ctt_code;
  String? guarantee_id;
  String? guarantee_type;
  String? guarantee_type_name;
  String? fullname;
  String? data;
  String? mn_total;
  int? mn_totalInt;
  String? payed;
  String? remain;
  int? payedInt;
  int? remainInt;
  String? nextpay;
  int? nextpayQR;
  String? due_date;
  int? periods;
  String? from;
  String? branchPICO;
  String? loanType; // สินเชื่อออนไลน์

  factory ResponseMyLoan.fromJson(Map<String, dynamic> json) => ResponseMyLoan(
    ctt_code: json["ctt_code"] ?? "",
    guarantee_id: json["guarantee_id"] ?? "",
    guarantee_type: json["guarantee_type"] ?? "",
    guarantee_type_name: json["guarantee_type_name"] ?? "",
    fullname: json["fullname"] ?? "",
    data: json["data"] ?? "",
    mn_total: json["mn_total"] ?? "0",
    mn_totalInt: json["mn_totalInt"] ?? 0,
    payed: json["payed"] ?? "0",
    remain: json["remain"] ?? "0",
    payedInt: json["payedInt"] ?? 0,
    remainInt: json["remainInt"] ?? 0,
    nextpay: json["nextpay"] ?? "0",
    nextpayQR: json["nextpayQR"] ?? 0,
    due_date: json["due_date"] ?? "",
    periods: json["periods"] ?? 0,
    from: json["from"] ?? "",
    branchPICO: json["branchPICO"] ?? "",
    loanType: json["loanType"] ?? "", // สินเชื่อออนไลน์
  );

  Map<String, dynamic> toJson() => {
    "ctt_code": ctt_code,
    "guarantee_id": guarantee_id,
    "guarantee_type": guarantee_type,
    "guarantee_type_name": guarantee_type_name,
    "fullname": fullname,
    "data": data,
    "mn_total": mn_total,
    "mn_totalInt": mn_totalInt,
    "payed": payed,
    "remain": remain,
    "payedInt": payedInt,
    "remainInt": remainInt,
    "nextpay": nextpay,
    "nextpayQR": nextpayQR,
    "due_date": due_date,
    "periods": periods,
    "from": from,
    "branchPICO": branchPICO,
    // "branchPICO": loanType // สินเชื่อออนไลน์
  };
}

class ResponseItemLastPay {
  ResponseItemLastPay({
    this.pay_date,
    this.pay_time,
    this.pay_amount,
    this.pay_details,
    this.pay_url,
    this.pay_date_TH
  });

  String? pay_date;
  String? pay_time;
  String? pay_amount;
  String? pay_details;
  String? pay_url;
  String? pay_date_TH;

  factory ResponseItemLastPay.fromJson(Map<String, dynamic> json) => ResponseItemLastPay(
      pay_date: json["pay_date"] ?? "",
      pay_time: json["pay_time"] ?? "00:00",
      pay_amount: json["pay_amount"] ?? "",
      pay_details: json["pay_details"] ?? "",
      pay_url: json["pay_url"] ?? "",
      pay_date_TH: json["pay_date_TH"] ?? ""
  );

  Map<String, dynamic> toJson() => {
    "pay_date": pay_date,
    "pay_time": pay_time,
    "pay_amount": pay_amount,
    "pay_details": pay_details,
    "pay_url": pay_url,
    "pay_date_TH": pay_date_TH
  };
}