class ReferralDownload {
  final String createTime;
  final String firstName;
  final String lastName;
  final String phone;
  final String imageUrl;
  final String refCode;

  ReferralDownload({
    required this.createTime,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.imageUrl,
    required this.refCode,
  });

  factory ReferralDownload.fromJson(Map<String, dynamic> json) => ReferralDownload(
        createTime: json['create_time'],
        firstName: json['cust_fname'],
        lastName: json['cust_lname'],
        phone: json['cust_phone'],
        imageUrl: json['cust_img'],
        refCode: json['cust_ref_code'],
      );
  }
