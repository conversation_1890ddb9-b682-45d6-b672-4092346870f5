class SendCode {
  SendCode({
    required this.from,
    required this.phone,
    required this.typeSMS,
  });

  String from;
  String phone;
  String typeSMS;

  factory SendCode.fromJson(Map<String, dynamic> json) => SendCode(
    from: json["from"],
    phone: json["phone"],
    typeSMS: json["typeSMS"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone,
    "from": from,
    "typeSMS": typeSMS,
  };
}

class ResponseSendCode {
  ResponseSendCode({
    this.success,
    this.statusCode,
    this.otp,
    this.refCode,
    this.result,
  });

  bool? success;
  int? statusCode;
  int? otp;
  String? refCode;
  bool? result;

  factory ResponseSendCode.fromJson(Map<String, dynamic> json) => ResponseSendCode(
    success: json["success"],
    statusCode: json["statusCode"],
    otp: json["otp"],
    refCode: json["refCode"],
    result: json["result"],
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "statusCode": statusCode,
    "otp": otp,
    "refCode": refCode,
    "result": result,
  };
}

class VerifyCode {
  VerifyCode({
    required this.phone,
    required this.otpCode,
    required this.refCode,
    required this.fromBU,
  });

  String phone;
  String otpCode;
  String refCode;
  String fromBU;

  factory VerifyCode.fromJson(Map<String, dynamic> json) => VerifyCode(
    phone: json["phone"],
    otpCode: json["otpCode"],
    refCode: json["refCode"],
    fromBU: json["fromBU"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone,
    "otpCode": otpCode,
    "refCode": refCode,
    "fromBU": fromBU,
  };
}

class ResponseVerifyCode {
  ResponseVerifyCode({
    this.statusCode,
    this.msg,
    this.likewallet,
  });

  int? statusCode;
  String? msg;
  Likewallet? likewallet;

  factory ResponseVerifyCode.fromJson(Map<String, dynamic> json) => ResponseVerifyCode(
      statusCode: json["statusCode"],
      msg: json["msg"],
      likewallet: json["likewallet"] == null ? null : Likewallet.fromJson(json["likewallet"])
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode,
    "msg": msg,
    "likewallet": likewallet?.toJson(),
  };
}


class Likewallet {
  Likewallet({
    this.statusCode,
    this.result,
  });

  int? statusCode;
  String? result;

  factory Likewallet.fromJson(Map<String, dynamic> json) => Likewallet(
    statusCode: json["statusCode"],
    result: json["result"],
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode,
    "result": result,
  };
}
