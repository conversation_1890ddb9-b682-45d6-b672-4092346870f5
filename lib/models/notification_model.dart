class Notification {
  final String running;
  final String idNotification;
  final String title;
  final String detail;
  final String imgNotify;
  final String updateTime;
  final String createTime;
  final String noti_time;
  final String typeNotification;
  final String ctt_code;
  final String subtitle;

  late String statusnotification;



  Notification({
    required this.running,
    required this.idNotification,
    required this.title,
    required this.detail,
    required this.imgNotify,
    required this.updateTime,
    required this.createTime,
    required this.noti_time,
    required this.typeNotification,
    required this.ctt_code,
    required this.statusnotification,

    required this.subtitle,
  });

  factory Notification.fromJson(Map<String, dynamic> json) {
    return Notification(
      running: json['running'] as String,
      idNotification: json['idnotification'] as String,
      title: json['title'] as String,
      detail: json['detail'] as String,
      imgNotify: json['img_notify'] as String,
      updateTime: json['update_time'] as String,
      createTime: json['create_time'] as String,
      noti_time: json['noti_time'] as String,
      typeNotification: json['typenotification'] as String,
      ctt_code: json['ctt_code'] as String,
      statusnotification: json['statusnotification']  ?? '',
      subtitle: json['subtitle'] as String,

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'running': running,
      'idnotification': idNotification,
      'title': title,
      'detail': detail,
      'img_notify': imgNotify,
      'update_time': updateTime,
      'create_time': createTime,
      'noti_time': noti_time,
      'typenotification': typeNotification,
      'ctt_code': ctt_code,
      'statusnotification': statusnotification,
      'subtitle': subtitle,
    };
  }
}