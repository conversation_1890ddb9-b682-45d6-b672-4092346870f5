class News {
  final String running;
  final String headerNews;
  final String subHeaderNews;
  final String bodyNews;
  final String urlImgNews;
  final String dateNews;
  final bool statusShow;

  News({
    required this.running,
    required this.headerNews,
    required this.subHeaderNews,
    required this.bodyNews,
    required this.urlImgNews,
    required this.dateNews,
    required this.statusShow,
  });

  factory News.fromJson(Map<String, dynamic> json) {
    return News(
      running: json['running'] ?? '',
      headerNews: json['header_news'] ?? '',
      subHeaderNews: json['sub_header_news'] ?? '',
      bodyNews: json['body_news'] ?? '',
      urlImgNews: json['url_img_news'] ?? '',
      dateNews: json['date_news'] ?? '',
      statusShow: json['status_show'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'running': running,
      'header_news': headerNews,
      'sub_header_news': subHeaderNews,
      'body_news': bodyNews,
      'url_img_news': urlImgNews,
      'date_news': dateNews,
      'status_show': statusShow,
    };
  }
}



class NewModel {
  final String running;
  final String headerNews;
  final String subHeaderNews;
  final String bodyNews;
  final String urlImgNews;
  final String dateNews;
  final bool statusShow;
  final int newsIndex;
 
  NewModel({
    required this.running,
    required this.headerNews,
    required this.subHeaderNews,
    required this.bodyNews,
    required this.urlImgNews,
    required this.dateNews,
    required this.statusShow,
    required this.newsIndex,
  });

  factory NewModel.fromJson(Map<String, dynamic> json) => NewModel(
    running: json['running'] ?? '',
    headerNews: json['header_news'] ?? '',
    subHeaderNews: json['sub_header_news'] ?? '',
    bodyNews: json['body_news'] ?? '',
    urlImgNews: json['url_img_news'] ?? '',
    dateNews: json['date_news'] ?? '',
    statusShow: json['status_show'] ?? '',
    newsIndex: json['newsIndex'] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    'running': running,
    'header_news': headerNews,
    'sub_header_news': subHeaderNews,
    'body_news': bodyNews,
    'url_img_news': urlImgNews,
    'date_news': dateNews,
    'status_show': statusShow,
    'newsIndex': newsIndex,
  };
}


class NewModelList {
  final List<NewModel>? data;

  NewModelList({this.data});

  factory NewModelList.fromJson(List<dynamic> parsedJson){
    List<NewModel>? data = <NewModel>[];
    data = parsedJson.map((i) => NewModel.fromJson(i)).toList();
    return NewModelList(data: data);
  }
}
