import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import '../../service/endpoint.dart';
import '../../service/http_service.dart';
import '../../view/screen/feedback/thanks_for_advice_screen.dart';
import '../profile/profile.controller.dart';
import '../transalation/translation_key.dart';
import 'package:get_storage/get_storage.dart';

class FeedbackController extends GetxController {
  // Constants and Data
  final List<String> dislikeFeedbackReason = [
    waitTooLong.tr,
    difficultUnderstand.tr,
    tooManySteps.tr,
    slowApplication.tr,
    others.tr,
  ];

  // Reactive States
  var isSelectedReason = List<bool>.generate(5, (index) => false).obs;
  var feedbackTextController = TextEditingController(); // Controller for text input
  final RxList<bool?> isLiked = List<bool?>.generate(4, (_) => null).obs;
  final RxList<bool?> isDisliked = List<bool?>.generate(4, (_) => null).obs;

  GetStorage box = GetStorage();

  // User Data ( by using late - the code guarantees correct and safe initialization )
  late final String userName;
  late final String userPhoneNo;

  @override
  void onInit() {
    super.onInit();
    final profileController = Get.find<ProfileController>();
    userName = '${profileController.profile.value.firstname} ${profileController.profile.value.lastname}';
    userPhoneNo = profileController.profile.value.phone.toString();
  }

  /// Cleanup when the controller is closed
  @override
  void onClose() {
    feedbackTextController.dispose();
    super.onClose();
  }

  // Toggle chip selection
  void toggleChipSelection(int index) {
    isSelectedReason[index] = !isSelectedReason[index];
  }

  // Get selected reason count
  int get selectedCount => isSelectedReason.where((item) => item).length;

  // Toggle like reaction
  void toggleLike(int index) {
    isLiked[index] = (isLiked[index] != true) ? true : null;
    isDisliked[index] = null;
  }

  // Toggle dislike reaction
  void toggleDislike(int index) {
    isDisliked[index] = (isDisliked[index] != true) ? true : null;
    isLiked[index] = null;
  }

  // Check if all feedback options are selected
  bool isAllFeedbackSelected() {
    for (int i = 0; i < isLiked.length; i++) {
      if (isLiked[i] != true && isDisliked[i] != true) {
        return false;
      }
    }
    return true;
  }

  /// Save user feedback (Dislike with Reasons)
  Future<bool> saveUserDislikeFeedback() async {
    try {
      final reasons = [
        for (int i = 0; i < isSelectedReason.length; i++)
          if (isSelectedReason[i]) dislikeFeedbackReason[i],
      ];
      final data = {
        "point": "-",
        "message": feedbackTextController.text,
        "type": reasons.isNotEmpty
            ? reasons.map((reason) => "- $reason").join("\n")
            : '- No reason provided',
        "os": Platform.operatingSystem,
        "fullname": userName,
        "phone": userPhoneNo,
        "phone_firebase": userPhoneNo,
      };
      final response = await HttpService.callAPIjwt(
        "POST",
        Endpoints.saveUserFeedbackReaction,
        data,
      );
      if (response != null && response["status"] == 200) {
        resetFeedbackForm(); // Clear feedback form
        Get.to(() => const ThanksForAdviceScreen());
        return true;
      } else {
        debugPrint("Save user feedback reaction failed: $response");
        return false;
      }
    } catch (e) {
      debugPrint("Error saving dislike feedback: $e");
      return false;
    }
  }

  /// Save user reaction feedback
  Future<bool> saveUserMainFeedbackReaction(String message, String type) async {
    try {
      final data = {
        "point": "-",
        "message": message,
        "type": type,
        "os": Platform.operatingSystem,
        "fullname": userName,
        "phone": userPhoneNo,
        "phone_firebase": userPhoneNo,
      };
      final response = await HttpService.callAPIjwt(
        "POST",
        Endpoints.saveUserFeedbackReaction,
        data,
      );
      if (response != null && response["status"] == 200) {
        debugPrint("Feedback saved successfully.");
        return true;
      } else {
        debugPrint("Feedback saving failed: $response");
        return false;
      }
    } catch (e) {
      debugPrint("Error saving main feedback: $e");
      return false;
    }
  }

  /// Submit multiple feedback reactions
  Future<bool> submitFeedback() async {
    try {
      final feedbackList = [
        for (int i = 0; i < isLiked.length; i++)
          {
            "message": 'feedbackReactionSelection${i + 1}'.tr,
            "type": isLiked[i] == true
                ? 'Like'
                : (isDisliked[i] == true ? 'Dislike' : 'No Reaction'),
          },
      ];
      return await saveUserFeedbackReaction(feedbackList);
    } catch (e) {
      debugPrint("Error submitting feedback: $e");
      return false;
    }
  }

  /// Save multiple feedback reactions
  Future<bool> saveUserFeedbackReaction(List<Map<String, String>> feedbackList) async {
    try {
      final data = {
        "feedbacks": feedbackList,
        "comments": '-',
        "from_menu": "feedback",
        "image": "-",
        "os": Platform.operatingSystem,
        "fullname": userName,
        "phone": userPhoneNo,
        "phone_firebase": userPhoneNo,
      };
      final response = await HttpService.callAPIjwt(
        "POST",
        Endpoints.saveMultipleFeedbackReaction,
        data,
      );
      if (response != null && response["status"] == 200) {
        debugPrint("Multiple feedback saved successfully.");
        box.write('feedback_profile', true);
        return true;
      } else {
        debugPrint("Multiple feedback saving failed: $response");
        return false;
      }
    } catch (e) {
      debugPrint("Error saving multiple feedback reactions: $e");
      return false;
    }
  }


  Future<dynamic> saveUserFeedbackFromNotification(String message, String type) async {
    try {
      Map<String, dynamic> data = {
        "point": "-",
        "message": message,
        "type": type,
        "os": Platform.operatingSystem,
        "fullname": userName,
        "phone": userPhoneNo,
        "phone_firebase": userPhoneNo,
      };
      final response = await HttpService.callAPIjwt("POST", Endpoints.saveUserFeedbackReaction, data);
      if (response["status"] == 200) {
        debugPrint("Feedback saved successfully: $data");
        return true;
      } else {
        debugPrint("Feedback saving failed: $data");
        return false;
      }
      return true;
    } catch (e) {
      debugPrint("Error saving feedback: $e");
      return false;
    }
  }

  // To reset feedback form
  void resetFeedbackForm() {
    isSelectedReason.value = List.filled(isSelectedReason.length, false);
    feedbackTextController.clear();
  }
}
