// import 'dart:async';
//
// import 'package:flutter/cupertino.dart';
// import 'package:get/get.dart';
// import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';
//
// class NavigatorContoller extends GetxController {
//   PersistentTabController tabController =
//       PersistentTabController(initialIndex: 0);
//
//   RxBool timeFinish = false.obs;
//   RxBool onTime = false.obs;
//   RxInt seconds = 0.obs;
//   RxInt seconds2 = 0.obs;
//   RxInt seconds3 = 0.obs;
//   bool? onPoi = false;
//   bool? timePOI = false;
//
//   Future<PersistentTabController?> jampPage(BuildContext context, numb) async {
//     if (numb == 1) {
//       startTimer();
//     } else {
//       stopTimer();
//     }
//
//     tabController.jumpToTab(numb);
//
//     update();
//     return tabController;
//   }
//
//   void startTimer() {
//     timeFinish.value = false;
//     update();
//     Timer();
//   }
//
//   void stopTimer() {
//     timeFinish.value = true;
//     update();
//   }
//
//   Future<void> Timer() async {
//     for (int i = 0; i < 4; i++) {
//       if (i == 2) {
//         onTime.value = true;
//       } else if (i == 0) {
//         onTime.value = false;
//       }
//       if (timeFinish.isFalse) {
//         {
//           await Future.delayed(Duration(milliseconds: 500), () {
//             seconds.value = i;
//             update();
//           });
//         }
//       }
//     }
//     if (timeFinish.isFalse) {
//       Timer();
//     }
//   }
// }
//
// class SteamEvent extends GetxController {
//   bool event = false;
//   bool checkStatus = false;
//   StreamController<bool> steamControl = StreamController<bool>();
//   StreamController<bool> homeControl = StreamController<bool>();
//   StreamController<bool> stLoanControl = StreamController<bool>();
//   StreamController<bool> mrControl = StreamController<bool>();
//   StreamController<bool> moreControl = StreamController<bool>();
//
//   StreamController<bool> refreshLikeControl = StreamController<bool>();
//   StreamController<bool> changeStateChatInApp = StreamController<bool>();
//
//   List<StreamController> checkCon = [];
//
//   bool checkStarter(control) {
//     // print(control.toString());
//     // print(checkCon.contains(control));
//     return checkCon.contains(control);
//   }
//
//   Stream startListening(StreamController control) {
//     checkCon.add(control);
//     update();
//     return control.stream;
//   }
//
//   Future<dynamic> changeEventStLoanControl(bool value) async {
//     if(checkStatus == value){
//       return null;
//     }else{
//       checkStatus = value;
//       stLoanControl.add(checkStatus);
//
//       update();
//       return true;
//     }
//
//   }
//
//   Future<dynamic> changeEventNavigator() async {
//     event = !event;
//     steamControl.add(event);
//
//     update();
//     return true;
//   }
//
//   Future<StreamController?> changeEvent(StreamController control) async {
//     event = !event;
//     control.add(event);
//
//     update();
//     return control;
//   }
//
//   Future<StreamController?> refreshLike() async {
//     refreshLikeControl.add(true);
//
//     update();
//     return refreshLikeControl;
//   }
//
//   Future<StreamController?> stopRefreshLike() async {
//     refreshLikeControl.add(false);
//
//     update();
//     return refreshLikeControl;
//   }
// }
//

import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import '../main.dart';
import 'profile/profile.controller.dart';

class NavigationController extends GetxController {
  var currentIndex = 0.obs;
  var showPopupHome = true.obs;
  var hasFinishedTutorial = false.obs;
  var isMRPage = false.obs;
  var isnotiPage = false.obs;
  var showPopupMR_TF = true.obs;
  var showPopupNotification = true.obs;
  final RxInt selectedTabIndex = 0.obs;
    final box = GetStorage();
  BuildContext context = Get.context!;
  final HomeController homeController = Get.put(HomeController());
  final MRController mrController = Get.put(MRController());
  final ProfileController profileController = Get.put(ProfileController());
  final shorebirdCodePush = ShorebirdCodePush();
  @override
  void onInit() {
    super.onInit();
    print('isHomePopupShown');

    final isPopupShown = box.read("isHomePopupShown") ?? false;
    print('isPopupShown $isPopupShown');
    // รอให้ build เสร็จสมบูรณ์ก่อนเรียก Popup
    WidgetsBinding.instance.addPostFrameCallback((_) async {


      final isUpdateAvailable =
      await shorebirdCodePush.isNewPatchAvailableForDownload();
      debugPrint('มี patch อัปเดต :  $isUpdateAvailable');
      if (!isUpdateAvailable) {
        // showTutorial();
      }


    });
  }

void setSelectedTab(int index) {
  print('tricker_count');
  selectedTabIndex.value = index;

  print(index);

}




  void changeIndex(int index) {
    currentIndex.value = index;
    if (index == 1) {
      showMRPopup();

    } else if (index == 2) {
      showNotificationPopup();

    }
  }

  void voiceHome(int itemIndex) {
    final StringBuffer fullTextBuffer = StringBuffer();

    // Part 1: Title text
    if (homeController.isGuest!.value) {
      if (homeController.isIndexTutorial.value == 8) {
        fullTextBuffer.write(popUpTutorialShowPoint_guest.tr);
      } else if ([3, 5, 6, 9, 10].contains(homeController.isIndexTutorial.value)) {
        fullTextBuffer.write(popUpTutorialRecommend_guest.tr);
      } else if (homeController.isIndexTutorial.value == 0) {
        fullTextBuffer.write(popUpTutorialGuest.tr);
      } else if (homeController.isIndexTutorial.value == 1) {
        fullTextBuffer.write(popUpTutorialGuest_perfix.tr);
      } else {
        fullTextBuffer.write(homeController.popUpText[itemIndex]['title']);
      }
    } else {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['title']);
    }

    // Part 2: Country or user info
    if (homeController.isIndexTutorial.value == 7) {
      String country = appConfigService.countryConfigCollection == "aam"
          ? ' AAM '
          : appConfigService.countryConfigCollection == "rafco"
          ? ' RAFCO '
          : " RPLC ";
      fullTextBuffer.write(country);
    } else if ([8, 9].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.isGuest!.value
          ? "${onBoardingVisit.tr}"
          : " ${profileController.profile!.value.firstname}");
    }

    // Part 3: User info (when required)
    if ([0, 1, 3, 5, 6, 10].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.isGuest!.value
          ? "${onBoardingVisit.tr}"
          : " ${profileController.profile!.value.firstname} ");
    }

    // Part 4: Content text
    fullTextBuffer.write(homeController.popUpText[itemIndex]['content']);
    fullTextBuffer.write(homeController.popUpText[itemIndex]['content1']);

    // Part 5: Special content for tutorial steps 2 and 4
    if (homeController.isGuest!.value &&
        (homeController.isIndexTutorial.value == 2 || homeController.isIndexTutorial.value == 4)) {
      fullTextBuffer.write(popUpTutorialImportant2_guest.tr);
    } else {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['content2']);
    }

    // Part 6: Extra user info for specific tutorial steps
    if ([2, 4].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.isGuest!.value
          ? onBoardingVisit.tr
          : " ${profileController.profile.value.firstname} ");
    }

    // Part 7: More content based on tutorial steps
    if (homeController.isIndexTutorial.value == 0) {
      fullTextBuffer.write(' AAM');
    } else if (homeController.isIndexTutorial.value == 2) {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['content3']);
    } else if (homeController.isIndexTutorial.value == 4 && !homeController.isGuest!.value) {
      fullTextBuffer.write(" ${profileController.profile.value.firstname} ");
    }

    // Part 8: Content for tutorial steps 0 and 4
    if ([0, 4].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['content3']);
    }

    // Speak the full concatenated text
    homeController.speak(fullTextBuffer.toString());
    hasFinishedTutorial.value = true;
  }

  void voiceMR(int index) {
    homeController.speak(() {
      // Validate index
      if (index < 0 || index >= mrController.popUpText.length) {
        return ''; // Fallback for invalid index
      }

      // Determine the title
      String title = (mrController.indexTutorialMR.value == 2 && homeController.isGuest!.value)
          ? popUpTutorialRecommend_guest.tr
          : mrController.popUpText[index]['title'];

      // Determine the prefix (if index is 2 and the user is a guest or the profile's first name)
      String prefix = (mrController.indexTutorialMR.value == 2)
          ? (homeController.isGuest!.value
          ? '${onBoardingVisit.tr}'
          : "${profileController.profile.value.firstname}")
          : "";

      // Retrieve content for the corresponding index, ensuring validity
      String content = (index >= 0 && index < mrController.popUpText.length)
          ? mrController.popUpText[index]['content']
          : ""; // Default empty if index is invalid

      String content1 = (index >= 0 && index < mrController.popUpText.length)
          ? mrController.popUpText[index]['content1']
          : ""; // Default empty if index is invalid

      String content2 = (index >= 0 && index < mrController.popUpText.length)
          ? mrController.popUpText[index]['content2']
          : ""; // Default empty if index is invalid

      // Return the final concatenated string (similar to how it's set in TextSpan)
      return title + prefix + content + content1 + content2;
    }());
  }

  void voiceNoti(itemIndex){
  print('voiceNoti');
    final StringBuffer fullTextBuffer = StringBuffer();

// Part 1: Title text
    if (homeController.isGuest!.value) {
      if (homeController.isIndexTutorial.value == 8) {
        fullTextBuffer.write(popUpTutorialShowPoint_guest.tr);
      } else if ([3, 5, 6, 9, 10].contains(homeController.isIndexTutorial.value)) {
        fullTextBuffer.write(popUpTutorialRecommend_guest.tr);
      } else if (homeController.isIndexTutorial.value == 0) {
        fullTextBuffer.write(popUpTutorialGuest.tr);
      } else if (homeController.isIndexTutorial.value == 1) {
        fullTextBuffer.write(popUpTutorialGuest_perfix.tr);
      } else {
        fullTextBuffer.write(homeController.popUpText[itemIndex]['title']);
      }
    } else {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['title']);
    }

// Part 2: Country or user info
    if (homeController.isIndexTutorial.value == 7) {
      String country = appConfigService.countryConfigCollection == "aam"
          ? ' AAM '
          : appConfigService.countryConfigCollection == "rafco"
          ? ' RAFCO '
          : " RPLC ";
      fullTextBuffer.write(country);
    } else if ([8, 9].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.isGuest!.value
          ? "${onBoardingVisit.tr}"
          : " ${profileController.profile!.value.firstname}");
    }

// Part 3: User info (when required)
    if ([0, 1, 3, 5, 6, 10].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.isGuest!.value
          ? "${onBoardingVisit.tr}"
          : " ${profileController.profile!.value.firstname} ");
    }

// Part 4: Content text
    fullTextBuffer.write(homeController.popUpText[itemIndex]['content']);
    fullTextBuffer.write(homeController.popUpText[itemIndex]['content1']);

// Part 5: Special content for tutorial steps 2 and 4
    if (homeController.isGuest!.value &&
        (homeController.isIndexTutorial.value == 2 || homeController.isIndexTutorial.value == 4)) {
      fullTextBuffer.write(popUpTutorialImportant2_guest.tr);
    } else {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['content2']);
    }

// Part 6: Extra user info for specific tutorial steps
    if ([2, 4].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.isGuest!.value
          ? onBoardingVisit.tr
          : " ${profileController.profile.value.firstname} ");
    }

// Part 7: More content based on tutorial steps
    if (homeController.isIndexTutorial.value == 0) {
      fullTextBuffer.write(' AAM');
    } else if (homeController.isIndexTutorial.value == 2) {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['content3']);
    } else if (homeController.isIndexTutorial.value == 4 && !homeController.isGuest!.value) {
      fullTextBuffer.write(" ${profileController.profile.value.firstname} ");
    }

// Part 8: Content for tutorial steps 0 and 4
    if ([0, 4].contains(homeController.isIndexTutorial.value)) {
      fullTextBuffer.write(homeController.popUpText[itemIndex]['content3']);
    }

// Speak the full concatenated text
    homeController.speak(fullTextBuffer.toString());


  }

  showTutorial() {
    final isPopupShown = box.read("isHomePopupShown") ?? false;
    showPopupHome.value = isPopupShown;
    if (!isPopupShown) {
      print('showTutorial');
      showPopupHome.value = isPopupShown;
      print('${appConfigService.countryConfigCollection}');
      if(!isPopupShown && appConfigService.countryConfigCollection =="aam"){
        voiceHome(0);
        // homeController.speak(
        //     popUpTutorialGuest.tr + onBoardingVisit.tr + homeController.popUpText[0]
        // [
        // 'content'] +  homeController.popUpText[0]
        // [
        // 'content1'] +  homeController.popUpText[0]
        // [
        // 'content2'] +  homeController.popUpText[0]
        // [
        // 'content3']);
      }
      box.write("isHomePopupShown", true);
      showDialog(
          context: context,
          builder: (builder) => GetBuilder<ProfileController>(
              init: ProfileController(),
              builder: (profileController) => Material(
                    color: Colors.transparent,
                    child: Container(
                        height: Get.height,
                        // color: Colors.red,
                        child:
                        SingleChildScrollView(
                          physics: NeverScrollableScrollPhysics(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              CarouselSlider.builder(
                                  itemCount: homeController.popUpText.length,
                                  itemBuilder:
                                      (BuildContext context, int itemIndex,
                                      int pageViewIndex) =>
                                      GetBuilder<HomeController>(
                                          init: HomeController(),
                                          builder:
                                              (homeControllers) => Stack(
                                            children: [
                                              buildTutorial(),
                                              Stack(
                                                children: [
                                                  Padding(
                                                    padding: EdgeInsets.only(left: 17.w,top: itemIndex==0 ||itemIndex==1||itemIndex==2||itemIndex==3||itemIndex==8? 500.h: itemIndex==4||itemIndex==5||itemIndex==6?556.h:itemIndex==7? 580.h:itemIndex==9||itemIndex==10?177.h:0.h),
                                                    child: Container(
                                                      // margin:EdgeInsets.only(top: 64.h),
                                                      height: 200.h,
                                                      width: 340.w,
                                                      decoration:
                                                      BoxDecoration(
                                                        borderRadius:
                                                        BorderRadius
                                                            .circular(
                                                            14),
                                                        color:
                                                        Colors.white,
                                                      ),
                                                      child: SingleChildScrollView(
                                                        physics: NeverScrollableScrollPhysics(),
                                                        child: 
                                                        Stack(
                                                          // crossAxisAlignment:
                                                          // CrossAxisAlignment
                                                          //     .center,
                                                          // mainAxisAlignment:
                                                          // MainAxisAlignment
                                                          //     .center,
                                                          children: [
                                                            Padding(
                                                              padding: EdgeInsets.only(
                                                                  left: 32.5
                                                                      .w,
                                                                  right: 64.5
                                                                      .w,
                                                                  top:
                                                                  38.h),
                                                              child: Text.rich(
                                                                  style: TextStyle(
                                                                    fontSize:
                                                                    14,
                                                                    fontWeight:
                                                                    FontWeight.w400,
                                                                    color: Colors
                                                                        .black,
                                                                  ),
                                                                  TextSpan(children: [
                                                                    TextSpan(
                                                                      text: homeController.isGuest!.value ?
                                                                      homeController.isIndexTutorial.value == 8 ?  popUpTutorialShowPoint_guest.tr :
                                                                      homeController.isIndexTutorial.value == 3 || homeController.isIndexTutorial.value == 5 || homeController.isIndexTutorial.value == 9 || homeController.isIndexTutorial.value == 10 || homeController.isIndexTutorial.value == 6 ? popUpTutorialRecommend_guest.tr
                                                                      : homeController.isIndexTutorial.value == 0 && homeController.isGuest!.value ?  popUpTutorialGuest.tr : homeController.isIndexTutorial.value == 1 ? popUpTutorialGuest_perfix.tr :
                                                                      homeController.popUpText[itemIndex]
                                                                            [
                                                                      'title']
                                                                          : homeController.popUpText[itemIndex]
                                                                      [
                                                                      'title']
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.isIndexTutorial.value == 7
                                                                          ? appConfigService.countryConfigCollection == "aam"
                                                                          ? ' AAM '
                                                                          : appConfigService.countryConfigCollection == "rafco"
                                                                          ? ' RAFCO '
                                                                          : " RPLC "
                                                                          : homeController.isIndexTutorial.value == 8 || homeController.isIndexTutorial.value == 9

                                                                          ?  homeController.isGuest!.value ? "${onBoardingVisit.tr}"
                                                                      :' ${profileController.profile!.value.firstname}'
                                                                          : "",
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.isIndexTutorial.value == 0 || homeController.isIndexTutorial.value == 1 || homeController.isIndexTutorial.value == 3 || homeController.isIndexTutorial.value == 5 || homeController.isIndexTutorial.value == 6 || homeController.isIndexTutorial.value == 10
                                                                          ?  homeController.isGuest!.value ?   "${onBoardingVisit.tr}"
                                                                          : " ${profileController.profile!.value.firstname} "

                                                                      : '',
                                                                      style:
                                                                      TextStyle(
                                                                        fontSize:
                                                                        14,
                                                                        fontWeight:
                                                                        FontWeight.w700,
                                                                        color:
                                                                        Colors.black,
                                                                      ),
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.popUpText[itemIndex]
                                                                      [
                                                                      'content'],
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.popUpText[itemIndex]
                                                                      [
                                                                      'content1'],
                                                                      style:
                                                                      TextStyle(
                                                                        fontSize:
                                                                        14,
                                                                        fontWeight:
                                                                        FontWeight.w700,
                                                                        color:
                                                                        Colors.black,
                                                                      ),
                                                                    ),
                                                                    TextSpan(
                                                                      text:
                                                                      homeController.isGuest!.value &&  ( homeController.isIndexTutorial.value == 2 ||  homeController.isIndexTutorial.value == 4 )
                                                                          ? popUpTutorialImportant2_guest.tr :
                                                                      homeController.popUpText[itemIndex]
                                                                      [
                                                                      'content2'],
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.isIndexTutorial.value == 2 || homeController.isIndexTutorial.value == 4
                                                                          ? homeController.isGuest!.value
                                                                          ? onBoardingVisit.tr
                                                                          : ""
                                                                          : "",
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.isIndexTutorial.value == 0
                                                                          ?  appConfigService.countryConfigCollection =="aam"?' AAM':""
                                                                          : homeController.isIndexTutorial.value == 2
                                                                          ? homeController.popUpText[itemIndex]['content3']
                                                                          : homeController.isIndexTutorial.value == 4
                                                                          ? homeController.isGuest!.value
                                                                          ? ''
                                                                          : " ${profileController.profile.value.firstname} "
                                                                          : '',
                                                                      style:
                                                                      TextStyle(
                                                                        fontSize:
                                                                        14,
                                                                        fontWeight:
                                                                        FontWeight.w700,
                                                                        color:
                                                                        Colors.black,
                                                                      ),
                                                                    ),
                                                                    TextSpan(
                                                                      text: homeController.isIndexTutorial.value == 0 || homeController.isIndexTutorial.value == 4
                                                                          ? homeController.popUpText[itemIndex]['content3']
                                                                          : "",
                                                                    ),
                                                                  ])),
                                                            ),
                                                            SizedBox(
                                                              height: 38.h,
                                                            ),
                                                            // Text(homeController
                                                            //     .isIndexTutorial
                                                            //     .value
                                                            //     .toString()),
                                                     
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                           Positioned(
        right: 26.w, // แปลงจาก padding.right
        left: 247.w, // แปลงจาก padding.left
        top: itemIndex == 0 ||
                itemIndex == 1 ||
                itemIndex == 2 ||
                itemIndex == 3 ||
                itemIndex == 8
            ? 450.h
            : itemIndex == 4 || itemIndex == 5 || itemIndex == 6
                ? 506.h
                : itemIndex == 7
                    ? 530.h
                    : itemIndex == 9 || itemIndex == 10
                        ? 127.h
                        : 0.h,
        child: Container(
          height: 91.h,
          width: 102.w,
          child: Image.asset(
            appConfigService.countryConfigCollection == "aam"
                ? homeController.popUpMas[itemIndex].toString()
                : appConfigService.countryConfigCollection == "rafco"
                    ? homeController.popUpMasRafco[itemIndex].toString()
                    : homeController.popUpMasRplc[itemIndex].toString(),
            fit: BoxFit.cover,
          ),
        ),
      ),
        //todo ปุ่ม
                                                    Positioned(
                                                              bottom: 30.h, // ระยะห่างจากขอบล่าง
                                                              right: 16.w, // ระยะห่างจากขอบขวา
                                                              child:
                                                              InkWell(
                                                                onTap: () async {

                                                                  print(
                                                                    "## กด next!!!"
                                                                  );
                                                                  // หยุดเสียงที่กำลังเล่น
                                                                  await flutterTts.stop();

                                                                  // รอให้แน่ใจว่าเสียงเก่าหยุดสนิทก่อน
                                                                  await Future.delayed(Duration(milliseconds: 300));

                                                                  // เพิ่มค่าของ index
                                                                  itemIndex++;
                                                                  homeController.setIndexTutorial(itemIndex);

                                                                  // ตรวจสอบค่าของ tutorial index และกำหนดค่าต่าง ๆ
                                                                  if (appConfigService.countryConfigCollection == "aam") {
                                                                    voiceHome(itemIndex);
                                                                  }

                                                                  if (homeController.isIndexTutorial.value == 4) {
                                                                    homeController.setIndexMenu(1);
                                                                  } else if (homeController.isIndexTutorial.value == 6) {
                                                                    homeController.setIndexMenu(2);
                                                                  } else if (homeController.isIndexTutorial.value > 10) {
                                                                    homeController.setIndexMenu(0);
                                                                    profileController.isShow.value = true;
                                                                    profileController.update();
                                                                    Navigator.pop(context);
                                                                    homeController.isShowTutorial!.value = true;
                                                                    homeController.update();
                                                                  }

                                                                  print(homeController.isIndexTutorial.value);

                                                                  if (homeController.isIndexTutorial.value > 11) {
                                                                    await flutterTts.stop();
                                                                  }

                                                                  // รอให้ TTS พร้อมก่อนเริ่มเสียงใหม่
                                                                  await Future.delayed(Duration(milliseconds: 300));

                                                                  // อ่านเสียงใหม่
                                                                  if (appConfigService.countryConfigCollection == "aam") {
                                                                    voiceHome(itemIndex);
                                                                    }
                                                                },


                                                                child: Container(
                                                                  width: 68.w,
                                                                  margin: EdgeInsets.only(right: 20.w),
                                                                  child: Row(
                                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,  // Align children at the ends of the Row
                                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                                    children: [
                                                                      Expanded(
                                                                        child: Text(
                                                                          homeController.isIndexTutorial.value >= 10
                                                                              ? backInNewPasswordFinish.tr
                                                                              : menuGetLoanNext.tr,
                                                                          style: TextStyle(
                                                                            fontSize: 14,
                                                                            fontWeight: FontWeight.w400,
                                                                            color: Colors.black.withOpacity(0.4),

                                                                          ),
                                                                        ),
                                                                      ),
                                                                      SizedBox(width: 8.w),
                                                                      Container(
                                                                        height: 24.h,
                                                                        child: Center(
                                                                          child: SvgPicture.string(
                                                                            color: Colors.black.withOpacity(0.4),
                                                                            '<svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.82614 11.37L7.10667 7.08944C7.25989 6.93654 7.38144 6.75492 7.46437 6.55499C7.54731 6.35505 7.59 6.14073 7.59 5.92427C7.59 5.70782 7.54731 5.49349 7.46437 5.29356C7.38144 5.09362 7.25989 4.91201 7.10667 4.75911L2.82614 0.478578C1.78493 -0.546105 0 0.181089 0 1.65201L0 10.1965C0 11.684 1.78493 12.4112 2.82614 11.37Z" fill="black"/></svg>',
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),

                                                              )

                                                            )
                                        
                                            
                                                ],
                                              ),


                                            ],
                                          )
                                          ),
                                  options: CarouselOptions(
                                    height: Get.height,
                                    viewportFraction: 1,
                                    // initialPage: 0,
                                    scrollPhysics:
                                    NeverScrollableScrollPhysics(),
                                  )),
                            ],
                          ),
                        )),
                  )));
    }
  }

  buildTutorial() {
    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (homeControllers) => Stack(
        children: [
          Positioned(
            top: homeController.isIndexTutorial.value > 6
                ? homeController
                    .sizePopUp[homeController.isIndexTutorial.value + 1]['top']
                : homeController.sizePopUp[homeController.isIndexTutorial.value]
                    ['top'],
            left: homeController.isIndexTutorial.value == 4
                ? 142.w
                : homeController.isIndexTutorial.value == 6
                    ? 220.w
                    : homeController.isIndexTutorial.value == 8
                        ? 262.w
                        : homeController.isIndexTutorial.value == 9
                            ? 280.w
                            : homeController.isIndexTutorial.value == 10
                                ? 248.w
                                : 24.w,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: homeController.isIndexTutorial.value == 0
                    ? Colors.transparent
                    : Colors.white,
              ),
              width: homeController.isIndexTutorial.value > 6
                  ? homeController
                          .sizePopUp[homeController.isIndexTutorial.value + 1]
                      ['width']
                  : homeController
                      .sizePopUp[homeController.isIndexTutorial.value]['width'],
              height: homeController.isIndexTutorial.value > 6
                  ? homeController
                          .sizePopUp[homeController.isIndexTutorial.value + 1]
                      ['height']
                  : homeController
                          .sizePopUp[homeController.isIndexTutorial.value]
                      ['height'],
              child: CarouselSlider.builder(
                  itemCount: appConfigService.countryConfigCollection == "aam"
                      ? homeController.imgPopUp.length
                      : appConfigService.countryConfigCollection == "rafco"
                          ? homeController.imgPopUpRafco.length
                          : homeController.imgPopUpRplc.length,
                  itemBuilder:
                      (BuildContext context, int index, int realIndex) {
                    return homeController.isIndexTutorial.value > 0
                        ? Padding(
                      padding: homeController.isIndexTutorial.value == 3
                          ? EdgeInsets.all(2)
                          : EdgeInsets.all(5),
                      child: Image.asset(
                        appConfigService.countryConfigCollection =="aam"?
                        homeController.imgPopUp[homeController.isIndexTutorial.value - 1]: appConfigService.countryConfigCollection=="rafco"
                            ?homeController.imgPopUpRafco[homeController.isIndexTutorial.value - 1]:homeController.imgPopUpRplc[homeController.isIndexTutorial.value - 1],
                        // scale: 2,
                        // fit: BoxFit.fitWidth,
                        // fit: BoxFit,
                      ),
                    )
                        : Container();
                  },
                  options: CarouselOptions(
                    height: 253.h,
                    // aspectRatio: 16 / 9,
                    viewportFraction: 1,
                    initialPage: 0,
                    scrollPhysics: NeverScrollableScrollPhysics(),
                  )),
            ),
          ),
          Positioned(
              top: homeController
                      .sizePopUp[homeController.isIndexTutorial.value]['top'] +
                  55.h,
              left: 17.w,
              child: homeController.isIndexTutorial.value == 6
                  ?Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14),
                    color: homeController.isIndexTutorial.value == 0
                        ? Colors.transparent
                        : Colors.white,
                  ),
                  width: homeController.sizePopUp[7]['width'],
                  height: homeController.sizePopUp[7]['height'],
                  child: Padding(
                    padding: EdgeInsets.all(5.0),
                    child: Image.asset(
                        appConfigService.countryConfigCollection=="aam"?
                        'assets/tutorial/aam/home_news.png'
                            : appConfigService.countryConfigCollection=="rafco"
                            ? 'assets/tutorial/rafco/home_news.png'
                            :'assets/tutorial/rplc/home_news.png'
                    ),
                  ))
                  : Container())
        ],
      ),
    );
  }

  showMRPopup() {
    final isPopupShown = box.read("isMRPopupShown") ?? false;
    if (!isPopupShown) {
      homeController.speak(mrController
          .popUpText[
      0]['title'] + (mrController
          .indexTutorialMR
          .value ==
          2
          ? "${profileController.profile.value.firstname}"
          : "") + mrController
          .popUpText[
      0]
      ['content'] + mrController
          .popUpText[
      0]
      ['content1'] + mrController
          .popUpText[
      0]
      ['content2']);
      box.write("isMRPopupShown", true);
    showDialog(
      context: Get.context!,
      builder: (context) {
        return  mrController.popUpText.isEmpty
            ? Center(child: CircularProgressIndicator())
            : Material(
            color: Colors.transparent,
            child: GetBuilder<ProfileController>(
              init: ProfileController(),
              builder: (profileControllers) => Container(
                height: Get.height,
                // color: Colors.greenAccent,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      CarouselSlider.builder(
                          itemCount: mrController.popUpText.length,
                          itemBuilder:
                              (BuildContext context, int index, int realIndex) {
                            return   GetBuilder<MRController>(
                                init: MRController(),
                                builder: (mrControllers) =>
                               Stack(
                                      children: [
                                        mrController.indexPageMR!.value == 0
                                            ? Padding(
                                                padding: EdgeInsets.only(
                                                    top: 25.h, left: 17.w),
                                                child: Container(
                                                  height: 119.h,
                                                  width: 201.w,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            14),
                                                  ),
                                                  child:
                                                      mrController.mrData.value
                                                                  .mr_id ==
                                                              null

                                                          /// ไม่ได้สมัคร MR
                                                          ? Padding(
                                                              padding:
                                                                  EdgeInsets
                                                                      .only(
                                                                left: 24.w,
                                                                right: 24.w,
                                                              ),
                                                              child: Column(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .center,
                                                                children: [
                                                                  Text(
                                                                    // mrController.mrReferral[0].cust_name.toString(),
                                                                    mr.tr,
                                                                    style:
                                                                        TextStyle(
                                                                      color: configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color,
                                                                      fontSize: configTheme()
                                                                          .primaryTextTheme
                                                                          .bodyLarge
                                                                          ?.fontSize,
                                                                      fontFamily: configTheme()
                                                                          .primaryTextTheme
                                                                          .bodyLarge
                                                                          ?.fontFamily,
                                                                      fontWeight: configTheme()
                                                                          .primaryTextTheme
                                                                          .bodyLarge
                                                                          ?.fontWeight,
                                                                    ),
                                                                  ),
                                                                  Row(
                                                                    children: [
                                                                      Container(
                                                                        height:
                                                                            55.h,
                                                                        width:
                                                                            50.w,
                                                                        child:
                                                                            Stack(
                                                                          alignment:
                                                                              Alignment.bottomRight,
                                                                          children: [
                                                                            Container(
                                                                              height: 55.h,
                                                                              width: 50.w,
                                                                              child: Container(
                                                                                height: 55.h,
                                                                                width: 50.w,
                                                                                decoration: BoxDecoration(
                                                                                  border: Border.all(
                                                                                    color: Color(0xFFEDEDED),
                                                                                    width: 5.w,
                                                                                  ),
                                                                                  shape: BoxShape.circle,
                                                                                ),
                                                                                child: Center(
                                                                                  child: Container(
                                                                                    width: 40,
                                                                                    height: 40,
                                                                                    // padding: const EdgeInsets.only(
                                                                                    //     top: 8, left: 8, right: 8),
                                                                                    // clipBehavior: Clip.antiAlias,
                                                                                    decoration: ShapeDecoration(
                                                                                      image: DecorationImage(
                                                                                        image: NetworkImage(profileControllers.profile.value.avatar.toString().isNotEmpty ? profileControllers.profile.value.avatar.obs.string : 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png'),
                                                                                        fit: BoxFit.cover,
                                                                                      ),
                                                                                      shape: RoundedRectangleBorder(
                                                                                        side: BorderSide(
                                                                                          width: 1.w,
                                                                                          strokeAlign: BorderSide.strokeAlignOutside,
                                                                                          color: Colors.white,
                                                                                        ),
                                                                                        borderRadius: BorderRadius.circular(20.r),
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                            Positioned(
                                                                              top: 38.h,
                                                                              // left: 14.w,d
                                                                              // right: 16.w,
                                                                              child: Row(
                                                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                                children: [
                                                                                  Container(height: 18.h, width: 18.w, child: SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="2.99707" y="3" width="18" height="18" rx="9" fill="#22409A"/><path d="M10.7059 7.93698C11.253 6.64566 11.5266 6 11.9971 6C12.4675 6 12.7411 6.64566 13.2883 7.93698L13.3138 7.99711C13.6229 8.72664 13.7774 9.0914 14.0924 9.31311C14.4074 9.53482 14.821 9.56988 15.648 9.64L15.7975 9.65267C17.151 9.76742 17.8278 9.8248 17.9726 10.2324C18.1174 10.64 17.6148 11.0729 16.6096 11.9386L16.2741 12.2275C15.7653 12.6658 15.5109 12.8849 15.3923 13.1721C15.3702 13.2256 15.3518 13.2805 15.3372 13.3364C15.2593 13.6358 15.3338 13.9536 15.4828 14.5894L15.5292 14.7873C15.803 15.9557 15.9399 16.5399 15.7009 16.7919C15.6115 16.886 15.4954 16.9538 15.3665 16.9872C15.0213 17.0763 14.5313 16.6983 13.5512 15.9423C12.9076 15.4459 12.5859 15.1976 12.2164 15.1418C12.0711 15.1198 11.923 15.1198 11.7777 15.1418C11.4083 15.1976 11.0865 15.4459 10.4429 15.9423C9.46287 16.6983 8.97283 17.0763 8.62767 16.9872C8.4987 16.9538 8.38259 16.886 8.29326 16.7919C8.0542 16.5399 8.19112 15.9557 8.46496 14.7873L8.51134 14.5894C8.66034 13.9536 8.73485 13.6358 8.6569 13.3364C8.64236 13.2805 8.62397 13.2256 8.60185 13.1721C8.48327 12.8849 8.22885 12.6658 7.72001 12.2275L7.38453 11.9386C6.37935 11.0729 5.87676 10.64 6.02157 10.2324C6.16638 9.8248 6.84314 9.76742 8.19666 9.65267L8.34617 9.64C9.17319 9.56988 9.58669 9.53482 9.9017 9.31311C10.2167 9.0914 10.3713 8.72664 10.6804 7.99711L10.7059 7.93698Z" fill="white"/></svg>')),
                                                                                  Text(
                                                                                    "0%",
                                                                                    textAlign: TextAlign.center,
                                                                                  )
                                                                                ],
                                                                              ),
                                                                            )
                                                                          ],
                                                                        ),
                                                                      ),
                                                                      SizedBox(
                                                                          width:
                                                                              22.w),
                                                                      Column(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.start,
                                                                        children: [
                                                                          // Text("DSF735"),
                                                                          Text(
                                                                            profileControllers.profile.value.ref_code.toString(),
                                                                            style:
                                                                                TextStyle(
                                                                              color: configTheme().textTheme.bodyMedium?.color,
                                                                              fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                                                              fontFamily: configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                                                              fontWeight: configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                                                              // height: 0,
                                                                            ),
                                                                          ),
                                                                          Text(
                                                                            "${profileControllers.profile.value.firstname.toString()} ${profileControllers.profile.value.lastname.toString()}",
                                                                            style:
                                                                                TextStyle(
                                                                              color: configTheme().textTheme.bodyMedium?.color,
                                                                              fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                                                                              fontFamily: configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                                                              fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                                                              // height: 0,
                                                                            ),
                                                                          ),
                                                                          Container(
                                                                              height: 22.h,
                                                                              decoration: BoxDecoration(
                                                                                color: mrController.mrData.value.mr_id == null ? Color(0xffffffff) : configTheme().primaryColor.withOpacity(0.05),
                                                                                borderRadius: BorderRadius.circular(6),
                                                                              ),
                                                                              child: Center(
                                                                                  child: Container(
                                                                                margin: EdgeInsets.only(left: 6.w, right: 6.w),
                                                                                child: Text(
                                                                                  '',
                                                                                  style: TextStyle(
                                                                                    color: configTheme().primaryColor.withOpacity(0.75),
                                                                                    fontSize: configTheme().primaryTextTheme.labelMedium?.fontSize,
                                                                                    fontFamily: configTheme().primaryTextTheme.labelMedium?.fontFamily,
                                                                                    fontWeight: configTheme().primaryTextTheme.labelMedium?.fontWeight,
                                                                                    // height: 0,
                                                                                  ),
                                                                                ),
                                                                              ))),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  )
                                                                ],
                                                              ),
                                                              // child: buildHead(profileControllers.profile.value.ref_code.toString(), '${profileControllers.profile.value.firstname} ${profileControllers.profile.value.lastname}', ""),
                                                            )

                                                          /// สมัคร MR
                                                          : Padding(
                                                              padding:
                                                                  EdgeInsets
                                                                      .only(
                                                                left: 24.w,
                                                                right: 24.w,
                                                              ),
                                                              child: Column(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .center,
                                                                children: [
                                                                  Text(
                                                                    // mrController.mrReferral[0].cust_name.toString(),
                                                                    mr.tr,
                                                                    style:
                                                                        TextStyle(
                                                                      color: configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color,
                                                                      fontSize: configTheme()
                                                                          .primaryTextTheme
                                                                          .bodyLarge
                                                                          ?.fontSize,
                                                                      fontFamily: configTheme()
                                                                          .primaryTextTheme
                                                                          .bodyLarge
                                                                          ?.fontFamily,
                                                                      fontWeight: configTheme()
                                                                          .primaryTextTheme
                                                                          .bodyLarge
                                                                          ?.fontWeight,
                                                                    ),
                                                                  ),
                                                                  Row(
                                                                    children: [
                                                                      Container(
                                                                        height:
                                                                            55.h,
                                                                        width:
                                                                            50.w,
                                                                        child:
                                                                            Stack(
                                                                          alignment:
                                                                              Alignment.bottomRight,
                                                                          children: [
                                                                            Container(
                                                                              height: 55.h,
                                                                              width: 50.w,
                                                                              child: Container(
                                                                                height: 55.h,
                                                                                width: 50.w,
                                                                                decoration: BoxDecoration(
                                                                                  border: Border.all(
                                                                                    color: Color(0xFFEDEDED),
                                                                                    width: 5.w,
                                                                                  ),
                                                                                  shape: BoxShape.circle,
                                                                                ),
                                                                                child: Center(
                                                                                  child: Container(
                                                                                    width: 40,
                                                                                    height: 40,
                                                                                    // padding: const EdgeInsets.only(
                                                                                    //     top: 8, left: 8, right: 8),
                                                                                    // clipBehavior: Clip.antiAlias,
                                                                                    decoration: ShapeDecoration(
                                                                                      image: DecorationImage(
                                                                                        image: NetworkImage(profileControllers.profile.value.avatar.toString().isNotEmpty ? profileControllers.profile.value.avatar.obs.string : 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png'),
                                                                                        fit: BoxFit.cover,
                                                                                      ),
                                                                                      shape: RoundedRectangleBorder(
                                                                                        side: BorderSide(
                                                                                          width: 1.w,
                                                                                          strokeAlign: BorderSide.strokeAlignOutside,
                                                                                          color: Colors.white,
                                                                                        ),
                                                                                        borderRadius: BorderRadius.circular(20.r),
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                            Positioned(
                                                                              top: 38.h,
                                                                              // left: 14.w,d
                                                                              // right: 16.w,
                                                                              child: Row(
                                                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                                children: [
                                                                                  Container(height: 18.h, width: 18.w, child: SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="2.99707" y="3" width="18" height="18" rx="9" fill="#22409A"/><path d="M10.7059 7.93698C11.253 6.64566 11.5266 6 11.9971 6C12.4675 6 12.7411 6.64566 13.2883 7.93698L13.3138 7.99711C13.6229 8.72664 13.7774 9.0914 14.0924 9.31311C14.4074 9.53482 14.821 9.56988 15.648 9.64L15.7975 9.65267C17.151 9.76742 17.8278 9.8248 17.9726 10.2324C18.1174 10.64 17.6148 11.0729 16.6096 11.9386L16.2741 12.2275C15.7653 12.6658 15.5109 12.8849 15.3923 13.1721C15.3702 13.2256 15.3518 13.2805 15.3372 13.3364C15.2593 13.6358 15.3338 13.9536 15.4828 14.5894L15.5292 14.7873C15.803 15.9557 15.9399 16.5399 15.7009 16.7919C15.6115 16.886 15.4954 16.9538 15.3665 16.9872C15.0213 17.0763 14.5313 16.6983 13.5512 15.9423C12.9076 15.4459 12.5859 15.1976 12.2164 15.1418C12.0711 15.1198 11.923 15.1198 11.7777 15.1418C11.4083 15.1976 11.0865 15.4459 10.4429 15.9423C9.46287 16.6983 8.97283 17.0763 8.62767 16.9872C8.4987 16.9538 8.38259 16.886 8.29326 16.7919C8.0542 16.5399 8.19112 15.9557 8.46496 14.7873L8.51134 14.5894C8.66034 13.9536 8.73485 13.6358 8.6569 13.3364C8.64236 13.2805 8.62397 13.2256 8.60185 13.1721C8.48327 12.8849 8.22885 12.6658 7.72001 12.2275L7.38453 11.9386C6.37935 11.0729 5.87676 10.64 6.02157 10.2324C6.16638 9.8248 6.84314 9.76742 8.19666 9.65267L8.34617 9.64C9.17319 9.56988 9.58669 9.53482 9.9017 9.31311C10.2167 9.0914 10.3713 8.72664 10.6804 7.99711L10.7059 7.93698Z" fill="white"/></svg>')),
                                                                                  Text(
                                                                                    "0%",
                                                                                    textAlign: TextAlign.center,
                                                                                  )
                                                                                ],
                                                                              ),
                                                                            )
                                                                          ],
                                                                        ),
                                                                      ),
                                                                      SizedBox(
                                                                          width:
                                                                              22.w),
                                                                      Column(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.start,
                                                                        children: [
                                                                          // Text("DSF735"),
                                                                          Text(
                                                                            mrController.mrData.value.mr_id.toString(),
                                                                            style:
                                                                                TextStyle(
                                                                              overflow: TextOverflow.ellipsis,
                                                                              color: configTheme().textTheme.bodyMedium?.color,
                                                                              fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                                                              fontFamily: configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                                                              fontWeight: configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                                                              // height: 0,
                                                                            ),
                                                                          ),
                                                                          Text(
                                                                            maxLines:
                                                                                1,
                                                                            "${profileControllers.profile.value.firstname.toString()} ${profileControllers.profile.value.lastname.toString()}",
                                                                            style:
                                                                                TextStyle(
                                                                              overflow: TextOverflow.ellipsis,
                                                                              color: configTheme().textTheme.bodyMedium?.color,
                                                                              fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                                                                              fontFamily: configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                                                              fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                                                              // height: 0,
                                                                            ),
                                                                          ),
                                                                          Container(
                                                                              height: 22.h,
                                                                              decoration: BoxDecoration(
                                                                                color: mrController.mrData.value.mr_id == null ? Color(0xffffffff) : configTheme().primaryColor.withOpacity(0.05),
                                                                                borderRadius: BorderRadius.circular(6),
                                                                              ),
                                                                              child: Center(
                                                                                  child: Container(
                                                                                margin: EdgeInsets.only(left: 6.w, right: 6.w),
                                                                                child: Text(
                                                                                  mrController.mrData.value.mr_rank.toString(),
                                                                                  style: TextStyle(
                                                                                    color: configTheme().primaryColor.withOpacity(0.75),
                                                                                    fontSize: configTheme().primaryTextTheme.labelMedium?.fontSize,
                                                                                    fontFamily: configTheme().primaryTextTheme.labelMedium?.fontFamily,
                                                                                    fontWeight: configTheme().primaryTextTheme.labelMedium?.fontWeight,
                                                                                    // height: 0,
                                                                                  ),
                                                                                ),
                                                                              ))),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  )
                                                                ],
                                                              ),
                                                              // child: buildHead(profileControllers.profile.value.ref_code.toString(), '${profileControllers.profile.value.firstname} ${profileControllers.profile.value.lastname}', ""),
                                                            ),
                                                ),
                                              )
                                            : mrController.indexPageMR!.value ==
                                                    1
                                                ? Padding(
                                                    padding: EdgeInsets.only(
                                                        top: 150.h, left: 17.w),
                                                    child: Container(
                                                      height: 329.h,
                                                      width: 344.w,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(14),
                                                        color: Colors.white,
                                                      ),
                                                      child: Padding(
                                                        padding:
                                                            EdgeInsets.all(5.0),
                                                        child: Image.asset(appConfigService
                                                                    .countryConfigCollection ==
                                                                "aam"
                                                            ? 'assets/tutorial/aam/mr_history.png'
                                                            : appConfigService
                                                                        .countryConfigCollection ==
                                                                    "rafco"
                                                                ? 'assets/tutorial/rafco/mr_history.png'
                                                                : 'assets/tutorial/rplc/mr_history.png'),
                                                      ),
                                                    ),
                                                  )
                                                : Padding(
                                                    padding: EdgeInsets.only(
                                                        top: 480.h, left: 17.w),
                                                    child: Container(
                                                      height: 189.h,
                                                      width: 340.w,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(14),
                                                        color: Colors.white,
                                                      ),
                                                      child: Padding(
                                                        padding:
                                                            EdgeInsets.all(5.0),
                                                        child: Image.asset(appConfigService
                                                                    .countryConfigCollection ==
                                                                "aam"
                                                            ? 'assets/tutorial/aam/mr_recoment.png'
                                                            : appConfigService
                                                                        .countryConfigCollection ==
                                                                    "rafco"
                                                                ? 'assets/tutorial/rafco/mr_recoment.png'
                                                                : 'assets/tutorial/rplc/mr_recoment.png'),
                                                      ),
                                                    ),
                                                  ),
                                        Stack(children: [
                                            Padding(
                                              padding: EdgeInsets.only(
                                                  top: mrController.indexPageMR!
                                                              .value ==
                                                          0
                                                      ? 250.h
                                                      : mrController
                                                                  .indexPageMR!
                                                                  .value ==
                                                              1
                                                          ? 550.h
                                                          : 238.h,
                                                  left: 17.w),
                                              child: Container(
                                                // margin: EdgeInsets.only(top: 150.h),
                                                height: 189.h,
                                                width: 340.w,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(14),
                                                  color: Colors.white,
                                                ),
                                                child: Column(
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.only(
                                                          left: 32.5.w,
                                                          right: 64.5.w,
                                                          top: 38.h),
                                                      child: Text.rich(
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: Colors.black,
                                                          ),
                                                          TextSpan(
                                                            children: [
                                                              TextSpan(
                                                                text: (index >= 0 && index < mrController.popUpText.length)
                                                                    ? (mrController.indexTutorialMR.value == 2 && homeController.isGuest!.value
                                                                    ? popUpTutorialRecommend_guest.tr
                                                                    : mrController.popUpText[index]['title'])
                                                                    : "Invalid Index", // ค่าเริ่มต้นเมื่อ index ไม่ถูกต้อง
                                                              ),
                                                              TextSpan(
                                                                text: mrController.indexTutorialMR.value == 2
                                                                    ? homeController.isGuest!.value
                                                                    ? '${onBoardingVisit.tr}'
                                                                    : "${profileControllers.profile.value.firstname}"
                                                                    : "",
                                                                style: TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight: FontWeight.w700,
                                                                  color: Colors.black,
                                                                ),
                                                              ),
                                                              TextSpan(
                                                                text: (index >= 0 && index < mrController.popUpText.length)
                                                                    ? mrController.popUpText[index]['content']
                                                                    : "", // ค่าเริ่มต้นเมื่อ index ไม่ถูกต้อง
                                                              ),
                                                              TextSpan(
                                                                text: (index >= 0 && index < mrController.popUpText.length)
                                                                    ? mrController.popUpText[index]['content1']
                                                                    : "", // ค่าเริ่มต้นเมื่อ index ไม่ถูกต้อง
                                                                style: TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight: FontWeight.w700,
                                                                  color: Colors.black,
                                                                ),
                                                              ),
                                                              TextSpan(
                                                                text: (index >= 0 && index < mrController.popUpText.length)
                                                                    ? mrController.popUpText[index]['content2']
                                                                    : "", // ค่าเริ่มต้นเมื่อ index ไม่ถูกต้อง
                                                              ),
                                                            ],
                                                          )
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 42.h,
                                                    ),
                                          
                                                  ],
                                                ),
                                              ),
                                            ),
                                     Positioned(
  top: mrController.indexPageMR!.value == 0
      ? 193.h
      : mrController.indexPageMR!.value == 1
          ? 498.h
          : 186.h,
  left: 247.w,
  right: 26.w,
  child: Container(
    height: 91.h,
    width: 102.w,
    child: (index >= 0 && index < mrController.masPopUp.length)
        ? Image.asset(
            appConfigService.countryConfigCollection == "aam"
                ? mrController.masPopUp[index]
                : appConfigService.countryConfigCollection == "rafco"
                    ? mrController.masPopUpRafco.length > index
                        ? mrController.masPopUpRafco[index]
                        : ""
                    : mrController.masPopUpRplc.length > index
                        ? mrController.masPopUpRplc[index]
                        : "",
            fit: BoxFit.cover,
          )
        : SizedBox.shrink(),
  ),
),
                                            Positioned(
  bottom: 20,
  right: 0,
  child: InkWell(
    onTap: () async {
      box.write("isMRShown", 'already');

      // หยุดเสียงที่กำลังเล่นอยู่
      await flutterTts.stop();

      // ตั้งค่าให้ TTS รอจนกว่าจะอ่านเสร็จก่อนทำอย่างอื่น
      await flutterTts.awaitSpeakCompletion(true);

      // รอให้แน่ใจว่า TTS หยุดสนิท
      await Future.delayed(Duration(milliseconds: 300));

      // เพิ่มค่าของ index
      index++;
      print('index: $index');

      // อัปเดตค่าของ index ใน controller
      mrController.setIndexTutorialMR(index);
      mrController.indexPageMR!.value = index;
      mrController.update();

      print('indexPageMR: ${mrController.indexPageMR!.value}');

      // ตรวจสอบว่าถึงหน้าสุดท้ายแล้วหรือยัง
      if (index > (mrController.popUpText.length - 1)) {
        await flutterTts.stop();
        Navigator.pop(context);
        return; // ออกจากฟังก์ชันถ้าถึงหน้าสุดท้ายแล้ว
      }

      // เรียกฟังก์ชันอ่านเสียงใหม่
      if (appConfigService.countryConfigCollection == "aam") {
        await Future.delayed(Duration(milliseconds: 300)); // หน่วงเพิ่มให้แน่ใจว่า TTS พร้อม
        voiceMR(index);
      }
    },
    child: Container(
      width: 53.w,
      margin: EdgeInsets.only(right: 20.w),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              mrController.indexTutorialMR.value >= 2
                  ? backInNewPasswordFinish.tr
                  : menuGetLoanNext.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.black.withOpacity(0.4),
              ),
            ),
            SizedBox(width: 8.w),
            Container(
              height: 24.h,
              child: Center(
                child: SvgPicture.string(
                  color: Colors.black.withOpacity(0.4),
                  '<svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.82614 11.37L7.10667 7.08944C7.25989 6.93654 7.38144 6.75492 7.46437 6.55499C7.54731 6.35505 7.59 6.14073 7.59 5.92427C7.59 5.70782 7.54731 5.49349 7.46437 5.29356C7.38144 5.09362 7.25989 4.91201 7.10667 4.75911L2.82614 0.478578C1.78493 -0.546105 0 0.181089 0 1.65201L0 10.1965C0 11.684 1.78493 12.4112 2.82614 11.37Z" fill="black"/></svg>',
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  ),
),
                                          ],
                                        ),
                                      ],
                                    ));
                          },
                          options: CarouselOptions(
                            height: Get.height,
                            // aspectRatio: 16 / 9,
                            viewportFraction: 1,
                            initialPage: 0,
                            scrollPhysics: NeverScrollableScrollPhysics(),
                          ))
                    ],
                  ),
                ),
              ),
            ));
      },
    );
    }
  }

  showNotificationPopup() {
    final isPopupShown = box.read("isNotificationPopupShown") ?? false;

    if (!isPopupShown) {
      if(appConfigService.countryConfigCollection == "aam"){
        homeController.speak(popUpTutorialNotificationPage.tr + popUpTutorialNotificationPage1.tr + popUpTutorialNotificationPage2.tr + backInNewPasswordFinish.tr );
      }
      box.write("isNotificationPopupShown", true);
      showDialog(
        context: Get.context!,
        builder: (context) {
          return Material(
            color: Colors.transparent,
            child: Stack(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 20.h, left: 17.w),
                  child: Container(
                    height: 89.h,
                    width: 303.w,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: Image.asset(
                        appConfigService.countryConfigCollection == "aam"
                            ? 'assets/tutorial/aam/noti.png'
                            : appConfigService.countryConfigCollection ==
                                    "rafco"
                                ? "assets/tutorial/rafco/noti.png"
                                : 'assets/tutorial/rplc/noti.png',
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 245.h, left: 17.w),
                  child: Container(
                    // margin: EdgeInsets.only(top: 150.h),
                    height: 189.h,
                    width: 340.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: Colors.white,
                    ),
                    child: Stack(
                      children: [
                        Column(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 32.5.w, right: 64.5.w, top: 38.h),
                              child: Text.rich(
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.black,
                                  ),
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: popUpTutorialNotificationPage.tr,
                                      ),
                                      TextSpan(
                                        text: popUpTutorialNotificationPage1.tr,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w700,
                                          color: Colors.black,
                                        ),
                                      ),
                                      TextSpan(
                                        text: popUpTutorialNotificationPage2.tr,
                                      ),
                                    ],
                                  )),
                            ),
                            SizedBox(
                              height: 42.h,
                            ),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: InkWell(
                                onTap: () async {
                                  await flutterTts.stop();

                                  // รอให้แน่ใจว่าเสียงเก่าหยุดสนิทก่อน
                                  await Future.delayed(Duration(milliseconds: 300));
                                  Navigator.pop(context);
                                },
                                child: Container(
                                    width: 60.w,
                                    margin: EdgeInsets.only(right: 20.w),
                                    // height: 24.h,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                            // mrController.indexTutorialMR.value >= 2
                                            //     ? backInNewPasswordFinish
                                            //     .tr
                                            //     :
                                            backInNewPasswordFinish.tr,
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              color:
                                                  Colors.black.withOpacity(0.4),
                                            )),
                                        SizedBox(
                                          width: 8.w,
                                        ),
                                        // Container(
                                        //   height: 24.h,
                                        //   // width: 24.w,
                                        //   child: Center(
                                        //     child: SvgPicture.string(
                                        //         color: Colors
                                        //             .black
                                        //             .withOpacity(0.4),
                                        //         '<svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.82614 11.37L7.10667 7.08944C7.25989 6.93654 7.38144 6.75492 7.46437 6.55499C7.54731 6.35505 7.59 6.14073 7.59 5.92427C7.59 5.70782 7.54731 5.49349 7.46437 5.29356C7.38144 5.09362 7.25989 4.91201 7.10667 4.75911L2.82614 0.478578C1.78493 -0.546105 0 0.181089 0 1.65201L0 10.1965C0 11.684 1.78493 12.4112 2.82614 11.37Z" fill="black"/></svg>'),
                                        //   ),
                                        // )
                                      ],
                                    )),
                              ),
                            )
                          ],
                        ),
                        // Align(
                        //   alignment: Alignment.topRight,
                        //   child: Container(
                        //     margin: EdgeInsets.only(right: 9.w),
                        //     height: 91.h,
                        //     width: 102.w,
                        //     child: Image.asset(
                        //       'assets/pop_up_tutorial/mas_rafco.png',
                        //       fit: BoxFit.cover,
                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  showSettingPopup() {
    final isPopupShown = box.read("isSettingPopupShown") ?? false;
    if (!isPopupShown) {
      homeController.speak(profileController
          .popUpTextSet[
      0]['title'] + profileController
          .popUpTextSet[
      0]
      ['content'] + profileController
          .popUpTextSet[
      0]
      ['content1'] );
      box.write("isMRPopupShown", true);
      box.write("isSettingPopupShown", true);
    showDialog(
      context: Get.context!,
      builder: (context) {
        return  profileController.popUpTextSet.isEmpty
            ? Center(child: CircularProgressIndicator())
          :Material(
            color: Colors.transparent,
            child: Container(
              height: Get.height,
              // color: Colors.greenAccent,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CarouselSlider.builder(
                        itemCount: profileController.popUpTextSet.length,
                        itemBuilder: (BuildContext context, int indexSet,
                            int realIndex) {
                          return GetBuilder<ProfileController>(
                            init: ProfileController(),
                              builder: (profileControllers)=>Stack(
                            children: [
                              profileController.indexPageSetting!.value == 0
                                  ? Container()
                                  : Padding(
                                padding: EdgeInsets.only(
                                    top: 160.h, left: 17.w),
                                child: Container(
                                  height: 61.h,
                                  width: 343.w,
                                  decoration: BoxDecoration(
                                    borderRadius:
                                    BorderRadius.circular(14),
                                    color: Colors.white,
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(5.0), 
                                    child: Image.asset(appConfigService
                                        .countryConfigCollection ==
                                        "aam"
                                        ? 'assets/tutorial/aam/setting.png'
                                        : appConfigService
                                        .countryConfigCollection ==
                                        "rafco"
                                        ? 'assets/tutorial/rafco/setting.png'
                                        : 'assets/tutorial/rplc/setting.png'),
                                  ),
                                ),
                              ),
                              Stack(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                        top: profileController
                                            .indexPageSetting!.value ==
                                            0
                                            ? 550.h
                                            : 344.h,
                                        left: 17.w),
                                    child: Container(
                                      // margin: EdgeInsets.only(top: 150.h),
                                      height: 189.h,
                                      width: 340.w,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(14),
                                        color: Colors.white,
                                      ),
                                      child: Column(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(
                                                left: 32.5.w,
                                                right: 64.5.w,
                                                top: 38.h),
                                            child: Text.rich(
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  color: Colors.black,
                                                ),
                                                TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: (indexSet >= 0 && indexSet < profileController.popUpTextSet.length)
                                                          ? profileController.popUpTextSet[indexSet]['title'] ?? ''
                                                          : '',
                                                    ),

                                                    TextSpan(
                                                      text: (indexSet >= 0 && indexSet < profileController.popUpTextSet.length)
                                                          ? profileController.popUpTextSet[indexSet]['content'] ?? ''
                                                          : '',
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                        FontWeight.w700,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: (indexSet >= 0 && indexSet < profileController.popUpTextSet.length)
                                                          ? profileController.popUpTextSet[indexSet]['content1'] ?? ''
                                                          : '',
                                                    ),
                                                    // TextSpan(
                                                    //   text:profileController.popUpTextSet.length.toString(),
                                                    // ),
                                                  ],
                                                )),
                                          ),
                                          SizedBox(
                                            height: 42.h,
                                          ),
//                                           Align(
//                                             alignment: Alignment.bottomRight,
//                                             child: InkWell(
//                           onTap: () async {
//                           // หยุดเสียงที่กำลังเล่น
//                           await flutterTts.stop();

//                           // ตั้งค่าให้ TTS รอจนกว่าจะอ่านเสร็จ
//                           await flutterTts.awaitSpeakCompletion(true);

//                           // รอให้แน่ใจว่าเสียงเก่าหยุดสนิท
//                           await Future.delayed(Duration(milliseconds: 300));

//                           // อัปเดตค่า index
//                           indexSet++;
//                           print('index: $indexSet');
//                           profileController.setIndexPopupSetting(indexSet);
//                           profileController.indexPageSetting!.value = indexSet;

//                           // ตรวจสอบว่า index อยู่ในขอบเขตก่อนอ่าน
//                           if (indexSet < profileController.popUpTextSet.length) {
//                           String textToSpeak =
//                           profileController.popUpTextSet[indexSet]['title'] +
//                           profileController.popUpTextSet[indexSet]['content'] +
//                           profileController.popUpTextSet[indexSet]['content1'];

//                           await Future.delayed(Duration(milliseconds: 300)); // หน่วงเพื่อให้แน่ใจว่า TTS พร้อม
//                           homeController.speak(textToSpeak);
//                           } else {
//                           print('Index out of range');
//                           await flutterTts.stop();
//                           Navigator.pop(context);
//                           return; // ป้องกันโค้ดด้านล่างทำงานเมื่อปิด popup
//                           }

//                           print('indexTextSet');
//                           print(profileController.popUpTextSet.length - 1);
//                           profileController.update();
//                           }
// ,
//                           child: Container(
//                                                 width: 60.w,
//                                                 margin: EdgeInsets.only(
//                                                   right: 20.w,
//                                                 ),
//                                                 child: SingleChildScrollView(
//                                                   physics : NeverScrollableScrollPhysics(),
//                                                   scrollDirection: Axis.horizontal,
//                                                   child: Row(
//                                                     crossAxisAlignment: CrossAxisAlignment.center,
//                                                     children: [
//                                                       Text(
//                                                         profileController.indexPopupSetting.value == 1
//                                                             ? backInNewPasswordFinish.tr
//                                                             : menuGetLoanNext.tr,
//                                                         style: TextStyle(
//                                                           fontSize: 14,
//                                                           fontWeight: FontWeight.w400,
//                                                           color: Colors.black.withOpacity(0.4),
//                                                         ),
//                                                       ),
//                                                       SizedBox(
//                                                         width: 8.w,
//                                                       ),
//                                                       Container(
//                                                         height: 24.h,
//                                                         child: Center(
//                                                           child: SvgPicture.string(
//                                                             color: Colors.black.withOpacity(0.4),
//                                                             '<svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.82614 11.37L7.10667 7.08944C7.25989 6.93654 7.38144 6.75492 7.46437 6.55499C7.54731 6.35505 7.59 6.14073 7.59 5.92427C7.59 5.70782 7.54731 5.49349 7.46437 5.29356C7.38144 5.09362 7.25989 4.91201 7.10667 4.75911L2.82614 0.478578C1.78493 -0.546105 0 0.181089 0 1.65201L0 10.1965C0 11.684 1.78493 12.4112 2.82614 11.37Z" fill="black"/></svg>',
//                                                           ),
//                                                         ),
//                                                       ),
//                                                     ],
//                                                   ),
//                                                 ),
//                                               ),
//                                             ),
//                                           )
                                        ],
                                      ),
                                    ),
                                  ),
                                 Positioned(
  bottom: 20,
  right: 0,
  child: InkWell(
    onTap: () async {
      // หยุดเสียงที่กำลังเล่น
      await flutterTts.stop();

      // ตั้งค่าให้ TTS รอจนกว่าจะอ่านเสร็จ
      await flutterTts.awaitSpeakCompletion(true);

      // รอให้แน่ใจว่าเสียงเก่าหยุดสนิท
      await Future.delayed(Duration(milliseconds: 300));

      // อัปเดตค่า index
      indexSet++;
      print('index: $indexSet');
      profileController.setIndexPopupSetting(indexSet);
      profileController.indexPageSetting!.value = indexSet;

      // ตรวจสอบว่า index อยู่ในขอบเขตก่อนอ่าน
      if (indexSet < profileController.popUpTextSet.length) {
        String textToSpeak = profileController.popUpTextSet[indexSet]['title'] +
            profileController.popUpTextSet[indexSet]['content'] +
            profileController.popUpTextSet[indexSet]['content1'];

        await Future.delayed(Duration(milliseconds: 300)); // หน่วงเพื่อให้แน่ใจว่า TTS พร้อม
        homeController.speak(textToSpeak);
      } else {
        print('Index out of range');
        await flutterTts.stop();
        Navigator.pop(context);
        return; // ป้องกันโค้ดด้านล่างทำงานเมื่อปิด popup
      }

      print('indexTextSet');
      print(profileController.popUpTextSet.length - 1);
      profileController.update();
    },
    child: Container(
      width: 60.w,
      margin: EdgeInsets.only(right: 20.w),
      child: SingleChildScrollView(
        physics: NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              profileController.indexPopupSetting.value == 1
                  ? backInNewPasswordFinish.tr
                  : menuGetLoanNext.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.black.withOpacity(0.4),
              ),
            ),
            SizedBox(width: 8.w),
            Container(
              height: 24.h,
              child: Center(
                child: SvgPicture.string(
                  color: Colors.black.withOpacity(0.4),
                  '<svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.82614 11.37L7.10667 7.08944C7.25989 6.93654 7.38144 6.75492 7.46437 6.55499C7.54731 6.35505 7.59 6.14073 7.59 5.92427C7.59 5.70782 7.54731 5.49349 7.46437 5.29356C7.38144 5.09362 7.25989 4.91201 7.10667 4.75911L2.82614 0.478578C1.78493 -0.546105 0 0.181089 0 1.65201L0 10.1965C0 11.684 1.78493 12.4112 2.82614 11.37Z" fill="black"/></svg>',
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  ),
),
                                ],
                              ),
                              Positioned(
  top: profileController.indexPageSetting!.value == 0 ? 498.h : 292.h,
  left: 247.w,
  right: 26.w,
  child: Container(
    height: 91.h,
    width: 102.w,
    child: Image.asset(
      appConfigService.countryConfigCollection == 'aam'
          ? profileController.popUpMas[indexSet]
          : appConfigService.countryConfigCollection == "rafco"
              ? profileController.popUpMasRafco[indexSet]
              : profileController.popUpMasRplc[indexSet],
      fit: BoxFit.cover,
    ),
  ),
),

                            ],
                          ));
                        },
                        options: CarouselOptions(
                          height: Get.height,
                          // aspectRatio: 16 / 9,
                          viewportFraction: 1,
                          // initialPage: 0,
                          scrollPhysics: NeverScrollableScrollPhysics(),
                        ))
                  ],
                ),
              ),
            ));
      },
    );
  }
}
}
