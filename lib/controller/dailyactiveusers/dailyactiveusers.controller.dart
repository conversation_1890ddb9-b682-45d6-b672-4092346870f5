import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io' show Platform;

import '../config/appConfig.controller.dart';
import '../profile/profile.controller.dart';
import '/service/http_service.dart';
import '/service/endpoint.dart';


class DailyActiveUsersController extends GetxController {
  Future<dynamic> saveLogMenu(String menuName) async {
    try {
      debugPrint("## saveLogMenu");
      final ProfileController profileCtl = Get.find<ProfileController>();

      if(profileCtl.profile.value.phone.obs.toString() == 'null' || profileCtl.profile.value.running.obs.toString() == 'null'){
        return;
      }
      Map data = {
        "phone": profileCtl.profile.value.phone.obs.string,
        "user_running": profileCtl.profile.value.running.obs.string,
        "menuId": menuName,
        "platform": Platform.isIOS ? "ios" : Platform.isAndroid ? "android" : "web",
      };
      final response =
          await HttpService.callAPIjwt("POST", Endpoints.saveActiveUsers, data);

      if (response["status"] == 200) {
        debugPrint(response["result"].toString());
      } else {
        debugPrint(response.toString());
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
