import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:AAMG/controller/navigator.controller.dart';
import 'package:AAMG/controller/notification/notification.controllet.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../app_config.dart';
import '../../firebase_options.dart';
import '../../service/secure_storage.dart';
import '../../view/screen/notify/notify_page.dart';
import '../AppConfigService.dart';

class NotifyController extends GetxController {
  SecureStorage secureStorage = Get.put(SecureStorage());
  AppConfigService appConfigService = Get.find<AppConfigService>();

  RxString title = "".obs;
  RxString body = "".obs;
  RxInt countChat = 0.obs;
  @override
  void onInit() {
    print('environment :: ${appConfigService.environment}');

    // _configureFirebaseMessaging();
    Future.delayed(Duration.zero, () {
      // if (!kIsWeb) {
      clearNotificationWhenOpenApp();
      initializeNotifications();

      // }
    });
    super.onInit();
  }

  void clearNotificationWhenOpenApp() async {
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    flutterLocalNotificationsPlugin.cancelAll();
  }

  void initializeNotifications() async {
    // NavigationController navigationController = Get.put(NavigationController());
    NotificationController notiController = Get.find<NotificationController>();

    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      print('✅ Firebase initialized');

      FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
      print('✅ firebaseMessaging');
      // Request permissions
      await firebaseMessaging.requestPermission();
      print('✅ firebaseMessaging.requestPermission');
      // Get token properly
      String? token = await firebaseMessaging.getToken();
      print('✅ fcmToken: $token');
      if (token != null) await setTokenNotify(token);

      // Subscribe to topics
      await setSubscribeToTopics(
          firebaseMessaging, ["NEWS", "DAILY_REWARD"]);

      // Web-specific logic
      if (kIsWeb) {
        await FirebaseMessaging.instance.setAutoInitEnabled(true);
        print('🌐 Web detected');
      }

      // iOS specific logic (must guard with !kIsWeb)
      if (!kIsWeb && Platform.isIOS) {
        await getAPNToken(firebaseMessaging);
      }
      print('🌐 Platform.isIOS');

      // Listen for foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage event) {
        final title = event.notification?.title ?? "ไม่มีหัวข้อ";
        final body = event.notification?.body ?? "ไม่มีข้อความ";

        debugPrint('📨 onMessage: ${event.notification?.title}');
        debugPrint('📨 onMessage: ${event.notification?.toMap()}');
        print('event');
        print(event.data['activity']);
        if (kIsWeb) {
          print('event-kIsWeb');
          print(event.data);
          final topic = event.data['activity'];
          final topics = (GetStorage().read('subscribedTopics') as List?)
                  ?.cast<String>() ??
              [];

          if (topic != null && !topics.contains(topic)) {
            debugPrint("⛔ Web ignored topic: $topic (not subscribed)");
            return;
          }
        }

        if (event.notification != null) {
          sendNotification(
              event.notification!.title!, event.notification!.body!, event);
        }
        debugPrint('📨 🔍 RemoteMessage = ${jsonEncode(event.toMap())}');
      });
      print('🌐 FirebaseMessaging.onMessage.listen');

      //TODO เปิดไปยังหน้าเมนูที่กำหนด เมื่อกด notification
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage event) async {
        print('🌐 FirebaseMessaging.onMessageOpenedApp.listen');
        print(event.data);

        if (AppConfig.of(Get.context!).countryConfigCollection.toString() ==
            'aam') {
          if (event.data['activity'].toString() == 'bill' ||
              event.data['activity'].toString() == 'requestIns' ||
              event.data['activity'].toString() == 'Nextpayaut' ||
              event.data['activity'].toString() == 'myloan' ||
              event.data['activity'].toString() == 'evaluation') {
            notiController.getNotification(Get.context!);

            notiController.setTrickerTypeNoti(0);
          } else if (event.data['activity'].toString() == "claimrewar") {
            notiController.getNotification(Get.context!);

            notiController.setTrickerTypeNoti(1);
          } else if (event.data['activity'].toString() == "news") {
            notiController.getNotification(Get.context!);

            notiController.setTrickerTypeNoti(2);
          } else if (event.data['activity'].toString() == "ChatInApp") {
            // Get.to(WebViewTelegram());
          }
        }
      });

      AwesomeNotifications().setListeners(
        onActionReceivedMethod: (ReceivedAction action) async {
          final target = action.payload?['target'];
          print('🚀 onActionReceived: $target');

          switch (target) {
            case 'TEST':
              notiController.getNotification(Get.context!);

              notiController.setTrickerTypeNoti(2);

              break;
            case 'claimrewar':
              notiController.getNotification(Get.context!);
              notiController.setTrickerTypeNoti(1);
              break;
            case 'bill':
            case 'requestIns':
            case 'Nextpayaut':
            case 'myloan':
            case 'evaluation':
              notiController.getNotification(Get.context!);

              notiController.setTrickerTypeNoti(0);
              break;
            default:
              print('⚠️ Unknown target');
          }
          Get.find<NavigationController>().changeIndex(2); // ไปหน้า notify
        },
      );
    } catch (e) {
      print("initializeNotifications");
      print(e);
    }
  }

  Future<void> firebaseMessagingBackgroundHandler(RemoteMessage event) async {
    sendLocalFlutterNotification(
        event.notification!.title!, event.notification!.body!);
  }

  Future<void> setSubscribeToTopics(
      FirebaseMessaging fcm, List<String> topics) async {
    print('setSubscribeToTopics');
    final storage = GetStorage();

    // ✅ ดึงหัวข้อที่เคย subscribe ไว้จาก local storage (Web) หรือไว้ใช้กับ Mobile ก็ได้
    final stored = storage.read('subscribedTopics');
    final List<String> subscribedTopics =
        (stored is List) ? stored.cast<String>() : [];

    // ✅ ถ้ายังไม่เคย subscribe เลย ให้ใช้ topics ที่ส่งมา
    final List<String> topicsToSubscribe =
        subscribedTopics.isNotEmpty ? subscribedTopics : topics;

    if (kIsWeb) {
      // 🌐 Web: จำลองการ subscribe โดยเก็บไว้ใน local storage
      await storage.write('subscribedTopics', topicsToSubscribe);
      debugPrint(
          "🌐 Web mode — Saved fake subscribed topics: $topicsToSubscribe");
      return;
    }

    // 📱 Mobile: subscribe จริงผ่าน FCM
    for (String topic in topicsToSubscribe) {
      try {
        await fcm.subscribeToTopic(topic);
        debugPrint("✅ Subscribed to topic: $topic");
      } catch (e) {
        debugPrint("❌ Failed to subscribe to topic: $topic — $e");
      }
    }

    // 📦 เก็บหัวข้อไว้ใน local storage ด้วย
    await storage.write('subscribedTopics', topicsToSubscribe);
  }

  Future<void> unsubscribeFromTopics(List<String> topics) async {
    for (String topic in topics) {
      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      debugPrint("❌ Unsubscribed from topic: $topic");
    }
  }

  Future<void> getAPNToken(_fcm) async {
    if (Platform.isIOS) {
      String? apnsToken = await _fcm.getAPNSToken();
      if (apnsToken != null) {
        await _fcm.subscribeToTopic('');
      } else {
        await Future<void>.delayed(
          const Duration(
            seconds: 3,
          ),
        );
        apnsToken = await _fcm.getAPNSToken();
        if (apnsToken != null) {
          await _fcm.subscribeToTopic('');
        }
      }
    } else {
      await _fcm.subscribeToTopic('');
    }
  }

  void sendNotification(String title, String body, event) async {
    debugPrint('sendNotification');
    debugPrint('title: $title');
    debugPrint('body: $body');
    debugPrint('event: ${event}');
    debugPrint('event: ${event.data}');

    try {
      if (kIsWeb) {
        // ✅ Show as Web snackbar

        Get.snackbar(title, body,
            snackPosition: SnackPosition.TOP,
            backgroundColor: Color(0xFF9D50DD),
            colorText: Colors.white,
            icon: Icon(Icons.notifications, color: Colors.white),
            duration: const Duration(seconds: 4),
            margin: const EdgeInsets.all(8),
            borderRadius: 16,
            boxShadows: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 8,
                offset: Offset(0, 4),
              ),
            ], onTap: (_) {
          if (event.data['activity'] == 'bill' ||
              event.data['activity'] == 'requestIns' ||
              event.data['activity'] == 'Nextpayaut' ||
              event.data['activity'] == 'myloan' ||
              event.data['activity'] == 'evaluation') {
            Get.to(() => NotifyPage(0));
          } else if (event.data['activity'] == 'claimrewar') {
            Get.to(() => NotifyPage(1));
          } else if (event.data['activity'] == 'news') {
            Get.to(() => NotifyPage(2));
          } else {
            print('Snackbar tapped');
          }
        });

        return;
      }

      AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
        if (!isAllowed) {
          // Insert here your friendly dialog box before call the request method
          AwesomeNotifications().requestPermissionToSendNotifications();
        }
      });

      // Initialize the notification channel
      AwesomeNotifications().initialize(
        'resource://mipmap/ic_launcher', // Set your app icon here
        [
          NotificationChannel(
            channelGroupKey: 'basic_tests',
            channelKey: 'basic_channel',
            channelName: 'Basic notifications',
            channelDescription: 'Notification channel for basic tests',
            defaultColor: Color(0xFF9D50DD),
            ledColor: Colors.white,
            importance: NotificationImportance.High,
            enableLights: true,
            enableVibration: true,
            playSound: true,
            icon: 'resource://mipmap/ic_launcher', // Use the drawable icon
            // soundSource: 'resource://raw/res_custom_notification', // Set custom sound if needed
          ),
        ],
        channelGroups: [
          NotificationChannelGroup(
            channelGroupKey: 'basic_tests',
            channelGroupName: 'Basic tests group',
          )
        ],
        debug: true, // Set to true if you want to debug
      );

      // Check if notification permissions are allowed
      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (!isAllowed) {
        // Request permissions if not allowed
        bool permissionsGranted =
            await AwesomeNotifications().requestPermissionToSendNotifications();
        if (!permissionsGranted) return; // Exit if permissions not granted
      }

      print('AwesomeNotifications().createNotification');

      // Create the notification
      final activity = event.data['activity'] ?? 'default';

      AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
          channelKey: "basic_channel",
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          autoDismissible: true,
          icon: 'resource://mipmap/ic_launcher',
          payload: {
            "target": activity, // 👈 ส่ง target ตาม activity
          },
        ),
      );
    } catch (e) {
      print('sendNotification : error');
      print(e);
    }
  }

  Future<void> sendLocalFlutterNotification(String title, String body) async {
    debugPrint('sendLocalFlutterNotification');
    debugPrint('title: $title');
    debugPrint('body: $body');
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    AndroidNotificationDetails android = const AndroidNotificationDetails(
      'channel id',
      'channel NAME',
      channelDescription: 'CHANNEL DESCRIPTION',
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
      icon: '@mipmap/ic_launcher',
      channelShowBadge: false,
    );

    NotificationDetails platform = NotificationDetails(
        android: android, iOS: DarwinNotificationDetails(badgeNumber: 0));

    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platform,
    );
  }

  void _configureFirebaseMessaging() {
    FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // setSubscribeToTopic(FirebaseMessaging.instance);
    FirebaseMessaging.instance.getToken().then((token) {
      print('token: $token');
      if (token != null && token.isNotEmpty) {
        setTokenNotify(token);
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((event) {
      if (event != null && event.isNotEmpty) {
        setTokenNotify(event);
      }
    });

    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {});

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // showFlutterNotification(message);
    });
    if (!kIsWeb) {
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    }

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage event) async {
      final activity = event.data['activity'] ?? '';
      print('🔁 onMessageOpenedApp: $activity');

      if (AppConfig.of(Get.context!).countryConfigCollection.toString() ==
          'aam') {
        switch (activity) {
          case 'bill':
          case 'requestIns':
          case 'Nextpayaut':
          case 'myloan':
          case 'evaluation':
            Get.to(() => NotifyPage(0));
            break;
          case 'claimrewar':
            Get.to(() => NotifyPage(1));
            break;
          case 'news':
            Get.to(() => NotifyPage(2));
            break;
          default:
            print('⚠️ Unknown activity in onMessageOpenedApp');
        }
      }
    });
  }

  setTokenNotify(tokenNotify) async {
    print('tokenNotify: $tokenNotify');
    GetStorage box = GetStorage();
    await box.write('tokenNotify', tokenNotify.toString());
  }

  Future<void> addNotiChat() async {
    GetStorage box = GetStorage();
    String deviceTK = await box.read('tokenNotify');
    Map data = {"device_token": deviceTK};
    // print(data);

    final resposne = await HttpService.post(
        Endpoints.getCountUnreadChat, data, HttpService.noKeyRequestHeaders);
    // print('response addNotiChat : ${resposne}');
    if (resposne["statusCode"] == 200) {
      countChat.value = resposne["count"];
    } else {
      countChat.value = 0;
    }

    update();
  }

  Future<void> delNotiChat() async {
    GetStorage box = GetStorage();
    String deviceTK = await box.read('tokenNotify');
    Map data = {"device_token": deviceTK};
    // print(data);

    await await HttpService.post(
        Endpoints.delCountUnreadChat, data, HttpService.noKeyRequestHeaders);
    countChat.value = 0;
    update();
  }
}

class NotifyType {
  static const success = 'success';
  static const error = 'error';
  static const warning = 'warning';
  static const info = 'info';
}
