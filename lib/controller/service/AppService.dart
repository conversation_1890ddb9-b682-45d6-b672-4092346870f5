import 'dart:convert';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../service/http_service.dart';
import '../../service/endpoint.dart';
import '../AppConfigService.dart';

class AppUploadService {
  static cameraImage() async {
    final ImagePicker _picker = ImagePicker();
    final cameraImage = await _picker.pickImage(source: ImageSource.camera);
    return cameraImage != null ? File(cameraImage.path) : null;
  }

  static pickImage() async {
    final ImagePicker _picker = ImagePicker();
    final pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    return pickedImage != null ? File(pickedImage.path) : null;
  }

  static upLoadImgToS3(XFile? imageFile, String imgPath) async {
    AppConfigService appConfig = Get.find<AppConfigService>();

    if (imageFile == null) return;

    // อ่าน bytes จากไฟล์ภาพ
    Uint8List bytes = await imageFile.readAsBytes();

    // แปลงเป็น base64 string
    String base64Image = base64Encode(bytes);

    print("base64Image:${base64Image}");
    print("ตรงนี้${appConfig.countryConfigCollection}");
    var name = "";
    var folder = "";
    // print("appConfig.countryConfigCollection:${appConfig.countryConfigCollection}");
    // name = "MappAAM";
    // folder = "MappAAM/ProfileImages";
    if (appConfig.countryConfigCollection.toString() == 'aam') {
      name = "MappAAM";
      folder = "MappAAM/$imgPath";
    } else if (appConfig.countryConfigCollection.toString() == 'rafco') {
      name = "MappRafco";
      folder = "MappRafco/$imgPath";
    } else {
      name = "MappRPLC";
      folder = "MappRPLC/$imgPath";
    }
    print(base64Image);

    if (base64Image != "" || base64Image != null) {
      Map map = {"name": name, "folder": folder, "image": base64Image};

      final response = await HttpService.post(
          Endpoints.uploadS3_Center, map, HttpService.noKeyRequestHeaders);

      if (response["statusCode"].toString() == "200") {
        return response["result"]["url"]["Location"];
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  static upLoadImgToS3_V1(File? imageFile, String imgPath) async {
    AppConfigService appConfig = Get.find<AppConfigService>();

    if (imageFile == null) return;

    // อ่าน bytes จากไฟล์ภาพ
    // Uint8List bytes = await imageFile.readAsBytes();
    //
    // // แปลงเป็น base64 string
    // String base64Image = base64Encode(bytes);
    String base64Image = base64Encode(imageFile.readAsBytesSync());

    print("base64Image:${base64Image}");
    print("ตรงนี้${appConfig.countryConfigCollection}");
    var name = "";
    var folder = "";
    // print("appConfig.countryConfigCollection:${appConfig.countryConfigCollection}");
    // name = "MappAAM";
    // folder = "MappAAM/ProfileImages";
    if (appConfig.countryConfigCollection.toString() == 'aam') {
      name = "MappAAM";
      folder = "MappAAM/$imgPath";
    } else if (appConfig.countryConfigCollection.toString() == 'rafco') {
      name = "MappRafco";
      folder = "MappRafco/$imgPath";
    } else {
      name = "MappRPLC";
      folder = "MappRPLC/$imgPath";
    }
    print(base64Image);

    if (base64Image != "" || base64Image != null) {
      Map map = {"name": name, "folder": folder, "image": base64Image};

      final response = await HttpService.post(
          Endpoints.uploadS3_Center, map, HttpService.noKeyRequestHeaders);

      if (response["statusCode"].toString() == "200") {
        return response["result"]["url"]["Location"];
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
}
