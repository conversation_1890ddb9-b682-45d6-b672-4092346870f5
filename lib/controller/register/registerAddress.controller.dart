import 'dart:convert';

import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:get/get.dart';
import 'package:AAMG/service/http_service.dart';

import '../../models/address_model.dart';
import '../../service/endpoint.dart';

class RegisterAddressController extends GetxController {
  RxList<City> citiesData = <City>[].obs;
  RxList addressRPLC = [].obs;
  RxList branchRAFCO = [].obs;
  RxString selectedCityCode = ''.obs;
  int? idCity;
  int? idDistrict;
  int? idSubDistrict;

  RxList<District> districtsData = <District>[].obs;
  RxString selectedDistrictCode = ''.obs;
  RxList<SubDistrict> subDistrictsData = <SubDistrict>[].obs;
  RxString selectedSubDistrictCode = ''.obs;
  @override
  onInit() {
    super.onInit();
    checkConfigAddress();
  }

  checkConfigAddress() {
    print("####### checkConfigAddress");
    if (appConfigService.countryConfigCollection.toString() == "aam") {
      getProvinceData();
    } else if (appConfigService.countryConfigCollection.toString() == "rplc") {
      getRPLCAddress();
    } else if (appConfigService.countryConfigCollection.toString() == "rafco") {
      getRAFCOAddress();
    }
  }

  /// เรียกข้อมูลจังหวัด AAM
  Future<dynamic> getProvinceData() async {
    try {
      print("####### getProvinceDataAAM");
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineProvince, {});

      // print('responseGetData: ${response}');
      ///
      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          citiesData.add(City(
            cityId: response['result'][i]['pv_code'],
            cityNameLocal: response['result'][i]['pv_local_name'],
            cityNameThai: response['result'][i]['pv_th_name'],
            cityNameEng: response['result'][i]['pv_en_name'],
            branch: response['result'][i]['branch'],
          ));
          // print('citiesData: ${citiesData}');
          update();
        }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลอำเภอ AAM
  Future<dynamic> getDistrictData() async {
    try {
      districtsData.clear();
      // idCity = 1;
      print(selectedCityCode.value.runtimeType);
      Map data = {
        "pv_code": idCity
      };
      // print('data: $data');
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineDistrict, data);

      // print('response: $response');

      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          districtsData.add(District(
            districtId: response['result'][i]['amp_code'],
            districtNameLocal: response['result'][i]['amp_local_name'],
            districtNameThai: response['result'][i]['amp_th_name'],
            districtNameEng: response['result'][i]['amp_en_name'],
            districtZipcode: response['result'][i]['amp_zipcode'],
          ));
          update();
        }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลตำบล  AAM
  Future<dynamic> getSubDistrictData() async {
    try {
      subDistrictsData.clear();
      // idCity = 1;
      print("getSubDistrictData: ");
      print(idDistrict.runtimeType);
      Map data = {
        "amp_code" : idDistrict,
        // "amp_code" : 1,
      };
      // print('data: $data');
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineSubDistrict, data);

      print('response SubDistrict :');
      // print(response);
      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          subDistrictsData.add(SubDistrict(
            subDistrictId: response['result'][i]['tum_code'],
            subDistrictNameLocal: response['result'][i]['tum_local_name'],
            subDistrictNameThai: response['result'][i]['tum_th_name'],
            subDistrictNameEng: response['result'][i]['tum_en_name'],
          ));
          print('subDistrictsData: ${subDistrictsData[i].subDistrictId}');
          update();
        }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลจังหวัด RPLC
  Future<dynamic> getRPLCAddress() async {
    try {
      // citiesData.clear();
      print('getRPLCAddress #######');
      print("Endpoints.getRPLCAddress");
      print(Endpoints.dropdownLoanOnlineProvince.toString());
      final response =
      await HttpService.callAPIjwtRPLC("POST", Endpoints.dropdownLoanOnlineProvince, {});
      // print('response: ${response}');
      if (response['status'] == 200) {
        // print('addressRPLC: ${addressRPLC.length}');
        for (var i = 0; i < response['result'].length; i++) {
          addressRPLC.add(response['result'][i]);
          citiesData.add(City(
            cityId: response['result'][i]['pv_code'],
            cityNameLocal: response['result'][i]['pv_local_name'],
            cityNameThai: response['result'][i]['pv_th_name'],
            cityNameEng: response['result'][i]['pv_en_name'],
            branch: response['result'][i]['branch'],
          ));
          update();
        }
        // print('citiesData: ${citiesData}');
        // print('citiesData: ${citiesData[0].cityId?.length}');
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลอำเภอ RPLC
  Future<dynamic> getDistrictDataRPLC() async {
    try {
      // citiesData.clear();
      districtsData.clear();
      // idCity = 1;
      print(selectedCityCode.value.runtimeType);
      Map data = {
        "pv_code": idCity
      };
      print('data: $data');
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineDistrict, data);

      print('response District :');
      // print(response);

      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          districtsData.add(District(
            districtId: response['result'][i]['amp_code'],
            districtNameLocal: response['result'][i]['amp_local_name'],
            districtNameThai: response['result'][i]['amp_th_name'],
            districtNameEng: response['result'][i]['amp_en_name'],
            districtZipcode: "",
          ));
          // branchRAFCO.add({
          //   "po_code": response['result']['dataDistrict'][i]['po_code'],
          //   "branch": response['result']['dataDistrict'][i]['branch_rptn']
          // });
          update();
        }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลตำบล RPLC
  Future<dynamic> getSubDistrictDataRPLC() async {
    try {
      // citiesData.clear();
      // subDistrictsData.clear();
      // idCity = 1;
      print(selectedCityCode.value.runtimeType);
      Map data = {
        "amp_code" : 1073
      };
      print('data: $data');
      print(Endpoints.dropdownLoanOnlineSubDistrict);
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineSubDistrict, data);

      print('response SubDistrict :');
      // print(response);

      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          subDistrictsData.add(SubDistrict(
            subDistrictId: response['result'][i]['tum_code'],
            subDistrictNameLocal: response['result'][i]['amp_local_name'],
            subDistrictNameThai: response['result'][i]['tum_th_name'],
            subDistrictNameEng: response['result'][i]['tum_en_name'],
            districtZipcode: "",
          ));
          // branchRAFCO.add({
          //   "po_code": response['result']['dataDistrict'][i]['po_code'],
          //   "branch": response['result']['dataDistrict'][i]['branch_rptn']
          // });
          update();
        }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลจังหวัด RFCO
  Future<dynamic> getRAFCOAddress() async {
    print("getRAFCOAddress");
    try {
      // citiesData.clear();
      // districtsData.clear();
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineProvince, {});
      // print('response Address :');
      // print(response);

      if (response['status'] == 200) {
        for (var  i = 0; i < response['result'].length; i++) {
          citiesData.add(City(
            cityId: response['result'][i]['pv_code'],
            cityNameLocal: response['result'][i]['pv_local_name'],
            cityNameThai: response['result'][i]['pv_th_name'],
            cityNameEng: response['result'][i]['pv_en_name'],
          ));
          // idCity = citiesData[0].cityId;
          update();
          // print('citiesData: ${citiesData[0].cityId}');
          // print('idCity: ${idCity}');
        }
        // idCity = citiesData[0].cityId;
        // print('citiesData: ${citiesData[0].cityId}');
        // for (var i = 0; i < response['result'].length; i++) {
        //   districtsData.add(District(
        //     districtId: response['result'][i]['amp_code'],
        //     districtNameLocal: response['result'][i]['amp_local_name'],
        //     districtNameThai: response['result'][i]['amp_th_name'],
        //     districtNameEng: response['result'][i]['amp_en_name'],
        //     districtZipcode: "",
        //   ));
        //   branchRAFCO.add({
        //     "po_code": response['result']['dataDistrict'][i]['po_code'],
        //     "branch": response['result']['dataDistrict'][i]['branch_rptn']
        //   });
        //   update();
        // }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  /// เรียกข้อมูลอำเภอ RFCO
  Future<dynamic> getRAFCOAddressDistrict() async {
    print("getRAFCOAddressDistrict");
    print('idCity: ${idCity}');
    // final registerController = Get.put(RegisterController());
    try {
      citiesData.clear();
      districtsData.clear();
      // idCity = 1;
      print(selectedCityCode.value.runtimeType);
      Map data = {
        "pv_code": idCity
      };
      // print('data: $data');
      final response = await HttpService.callAPIjwt("POST", Endpoints.dropdownLoanOnlineDistrict, data);

      // print('response District :');
      // print(response);

      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          districtsData.add(District(
            districtId: response['result'][i]['amp_code'],
            districtNameLocal: response['result'][i]['amp_local_name'],
            districtNameThai: response['result'][i]['amp_th_name'],
            districtNameEng: response['result'][i]['amp_en_name'],
            districtZipcode: "",
          ));
          // branchRAFCO.add({
          //   "po_code": response['result']['dataDistrict'][i]['po_code'],
          //   "branch": response['result']['dataDistrict'][i]['branch_rptn']
          // });
          update();
        }
      } else {
        print('Error: ${response['message']}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  void checkConfigDistrict() {
    if (appConfigService.countryConfigCollection.toString() == "aam") {
      getDistrictData();
      getSubDistrictData();
    } else if (appConfigService.countryConfigCollection.toString() == "rplc") {
      getDistrictDataRPLC();
      getSubDistrictDataRPLC();
    }
    else if (appConfigService.countryConfigCollection.toString() == "rafco") {
      getRAFCOAddressDistrict();
    }
  }

  Future<dynamic> getDistrictDataRAFCO() async {
    try {
      districtsData.clear();
      // print('selectedCityCode: ${selectedCityCode.value}');
      // print('addressRPLC: ${addressRPLC.length}');

      // แปลง JSON เป็น List<Map>
      List<Map<String, dynamic>> data =
      addressRPLC.map((e) => e as Map<String, dynamic>).toList();

      // print('data: $data');

      // ดึงข้อมูลจาก JSON โดยใช้ "name_code"
      Map<String, dynamic>? foundData;
      for (Map<String, dynamic> item in data) {
        if (item["amp_code"] == selectedCityCode.value) {
          foundData = item;
          break;
        }
      }

      // ตรวจสอบว่ามีข้อมูลตรงกับ "name_code" หรือไม่
      if (foundData != null) {
        // แสดงข้อมูล

        for (var i = 0; i < foundData["city"].length; i++) {
          districtsData.add(District(
              districtId: foundData["amp_code"],
              districtNameLocal: foundData["city"][i]["amp_local_name"],
              districtZipcode: ""));
          update();
        }
      } else {
        print("ไม่พบข้อมูลสำหรับ name_code: $selectedCityCode.value");
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  void setSelectedCityCode(int cityCode) {
    idCity = cityCode;
    print("setSelectedCityCode: $idCity");
    // selectedCityCode.value = cityCode!;
    update();
    checkConfigDistrict();
    // getDistrictData();
    // getDistrictDataRPLC();
  }

  void setSelectedDisCode(int disCode) {
    idDistrict = disCode;
    print("setSelectedDisCode: $idDistrict");
    update();
    checkConfigDistrict();
    // getDistrictData();
    // getDistrictDataRPLC();
  }

  void setSelectedSubDisCode(int subDisCode) {
    idSubDistrict = subDisCode;
    update();
    checkConfigDistrict();
    // getDistrictData();
    // getDistrictDataRPLC();
  }

}