import 'package:AAMG/controller/login/login.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/screen/register/register_info.dart';
import 'package:flutter/material.dart';
import 'package:sentry/sentry.dart';
import '../../app_config.dart';
import '../../models/register_model.dart';
import '../../service/endpoint.dart';
import '../../view/componance/themes/theme.dart';
import '../../view/componance/widgets/app_pop_up/register_popups.dart';
import '../../view/screen/pincode/pincode.dart';
import '../AppConfigService.dart';
import '../likepoint/webview.point.controller.dart';
import '../pincode/pincode.controller.dart';
import 'registerVerify.controller.dart';

class RegisterController extends GetxController {
  RxInt? indexPage = 0.obs;
  RxBool? referalComplete = false.obs;
  RxBool? referalLoading = false.obs;
  RxBool? referalAlert = false.obs;
  RxBool? nameComplete = false.obs;
  RxBool? lastNameComplete = false.obs;
  RxBool? ProvinceComplete = false.obs;
  RxBool? DistrictComplete = false.obs;
  RxBool? regisInfoComplete = false.obs;
  RxBool? regisInfoAlert = false.obs;

  TextEditingController provinceController = TextEditingController();
  TextEditingController districtController = TextEditingController();

  RxString? selectedProvince = RxString('');

  RxString? selectedDistrict = RxString('');
  RxBool? isProvice = false.obs;
  RxBool? isDistrict = false.obs;
  RxBool? isPhoneValid = false.obs;
  RxBool? isScanIdCard = false.obs;

  RxString refCode = 'xxccxcxc'.obs;

  Rx<TextEditingController> phone_regis = TextEditingController().obs;
  Rx<TextEditingController> referalCode = TextEditingController().obs;
  Rx<TextEditingController> first_name = TextEditingController().obs;
  Rx<TextEditingController> last_name = TextEditingController().obs;
  FocusNode textFieldFocus = FocusNode();
  FocusNode textFieldFNameFocus = FocusNode();
  FocusNode textFieldlLNameFocus = FocusNode();

  final RxList<Subscription> subscriptions = RxList<Subscription>([]);
  Rx<Subscription> registerData = Subscription().obs;

  GetStorage storage = GetStorage();

  RxString phone_code = ''.obs;

  RxBool? isOldUser = false.obs;

  RxString phone_chkFormat = ''.obs;

  int idCity = 0;
  @override
  void onInit() {
    super.onInit();
    // isOldUser!.value = false;
    // checkFormatedPhone(phone_regis.value.text);
    // setInitPhoneCode();
  }

  void setInitPhoneCode() {
    if (appConfigService.countryConfigCollection == 'aam') {
      setPhoneCode("+66");
    } else if (appConfigService.countryConfigCollection == 'rplc') {
      setPhoneCode("+856");
    } else if (appConfigService.countryConfigCollection == 'rafco') {
      setPhoneCode("+855");
    }
    update();
  }

  Future<void> checkFormatedPhone(value) async {
    var phone = value;
    if (phone.toString().contains('-')) {
      phone = phone.toString().replaceAll('-', '');
    }
    phone_regis.value.text = phone.toString();
    isPhoneValid!.value = true;
    registerData.value.phoneNumber =
        phone.toString(); //TODO set phone to model data
    update();
    var phone_firebase = '';

    if (phone_regis.value.text.startsWith('0')) {
      phone_firebase = '$phone_code${phone_regis.value.text.substring(1)}';
    } else {
      phone_firebase = '$phone_code${phone_regis.value.text}';
    }
    debugPrint('Phone firebase : $phone_firebase');

    if (phone_chkFormat.value != phone_firebase) {
      isOldUser!.value = false;
      update();
    }
    //TODO check ว่าเป็น user เก่าหรือไม่
    await checkloginWithPhoneOldUser(phone_firebase);
  }

  Future<void> checkloginWithPhoneOldUser(String phone_firebase) async {
    try {
      Map data = {
        "phone_firebase": phone_firebase,
      };
      AppLoading.loadingVerify(Get.context!);
      final response =
      await HttpService.callAPIjwt("POST", Endpoints.loginWithPhone, data);
      AppLoading.Loaderhide(Get.context!);
      // debugPrint("response : $response");

      if (response['result']['statusCode'] == 200) {
        if (phone_firebase == response['result']['phone_firebase']) {
          isOldUser!.value = true;
          update();
        } else {
          isOldUser!.value = false;
          update();
        }
      } else {
        print('new user');
      }
      update();
    } catch (e) {
      print(e);
    }
  }

  //TODO set ข้อมูล และส่งข้อมูลไปหน้า login
  void setPhoneDataLoginOldUser(context) {
    Get.put(LoginController())
        .setPhone(phone_code.value, phone_regis.value.text);
    Get.put(LoginController()).checkOTPConfig(context, phone_regis.value.text);
  }

  Future<void> checkReferalCode(context, refcode) async {
    try {
      // print('Check referal code : ${referalCode.value.text}');
      FocusScope.of(context).requestFocus(FocusNode()); //
      //TODO check referal code ....

      referalLoading!.value = true;
      update();

      var phone_firebase = '$phone_code${phone_regis.value.text.substring(1)}';

      Map data = {"phone_firebase": phone_firebase, "ref_code": refcode};
      debugPrint("data : $data");

      final response =
      await HttpService.callAPIjwt("POST", Endpoints.checkRefCode, data);
      debugPrint("response : $response");

      referalLoading!.value = false;
      update();

      if (response['status'] == 200) {
        referalComplete!.value = true;
        referalAlert!.value = false;
        print('Referal code is correct');
        referalCode.value.text = response['result']['ref_code'];
        registerData.value.referrerCode = response['result']
        ['ref_code']; //TODO set referal code to model data
        update();
      } else {
        //TODO รหัส referal code ไม่ถูกต้อง
        referalComplete!.value = false;
        referalAlert!.value = true;
      }

      // referalComplete!.value = true; //TODO set referal code for test
      // update();
      //
      // if (referalComplete!.value) {
      //   print('Referal code is correct');
      //   registerData.value.referrerCode =
      //       referalCode.value.text; //TODO set referal code to model data
      // } else {
      //   //TODO รหัส referal code ไม่ถูกต้อง
      //   referalComplete!.value = false;
      //   referalAlert!.value = true;
      // }
      // update();
    } catch (error) {
      print(error);
      referalComplete!.value = false;
      referalAlert!.value = false;
      update();
    }
  }

  void setReferalCode(value) {
    referalComplete!.value = value;
    update();
  }

  //TODO set ข้อมูลจาก API OCR
  Future<void> setPersonalDataOCR(
      String idCard,
      String firstName,
      String lastName,
      String address,
      String subDistrict,
      String district,
      String province,
      String birthday) async {
    try {
      first_name.value.text = firstName;
      last_name.value.text = lastName;
      selectedProvince!.value = province;
      selectedDistrict!.value = district;
      registerData.value.idCard = idCard;
      registerData.value.address = address;
      registerData.value.subDistrict = subDistrict;
      registerData.value.birthday = birthday;
      isScanIdCard!.value = true;
      nameComplete!.value = true;
      lastNameComplete!.value = true;
      ProvinceComplete!.value = true;
      DistrictComplete!.value = true;
      update();
    } catch (e) {
      debugPrint('Error : $e');
    }
  }

  void checkName() {
    try {
      if (first_name.value.text.isNotEmpty && last_name.value.text.isNotEmpty) {
        nameComplete!.value = true;
        lastNameComplete!.value = true;
        registerData.value.firstName =
            first_name.value.text; //TODO set first name to model data
        registerData.value.lastName =
            last_name.value.text; //TODO set last name to model data
        setNextage();
      } else {
        nameComplete!.value = false;
        lastNameComplete!.value = false;
      }
      update();
    } catch (e) {
      debugPrint('Error : $e');
    }
  }

  void checkAddressData(context) async{
    try {
      print(selectedProvince!.value);
      print(selectedDistrict!.value);
      if (selectedProvince!.value.isNotEmpty &&
          selectedDistrict!.value.isNotEmpty) {
        // final regisAddress = Get.find<RegisterAddressController>();
        ProvinceComplete!.value = true;
        DistrictComplete!.value = true;
        registerData.value.province =
            selectedProvince!.value; //TODO set province to model data
        registerData.value.district =
            selectedDistrict!.value; //TODO set district to model data
        // registerData.value.postalCode =
        //     regisAddress.districtsData[0].districtZipcode.toString(); //comment the code
        regisInfoComplete!.value = true;
        update();
        // print("######");
        // print(registerData.value.toJson());
        await completeRegisInfoPage();
      } else {
        ProvinceComplete!.value = false;
        DistrictComplete!.value = false;
        update();
      }
    } catch (e) {
      debugPrint('Error : $e');
    }
  }

  void setIndexPage(int index) {
    indexPage = index.obs;
    update();
  }

  void setName(value) {
    nameComplete!.value = value;
    update();
  }

  void setLastName(value) {
    lastNameComplete!.value = value;
    update();
  }

  //TODO : check รหัสแนะนำ
  void completeReferalPage() {
    if (referalComplete!.value == true) {
      setNextage();
    } else {
      referalAlert!.value = true;
      update();
    }
  }

  void skipReferalPage() {
    if (indexPage!.value == 0) {
      referalCode.value.text = '';
      registerData.value.referrerCode = '';
      update();
      setNextage();
    }
  }

  void setDropdown(context, String type, int value) {
    print('type : $type');
    if (type == accountAddressProvince.tr) {
      final regisAddress = Get.find<RegisterAddressController>();
      print('city code : ${regisAddress.citiesData[value].cityId}');
      selectedProvince!.value =
          regisAddress.citiesData[value].cityNameLocal.toString();
      regisAddress.setSelectedCityCode(regisAddress.citiesData[value].cityId!);
      ProvinceComplete!.value = true;
      update();
    } else {
      final regisAddress = Get.find<RegisterAddressController>();
      selectedDistrict!.value =
          regisAddress.districtsData[value].districtNameLocal.toString();
      DistrictComplete!.value = true;
      update();
    }
    update();
    Navigator.pop(context);
  }

  Future<void> completeRegisInfoPage() async{
    debugPrint("datacust completeRegisInfoPage :\n ${registerData.value.toJson()}");


    if (regisInfoComplete!.value == true) {
      // print(registerData.value.toJson());
      // var regisVerify;
      // if (Get.isRegistered<RegisterVerifyController>()) {
      //   regisVerify = Get.find<RegisterVerifyController>();
      // } else {
      //   regisVerify = Get.put(RegisterVerifyController());
      // }

      // print(phone_code.value);
      registerData.value.phone_code = phone_code.value;
      update();
      Get.find<RegisterVerifyController>().checkOTPConfig(
           registerData.value.phoneNumber.toString(),phone_code.value,registerData.value);
    } else {
      regisInfoAlert!.value = true;
      update();
    }
  }

  void setNextage() {
    if (indexPage!.value != 2) {
      indexPage!.value = indexPage!.value + 1;
      update();
    }
  }

  void setScanIdcard(bool value) {
    isScanIdCard!.value = value;
    update();
    Get.to(const RegisterInfo());
  }

  void setScanIdcard2(bool value) {
    isScanIdCard!.value = value;
    update();
    Get.back();
    // Get.to(const RegisterInfo());
  }

  void skipScanIdCard() {
    isScanIdCard!.value = true;
    update();
  }

  Future<dynamic> registerUserData(register_data) async {
    try {
      // //TODO กำหนดค่าเริ่มต้น activity id Likepoint2.0 สำหรับ POI ของแอป
      // if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
      //   Get.put(WebViewPointController()).setInitPOIData();
      // }
      registerData.value = register_data;
      update();
      AppLoading.loadingVerify(Get.context!);

      var phone_firebase = "";
      if (registerData.value.phoneNumber.toString().startsWith('0')) {
        phone_firebase =
        '${registerData.value.phone_code}${registerData.value.phoneNumber.toString().substring(1)}';
      } else {
        phone_firebase =
        '${registerData.value.phone_code}${registerData.value.phoneNumber.toString()}'; //TODO set phone firebase
      }
      // '$phone_code${registerData.value.phoneNumber.toString().substring(1)}';
      final WebViewPointController webViewPointCtl =
      Get.put(WebViewPointController());
      await webViewPointCtl.getActivityPOI(); //TODO ดึงข้อมูล POI ของแอป
      // print("activityID : ${webViewPointCtl.download_poi.value}");
      // print("merchantID : ${webViewPointCtl.merchantID!.value}");

      Map data = {
        "phone": registerData.value.phoneNumber.toString(),
        "phoneFirebase": phone_firebase,
        "displayName": "",
        "typeConnect": "username",
        "username": "",
        "password": "",
        "firstname": registerData.value.firstName.toString(),
        "lastname": registerData.value.lastName.toString(),
        "refcode": registerData.value.referrerCode.toString(),
        "like_uid": "",
        "birthday": "",
        "email": "",
        "idcard": registerData.value.idCard.toString() ?? "",
        "tumbol": registerData.value.subDistrict.toString(),
        "amphur": registerData.value.district.toString(),
        "province": registerData.value.province.toString(),
        "addressOther": "",
        "zipcode": registerData.value.postalCode.toString(),
        "branch": "",
        "merchantID": (webViewPointCtl.merchantID?.value?.isEmpty ?? true)
            ? '-'
            : webViewPointCtl.merchantID!.value!, //TODO set merchantID POI
        "activityID": (webViewPointCtl.download_poi?.value?.isEmpty ?? true)
            ? '-'
            : webViewPointCtl
            .download_poi!.value! //TODO set activityID POI download
      };
      print("สมัครสมาชิก");
      // debugPrint(data.toString());
      final response =
      await HttpService.callAPIjwt("POST", Endpoints.register, data);
      print("responseRegister");
      // debugPrint(response);
      if (response != false && response['status'] == 200) {
        AppLoading.Loaderhide(Get.context!);
        Get.snackbar("Success", "Registration completed successfully");
        loginWithPhone();
      } else {
        AppLoading.Loaderhide(Get.context!);
        // Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", response.body);
      }
    } catch (exception, stackTrace) {
      AppLoading.Loaderhide(Get.context!);
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        // Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> loginWithPhone() async {
    print("loginWithPhone");
    // print(phone_regis.value.text);
    try {

      if (registerData.value.phoneNumber.toString().isEmpty) {
        debugPrint("phone_login : ${registerData.value.phoneNumber.toString()}");
        return;
      } else {
        print("else");
        var phone = registerData.value.phoneNumber.toString();
        var phoneFirebase = "${registerData.value.phone_code.toString()}${phone.substring(1)}";
        Map data = {
          "phone_firebase": phoneFirebase,
        };
        debugPrint("data : $data");
        final response = await HttpService.callAPIjwt(
            "POST", Endpoints.loginWithPhone, data);
        // debugPrint("response : $response");
        if (response['status'] == 200) {
          storage.write("isGuest", false);
          storage.write("token", response['result']['accessToken']);
          storage.write("phone_firebase", phoneFirebase);
          getDataCust();
        } else {
          debugPrint("Error : ${response['message']}");
        }
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> getDataCust() async {
    try {
      print("getDataCust");

      if (registerData.value.phoneNumber.toString().isEmpty) {
        debugPrint("phone_login : ${registerData.value.phoneNumber.toString()}");
        return;
      } else {
        var phone_firebase =
            '${registerData.value.phone_code.toString()}${registerData.value.phoneNumber.toString().substring(1)}';

        Map data = {
          "phone_firebase": phone_firebase,
        };
        debugPrint("data : $data");

        final response =
        await HttpService.callAPIjwt("POST", Endpoints.getDataCust, data);
        // debugPrint("response : $response");
        if (response['status'].toString() == '200') {
          storage.write("session", true);
          storage.write("user_id", response['result']['running'].toString());

          if (response['result']['phone_firebase'].toString().isEmpty ||
              response['result']['phone_firebase'].toString() == 'null') {
            var phone_firebase =
                '${registerData.value.phone_code.toString()}${registerData.value.phoneNumber.toString().substring(1)}';
            storage.write("phone_firebase", phone_firebase);
          } else {
            storage.write("phone_firebase",
                response['result']['phone_firebase'].toString());
          }
          Get.lazyPut(() => PincodeController().setBackIn(false), fenix: true);
          Get.lazyPut(() => PincodeController().setRegister(true),
              fenix: true); //TODO set data สำหรับหน้า set pincode
          Get.to(PinCodePage());
        } else {
          print("Error : ${response['message']}");
        }
      }
    } catch (e) {
      print(e);
    }
  }

  void setPhoneCode(String phoneCode) {
    phone_code.value = phoneCode;
    update();
    var registerCtl;
    if (Get.isRegistered<RegisterVerifyController>()) {
      registerCtl = Get.find<RegisterVerifyController>();
    } else {
      registerCtl = Get.put(RegisterVerifyController());
    }
    registerCtl.setPhoneCode(phoneCode);
  }

  void setRefCodeData(String refCodeData) {
    print('refCodeData : $refCodeData');
    referalCode.value.text = refCodeData;
    update();
    Navigator.pop(Get.context!, refCodeData);
    // Get.back();
    checkReferalCode(Get.context!, refCodeData);
  }

    void setRefCode(String refCodeData) {
    print('refCodeData : $refCodeData');
    referalCode.value.text = refCodeData;
    update();
    checkReferalCode(Get.context!, refCodeData);
  }
}