import 'dart:convert';

import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/service/AppUrl.dart';
import 'package:AAMG/view/screen/kyc/bookbank_verify.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:tflite_v2/tflite_v2.dart';
// import 'package:tflite_flutter/tflite_flutter.dart';

import '../../view/componance/AppLoading.dart';
import '../../service/endpoint.dart';
import '../service/AppService.dart';

class ScanController extends GetxController {
  // final RegisterController registerCtl = Get.find<RegisterController>();
  final RegisterController registerCtl = Get.put(RegisterController());
  RxBool isCameraInitialized = false.obs;
  var cameraCount = 0;

  late CameraController? cameraController;
  RxBool? isCameraReady = false.obs;
  late List<CameraDescription> cameras;

  var x, y, w, h = 0.0;
  var label = '';

  XFile? imgXFile;
  RxString imgBlob = "".obs;
  RxBool isFlashOn = false.obs;
  RxString imgUrl = "".obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    // initCamera();
    // initializeController();
    // initTFLite();
    setCamera();
  }

  Future<void> initializeController() async {
    print('initializeController');
    final cameras = await availableCameras();
    final camera = cameras.first;
    cameraController = CameraController(camera, ResolutionPreset.medium, enableAudio: false);
    await cameraController!.initialize();
    // cameraController!.setFlashMode(FlashMode.off);
    isCameraReady!.value = true;
    update();

    final backCamera = cameras.firstWhere(
      (camera) => camera.lensDirection == CameraLensDirection.back,
      orElse: () =>
          cameras.first, // Use the first camera if no back camera is found
    );

    // Handle the case where no back camera is found
    if (backCamera == null) {
      // Handle the error, e.g., display a message to the user
      print("No back camera found.");
      return;
    }
  }

  void setCamera() async {
    var cameras = await availableCameras();
    final camera = cameras.first;
    cameraController =
        CameraController(camera, ResolutionPreset.medium, enableAudio: false);
    // var cameras = await availableCameras();
    // Filter for the back camera
    final backCamera = cameras.firstWhere(
      (camera) => camera.lensDirection == CameraLensDirection.back,
      orElse: () =>
          cameras.first, // Use the first camera if no back camera is found
    );

    // Handle the case where no back camera is found
    if (backCamera == null) {
      // Handle the error, e.g., display a message to the user
      print("No back camera found.");
      return;
    }

    await initCamera_(backCamera);
  }

  Future initCamera_(CameraDescription cameraDescription) async {
// create a CameraController
    cameraController = CameraController(
        cameraDescription, ResolutionPreset.medium,
        enableAudio: false);
// Next, initialize the controller. This returns a Future.
    try {
      await cameraController!.initialize().then((value) {
        isCameraReady!.value = true;
        update();
      });
    } on CameraException catch (e) {
      debugPrint("camera error $e");
    }
  }

  initCamera() async {
    if (await Permission.camera.request().isGranted) {
      cameras = await availableCameras();
      cameraController = CameraController(cameras[0], ResolutionPreset.max);
      await cameraController!.initialize().then((image) {
        cameraController!.startImageStream((image) {
          cameraCount++;
          isCameraInitialized.value = true;
          if (isCameraInitialized.value) {
            // objectDetection(image);
          }
        });
        update();
      });
    }
  }

  // initTFLite() async {
  //   await Tflite.loadModel(
  //     model: 'assets/detect_object/model_unquant.tflite',
  //     labels: 'assets/detect_object/labels.txt',
  //     isAsset: true,
  //     numThreads: 1,
  //     useGpuDelegate: false,
  //   );
  // }

  // initTFLite2() async {
  //    TfliteFlutter.loadModel(
  //     model: 'assets/detect_object/model_unquant.tflite',
  //     labels: 'assets/detect_object/labels.txt',
  //     isAsset: true,
  //     numThreads: 1,
  //     useGpuDelegate: false,
  //   );
  // }

  // objectDetection(CameraImage image) async {
  //   var detector = await Tflite.detectObjectOnFrame(
  //     bytesList: image.planes.map((plane) {
  //       print("gddfdfdfdfdfdfdfdf");
  //       print(plane.bytes.length);
  //       return plane.bytes;
  //     }).toList(),
  //     asynch: true,
  //     imageHeight: image.height,
  //     imageWidth: image.width,
  //     imageMean: 127.5,
  //     imageStd: 127.5,
  //     rotation: 90,
  //     numResultsPerClass: 1,
  //     threshold: 0.4,
  //   );
  //
  //   if (detector != null) {
  //     var ourDetector = detector.first;
  //     if (detector.first['confidenceInClass'] * 100 > 45) {
  //       x = ourDetector['rect']['x'];
  //       y = ourDetector['rect']['y'];
  //       w = ourDetector['rect']['w'];
  //       h = ourDetector['rect']['h'];
  //       label = ourDetector['detectedClass'].toString();
  //     }
  //     update();
  //   }
  // }

  Future<void> uploadImage(context) async {
    try {
      var img =
      await AppUploadService.upLoadImgToS3(
          imgXFile,
          "ProfileImages");
      print("imgUrl : $img");
      imgUrl.value = img;
      update();
      getOCRBookbankData(context);
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> getOCRData(context) async {
    try {
      // AppLoader.loader(context);

      String base64Image = base64Encode(await imgXFile!.readAsBytes());

      Map<String, dynamic> uploadData = {
        "name": "MappAAM",
        "folder": "MappAAM/imageOCR/Idcard",
        "image": base64Image,
      };

      final uploadResponse = await HttpService.postAssets(
        Endpoints.uploadS3_Center,
        uploadData,
        HttpService.noKeyRequestHeaders,
      );

      if (uploadResponse['statusCode'] == 200) {
        var imgUrl = uploadResponse["result"]["url"]["Location"].toString();

        Map<String, dynamic> ocrData = {
          "url_image":imgUrl,
              // "https://mapp-app.s3.ap-southeast-1.amazonaws.com/MappAAM/imageOCR/Idcard/MappAAM-48l5Hn6ZLCMqZlBvBpA5.png",
          "type": "idcard"
        };

        print('ocrData => ${ocrData}');

        final ocrResponse = await HttpService.callAPIjwt(
            "POST",
            Endpoints.ocrUrl
            // "https://agilesoftgroup.com/AAMGp3-UAT/scanimage"
            , ocrData);


        // print('ocrResponse => ${HttpService.KeyRequestHeaders}');
        // print('ocrResponse => ${ocrResponse['data'][0]['id']}');
        // print('ocrResponse => ${ocrResponse['data']['id']}');
        print('ocrResponse => ${ocrResponse}');

        if (ocrResponse['status'] == 200) {
          AppLoading.Loaderhide(context);
          // ocrResponse['data']['firstnameTH'];
          await registerCtl.setPersonalDataOCR(
            ocrResponse['result']['id_number'],
            ocrResponse['result']['th_fname'],
            ocrResponse['result']['th_lname'],
            ocrResponse['result']['home_address'],
            ocrResponse['result']['sub_district'],
            ocrResponse['result']['district'],
            ocrResponse['result']['province'],
            ocrResponse['result']['en_dob'],
          );

          Get.back(
            result: {
              "status": true,
              "data": ocrResponse['result'],
              'imageUrl': imgUrl,
            },
          );
        } else {
          AppLoading.Loaderhide(context);
          Get.back(result: {
            "status": false,
          });
          Get.snackbar(
            "Error",
            "OCR Failed",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        AppLoading.Loaderhide(context);
        Get.back(result: {
          "status": false,
        });
      }
    } catch (e) {
      AppLoading.Loaderhide(context);
      Get.back(result: {
        "status": false,
      });
    }
  }

  Future<dynamic> getOCRBookbankData(context) async {
    try {

      // todo : เข้า OCR ธนาคาร...

      Get.to(() => const BookBankVerify(),transition: Transition.rightToLeft,duration: const Duration(milliseconds: 200));

    } catch (e) {
      AppLoading.Loaderhide(context);
      Get.back(result: {
        "status": false,
      });
    }
  }

  Future<void> toggleFlash() async {
    if (cameraController != null && cameraController!.value.isInitialized) {
      try {
        if (isFlashOn.value) {
          await cameraController!.setFlashMode(FlashMode.off);
        } else {
          await cameraController!.setFlashMode(FlashMode.torch);
        }

        isFlashOn.value = !isFlashOn.value;
        update();
      } catch (e) {
        print('Error toggling flash: $e');
      }
    }
  }
}
