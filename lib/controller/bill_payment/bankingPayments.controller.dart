import 'dart:convert';
import 'dart:io';

import 'package:AAMG/controller/bill_payment/billPayment.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:crypto/crypto.dart';
import 'package:open_store/open_store.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../service/AppService.dart';
import '../../service/http_service.dart';
import '../../view/screen/bill_payment/payment_banking.dart';
import '../../view/screen/bill_payment/payment_success.dart';

class BankingPaymentController extends GetxController {
  RxBool countTimer = false.obs;
  RxString appUrl = "".obs;
  RxString amountStr = "".obs;
  RxString txnRef = "".obs;
  RxBool paymentAC = false.obs;
  var callbackRes;
  final ContractListController contractListCtl =
      Get.find<ContractListController>();

  @override
  void onInit() {
    super.onInit();
  }

  Future<void> setInitailContractData() async {
    try {
      final BillPaymentController billPaymentCtl =
          Get.find<BillPaymentController>();
      txnRef.value = contractListCtl
              .contractList[billPaymentCtl.selectedContractIndex.value]
              .ctt_code ??
          contractListCtl
              .contractList[billPaymentCtl.selectedContractIndex.value].name!;
      update();

      apiPostGetLinkPayment();
      listeningEvent();
      // PaymentBankingPopUp.TimeOut(Get.context!);
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> apiPostGetLinkPayment() async {
    var os = await AppService.getOS();

    DateTime now = DateTime.now().toUtc();
    String formattedDate = DateFormat('MM/dd').format(now);
    var keyP = 'rKLaHDzhTCWQJMXG26Q5vLuupkqz4KtA';
    // var keyP = dotenv.env['SECERT_JWT'];
    var key = utf8.encode(keyP!);
    var bytes = utf8.encode(formattedDate);
    var hmacSha256 = new Hmac(sha256, key);
    var digest = hmacSha256.convert(bytes);

    final jwt = JWT({
      "sub": 'alcdaapi_ags',
      // "sub": dotenv.env['R_USER'],
      "iat": DateTime.now().toUtc().millisecondsSinceEpoch,
    });

    var token = jwt.sign(SecretKey('$digest'));

    if (os == "ios") {
      appUrl.value = "rafco://";
    } else {
      appUrl.value = "https://rafco.page.link/start";
    }
    amountStr.value = Get.find<BillPaymentController>()
        .amountController
        .value
        .text
        .replaceAll(RegExp(','), '');
    update();

    Map data = {
      "app_name": "RPTN",
      "payment_amount": amountStr.value,
      "payment_amount_ccy": "USD",
      "payment_purpose": "BuyForGirlFriend",
      "txn_ref": txnRef.value,
      "app_url": appUrl.value
    };
    // print(data);

    final response = await HttpService.apiPostGetLinkACELDA(
        "https://acleda-h2jyl6xooq-de.a.run.app/convert_deeplink", data, token);

    var linkIOS = response["IOS"].toString();
    var linkAndroid = response["ANDROID"].toString();

    if (os == "ios") {
      try {
        if (!await launch(linkIOS)) throw 'Could not launch $linkIOS';
      } catch (e) {
        OpenStore.instance.open(appStoreId: "**********");
      }
    } else if (os == "android") {
      try {
        if (!await launch(linkAndroid)) throw 'Could not launch $linkAndroid';
      } catch (e) {
        const url = "market://details?id=com.domain.acledabankqr";
        if (await canLaunch(url)) {
          await launch(url);
        }
        throw 'Could not launch $linkAndroid';
      }
    } else {
      Get.snackbar('error', 'Device not support');
    }
  }

  //TODO connect mqtt server
  Future<MqttServerClient> listeningEvent() async {
    try {
      // String hosting = dotenv.env['hostingCallback'].toString();
      // String Uname = dotenv.env['usernameAcleda'].toString();
      // String Pass = dotenv.env['passwordAcleda'].toString();
      // String topic = dotenv.env['topicAcleda'].toString();
      String hosting = '**************';
      String Uname = 'agsdocker';
      String Pass = 'Prachakij_01';
      String topic = 'acleda/callback';
      var pong = 0;

      MqttServerClient client = MqttServerClient(hosting, 'flutter_client');
      client.keepAlivePeriod = 60;
      client.logging(on: true);
      client.onConnected = onConnected;
      client.onDisconnected = onDisconnected;
      client.onSubscribed = onSubscribed;

      client.pongCallback = () {
        pong++;
        // print('Ping response client callback invoked total ping $pong times');
        if (pong == 2) {
          if (countTimer.value == false) {
            // Navigator.pop(context);
            client.disconnect();
            Get.back();
          }
        } else if (countTimer.value) {
          client.disconnect();
        }
      };

      final connMessage = MqttConnectMessage()
          .authenticateAs(Uname, Pass)
          .withWillTopic('willtopic')
          .withWillMessage('Will message')
          .startClean()
          .withWillQos(MqttQos.atLeastOnce);
      client.connectionMessage = connMessage;
      // client.subscribe(topic, MqttQos.atMostOnce);
      try {
        await client.connect();
      } catch (e) {
        // print('Exception: $e');
        client.disconnect();
      }
      client.subscribe(topic, MqttQos.atMostOnce);

      /// The client has a change notifier object(see the Observable class) which we then listen to to get
      /// notifications of published updates to each subscribed topic.
      client.updates!.listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
        final recMess = c![0].payload as MqttPublishMessage;
        final pt =
            MqttPublishPayload.bytesToStringAsString(recMess.payload.message);
        // print(
        //     'EXAMPLE::Change notification:: topic is <${c[0].topic}>, payload is <-- $pt -->');

        final decodeCallback = json.decode(pt);

        if (decodeCallback["Txn_ref"] == txnRef) {
          // print(txnRef);
          // print("==========================================================================================================================================================================================================================================================================================================================================");
          // Navigator.pop(context);

          paymentAC.value = true;
          callbackRes = decodeCallback;
          countTimer.value = true;
          update();
          // print(callbackRes["TransactionId"]);
          client.disconnect();
          Get.find<BillPaymentController>().getCurrentTimeTransection();
          //TODO ไปหน้าจ่ายเงินสำเร็จ
          Get.off(PaymentSuccess());
        }
      });
      return client;
    } catch (e) {
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return MqttServerClient('host', 'flutter_client');
    }
  }

  void onConnected() {
    // print('EXAMPLE::Mosquitto client conected...');
  }

  void onDisconnected() {
    // print('EXAMPLE:: OnDisconnected client callback - Client disconnection');
    if (countTimer.value == false) {
      PaymentBankingPopUp.TimeOut(Get.context!);
    }
  }

  void onSubscribed(String topic) {
    // print('Subscribed topic: $topic');
  }

  void setCountTimer(value) {
    countTimer.value = value;
    update();
  }

  void resetData() {
    countTimer.value = false;
    appUrl.value = "";
    amountStr.value = "";
    txnRef.value = "";
    paymentAC.value = false;
    callbackRes = null;
    update();
  }
}
