import 'package:AAMG/controller/sms/send.request.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:sentry/sentry.dart';

import '../../models/otp_model.dart';
import '../../service/http_service.dart';
import '../../view/componance/themes/theme.dart';
import '../../service/endpoint.dart';

class VerificationSMSController extends GetxController {

  RxString? refCode = "".obs;


  void setRefCode(value) {
    refCode!.value = value;
    update();
  }



  Future<dynamic> checkVerifyOTPConfig(context, String phone, String otpCode) async {
    try {
      var response;
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        response =
        await checkOTPVerify(context, phone, otpCode, refCode!.value, 'AAM');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        response =
        await verifyOTP_RPLC(context, phone, otpCode, refCode!.value, 'RPLC');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        response =
        await verifyOTP_Rafco(context, phone, otpCode, refCode!.value, 'RAFCO');
      }
      return response;
    } catch (e) {
      print('$e => error checkVerifyOTPConfig');
    }
  }

  Future<dynamic> checkOTPVerify(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": otpCode,
          "refCode": refCode,
          "fromBU": fromBU,
        });

        final resVerify = await HttpService.callAPICloudflare(
            "POST", "verifyOTP_CF", valueVerify.toJson());
        var responseVerify = ResponseVerifyCode.fromJson(resVerify["result"]);

        var status = resVerify["result"]['statusCode'];
        update();
        print("status : $status");
        return status;
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> verifyOTP_RPLC(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      var phoneParam = "";
      if (checkPhone == "0") {
        phoneParam = Get.find<SendRequestSMSController>().phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = Get.find<SendRequestSMSController>().phone_code!.value + phone;
      }

      final data = {
        "phone": phoneParam,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> verifyOTP_Rafco(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      var phoneParam = "";
      if (checkPhone == "0") {
        phoneParam = Get.find<SendRequestSMSController>().phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = Get.find<SendRequestSMSController>().phone_code!.value + phone;
      }

      final data = {
        "phone": phoneParam,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> switchVerify(data) async {
    var response;
    if (Endpoints.verifyOTPCF == "next") {
      response = await HttpService.post(
          Endpoints.verifyOTP_RPLC, data, HttpService.noKeyRequestHeaders);
    } else {
      response = await HttpService.callAPIsmsService(
          'POST', Endpoints.verifyOTPCF, data);
    }
    return response;
  }


}