import 'dart:async';
import 'dart:io';

import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/sms/send.request.controller.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/pincode/reset_pincode_verify.dart';
import 'package:AAMG/view/screen/register/register_success.dart';
import 'package:flutter/services.dart';

import '../../models/otp_model.dart';
import '../../service/http_service.dart';
import '../../view/componance/themes/theme.dart';
import '../../service/endpoint.dart';
import '../../view/screen/pincode/pincode.dart';
import '../../view/screen/pincode/reset_pincode.dart';
import '../sms/verification.controller.dart';

class PincodeController extends GetxController {
  RxString? passcode = RxString('');
  RxBool? alertPasscode = false.obs;
  RxBool? isBackIn = false.obs;
  RxBool? isResetPin = false.obs;
  RxBool? isResetPinSuccess = false.obs;
  RxBool? isRegister = false.obs;

  RxInt stepPincode = 1.obs;

  RxString refCode = ''.obs;
  RxBool? isVerify = false.obs;
  RxBool? isAlert = false.obs;
  RxInt counter_OTP = 60.obs;
  late Timer timer;
  GetStorage storage = GetStorage();
  RxBool? isAuthenticating = false.obs;
  RxString? authorized = RxString('Not Authorized');
  RxBool? canCheckBiometrics = false.obs;
  RxBool? canChkBiometrics = false.obs;

  Rx<TextEditingController> otpController = TextEditingController().obs;
  final LocalAuthentication auth = LocalAuthentication();
  final AppConfigController appConfigCtl = Get.put(AppConfigController());

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    checkSession();
  }

  void addPincode(text) {
    if (text != "delete" || text != "biometric") {
      if (passcode!.value.length < 6) {
        passcode!.value = passcode!.value + text;
        update();
      } else if (passcode!.value.length == 6) {
        passcode!.value = passcode!.value;
        update();
      }
    }
    if (passcode!.value.length == 6) {
      //TODO: check passcode
      checkPassCode();
    }
    print('passcode: ${passcode!.value}');
  }

  void deletePincode() {
    if (passcode!.value.length > 0) {
      passcode!.value =
          passcode!.value.substring(0, passcode!.value.length - 1);
      update();
    }
    print('passcode delete: ${passcode!.value}');
  }

  Future<dynamic> checkSession() async {
    try {
      // final ProfileController profileController = Get.put(ProfileController());
      var chk = await GetStorage().read('session');
      print('session: $chk');
      if (chk == true) {
        // isBackIn!.value = true;
        var pass_code = await GetStorage().read('passcode');
        if (pass_code != null) {
          print('passcode: $pass_code');
          // passcode!.value = pass_code;
          stepPincode.value = 2;
          isBackIn!.value = true;
          update();
          // todo check biometric
          checkBiometricLogin();
          // profileController.getProfile();
        } else {
          passcode!.value = '';
          stepPincode.value = 1;
          update();
        }
        update();
      } else {
        isBackIn!.value = false;
        passcode!.value = '';
        stepPincode.value = 1;
        update();
      }
    } catch (e) {
      print('error: $e');
    }
  }

  Future<dynamic> checkPassCode() async {
    try {
      debugPrint('stepPincode: ${stepPincode.value}');

      if (stepPincode.value == 1) {
        GetStorage().write('passcode', passcode!.value);
        stepPincode.value = 2;
        passcode!.value = '';
        update();
      } else if (stepPincode.value == 2) {
        var pass_code = await GetStorage().read('passcode');
        if (pass_code == passcode!.value) {
          print('passcode benar');
          GetStorage().write('session', true);
          passcode!.value = '';
          update();
          debugPrint("ยืนยัน pin code ถูกต้อง");
          if (isRegister!.value == true) {
            isRegister!.value = false;
            update();
            Get.offAll(const RegisterSuccess());
          } else if (isResetPin!.value == true) {
            isResetPin!.value = false;
            update();
            Get.offAll(const ResetPinCocdeSuccess());
          } else {
            Get.offAll(HomeNavigator());
          }
        } else {
          print('passcode salah');
          alertPasscode!.value = true;
          passcode!.value = '';
          update();
        }
      }
    } catch (e) {
      print('error: $e');
    }
  }

  Future<dynamic> checkBiometricLogin() async {
    try {
      // var chk = await GetStorage().read('phone_firebase');
      // print('phone_firebase: $chk');
      // print("checkBiometrics #####");
      if (!kIsWeb) {
        debugPrint('stepPincode: ${stepPincode.value}');
        var pinCode_value = await GetStorage().read('passcode');

        if (pinCode_value != '' || pinCode_value != 'null') {
          try {
            canCheckBiometrics?.value =
                await LocalAuthentication().canCheckBiometrics;
            update();
            //TODO อุปกรณ์ไม่รองรับ
            if(canCheckBiometrics!.value == false) {
              appConfigCtl.setBiometrics(false);
            }
          } on PlatformException catch (e) {
            print(e);
            //TODO อุปกรณ์ไม่รองรับ, error
            appConfigCtl.setBiometrics(false);
          }
          if (stepPincode.value == 2 && canCheckBiometrics?.value == true) {
            authenticate();
          }
        }
      }
    } catch (e) {
      print('checkBiometrics -> ${e}');
    }
  }

  Future<void> authenticate() async {
    bool authenticated = false;
    List<BiometricType> availableBiometrics =
        await auth.getAvailableBiometrics();
    try {
      isAuthenticating!.value = true;
      authorized!.value = 'Authenticating';
      update();

      if (Platform.isIOS) {
        if (availableBiometrics.contains(BiometricType.face)) {
          authenticated = await auth.authenticate(
              localizedReason: 'Enable Face ID to sign in',
              options: const AuthenticationOptions(
                  useErrorDialogs: true,
                  stickyAuth: true,
                  biometricOnly: true));
        } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
          authenticated = await auth.authenticate(
            localizedReason: "Please authenticate to complete your transaction",
            options: const AuthenticationOptions(
                useErrorDialogs: true, stickyAuth: true, biometricOnly: true),
          );
        }
      } else {
        authenticated = await auth.authenticate(
          localizedReason: "Please authenticate to complete your transaction",
          options: const AuthenticationOptions(
              biometricOnly: true, useErrorDialogs: true, stickyAuth: true),
        );
      }

      isAuthenticating!.value = false;
      authorized!.value = 'Authenticating';
      update();
    } on PlatformException catch (e) {
      print('authenticate -> ${e}');
    }

    print('authenticated: $authenticated');
    final String message = authenticated ? 'Authorized' : 'Not Authorized';
    authorized!.value = message;
    update();
    Biometrics();
  }

  Future<void> Biometrics() async {
    print('Biometrics func');
    if (stepPincode.value == 2) {
      if (authorized?.value == "Authorized") {
        print('Authorized');
        // await secureStorage.writeSecureData('statuspageupdate', '');
        GetStorage().write('session', true);
        passcode!.value = '';
        update();
        appConfigCtl.setBiometrics(true);

        /// ถูกต้อง !! ไปหน้า Home
        Get.offAll(HomeNavigator());
      }
    }
  }

  Future<bool> checkBiometric(context) async {
    print('checkBiometric');
    final isDeviceSupported = await _isDeviceSupported();
    if (!isDeviceSupported) {
      return false; // Device doesn't support biometric authentication
    }

    final isBiometricAvailable = await _isBiometricAvailable();
    if (!isBiometricAvailable) {
      // Biometric authentication is not set up on the device
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Biometric Authentication'),
            content: const Text(
                'Biometric authentication is not set up on your device. Please go to Settings and enable it.'),
            actions: <Widget>[
              TextButton(
                child: const Text('OK'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
      return false;
    }

    return true; // Device supports biometric authentication and is available
  }

  Future<bool> _isDeviceSupported() async {
    try {
      final isSupported = await LocalAuthentication().isDeviceSupported();
      // hasDeviceAuthenticator;
      print('isSupported: $isSupported');
      return isSupported;
    } on PlatformException catch (e) {
      if (e.code == 'unimplemented') {
        return false; // Device doesn't support biometric authentication
      } else {
        rethrow; // Rethrow the exception for further handling
      }
    }
  }

  Future<bool> _isBiometricAvailable() async {
    try {
      final LocalAuthentication auth = LocalAuthentication();
      bool isAvailable = await auth.canCheckBiometrics;
      // final isAvailable = await LocalAuthentication().canCheckBiometrics;
      // isBiometricAvailable;
      print('isAvailable: $isAvailable');
      return isAvailable;
    } on PlatformException catch (e) {
      if (e.code == 'unimplemented') {
        return false; // Biometric authentication is not available
      } else {
        rethrow; // Rethrow the exception for further handling
      }
    }
  }

  void resetPin() {
    isResetPin!.value = true;
    update();
    print('resetPin');
  }

  Future<dynamic> sendOtpResetPin(context) async {
    try {
      final SendRequestSMSController smsController =
          Get.put(SendRequestSMSController());

      var phone_code = "";
      var phone = storage.read('phone_firebase');
      if (phone.startsWith('+66')) {
        phone = phone.replaceFirst('+66', '0');
        phone_code = "+66";
      } else if (phone.startsWith('+856')) {
        // แทนที่ +856 ด้วยรหัสที่คุณต้องการ เช่น ถ้าต้องการใช้ '0056'
        phone = phone.replaceFirst('+856', '0');
        phone_code = "+856";
      } else if (phone.startsWith('+855')) {
        // แทนที่ +855 ด้วยรหัสที่คุณต้องการ เช่น ถ้าต้องการใช้ '0055'
        phone = phone.replaceFirst('+855', '0');
        phone_code = "+855";
      }
      print(Get.find<ProfileController>().profile.value.phone.toString());
      print("#####");
      print(phone);
      //TODO call api send otp
      smsController.setVerificationPage(VerifyResetPincode());
      if(appConfigService.countryConfigCollection.toString() != "aam") {
         smsController.setPhoneCode(phone_code);
      }
      await smsController.checkOTPConfig(context, phone);
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> resendOtp(context) async {
    //TODO resend otp

    try {
      final SendRequestSMSController smsController =
          Get.put(SendRequestSMSController());
      var phone = storage.read('phone_firebase');
      if (phone.startsWith('+66')) {
        phone = phone.replaceFirst('+66', '0');
      }
      otpController.value.text = '';
      update();
      //TODO call api
      await smsController.checkResendOTPConfig(
          context,
          phone);
      // resetOTP();
    } catch (e) {
      print(e);
    }
  }

  void setVerify(value) {
    isVerify!.value = value;
    update();
  }

  void completeVerify() {
    if (isVerify!.value == true) {
      Get.toNamed('/home');
    } else {
      isAlert!.value = true;
      update();
    }
  }

  void checkOtpFormat(context, value) async {
    if (otpController.value.text.length == 6) {
      setVerify(true);
      // TODO verify otp then if true
      var phone = storage.read('phone_firebase');
      if (phone.startsWith('+66')) {
        phone = phone.replaceFirst('+66', '0');
      }
      var chkOtp = await Get.find<VerificationSMSController>()
          .checkVerifyOTPConfig(context, phone, otpController.value.text);
      print('chkOtp : ${chkOtp.toString()}');
      if (chkOtp.toString() == '200') {
        // TODO verify otp success
        isBackIn!.value = false;
        passcode!.value = '';
        stepPincode.value = 1;
        isResetPinSuccess!.value = true;
        update();
        Get.to(PinCodePage());
      } else {
        isAlert!.value = true;
        setVerify(false);
        update();
      }
    } else {
      setVerify(false);
      isAlert!.value = true;
      update();
    }
  }

  //TODO check otp format for Login
  // void checkOtpFormat(context, value, phone) async {
  //   if (otpController.value.text.length == 6) {
  //     setVerify(true);
  //     // TODO verify otp then if true
  //     var chkOtp = await checkVerifyOTPConfig(
  //         context, phone, otpController.value.text, refCode.value, '');
  //     print('chkOtp : ${chkOtp.toString()}');
  //     if (chkOtp.toString() == '200') {
  //       //TODO call api
  //       //reset pin
  //       isBackIn!.value = true;
  //       passcode!.value = '';
  //       stepPincode.value = 1;
  //       update();
  //       Get.to(PinCodePage());
  //     } else {
  //       isAlert!.value = true;
  //       setVerify(false);
  //       update();
  //     }
  //   } else {
  //     // isAlert!.value = true;
  //     setVerify(false);
  //     update();
  //   }
  // }

  void setDefalttheme() {
    otpController.value.text;
    update();
  }

  void startOTPTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (counter_OTP.value > 0) {
        counter_OTP.value--;
        update();
      } else {
        timer.cancel();
      }
    });
    print('start timer');
    print(counter_OTP.value);
  }

  Future<dynamic> checkVerifyOTPConfig(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      var response;
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        response =
            await checkOTPVerify(context, phone, otpCode, refCode, 'AAM');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        response =
            await verifyOTP_RPLC(context, phone, otpCode, refCode, 'RPLC');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        response =
            await verifyOTP_Rafco(context, phone, otpCode, refCode, 'RAFCO');
      }
      return response;
    } catch (e) {
      print('$e => error checkVerifyOTPConfig');
    }
  }

  Future<dynamic> checkOTPVerify(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": otpCode,
          "refCode": refCode,
          "fromBU": fromBU,
        });

        final resVerify = await HttpService.callAPICloudflare(
            "POST", "verifyOTP_CF", valueVerify.toJson());
        var responseVerify = ResponseVerifyCode.fromJson(resVerify["result"]);

        var status = resVerify["result"]['statusCode'];
        update();
        print("status : $status");
        return status;
      }
    } catch (e) {
      print('$e => error checkVerify');
    }
  }

  Future<dynamic> verifyOTP_RPLC(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      final data = {
        "phone": phone,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> verifyOTP_Rafco(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      final data = {
        "phone": phone,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> switchVerify(data) async {
    var response;
    if (Endpoints.verifyOTPCF == "next") {
      response = await HttpService.post(
          Endpoints.verifyOTP_RPLC, data, HttpService.noKeyRequestHeaders);
    } else {
      response = await HttpService.callAPIsmsService(
          'POST', Endpoints.verifyOTPCF, data);
    }
    return response;
  }

  void setBackIn(value) {
    isBackIn!.value = value;
    stepPincode.value = 1;
    update();
  }

  void setRegister(value) {
    isRegister!.value = value;
    stepPincode.value = 1;
    update();
  }
}
