import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get_storage/get_storage.dart';

import '../../view/screen/pincode/pincode.dart';
import '../home/<USER>';
import '../pincode/pincode.controller.dart';

class FaceBookController extends GetxController {
  RxString fb_uid = ''.obs;
  RxString fb_name = ''.obs;
  RxString fb_email = ''.obs;
  RxString fb_img = ''.obs;
  RxString fb_accessToken = ''.obs;

  GetStorage storage = GetStorage();

  Future<bool> checkFacebookUsers(String uid) async {
    try {
      if (uid == null || uid == '') {
        return false;
      } else {
        var hasSession =  await storage.read('session');
        Map data = {
          'uid': uid,
        };
        final response = await HttpService.callAPIjwt(
            'POST', Endpoints.checkFaceBookUsers, data);

        print('checkFacebookUsers : ${response}');
        if (response != false && response['status'] == 200) {
          if(hasSession == true){
            //TODO facebook id นี้ถูกใช้กับบัญชีอื่นแล้ว
            Get.snackbar("Connect Failed", 'Another Account is Already connected with Facebook ID');
            return false;
          }
          return response['result']['isRegister'] ?? true;
        } else {
          return false;
        }
      }
    } catch (e) {
      print(e);
      return false;
    }
  }

  //TODO set facebook user data
  Future<void> setUserData(uid, name, email, fbImg, fbAccessToken) async {
    try {
      storage.write('userID', "FB-${uid}");
      storage.write('fb_uid', uid);
      storage.write('fb_name', name);
      storage.write('fb_email', email);
      storage.write('fb_img', fbImg);
      storage.write('fb_accessToken', fbAccessToken);

      fb_uid.value = uid;
      fb_name.value = name;
      fb_email.value = email;
      fb_img.value = fbImg;
      fb_accessToken.value = fbAccessToken;
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<bool> updateFacebookUsers(
      uid, name, email, fbImg, fbAccessToken) async {
    try {
      final ProfileController profileCtl = Get.find<ProfileController>();
      Map data = {
        "uid": uid,
        "phone_firebase": profileCtl.profile.value.phoneFirebase,
        "account_name": name,
        "account_img": fbImg,
        "account_email": email
      };

      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.updateFaceBookUsers, data);

      if (response != false && response['status'] == 200) {
        print('updateFaceBookUsers : ${response}');
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }

  Future<dynamic> loginWithFacebook(context) async {
    try {
      final HomeController homeController = Get.find<HomeController>();

      Map data = {
        "uid": fb_uid.value,
      };
      print('loginWithFacebook : ${data}');
      print('loginWithFacebook : ${Endpoints.loginWithFacebook}');
      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.loginWithFacebook, data);

      print('loginWithFacebook : ${response}');
      if (response['status'].toString() == '200') {
        storage.write("isGuest", false);
        storage.write("session", true);
        storage.write("user_id", response['result']['running'].toString());
        storage.write(
            "phone_firebase", response['result']['phone_firebase'].toString());
        homeController.isShowTutorial!.value = true;
        homeController.update();
        AppLoading.Loaderhide(context);
        Get.lazyPut(() => PincodeController().setBackIn(false), fenix: true);
        Get.to(PinCodePage());
      } else {
        AppLoading.Loaderhide(context);
        Get.snackbar("Facebook Login failed", 'Please try again');
      }
    } catch (e) {
      print("catch : ${e}");
      print(e);
      AppLoading.Loaderhide(context);
      Get.snackbar("Facebook Login failed", 'Please try again');
    }
  }

  // @Override
  // public void onCreate() {
  //   super.onCreate();
  //   FacebookSdk.sdkInitialize(getApplicationContext());
  //   AppEventsLogger.activateApp(this);
  // }

  // Future<void> loginWithFacebook() async {
  //   try {
  //     print("facebook function ");
  //     // print(await FacebookAuth.instance);
  //     // LoginResult? _result = await FacebookAuth.instance
  //     //     .login(loginBehavior: LoginBehavior.nativeWithFallback);
  //
  //     // print(_result);
  //     LoginResult? result = await FacebookAuth.instance.login(loginBehavior: LoginBehavior.nativeWithFallback);
  //     print("facebook function 1");
  //     print(result);
  //     // LoginResult? result = await FacebookAuth.instance.login(loginBehavior: LoginBehavior.nativeWithFallback);
  //     final userData = await FacebookAuth.instance.getUserData();
  //     final accessToken = await FacebookAuth.instance.accessToken;
  //     print('Access Token: ${accessToken}');
  //     print('User Data: $userData');
  //     print("facebook function 2");
  //      print(result);
  //     if (result.status == LoginStatus.success) {
  //       final userData = await FacebookAuth.instance.getUserData();
  //       print('User Data: $userData');
  //     } else {
  //       print('Login failed: ${result.message}');
  //     }
  //   } catch (e) {
  //     print('Error: $e');
  //   }
  // }
  //
  // Future<void> facebookLogin() async {
  //   try {
  //     // เริ่มกระบวนการล็อกอิน
  //     final LoginResult result = await FacebookAuth.instance.login(
  //       permissions: ['email', 'public_profile'], // ระบุ Permissions ที่ต้องการ
  //     );
  //
  //     print(result);
  //
  //     if (result.status == LoginStatus.success) {
  //       // รับ Access Token
  //       final AccessToken? accessToken = result.accessToken;
  //       print('Access Token: ${accessToken}');
  //
  //       // ใช้ Access Token เพื่อดึงข้อมูลผู้ใช้
  //       final userData = await FacebookAuth.instance.getUserData();
  //       print('User Data: $userData');
  //     } else {
  //       // ตรวจสอบข้อผิดพลาด
  //       print('Login failed: ${result.message}');
  //     }
  //   } catch (e) {
  //     print('Error during Facebook login: $e');
  //   }
  // }
  //
  //
  // Future<void> loginWithFacebook_v2() async {
  //   try {
  //     final LoginResult result = await FacebookAuth.instance.login(
  //       loginBehavior: LoginBehavior.nativeWithFallback, // ใช้ Native Login หรือ fallback ไป Web Login
  //       // permissions: ['email', 'public_profile'], // ขอ permissions ที่ต้องการ
  //     );
  //
  //     if (result.status == LoginStatus.success) {
  //       final accessToken = result.accessToken;
  //       final userData = await FacebookAuth.instance.getUserData(
  //         fields: "name,email,picture",
  //       );
  //       print('User Data: $userData');
  //     } else if (result.status == LoginStatus.cancelled) {
  //       print('User cancelled the login process.');
  //     } else {
  //       print('Login failed: ${result.message}');
  //     }
  //   } catch (e) {
  //     print('Error during Facebook login: $e');
  //   }
  // }
}
