import 'package:AAMG/controller/social_login/sign_in_state.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/services.dart';

import 'auth_result.dart';

abstract class BaseAuthProvider {
  Future<AuthResult> signIn(context);
  Future<AuthResult> signUp(context);

  Future<AuthResult> signOut() async {
    await FirebaseAuth.instance.signOut();

    return AuthResult(
      isSuccess: true,
      signInState: SignInState.SIGNED_OUT,
    );
  }

  bool isErrorRequireRegistration(PlatformException err) {
    if (err.code == "157") {
      return true;
    } else {
      return false;
    }
  }
}
