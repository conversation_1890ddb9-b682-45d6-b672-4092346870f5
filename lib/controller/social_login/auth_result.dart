import 'package:AAMG/controller/social_login/sign_in_state.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/services.dart';

class AuthResult {
  late bool isSuccess = false;
  late bool isRequireRegistration = false;
  late bool isCancel = false;

  var revokeUser = false;

  String? tokenId;
  String? resultCode;
  String? username;

  String? error;

  UserCredential? credential;
  User? userCredential;

  late SignInState signInState;

  dynamic data;

  late String? loginWith;

  AuthResult(
      {this.isSuccess = false,
      this.resultCode,
      this.signInState = SignInState.UNKNOWN,
      this.isRequireRegistration = false,
      this.error,
      this.data,
      this.tokenId,
      this.isCancel = false,
      this.credential,
        this.loginWith,
      });

  AuthResult.setSignInState(
    this.signInState, {
    this.isSuccess = false,
    this.resultCode,
        this.loginWith,
  });

  AuthResult.requireRequireRegistration(
    String? idToken,
    PlatformException err,
  ) {
    isSuccess = false;
    isRequireRegistration = true;
    error = err.message ?? '';
    data = err.details;
    tokenId = idToken;
    loginWith;
  }
}
