import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:google_sign_in/google_sign_in.dart';

class GoogleController extends GetxController {
  Future<User?> signInWithGoogle() async {
    try {
      // เริ่ม Google Sign-In
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

      print(googleUser);

      if (googleUser == null) {
        // ผู้ใช้ยกเลิกการเข้าสู่ระบบ
        debugPrint('Google Sign-In ถูกยกเลิก');
        return null;
      }

      debugPrint('Google Sign-In สำเร็จ: ${googleUser.displayName}');

      // รับ Credential จาก Google
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        debugPrint('Google Authentication Token ไม่สมบูรณ์');
        return null;
      }

      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // ล็อกอินกับ Firebase
      final UserCredential userCredential =
      await FirebaseAuth.instance.signInWithCredential(credential);

      final User? user = userCredential.user;
      if (user != null) {
        debugPrint('ล็อกอินสำเร็จ: ${user.displayName} (${user.email})');
        return user;
      } else {
        debugPrint('Firebase Authentication ไม่สำเร็จ');
        return null;
      }
    } catch (e) {
      debugPrint('Google Sign-In Error: $e');
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
      await GoogleSignIn().signOut();
      debugPrint('ออกจากระบบสำเร็จ');
    } catch (e) {
      debugPrint('Google Sign-Out Error: $e');
    }
  }
}