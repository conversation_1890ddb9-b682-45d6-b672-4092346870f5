enum SignInState {
  SIGNED_IN,
  SIGNED_OUT,
  SESSION_EXPIRED,
  SESSION_INVALID,
  NEED_VERIFY_OTP,
  NEED_REGISTER_PHONE,
  UNKNOWN
}

enum SignInStateAndroid {
  SIGNED_IN,
  SIGNED_OUT,
  SESSION_EXPIRED,
  SESSION_INVALID,
  NEED_VERIFY_OTP,
  NEED_REGISTER_PHONE,
  UNKNOWN
}

enum SignInStateiOS {
  SignedIn,
  SignedOut,
  SessionExpired,
  SessionInvalid,
  needVerifyOtp,
  needRegisterPhone,
  Unknown
}

class SignInStateUtil {
  SignInState fromString(String state) {
    SignInState result = SignInState.UNKNOWN;

    switch (state) {
      case "SIGNED_IN":
        result = SignInState.SIGNED_IN;

        break;
      case "SIGNED_OUT":
        result = SignInState.SIGNED_OUT;
        // signOut();
        break;
      case "SESSION_EXPIRED":
        result = SignInState.SESSION_EXPIRED;
        // signOut();
        break;
      case "SESSION_INVALID":
        result = SignInState.SESSION_INVALID;
        // signOut();
        break;
      case "NEED_VERIFY_OTP":
        result = SignInState.NEED_VERIFY_OTP;
        break;
      case "NEED_REGISTER_PHONE":
        result = SignInState.NEED_REGISTER_PHONE;
        break;
      case "SignedIn":
        result = SignInState.SIGNED_IN;

        break;
      case "SignedOut":
        result = SignInState.SIGNED_OUT;
        // signOut();
        break;
      case "SessionExpired":
        result = SignInState.SESSION_EXPIRED;
        // signOut();
        break;
      case "SessionInvalid":
        result = SignInState.SESSION_INVALID;
        // signOut();
        break;
      case "needVerifyOtp":
        result = SignInState.NEED_VERIFY_OTP;
        break;
      case "needRegisterPhone":
        result = SignInState.NEED_REGISTER_PHONE;
        break;
      default:
        // signOut();
        result = SignInState.UNKNOWN;
        break;
    }

    return result;
  }
}