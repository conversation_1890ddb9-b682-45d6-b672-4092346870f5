import 'package:AAMG/controller/config/appConnect.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/social_login/facebook.controller.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';

import '../../view/componance/AppLoading.dart';
import 'auth_result.dart';
import 'base_auth.dart';
import 'sign_in_state.dart';


class FacebookAuthentication extends BaseAuthProvider {
  static String? _idToken, _uid, _name, _email, _fbImg, _fbAccessToken;
  static User? userCredential;
  static AccessToken? accessToken;
  static OAuthCredential? credential;
  static final FaceBookController _faceBookController = Get.put(FaceBookController());
  static bool isSignin = false;

  //TODO ล็อกอินด้วย facebook
  @override
  Future<AuthResult> signIn(context) async {
    final AuthResult result = AuthResult();
    AppLoading.loadingVerify(context);
    try {
      debugPrint("Starting Facebook sign-in");
      isSignin = true;
      final facebookResult = await signInWithFacebook();
      if (!facebookResult.isSuccess) {
        _handleLoginFailure(context, result, "Facebook login failed");
        return result;
      }
      debugPrint('Facebook login successful');
      final firebaseAuthResult = await signInWithFirebase();
      if (!firebaseAuthResult) {
        _handleLoginFailure(
          context,
          result,
          "User registration required",
          true,
        );
        return result;
      }

      result
        ..isSuccess = true
        ..signInState = SignInState.SIGNED_IN
        ..loginWith = 'facebook';
      _faceBookController.loginWithFacebook(context); //TODO ล็อกอินด้วย facebook
    } catch (err, stack) {
      _handlePlatformException(err, result, context, stack);
    }
    AppLoading.Loaderhide(context);
    return result;
  }

  @override
  Future<AuthResult> signUp(context) async {
    final AuthResult result = AuthResult();
    try {
      AppLoading.loadingVerify(context);
      debugPrint("Starting Facebook connection");
      final facebookResult = await connectWithFacebook();
      if (!facebookResult.isSuccess) {
        result.isRequireRegistration = true;
        AppLoading.Loaderhide(context);
        return result;
      }

      debugPrint('Facebook connection successful');
      final firebaseAuthResult = await signInWithFirebase();
      if (!firebaseAuthResult) {
        result
          ..isSuccess = false
          ..isRequireRegistration = true
          ..data = "User registration required";
        AppLoading.Loaderhide(context);
        return result;
      }

      result
        ..isSuccess = true
        ..signInState = SignInState.SIGNED_IN
        ..loginWith = 'facebook';
      Get.snackbar("Connect Success", 'You have connected with Facebook');
      await Get.find<ProfileController>().getProfile();
      Get.find<AppConnectController>().checkFacebook();
    } catch (err, stack) {
      _handlePlatformException(err, result, context, stack);
    }
    AppLoading.Loaderhide(context);
    return result;
  }

  Future<AuthResult> signInWithFacebook() async {
    final AuthResult result = AuthResult();
    try {
      // TODO เช็ค Facebook UID ว่ามีหรือไม่ ถ้าไม่มี = ยังไม่เคย connect กับ facebook
      final facebookUid = await promptUserForUid();
      if (facebookUid == null || facebookUid.isEmpty) {
        return _returnFailedResult(result, "Facebook UID is required");
      }
      //TODO check user already register in RDS DB or not
      final isRegistered = await _faceBookController.checkFacebookUsers(facebookUid);
      if (!isRegistered) {
        return _returnFailedResult(result, "User registration required", true);
      }
      // TODO เจอ userId ในระบบ >> ล็อกอิน
      final fbLoginResult = await FacebookAuth.instance
          .login(loginBehavior: LoginBehavior.nativeWithFallback);
      if (fbLoginResult.status == LoginStatus.success) {
        await _handleSuccessfulFacebookLogin(fbLoginResult);
        result.isSuccess = true;
      } else {
        _returnFailedResult(result, "Facebook login failed");
      }
    } catch (e) {
      debugPrint(e.toString());
      result.isSuccess = false;
    }
    return result;
  }

  Future<AuthResult> connectWithFacebook() async {
    final AuthResult result = AuthResult();
    try {
      // TODO เช็ค Facebook UID ว่ามีหรือไม่ ถ้าไม่มี = ยังไม่เคย connect กับ facebook
      final facebookUid = await promptUserForUid();
      // if (facebookUid == null || facebookUid.isEmpty) {
      //   return _returnFailedResult(result, "Facebook UID is required");
      // }
      //TODO check user already register in RDS DB or not
      final isRegistered = await _faceBookController.checkFacebookUsers(facebookUid!);
      if (isRegistered) {
        return _returnFailedResult(result, "Already registration", true);
      }

      // TODO ไม่เจอ userId ในระบบ >> เชื่อมต่อ facebook
      final fbLoginResult = await FacebookAuth.instance
          .login(loginBehavior: LoginBehavior.nativeWithFallback);
      if (fbLoginResult.status == LoginStatus.success) {
        await _handleSuccessfulFacebookLogin(fbLoginResult);
        result.isSuccess = true;
      } else {
        result.isSuccess = false;
      }
    } catch (e) {
      debugPrint(e.toString());
      result.isSuccess = false;
    }
    return result;
  }

  Future<void> _handleSuccessfulFacebookLogin(LoginResult fbLoginResult) async {
    // TODO Retrieve Facebook user data
    accessToken = await FacebookAuth.instance.accessToken;
    // TODO Authenticate with Firebase using Facebook credentials
    credential = FacebookAuthProvider.credential(fbLoginResult.accessToken!.tokenString);
    final userData = await FacebookAuth.instance.getUserData();
    _uid = userData['id'];
    _name = userData['name'];
    _email = userData['email'];
    _fbImg = userData['picture']['data']['url'];
    _fbAccessToken = accessToken!.tokenString;
    //TODO อัปเดตเข้า Get Storage
    _faceBookController.setUserData(
      _uid ?? '',
      _name ?? '',
      _email ?? '',
      _fbImg ?? '',
      _fbAccessToken ?? '',
    );
  }

  Future<bool> signInWithFirebase() async {
    await FirebaseAuth.instance.signInWithCredential(credential!);
    userCredential = FirebaseAuth.instance.currentUser;

    if (userCredential == null) return false;

    //TODO ถ้า sign in ไม่ต้องอัปเดตขึ้น RDS DB
    if(isSignin) return accessToken != null;

    final response = await _faceBookController.updateFacebookUsers(
      _uid!,
      _name!,
      _email!,
      _fbImg!,
      _fbAccessToken!,
    );
    return response && accessToken != null;
  }

  Future<String?> promptUserForUid() async {
    try {
      // print('promptUserForUid');
      await FacebookAuth.instance.login();
      final userData = await FacebookAuth.instance.getUserData();
      if (userData['id'] != null && userData['id'].isNotEmpty) {
        return userData['id'];
      }
    } catch (e) {
      debugPrint("promptUserForUid");
      debugPrint(e.toString());
    }
    return null;
  }

  void _handleLoginFailure(BuildContext context, AuthResult result, String message, [bool requireRegistration = false]) {
    AppLoading.Loaderhide(context);
    result
      ..isSuccess = false
      ..isRequireRegistration = requireRegistration
      ..data = message;
    Get.snackbar("Facebook Login failed", message);
  }

  void _handlePlatformException(Object err, AuthResult result, BuildContext context, StackTrace stack) {
    AppLoading.Loaderhide(context);
    debugPrint('Error: $err\nStack: $stack');
    result
      ..isSuccess = false
      ..error = err.toString();
  }

  AuthResult _returnFailedResult(AuthResult result, String message, [bool requireRegistration = false]) {
    result
      ..isSuccess = false
      ..isRequireRegistration = requireRegistration
      ..data = message;
    return result;
  }

  //TODO ล้าง session facebook ทั้งหมด กรณีไม่ต้องการเชื่อมต่อ facebook อีก
  @override
  Future<AuthResult> signOut() async {
    await FacebookAuth.instance.logOut();
    return await super.signOut();
  }
}
