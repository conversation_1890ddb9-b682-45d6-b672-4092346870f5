
import 'dart:async';

import 'package:get/get.dart';

class SteamEvent extends GetxController {
  bool event = false;
  bool checkStatus = false;
  StreamController<bool> steamControl = StreamController<bool>();
  StreamController<bool> homeControl = StreamController<bool>();
  StreamController<bool> stLoanControl = StreamController<bool>();
  StreamController<bool> mrControl = StreamController<bool>();
  StreamController<bool> moreControl = StreamController<bool>();

  StreamController<bool> refreshLikeControl = StreamController<bool>();
  StreamController<bool> changeStateChatInApp = StreamController<bool>();

  List<StreamController> checkCon = [];

  bool checkStarter(control) {
    // print(control.toString());
    // print(checkCon.contains(control));
    return checkCon.contains(control);
  }

  Stream startListening(StreamController control) {
    checkCon.add(control);
    update();
    return control.stream;
  }

  Future<dynamic> changeEventStLoanControl(bool value) async {
    if(checkStatus == value){
      return null;
    }else{
      checkStatus = value;
      stLoanControl.add(checkStatus);

      update();
      return true;
    }

  }

  Future<dynamic> changeEventNavigator() async {
    event = !event;
    steamControl.add(event);

    update();
    return true;
  }

  Future<StreamController?> changeEvent(StreamController control) async {
    event = !event;
    control.add(event);

    update();
    return control;
  }

  Future<StreamController?> refreshLike() async {
    refreshLikeControl.add(true);

    update();
    return refreshLikeControl;
  }

  Future<StreamController?> stopRefreshLike() async {
    refreshLikeControl.add(false);

    update();
    return refreshLikeControl;
  }
}