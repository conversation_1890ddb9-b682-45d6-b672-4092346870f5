import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/view/screen/home/<USER>';

class HomeController extends GetxController {
  RxBool? isShowAAMNote = true.obs;
  var indexMenu = 0.obs;
  RxInt? indexMyloan = 0.obs;
  RxInt? indexNews = 0.obs;
  RxBool? showLoanData = false.obs;
  RxBool? isGuest = false.obs;
  RxString? showUpdateData = 'no'.obs;
  RxBool? isShowTutorial = false.obs;
  RxInt  isIndexTutorial = 0.obs;
  RxInt?  isIndexHome = 0.obs;
  FlutterTts flutterTts = FlutterTts();
  GetStorage storage = GetStorage();


  RxBool closeHead = false.obs;
  List popUpText = [
    {'title': popUpTutorialHello.tr, 'content': popUpTutorialHello1.tr,
      'content1':popUpTutorialName.tr,'content2':popUpTutorialHello2.tr,
      'content3':popUpTutorialHello3.tr},
    {'title': popUpTutorialExplore.tr, 'content': popUpTutorialExplore1.tr,
      'content1':popUpTutorialExplore2.tr,'content2':'','content3':''},
    {'title': popUpTutorialImportant.tr, 'content':"",
      'content1':popUpTutorialImportant1.tr,'content2':popUpTutorialImportant2.tr,
      'content3':popUpTutorialImportant4.tr},
    {'title': popUpTutorialOutstanding.tr, 'content': popUpTutorialOutstanding1.tr,
      'content1':'','content2':'',
      'content3':''},
    {'title': popUpTutorialImportant.tr, 'content': "",
      'content1':popUpTutorialLoans.tr,'content2':popUpTutorialOutstanding.tr,
      'content3':popUpTutorialLoans1.tr},
    {'title': popUpTutorialOutstanding.tr, 'content': popUpTutorialCheckLoan.tr,
      'content1':popUpTutorialCheckLoan1.tr,'content2':popUpTutorialCheckLoan2.tr,
      'content3':''},
    {'title': popUpTutorialStayUpdate.tr, 'content': popUpTutorialStayUpdate1.tr,
      'content1':popUpTutorialStayUpdate2.tr,'content2':'',
      'content3':''},
    {'title': popUpTutorialAAMService.tr, 'content': popUpTutorialAAMService1.tr,
      'content1':popUpTutorialAAMService2.tr,'content2':popUpTutorialAAMService3.tr,
      'content3':''},
    {'title': popUpTutorialShowPoint.tr, 'content': popUpTutorialShowPoint1.tr,
      'content1':'','content2':'',
      'content3':''},
    {'title': popUpTutorialChangData.tr, 'content': popUpTutorialChangData1.tr,
      'content1':'','content2':'',
      'content3':''},
    {'title': popUpTutorialAssistance.tr, 'content': popUpTutorialAssistance1.tr,
      'content1':'','content2':'',
      'content3':''},
    {'title': '', 'content': '',
      'content1':'','content2':'',
      'content3':''},
  ];
  List imgPopUp =[
    'assets/tutorial/aam/home_category.png',
    'assets/tutorial/aam/home_category1.png',
    'assets/tutorial/aam/home_bill.png',
    'assets/tutorial/aam/home_category2.png',
    'assets/tutorial/aam/home_loan.png',
    'assets/tutorial/aam/home_category3.png',
    'assets/tutorial/aam/home_service.png',
    'assets/tutorial/aam/home_point.png',
    'assets/tutorial/aam/home_setting.png',
    'assets/tutorial/aam/home_chat.png',
    'assets/tutorial/aam/home_chat.png',
  ];
  List imgPopUpRafco =[
    'assets/tutorial/rafco/home_category.png',
    'assets/tutorial/rafco/home_category1.png',
    'assets/tutorial/rafco/home_bill.png',
    'assets/tutorial/rafco/home_category2.png',
    'assets/tutorial/rafco/home_loan.png',
    'assets/tutorial/rafco/home_category3.png',
    'assets/tutorial/rafco/home_service.png',
    'assets/tutorial/rafco/home_point.png',
    'assets/tutorial/rafco/home_setting.png',
    'assets/tutorial/rafco/home_chat.png',
    'assets/tutorial/rafco/home_chat.png',
  ];
  List imgPopUpRplc =[
    'assets/tutorial/rplc/home_category.png',
    'assets/tutorial/rplc/home_category1.png',
    'assets/tutorial/rplc/home_bill.png',
    'assets/tutorial/rplc/home_category2.png',
    'assets/tutorial/rplc/home_loan.png',
    'assets/tutorial/rplc/home_category3.png',
    'assets/tutorial/rplc/home_service.png',
    'assets/tutorial/rplc/home_point.png',
    'assets/tutorial/rplc/home_setting.png',
    'assets/tutorial/rplc/home_chat.png',
    'assets/tutorial/rplc/home_chat.png',
  ];


  List sizePopUp =[
    //0
    {
      'width': 308.w,
      'height': 84.h,
      'top': 0.h
    },
    {
      //1
      'width': 308.w,
      'height': 84.h,
      'top': 140.h
    },
    {
      //2
      'width': 125.w,
      'height': 48.h,
      'top': 170.h
    },
    {
      //3
      'width': 340.w,
      'height': 218.h,
      'top': 220.h
    },
    {
      //4
      'width': 82.w,
      'height': 48.h,
      'top': 170.h
    },
    {
      //5
      'width': 340.w,
      'height': 261.h,
      'top': 220.h
    },
    {
      //6
      'width': 82.w,
      'height': 48.h,
      'top': 170.h
    },
    {
      //7
      'width': 340.w,
      'height': 180.h,
      'top': 220.h
    },
    {
      //8
      'width': 173.w,
      'height': 142.h,
      'top': 430.h
    },
    {
      //9
      'width': 102.w,
      'height': 68.h,
      'top': 60.h
    },
    {
      //10
      'width': 45.w,
      'height': 45.h,
      'top': 20.h
    },
    {
      //11
      'width': 45.w,
      'height': 45.h,
      'top': 20.h
    },
    {
      //12
      'width': 45.w,
      'height': 45.h,
      'top': 20.h
    },
  ];
  List popUpMas =[

      'assets/tutorial/aam/mas_aam1.png',
      'assets/tutorial/aam/mas_aam2.png',
      'assets/tutorial/aam/mas_aam3.png',
      'assets/tutorial/aam/mas_aam2.png',
      'assets/tutorial/aam/mas_aam3.png',
      'assets/tutorial/aam/mas_aam4.png',
      'assets/tutorial/aam/mas_aam5.png',
      'assets/tutorial/aam/mas_aam5.png',
      'assets/tutorial/aam/mas_aam2.png',
      'assets/tutorial/aam/mas_aam3.png',
      'assets/tutorial/aam/mas_aam5.png',

  ];
  List popUpMasRafco =[
      'assets/tutorial/rafco/mas_rafco1.png',
      'assets/tutorial/rafco/mas_rafco2.png',
      'assets/tutorial/rafco/mas_rafco3.png',
      'assets/tutorial/rafco/mas_rafco2.png',
      'assets/tutorial/rafco/mas_rafco3.png',
      'assets/tutorial/rafco/mas_rafco4.png',
      'assets/tutorial/rafco/mas_rafco5.png',
      'assets/tutorial/rafco/mas_rafco5.png',
      'assets/tutorial/rafco/mas_rafco2.png',
      'assets/tutorial/rafco/mas_rafco3.png',
      'assets/tutorial/rafco/mas_rafco5.png',

  ];
  List popUpMasRplc =[
      'assets/tutorial/rplc/mas_rplc1.png',
      'assets/tutorial/rplc/mas_rplc2.png',
      'assets/tutorial/rplc/mas_rplc3.png',
      'assets/tutorial/rplc/mas_rplc2.png',
      'assets/tutorial/rplc/mas_rplc3.png',
      'assets/tutorial/rplc/mas_rplc4.png',
      'assets/tutorial/rplc/mas_rplc5.png',
      'assets/tutorial/rplc/mas_rplc5.png',
      'assets/tutorial/rplc/mas_rplc2.png',
      'assets/tutorial/rplc/mas_rplc3.png',
      'assets/tutorial/rplc/mas_rplc5.png',

  ];

  void closeAAMNote() {
    isShowAAMNote!.value = false;
    update();
  }

  void setIndexMenu(int index) {
    indexMenu.value = index;
    if (index == 0) {
      Get.find<ContractListController>().setIndexMyBill(0);
    } else if (index == 1) {
      indexMyloan!.value = 0;
    }
    print(indexMenu.value);
  }


  void setIndexMyloan(int index) {
    indexMyloan!.value = index;
    update();
  }

  void setIndexNews(int index) {
    indexNews!.value = index;
    update();
  }


  void setIndexTutorial(int index) {
    isIndexTutorial.value = index;

    update();
  }

  final Map<String, String> languageCodes = {
    "RAFCO": "km-KH", // กัมพูชา
    "RPLC": "lo-LA", // ลาว
    "AAM": "th-TH", // ไทย
  };


  void speak(text) async {
    print("text ########");
    if(appConfigService.countryConfigCollection.toString() == 'aam'){
      await flutterTts.speak(text);
    }
  }

  Future<void> setGuestUser()async {
    isGuest!.value = true;
    isShowTutorial!.value = true;
    await storage.write('isGuest', true);
    // storage.write('isShowTutorial', true);
    update();
    print('isGuest: ${isGuest!.value}');
    // print('isShowTutorial: ${isShowTutorial!.value}');
    Get.to(() =>  HomeNavigator());
  }

  void checkGuestData() {
    storage.read('isGuest') == null
        ? isGuest!.value = true
        : isGuest!.value = storage.read('isGuest');
    update();
  }

  /// for migation popup
  changeCloseHead(input) {
    closeHead.value = input;
    update();
  }
}
