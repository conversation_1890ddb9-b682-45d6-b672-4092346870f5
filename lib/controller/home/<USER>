import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../service/http_service.dart';
import '../../service/endpoint.dart';
import '../profile/profile.controller.dart';

class PolicyController extends GetxController {
  RxBool? isAcceptedTermPolicy = false.obs;
  RxBool? isAcceptedPrivacyPolicy = false.obs;

  void acceptTermPolicy() {
    isAcceptedTermPolicy!.value = !isAcceptedTermPolicy!.value;
    update();
  }

  void acceptPrivacyPolicy() {
    isAcceptedPrivacyPolicy!.value = !isAcceptedPrivacyPolicy!.value;
    update();
  }

  Future<bool> acceptAllPolicy() async {
    try {
      if (isAcceptedTermPolicy!.value && isAcceptedPrivacyPolicy!.value) {
        //TODO update policy to DB
        Map data = {
          "phone_firebase": Get.find<ProfileController>()
              .profile
              .value
              .phoneFirebase
              .obs
              .toString(),
        };
        print('fdfsddsdssdssdsds => $data');
        print(Endpoints.updateAppAgreement);

        final response = await HttpService.callAPIjwt(
            "POST", Endpoints.updateAppAgreement, data);
        if (response['status'] == 200) {
          debugPrint("Update App Agreement policy success");

          if (appConfigService.countryConfigCollection.toString() == "aam") {
            final res_privacy = await HttpService.callAPIjwt(
                "POST", Endpoints.updateAppPrivacyPolicy, data);
            if (res_privacy['status'] == 200) {
              debugPrint("Update privacy policy success");
            } else {
              debugPrint("Update privacy policy failed");
            }
          }
          Get.find<ProfileController>().getProfile();
          return true;
        } else {
          debugPrint("Update App Agreement policy failed");
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }
  Future<bool> acceptAllPolicyAICP() async {
    try {
      if (isAcceptedTermPolicy!.value && isAcceptedPrivacyPolicy!.value) {
        //TODO update policy to DB
        Map data = {
          "phone_firebase": Get.find<ProfileController>()
              .profile
              .value
              .phoneFirebase
              .obs
              .toString(),
        };
        print('fdfsddsdssdssdsds => $data');

        // print("ตรงนี้หย๋อ${Endpoints.updateAICPAgreement}");

        final response = await HttpService.callAPIjwt("POST", 'https://agilesoftgroup.com/RAFCOp3-UAT/updateAICPAgreement', data);
        print('response => $response');
        if (response['status'] == 200) {
          debugPrint("Update App Agreement policy success");

          // if (appConfigService.countryConfigCollection.toString() == "aam") {
          //   final res_privacy = await HttpService.callAPIjwt(
          //       "POST", Endpoints.updateAppPrivacyPolicy, data);
          //   if (res_privacy['status'] == 200) {
          //     debugPrint("Update privacy policy success");
          //   } else {
          //     debugPrint("Update privacy policy failed");
          //   }
          // }
          Get.find<ProfileController>().getProfile();
          return true;
        } else {
          debugPrint("Update App Agreement policy failed");
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }
}
