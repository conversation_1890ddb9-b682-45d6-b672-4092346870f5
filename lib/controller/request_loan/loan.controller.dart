import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/home/<USER>';
// import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sentry/sentry.dart';

import '../../view/componance/utils/AppImageAssets.dart';
import '../../service/endpoint.dart';
import '../../view/componance/widgets/app_pop_up/request_loan/request_success.dart';
import '../../view/screen/loan_screen/loan_request_screen.dart';
import '../../view/screen/loan_screen/loan_status.dart';

class LoanController extends GetxController {
  Rx<TextEditingController> loan_amount = TextEditingController().obs;
  FocusNode loanAmountFocus = FocusNode();
  // final formatter = CurrencyTextInputFormatter(decimalDigits: 0, symbol: '');

  RxString selectedGuarantee = ''.obs;
  RxInt selectedGuaranteeIndex = 0.obs;
  RxString selectedPeriod = ''.obs;
  RxInt selectedPeriodIndex = 0.obs;

  RxList<String> guaranteeIconListSelected = [
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#995DFE"/><path d="M29.585 28.6663H14.4184V29.7497C14.4184 30.348 13.9333 30.833 13.335 30.833H12.2517C11.6534 30.833 11.1684 30.348 11.1684 29.7497V19.9997L13.8911 12.7389C14.2083 11.8933 15.0167 11.333 15.9199 11.333H28.0835C28.9867 11.333 29.7951 11.8933 30.1123 12.7389L32.835 19.9997V29.7497C32.835 30.348 32.35 30.833 31.7517 30.833H30.6684C30.07 30.833 29.585 30.348 29.585 29.7497V28.6663ZM13.4824 19.9997H30.521L28.0835 13.4997H15.9199L13.4824 19.9997ZM16.0434 25.4163C16.9408 25.4163 17.6684 24.6888 17.6684 23.7913C17.6684 22.8939 16.9408 22.1663 16.0434 22.1663C15.1459 22.1663 14.4184 22.8939 14.4184 23.7913C14.4184 24.6888 15.1459 25.4163 16.0434 25.4163ZM27.96 25.4163C28.8575 25.4163 29.585 24.6888 29.585 23.7913C29.585 22.8939 28.8575 22.1663 27.96 22.1663C27.0626 22.1663 26.335 22.8939 26.335 23.7913C26.335 24.6888 27.0626 25.4163 27.96 25.4163Z" fill="white"/></svg>',
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#995DFE"/><g clip-path="url(#clip0_3651_26695)"><path d="M15.6467 31.0964C16.9364 30.7056 17.665 29.3433 17.2742 28.0537C16.8834 26.764 15.5212 26.0354 14.2315 26.4262C12.9419 26.817 12.2132 28.1793 12.604 29.4689C12.9948 30.7586 14.3571 31.4872 15.6467 31.0964Z" fill="white"/><path d="M28.9286 31.2043C30.2762 31.2043 31.3686 30.1119 31.3686 28.7643C31.3686 27.4167 30.2762 26.3242 28.9286 26.3242C27.581 26.3242 26.4886 27.4167 26.4886 28.7643C26.4886 30.1119 27.581 31.2043 28.9286 31.2043Z" fill="white"/><path d="M34.6572 20.6716C33.9397 19.412 33.1231 18.2114 32.2152 17.0814C31.7907 16.5563 31.2562 16.1307 30.6494 15.8347C30.0426 15.5386 29.3782 15.3793 28.7031 15.368C27.8906 15.3548 27.0898 15.3472 26.488 15.3472H26.482C26.3547 14.7067 26.0232 14.1247 25.5372 13.6886C25.0512 13.2524 24.437 12.9855 23.7865 12.928C22.9105 12.8508 19.4752 12.7949 17.8298 12.7949C16.1845 12.7949 12.7517 12.8508 11.8732 12.928C11.1669 12.9903 10.5051 13.2993 10.0037 13.8006C9.50232 14.302 9.19338 14.9639 9.13102 15.6702C9.05586 16.5461 9 19.0248 9 20.6696C9 22.3144 9.05586 24.7925 9.13355 25.669C9.18642 26.2665 9.41602 26.8347 9.79306 27.3012C10.1701 27.7676 10.6775 28.1113 11.2506 28.2883C11.3683 27.3703 11.8243 26.5293 12.5295 25.9298C13.2347 25.3304 14.1382 25.0158 15.0631 25.0476C15.9881 25.0793 16.8679 25.4552 17.5303 26.1016C18.1927 26.7479 18.59 27.6183 18.6444 28.5422C19.7463 28.5341 21.1768 28.5112 22.2818 28.4792C23.1629 28.5066 24.2582 28.527 25.22 28.5376C25.277 27.6117 25.6782 26.7405 26.345 26.0954C27.0117 25.4503 27.8956 25.0779 28.8229 25.0515C29.7502 25.0251 30.6539 25.3465 31.3562 25.9526C32.0586 26.5587 32.5088 27.4057 32.6184 28.3269C33.2221 28.1708 33.7626 27.8315 34.1657 27.3558C34.5688 26.8801 34.8147 26.2911 34.8695 25.67C34.9365 24.9144 34.9873 23.4407 35 21.9939C35.0042 21.5307 34.886 21.0745 34.6572 20.6716ZM32.1847 21.4496H26.6607C26.6607 21.1764 26.6643 20.9138 26.6643 20.6696C26.6643 19.5361 26.6373 18.0066 26.5957 16.8788C27.1741 16.8788 27.9257 16.8869 28.6803 16.8991C29.1312 16.9067 29.5749 17.0133 29.98 17.2113C30.3852 17.4094 30.7419 17.6941 31.0248 18.0452C31.642 18.8122 32.214 19.6145 32.7377 20.4482C32.8 20.547 32.8347 20.6608 32.8381 20.7776C32.8415 20.8944 32.8135 21.0099 32.757 21.1122C32.7005 21.2145 32.6176 21.2998 32.5169 21.3591C32.4163 21.4184 32.3015 21.4497 32.1847 21.4496Z" fill="white"/></g><defs><clipPath id="clip0_3651_26695"><rect width="26" height="26" fill="white" transform="translate(9 9)"/></clipPath></defs></svg>',
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#995DFE"/><path d="M21.943 25.8203C20.6886 25.8203 19.6623 26.8466 19.6623 28.101V32.7194C19.6623 33.9738 20.6886 35.0001 21.943 35.0001C23.1974 35.0001 24.2237 33.9738 24.2237 32.7194V28.101C24.2237 26.8466 23.1974 25.8203 21.943 25.8203Z" fill="white"/><path d="M30.2105 11.8509H25.8202C25.307 10.1974 23.7676 9 21.943 9C20.1184 9 18.5789 10.1974 18.0658 11.8509H13.7325C13.1053 11.8509 12.5921 12.364 12.5921 12.9912C12.5921 13.6184 13.1053 14.1316 13.7325 14.1316H18.1228C18.4079 15.1009 19.0351 15.8991 19.8903 16.4123C16.8114 17.3246 14.5307 20.1754 14.5307 23.5395V29.5263C14.5307 30.5526 15.386 31.4079 16.4123 31.4079H17.9518V28.1579C17.9518 25.9342 19.7763 24.1096 22 24.1096C24.2237 24.1096 26.0482 25.9342 26.0482 28.1579V31.4079H27.5877C28.614 31.4079 29.4693 30.5526 29.4693 29.5263V23.5965C29.4693 20.2324 27.1886 17.3246 24.1096 16.4693C24.9649 15.9561 25.5921 15.1579 25.8771 14.1886H30.2675C30.8947 14.1886 31.4078 13.6754 31.4078 13.0482C31.3509 12.364 30.8377 11.8509 30.2105 11.8509ZM21.943 15.3289C20.6886 15.3289 19.6623 14.3026 19.6623 13.0482C19.6623 11.7939 20.6886 10.7675 21.943 10.7675C23.1974 10.7675 24.2237 11.7939 24.2237 13.0482C24.2807 14.3026 23.2544 15.3289 21.943 15.3289Z" fill="white"/></svg>',
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#995DFE"/><path fill-rule="evenodd" clip-rule="evenodd" d="M16.5833 16.5837C16.5833 13.5921 19.0085 11.167 22 11.167C24.9915 11.167 27.4167 13.5921 27.4167 16.5837C27.4167 19.2042 25.5557 21.3901 23.0833 21.892V27.417C23.0833 28.0153 22.5983 28.5003 22 28.5003C21.4017 28.5003 20.9167 28.0153 20.9167 27.417V21.892C18.4442 21.3901 16.5833 19.2042 16.5833 16.5837ZM18.3467 24.8418C18.6017 25.0474 18.75 25.3575 18.75 25.6852V27.417C18.75 29.212 20.205 30.667 22 30.667C23.795 30.667 25.25 29.212 25.25 27.417V25.6852C25.25 25.3575 25.3983 25.0474 25.6533 24.8418C25.9083 24.6362 26.2428 24.557 26.563 24.6264C27.9297 24.9229 29.1475 25.3623 30.053 25.9383C30.9176 26.4884 31.75 27.336 31.75 28.5003C31.75 29.6648 30.9176 30.5123 30.053 31.0624C29.1475 31.6384 27.9297 32.0778 26.563 32.3742C25.2057 32.6687 23.6482 32.8337 22 32.8337C20.3518 32.8337 18.7943 32.6687 17.437 32.3742C16.0702 32.0778 14.8525 31.6384 13.9471 31.0624C13.0824 30.5123 12.25 29.6648 12.25 28.5003C12.25 27.336 13.0824 26.4884 13.9471 25.9383C14.8525 25.3623 16.0702 24.9229 17.437 24.6264C17.7572 24.557 18.0916 24.6362 18.3467 24.8418Z" fill="white"/></svg>'
    // AppImageAssets.aam_loan_car_selected,
    // AppImageAssets.aam_loan_truck_selected,
    // AppImageAssets.aam_loan_motocycle_selected,
    // AppImageAssets.aam_loan_land_selected,
  ].obs;

  RxList<String> guaranteeIconList = [
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#792AFF" fill-opacity="0.05"/><path d="M32.252 33H14.752V34.25C14.752 34.9404 14.1923 35.5 13.502 35.5H12.252C11.5616 35.5 11.002 34.9404 11.002 34.25V23L14.1436 14.6222C14.5095 13.6464 15.4423 13 16.4845 13H30.5195C31.5616 13 32.4943 13.6464 32.8603 14.6222L36.002 23V34.25C36.002 34.9404 35.4423 35.5 34.752 35.5H33.502C32.8116 35.5 32.252 34.9404 32.252 34.25V33ZM13.672 23H33.332L30.5195 15.5H16.4845L13.672 23ZM16.627 29.25C17.6625 29.25 18.502 28.4105 18.502 27.375C18.502 26.3395 17.6625 25.5 16.627 25.5C15.5914 25.5 14.752 26.3395 14.752 27.375C14.752 28.4105 15.5914 29.25 16.627 29.25ZM30.377 29.25C31.4125 29.25 32.252 28.4105 32.252 27.375C32.252 26.3395 31.4125 25.5 30.377 25.5C29.3415 25.5 28.502 26.3395 28.502 27.375C28.502 28.4105 29.3415 29.25 30.377 29.25Z" fill="#995DFE"/></svg>',
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#792AFF" fill-opacity="0.05"/><g clip-path="url(#clip0_3651_25681)"><path d="M16.1693 33.4961C17.6574 33.0452 18.4981 31.4733 18.0472 29.9852C17.5963 28.4972 16.0244 27.6564 14.5363 28.1074C13.0483 28.5583 12.2075 30.1301 12.6585 31.6182C13.1094 33.1063 14.6812 33.947 16.1693 33.4961Z" fill="#995DFE"/><path d="M31.4945 33.6206C33.0494 33.6206 34.3099 32.3601 34.3099 30.8052C34.3099 29.2503 33.0494 27.9897 31.4945 27.9897C29.9396 27.9897 28.6791 29.2503 28.6791 30.8052C28.6791 32.3601 29.9396 33.6206 31.4945 33.6206Z" fill="#995DFE"/><path d="M38.1045 21.4679C37.2766 20.0144 36.3344 18.6292 35.2867 17.3253C34.797 16.7194 34.1803 16.2284 33.4801 15.8868C32.7799 15.5452 32.0133 15.3614 31.2344 15.3483C30.2969 15.3331 29.3729 15.3243 28.6785 15.3243H28.6715C28.5246 14.5853 28.1422 13.9138 27.5814 13.4105C27.0207 12.9072 26.3119 12.5993 25.5613 12.5329C24.5506 12.4438 20.5867 12.3794 18.6883 12.3794C16.7898 12.3794 12.8289 12.4438 11.8152 12.5329C11.0003 12.6049 10.2366 12.9613 9.65811 13.5398C9.0796 14.1184 8.72313 14.882 8.65117 15.697C8.56445 16.7077 8.5 19.5677 8.5 21.4655C8.5 23.3634 8.56445 26.2228 8.6541 27.2341C8.71511 27.9235 8.98002 28.5791 9.41507 29.1174C9.85012 29.6556 10.4356 30.0521 11.0969 30.2563C11.2326 29.1971 11.7588 28.2267 12.5725 27.5351C13.3862 26.8435 14.4287 26.4804 15.4959 26.5171C16.5632 26.5537 17.5784 26.9874 18.3427 27.7332C19.107 28.479 19.5654 29.4833 19.6281 30.5493C20.8996 30.5399 22.5502 30.5136 23.8252 30.4767C24.8418 30.5083 26.1057 30.5317 27.2154 30.544C27.2811 29.4756 27.7441 28.4704 28.5134 27.7261C29.2827 26.9818 30.3026 26.5521 31.3726 26.5216C32.4426 26.4911 33.4853 26.862 34.2957 27.5614C35.1061 28.2607 35.6255 29.2379 35.752 30.3009C36.4485 30.1208 37.0722 29.7293 37.5373 29.1804C38.0025 28.6315 38.2861 27.952 38.3494 27.2353C38.4268 26.3634 38.4854 24.663 38.5 22.9937C38.5049 22.4591 38.3684 21.9328 38.1045 21.4679ZM35.2516 22.3655H28.8777C28.8777 22.0503 28.8818 21.7474 28.8818 21.4655C28.8818 20.1577 28.8508 18.3929 28.8027 17.0915C29.4701 17.0915 30.3373 17.1009 31.208 17.1149C31.7283 17.1237 32.2403 17.2467 32.7077 17.4753C33.1752 17.7038 33.5868 18.0322 33.9133 18.4374C34.6254 19.3224 35.2853 20.2482 35.8896 21.2101C35.9616 21.3241 36.0016 21.4554 36.0055 21.5901C36.0094 21.7249 35.9771 21.8582 35.9119 21.9763C35.8467 22.0943 35.7511 22.1927 35.6349 22.2611C35.5188 22.3296 35.3864 22.3657 35.2516 22.3655Z" fill="#995DFE"/></g><defs><clipPath id="clip0_3651_25681"><rect width="30" height="30" fill="white" transform="translate(8.5 8)"/></clipPath></defs></svg>',
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#792AFF" fill-opacity="0.05"/><path d="M23.4342 27.4077C21.9868 27.4077 20.8026 28.5919 20.8026 30.0393V35.3682C20.8026 36.8156 21.9868 37.9998 23.4342 37.9998C24.8816 37.9998 26.0658 36.8156 26.0658 35.3682V30.0393C26.0658 28.5919 24.8816 27.4077 23.4342 27.4077Z" fill="#995DFE"/><path d="M32.9737 11.2895H27.9079C27.3158 9.38158 25.5395 8 23.4342 8C21.3289 8 19.5526 9.38158 18.9605 11.2895H13.9605C13.2368 11.2895 12.6447 11.8816 12.6447 12.6053C12.6447 13.3289 13.2368 13.9211 13.9605 13.9211H19.0263C19.3552 15.0395 20.0789 15.9605 21.0658 16.5526C17.5131 17.6053 14.8816 20.8947 14.8816 24.7763V31.6842C14.8816 32.8684 15.8684 33.8553 17.0526 33.8553H18.8289V30.1053C18.8289 27.5394 20.9342 25.4342 23.5 25.4342C26.0658 25.4342 28.171 27.5395 28.171 30.1053V33.8553H29.9473C31.1316 33.8553 32.1184 32.8684 32.1184 31.6842V24.8421C32.1184 20.9605 29.4868 17.6053 25.9341 16.6184C26.921 16.0263 27.6447 15.1053 27.9736 13.9868H33.0394C33.7631 13.9868 34.3552 13.3947 34.3552 12.6711C34.2895 11.8816 33.6973 11.2895 32.9737 11.2895ZM23.4342 15.3026C21.9868 15.3026 20.8026 14.1184 20.8026 12.6711C20.8026 11.2237 21.9868 10.0395 23.4342 10.0395C24.8816 10.0395 26.0658 11.2237 26.0658 12.6711C26.1316 14.1184 24.9473 15.3026 23.4342 15.3026Z" fill="#995DFE"/></svg>',
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#792AFF" fill-opacity="0.05"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.25 16.75C17.25 13.2982 20.0482 10.5 23.5 10.5C26.9517 10.5 29.75 13.2982 29.75 16.75C29.75 19.7737 27.6027 22.2959 24.75 22.875V29.25C24.75 29.9404 24.1904 30.5 23.5 30.5C22.8096 30.5 22.25 29.9404 22.25 29.25V22.875C19.3972 22.2959 17.25 19.7737 17.25 16.75ZM19.2846 26.2786C19.5789 26.5159 19.75 26.8736 19.75 27.2517V29.25C19.75 31.3211 21.4289 33 23.5 33C25.5711 33 27.25 31.3211 27.25 29.25V27.2517C27.25 26.8736 27.4211 26.5159 27.7154 26.2786C28.0096 26.0414 28.3955 25.95 28.765 26.0301C30.342 26.3722 31.7471 26.8792 32.7919 27.5439C33.7895 28.1785 34.75 29.1565 34.75 30.5C34.75 31.8436 33.7895 32.8215 32.7919 33.4562C31.7471 34.1209 30.342 34.6279 28.765 34.9699C27.1989 35.3096 25.4017 35.5 23.5 35.5C21.5983 35.5 19.8012 35.3096 18.235 34.9699C16.658 34.6279 15.2529 34.1209 14.2082 33.4562C13.2105 32.8215 12.25 31.8436 12.25 30.5C12.25 29.1565 13.2105 28.1785 14.2082 27.5439C15.2529 26.8792 16.658 26.3722 18.235 26.0301C18.6045 25.95 18.9903 26.0414 19.2846 26.2786Z" fill="#995DFE"/></svg>'
    // AppImageAssets.aam_loan_car,
    // AppImageAssets.aam_loan_truck,
    // AppImageAssets.aam_loan_motocycle,
    // AppImageAssets.aam_loan_land,
  ].obs;

  RxList<String> guaranteeIconListRafco = [
    '<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46 23C46 41.4 41.4 46 23 46C4.6 46 0 41.4 0 23C0 4.6 4.6 0 23 0C41.4 0 46 4.6 46 23Z" fill="#EA1B23" fill-opacity="0.05"/><path d="M31.752 33H14.252V34.25C14.252 34.9404 13.6923 35.5 13.002 35.5H11.752C11.0616 35.5 10.502 34.9404 10.502 34.25V23L13.6436 14.6222C14.0095 13.6464 14.9423 13 15.9845 13H30.0195C31.0616 13 31.9943 13.6464 32.3603 14.6222L35.502 23V34.25C35.502 34.9404 34.9423 35.5 34.252 35.5H33.002C32.3116 35.5 31.752 34.9404 31.752 34.25V33ZM13.172 23H32.832L30.0195 15.5H15.9845L13.172 23ZM16.127 29.25C17.1625 29.25 18.002 28.4105 18.002 27.375C18.002 26.3395 17.1625 25.5 16.127 25.5C15.0914 25.5 14.252 26.3395 14.252 27.375C14.252 28.4105 15.0914 29.25 16.127 29.25ZM29.877 29.25C30.9125 29.25 31.752 28.4105 31.752 27.375C31.752 26.3395 30.9125 25.5 29.877 25.5C28.8415 25.5 28.002 26.3395 28.002 27.375C28.002 28.4105 28.8415 29.25 29.877 29.25Z" fill="#EA1B23"/></svg>',
    '<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46 23C46 41.4 41.4 46 23 46C4.6 46 0 41.4 0 23C0 4.6 4.6 0 23 0C41.4 0 46 4.6 46 23Z" fill="#EA1B23" fill-opacity="0.05"/><path d="M22.9342 27.4082C21.4868 27.4082 20.3026 28.5924 20.3026 30.0398V35.3687C20.3026 36.8161 21.4868 38.0003 22.9342 38.0003C24.3816 38.0003 25.5658 36.8161 25.5658 35.3687V30.0398C25.5658 28.5924 24.3816 27.4082 22.9342 27.4082Z" fill="#EA1B23"/><path d="M32.4737 11.2895H27.4079C26.8158 9.38158 25.0396 8 22.9342 8C20.8289 8 19.0527 9.38158 18.4606 11.2895H13.4606C12.7369 11.2895 12.1448 11.8816 12.1448 12.6053C12.1448 13.3289 12.7369 13.9211 13.4606 13.9211H18.5264C18.8553 15.0395 19.579 15.9605 20.5658 16.5526C17.0132 17.6053 14.3816 20.8947 14.3816 24.7763V31.6842C14.3816 32.8684 15.3685 33.8553 16.5527 33.8553H18.329V30.1053C18.329 27.5394 20.4342 25.4342 23 25.4342C25.5658 25.4342 27.6711 27.5395 27.6711 30.1053V33.8553H29.4474C30.6316 33.8553 31.6185 32.8684 31.6185 31.6842V24.8421C31.6185 20.9605 28.9869 17.6053 25.4342 16.6184C26.421 16.0263 27.1447 15.1053 27.4737 13.9868H32.5395C33.2631 13.9868 33.8553 13.3947 33.8553 12.6711C33.7895 11.8816 33.1974 11.2895 32.4737 11.2895ZM22.9342 15.3026C21.4869 15.3026 20.3027 14.1184 20.3027 12.6711C20.3027 11.2237 21.4869 10.0395 22.9342 10.0395C24.3816 10.0395 25.5658 11.2237 25.5658 12.6711C25.6316 14.1184 24.4474 15.3026 22.9342 15.3026Z" fill="#EA1B23"/></svg>',
    '<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46 23C46 41.4 41.4 46 23 46C4.6 46 0 41.4 0 23C0 4.6 4.6 0 23 0C41.4 0 46 4.6 46 23Z" fill="#EA1B23" fill-opacity="0.05"/><path d="M23 9.2002C20.6445 9.2002 18.0828 9.48848 16.7633 9.65957C15.8727 9.77441 15.2 10.5408 15.2 11.4432V18.8002H14.6C14.2602 18.8002 14 18.54 14 18.2002V14.7408C14 14.04 13.3672 13.4002 12.6734 13.4002H12.3266C11.6328 13.4002 11 14.04 11 14.7408V16.7986C11 17.5439 11.6188 18.2002 12.3266 18.2002H12.8C12.8 19.1869 13.6133 20.0002 14.6 20.0002H15.2V31.1002C15.2 31.9275 15.8727 32.6002 16.7 32.6002C17.5273 32.6002 18.2 31.9275 18.2 31.1002V30.2729C18.4063 30.3174 18.6219 30.3596 18.8445 30.3994H18.8469C19.0484 30.4393 19.2641 30.4721 19.4773 30.5072L19.6672 28.1822L19.6719 28.133L19.6813 28.0838C19.9367 26.5299 21.2445 25.4002 22.7938 25.4002H23.2063C24.7531 25.4002 26.0609 26.5064 26.3141 28.0346L26.3211 28.0814L26.3281 28.1307L26.5227 30.5096C26.9773 30.4369 27.4062 30.3572 27.8 30.2752V31.1002C27.8 31.9275 28.4727 32.6002 29.3 32.6002C30.1297 32.6002 30.8 31.9275 30.8 31.1002V20.0002H31.4C32.3867 20.0002 33.2 19.1869 33.2 18.2002H33.6734C34.3813 18.2002 35 17.5439 35 16.7986V14.7408C35 14.04 34.3672 13.4002 33.6734 13.4002H33.3266C32.6328 13.4002 32 14.04 32 14.7408V18.2002C32 18.54 31.7398 18.8002 31.4 18.8002H30.8V11.4432C30.8 10.5408 30.1273 9.77441 29.2367 9.65957C27.9172 9.48848 25.3555 9.2002 23 9.2002ZM23 10.4002C25.2875 10.4002 27.793 10.6814 29.082 10.8479C29.3773 10.8877 29.6 11.1432 29.6 11.4432V17.6002H24.3219L27.65 13.7939C27.8094 13.6205 27.8492 13.3674 27.7531 13.1518C27.657 12.9361 27.4438 12.7955 27.2094 12.7955C27.0312 12.7932 26.8625 12.8705 26.75 13.0064L22.7281 17.6002H16.4V11.4432C16.4 11.1432 16.6227 10.8854 16.918 10.8479C18.207 10.6814 20.7125 10.4002 23 10.4002ZM18.8 20.0002C19.4633 20.0002 20 20.5369 20 21.2002C20 21.8635 19.4633 22.4002 18.8 22.4002C18.1367 22.4002 17.6 21.8635 17.6 21.2002C17.6 20.5369 18.1367 20.0002 18.8 20.0002ZM27.2 20.0002C27.8633 20.0002 28.4 20.5369 28.4 21.2002C28.4 21.8635 27.8633 22.4002 27.2 22.4002C26.5367 22.4002 26 21.8635 26 21.2002C26 20.5369 26.5367 20.0002 27.2 20.0002ZM22.7938 26.6002C21.8375 26.6002 21.0266 27.2869 20.8625 28.2783L20.6023 31.5033C20.5813 31.7494 20.7172 31.9838 20.9398 32.0916C20.9844 32.1127 22.0156 32.6002 23 32.6002C23.9844 32.6002 25.0156 32.1127 25.0602 32.0916C25.2828 31.9838 25.4164 31.7494 25.3977 31.5033L25.1305 28.2314C24.9734 27.2869 24.1648 26.6002 23.2063 26.6002H22.7938ZM21.2 33.4791V35.0002C21.2 35.9916 22.0086 36.8002 23 36.8002C23.9914 36.8002 24.8 35.9916 24.8 35.0002V33.4791C24.3125 33.6385 23.6656 33.8002 23 33.8002C22.3367 33.8002 21.6875 33.6385 21.2 33.4791Z" fill="#EA1B23"/></svg>',
    '<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46 23C46 41.4 41.4 46 23 46C4.6 46 0 41.4 0 23C0 4.6 4.6 0 23 0C41.4 0 46 4.6 46 23Z" fill="#EA1B23" fill-opacity="0.05"/><path fill-rule="evenodd" clip-rule="evenodd" d="M16.75 16.75C16.75 13.2982 19.5482 10.5 23 10.5C26.4517 10.5 29.25 13.2982 29.25 16.75C29.25 19.7737 27.1027 22.2959 24.25 22.875V29.25C24.25 29.9404 23.6904 30.5 23 30.5C22.3096 30.5 21.75 29.9404 21.75 29.25V22.875C18.8972 22.2959 16.75 19.7737 16.75 16.75ZM18.7846 26.2786C19.0789 26.5159 19.25 26.8736 19.25 27.2517V29.25C19.25 31.3211 20.9289 33 23 33C25.0711 33 26.75 31.3211 26.75 29.25V27.2517C26.75 26.8736 26.9211 26.5159 27.2154 26.2786C27.5096 26.0414 27.8955 25.95 28.265 26.0301C29.842 26.3722 31.2471 26.8792 32.2919 27.5439C33.2895 28.1785 34.25 29.1565 34.25 30.5C34.25 31.8436 33.2895 32.8215 32.2919 33.4562C31.2471 34.1209 29.842 34.6279 28.265 34.9699C26.6989 35.3096 24.9017 35.5 23 35.5C21.0983 35.5 19.3012 35.3096 17.735 34.9699C16.158 34.6279 14.7529 34.1209 13.7082 33.4562C12.7105 32.8215 11.75 31.8436 11.75 30.5C11.75 29.1565 12.7105 28.1785 13.7082 27.5439C14.7529 26.8792 16.158 26.3722 17.735 26.0301C18.1045 25.95 18.4903 26.0414 18.7846 26.2786Z" fill="#EA1B23"/></svg>',
  ].obs;

  RxList<String> guaranteeIconListSelectedRafco = [
    '<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M34 17C34 30.6 30.6 34 17 34C3.4 34 0 30.6 0 17C0 3.4 3.4 0 17 0C30.6 0 34 3.4 34 17Z" fill="#EA1B23"/><path d="M23.4181 24.3337H10.5847V25.2503C10.5847 25.7566 10.1743 26.167 9.66805 26.167H8.75138C8.24513 26.167 7.83472 25.7566 7.83472 25.2503V17.0003L10.1386 10.8566C10.4069 10.141 11.091 9.66699 11.8552 9.66699H22.1476C22.9118 9.66699 23.5958 10.141 23.8642 10.8566L26.1681 17.0003V25.2503C26.1681 25.7566 25.7577 26.167 25.2514 26.167H24.3347C23.8284 26.167 23.4181 25.7566 23.4181 25.2503V24.3337ZM9.79272 17.0003H24.2101L22.1476 11.5003H11.8552L9.79272 17.0003ZM11.9597 21.5837C12.7191 21.5837 13.3347 20.968 13.3347 20.2087C13.3347 19.4493 12.7191 18.8337 11.9597 18.8337C11.2003 18.8337 10.5847 19.4493 10.5847 20.2087C10.5847 20.968 11.2003 21.5837 11.9597 21.5837ZM22.0431 21.5837C22.8024 21.5837 23.4181 20.968 23.4181 20.2087C23.4181 19.4493 22.8024 18.8337 22.0431 18.8337C21.2837 18.8337 20.6681 19.4493 20.6681 20.2087C20.6681 20.968 21.2837 21.5837 22.0431 21.5837Z" fill="white"/></svg>',
    '<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M34 17C34 30.6 30.6 34 17 34C3.4 34 0 30.6 0 17C0 3.4 3.4 0 17 0C30.6 0 34 3.4 34 17Z" fill="#EA1B23"/><g clip-path="url(#clip0_3697_29785)"><path d="M16.9518 20.2324C15.8904 20.2324 15.022 21.1008 15.022 22.1622V26.0701C15.022 27.1315 15.8904 28 16.9518 28C18.0132 28 18.8816 27.1315 18.8816 26.0701V22.1622C18.8816 21.1008 18.0132 20.2324 16.9518 20.2324Z" fill="white"/><path d="M23.9473 8.41228H20.2324C19.7982 7.01316 18.4956 6 16.9517 6C15.4078 6 14.1052 7.01316 13.671 8.41228H10.0043C9.47364 8.41228 9.03943 8.84649 9.03943 9.37719C9.03943 9.90789 9.47364 10.3421 10.0043 10.3421H13.7193C13.9605 11.1623 14.4912 11.8377 15.2149 12.2719C12.6096 13.0439 10.6798 15.4561 10.6798 18.3026V23.3684C10.6798 24.2368 11.4035 24.9605 12.2719 24.9605H13.5745V22.2105C13.5745 20.3289 15.1184 18.7851 17 18.7851C18.8815 18.7851 20.4254 20.3289 20.4254 22.2105V24.9605H21.728C22.5964 24.9605 23.3201 24.2368 23.3201 23.3684V18.3509C23.3201 15.5043 21.3903 13.0439 18.785 12.3202C19.5087 11.886 20.0394 11.2105 20.2806 10.3904H23.9955C24.5262 10.3904 24.9604 9.95614 24.9604 9.42544C24.9122 8.84649 24.478 8.41228 23.9473 8.41228ZM16.9517 11.3553C15.8903 11.3553 15.0219 10.4868 15.0219 9.42544C15.0219 8.36404 15.8903 7.49561 16.9517 7.49561C18.0131 7.49561 18.8815 8.36404 18.8815 9.42544C18.9298 10.4868 18.0614 11.3553 16.9517 11.3553Z" fill="white"/></g><defs><clipPath id="clip0_3697_29785"><rect width="22" height="22" fill="white" transform="translate(6 6)"/></clipPath></defs></svg>',
    '<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M34 17C34 30.6 30.6 34 17 34C3.4 34 0 30.6 0 17C0 3.4 3.4 0 17 0C30.6 0 34 3.4 34 17Z" fill="#EA1B23"/><path d="M17 6.87988C15.2726 6.87988 13.394 7.09129 12.4264 7.21676C11.7732 7.30098 11.28 7.86301 11.28 8.52473V13.9199H10.84C10.5907 13.9199 10.4 13.7291 10.4 13.4799V10.943C10.4 10.4291 9.93589 9.95988 9.42714 9.95988H9.17276C8.66401 9.95988 8.19995 10.4291 8.19995 10.943V12.4521C8.19995 12.9986 8.6537 13.4799 9.17276 13.4799H9.51995C9.51995 14.2035 10.1164 14.7999 10.84 14.7999H11.28V22.9399C11.28 23.5466 11.7732 24.0399 12.38 24.0399C12.9867 24.0399 13.48 23.5466 13.48 22.9399V22.3332C13.6312 22.3658 13.7893 22.3968 13.9526 22.426H13.9543C14.1021 22.4552 14.2603 22.4793 14.4167 22.505L14.5559 20.8L14.5593 20.7639L14.5662 20.7279C14.7535 19.5883 15.7126 18.7599 16.8487 18.7599H17.1512C18.2856 18.7599 19.2446 19.5711 19.4303 20.6918L19.4354 20.7261L19.4406 20.7622L19.5832 22.5068C19.9167 22.4535 20.2312 22.395 20.52 22.3349V22.9399C20.52 23.5466 21.0132 24.0399 21.62 24.0399C22.2284 24.0399 22.72 23.5466 22.72 22.9399V14.7999H23.16C23.8835 14.7999 24.48 14.2035 24.48 13.4799H24.8271C25.3462 13.4799 25.8 12.9986 25.8 12.4521V10.943C25.8 10.4291 25.3359 9.95988 24.8271 9.95988H24.5728C24.064 9.95988 23.6 10.4291 23.6 10.943V13.4799C23.6 13.7291 23.4092 13.9199 23.16 13.9199H22.72V8.52473C22.72 7.86301 22.2267 7.30098 21.5735 7.21676C20.6059 7.09129 18.7273 6.87988 17 6.87988ZM17 7.75988C18.6775 7.75988 20.5148 7.96613 21.4601 8.08816C21.6767 8.11738 21.84 8.30473 21.84 8.52473V13.0399H17.9693L20.41 10.2486C20.5268 10.1214 20.556 9.93582 20.4856 9.7777C20.4151 9.61957 20.2587 9.51645 20.0868 9.51645C19.9562 9.51473 19.8325 9.57145 19.75 9.67113L16.8006 13.0399H12.16V8.52473C12.16 8.30473 12.3232 8.11566 12.5398 8.08816C13.4851 7.96613 15.3225 7.75988 17 7.75988ZM13.92 14.7999C14.4064 14.7999 14.8 15.1935 14.8 15.6799C14.8 16.1663 14.4064 16.5599 13.92 16.5599C13.4335 16.5599 13.04 16.1663 13.04 15.6799C13.04 15.1935 13.4335 14.7999 13.92 14.7999ZM20.08 14.7999C20.5664 14.7999 20.96 15.1935 20.96 15.6799C20.96 16.1663 20.5664 16.5599 20.08 16.5599C19.5935 16.5599 19.2 16.1663 19.2 15.6799C19.2 15.1935 19.5935 14.7999 20.08 14.7999ZM16.8487 19.6399C16.1475 19.6399 15.5528 20.1435 15.4325 20.8705L15.2417 23.2355C15.2262 23.416 15.3259 23.5879 15.4892 23.6669C15.5218 23.6824 16.2781 24.0399 17 24.0399C17.7218 24.0399 18.4781 23.6824 18.5107 23.6669C18.674 23.5879 18.772 23.416 18.7582 23.2355L18.5623 20.8361C18.4471 20.1435 17.8542 19.6399 17.1512 19.6399H16.8487ZM15.68 24.6844V25.7999C15.68 26.5269 16.2729 27.1199 17 27.1199C17.727 27.1199 18.32 26.5269 18.32 25.7999V24.6844C17.9625 24.8013 17.4881 24.9199 17 24.9199C16.5135 24.9199 16.0375 24.8013 15.68 24.6844Z" fill="white"/></svg>',
    '<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M34 17C34 30.6 30.6 34 17 34C3.4 34 0 30.6 0 17C0 3.4 3.4 0 17 0C30.6 0 34 3.4 34 17Z" fill="#EA1B23"/><path fill-rule="evenodd" clip-rule="evenodd" d="M12.4167 12.4163C12.4167 9.88504 14.4687 7.83301 17 7.83301C19.5313 7.83301 21.5833 9.88504 21.5833 12.4163C21.5833 14.6337 20.0087 16.4833 17.9167 16.908V21.583C17.9167 22.0893 17.5063 22.4997 17 22.4997C16.4937 22.4997 16.0833 22.0893 16.0833 21.583V16.908C13.9913 16.4833 12.4167 14.6337 12.4167 12.4163ZM13.9087 19.404C14.1245 19.578 14.25 19.8403 14.25 20.1176V21.583C14.25 23.1018 15.4812 24.333 17 24.333C18.5188 24.333 19.75 23.1018 19.75 21.583V20.1176C19.75 19.8403 19.8755 19.578 20.0913 19.404C20.3071 19.23 20.59 19.163 20.861 19.2218C22.0175 19.4727 23.0479 19.8445 23.814 20.3318C24.5456 20.7972 25.25 21.5144 25.25 22.4997C25.25 23.485 24.5456 24.2021 23.814 24.6676C23.0479 25.155 22.0175 25.5268 20.861 25.7776C19.7125 26.0267 18.3946 26.1663 17 26.1663C15.6054 26.1663 14.2875 26.0267 13.139 25.7776C11.9825 25.5268 10.9521 25.155 10.186 24.6676C9.45434 24.2021 8.75 23.485 8.75 22.4997C8.75 21.5144 9.45434 20.7972 10.186 20.3318C10.9521 19.8445 11.9825 19.4727 13.139 19.2218C13.4099 19.163 13.6929 19.23 13.9087 19.404Z" fill="white"/></svg>',
  ].obs;

  RxList<String> guaranteeIconListSelectedRPLC = [
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#FFC20E"/><path d="M21.9428 25.8203C20.6884 25.8203 19.6621 26.8466 19.6621 28.101V32.7194C19.6621 33.9738 20.6884 35.0001 21.9428 35.0001C23.1972 35.0001 24.2235 33.9738 24.2235 32.7194V28.101C24.2235 26.8466 23.1972 25.8203 21.9428 25.8203Z" fill="white"/><path d="M30.2102 11.8509H25.8199C25.3067 10.1974 23.7673 9 21.9427 9C20.1181 9 18.5786 10.1974 18.0655 11.8509H13.7321C13.105 11.8509 12.5918 12.364 12.5918 12.9912C12.5918 13.6184 13.105 14.1316 13.7321 14.1316H18.1225C18.4076 15.1009 19.0348 15.8991 19.89 16.4123C16.8111 17.3246 14.5304 20.1754 14.5304 23.5395V29.5263C14.5304 30.5526 15.3857 31.4079 16.412 31.4079H17.9514V28.1579C17.9514 25.9342 19.776 24.1096 21.9997 24.1096C24.2234 24.1096 26.0479 25.9342 26.0479 28.1579V31.4079H27.5874C28.6137 31.4079 29.469 30.5526 29.469 29.5263V23.5965C29.469 20.2324 27.1883 17.3246 24.1093 16.4693C24.9646 15.9561 25.5918 15.1579 25.8768 14.1886H30.2672C30.8944 14.1886 31.4075 13.6754 31.4075 13.0482C31.3506 12.364 30.8374 11.8509 30.2102 11.8509ZM21.9427 15.3289C20.6883 15.3289 19.662 14.3026 19.662 13.0482C19.662 11.7939 20.6883 10.7675 21.9427 10.7675C23.1971 10.7675 24.2234 11.7939 24.2234 13.0482C24.2804 14.3026 23.2541 15.3289 21.9427 15.3289Z" fill="white"/></svg>',
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#FFC20E"/><path d="M29.5846 28.6663H14.418V29.7497C14.418 30.348 13.9329 30.833 13.3346 30.833H12.2513C11.653 30.833 11.168 30.348 11.168 29.7497V19.9997L13.8908 12.7389C14.2079 11.8933 15.0163 11.333 15.9195 11.333H28.0831C28.9863 11.333 29.7947 11.8933 30.1119 12.7389L32.8346 19.9997V29.7497C32.8346 30.348 32.3496 30.833 31.7513 30.833H30.668C30.0696 30.833 29.5846 30.348 29.5846 29.7497V28.6663ZM13.482 19.9997H30.5206L28.0831 13.4997H15.9195L13.482 19.9997ZM16.043 25.4163C16.9404 25.4163 17.668 24.6888 17.668 23.7913C17.668 22.8939 16.9404 22.1663 16.043 22.1663C15.1455 22.1663 14.418 22.8939 14.418 23.7913C14.418 24.6888 15.1455 25.4163 16.043 25.4163ZM27.9596 25.4163C28.8571 25.4163 29.5846 24.6888 29.5846 23.7913C29.5846 22.8939 28.8571 22.1663 27.9596 22.1663C27.0622 22.1663 26.3346 22.8939 26.3346 23.7913C26.3346 24.6888 27.0622 25.4163 27.9596 25.4163Z" fill="white"/></svg>',
    '<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 22C44 39.6 39.6 44 22 44C4.4 44 0 39.6 0 22C0 4.4 4.4 0 22 0C39.6 0 44 4.4 44 22Z" fill="#FFC20E"/><path fill-rule="evenodd" clip-rule="evenodd" d="M16.5833 16.5837C16.5833 13.5921 19.0085 11.167 22 11.167C24.9915 11.167 27.4167 13.5921 27.4167 16.5837C27.4167 19.2042 25.5557 21.3901 23.0833 21.892V27.417C23.0833 28.0153 22.5983 28.5003 22 28.5003C21.4017 28.5003 20.9167 28.0153 20.9167 27.417V21.892C18.4442 21.3901 16.5833 19.2042 16.5833 16.5837ZM18.3467 24.8418C18.6017 25.0474 18.75 25.3575 18.75 25.6852V27.417C18.75 29.212 20.205 30.667 22 30.667C23.795 30.667 25.25 29.212 25.25 27.417V25.6852C25.25 25.3575 25.3983 25.0474 25.6533 24.8418C25.9083 24.6362 26.2428 24.557 26.563 24.6264C27.9297 24.9229 29.1475 25.3623 30.053 25.9383C30.9176 26.4884 31.75 27.336 31.75 28.5003C31.75 29.6648 30.9176 30.5123 30.053 31.0624C29.1475 31.6384 27.9297 32.0778 26.563 32.3742C25.2057 32.6687 23.6482 32.8337 22 32.8337C20.3518 32.8337 18.7943 32.6687 17.437 32.3742C16.0702 32.0778 14.8525 31.6384 13.9471 31.0624C13.0824 30.5123 12.25 29.6648 12.25 28.5003C12.25 27.336 13.0824 26.4884 13.9471 25.9383C14.8525 25.3623 16.0702 24.9229 17.437 24.6264C17.7572 24.557 18.0916 24.6362 18.3467 24.8418Z" fill="white"/></svg>',
  ].obs;

  RxList<String> guaranteeIconListRPLC = [
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#FFC20E" fill-opacity="0.05"/><path d="M23.4343 27.4082C21.9869 27.4082 20.8027 28.5924 20.8027 30.0398V35.3687C20.8027 36.8161 21.9869 38.0003 23.4343 38.0003C24.8817 38.0003 26.0659 36.8161 26.0659 35.3687V30.0398C26.0659 28.5924 24.8817 27.4082 23.4343 27.4082Z" fill="#FFC20E"/><path d="M32.9735 11.2895H27.9077C27.3156 9.38158 25.5393 8 23.434 8C21.3287 8 19.5524 9.38158 18.9603 11.2895H13.9603C13.2366 11.2895 12.6445 11.8816 12.6445 12.6053C12.6445 13.3289 13.2366 13.9211 13.9603 13.9211H19.0261C19.3551 15.0395 20.0787 15.9605 21.0656 16.5526C17.513 17.6053 14.8814 20.8947 14.8814 24.7763V31.6842C14.8814 32.8684 15.8682 33.8553 17.0524 33.8553H18.8287V30.1053C18.8287 27.5394 20.934 25.4342 23.4998 25.4342C26.0656 25.4342 28.1708 27.5395 28.1708 30.1053V33.8553H29.9472C31.1314 33.8553 32.1182 32.8684 32.1182 31.6842V24.8421C32.1182 20.9605 29.4866 17.6053 25.934 16.6184C26.9208 16.0263 27.6445 15.1053 27.9734 13.9868H33.0392C33.7629 13.9868 34.355 13.3947 34.355 12.6711C34.2893 11.8816 33.6972 11.2895 32.9735 11.2895ZM23.434 15.3026C21.9866 15.3026 20.8024 14.1184 20.8024 12.6711C20.8024 11.2237 21.9866 10.0395 23.434 10.0395C24.8814 10.0395 26.0656 11.2237 26.0656 12.6711C26.1314 14.1184 24.9472 15.3026 23.434 15.3026Z" fill="#FFC20E"/></svg>',
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#FFC20E" fill-opacity="0.1"/><path d="M32.252 33H14.752V34.25C14.752 34.9404 14.1923 35.5 13.502 35.5H12.252C11.5616 35.5 11.002 34.9404 11.002 34.25V23L14.1436 14.6222C14.5095 13.6464 15.4423 13 16.4845 13H30.5195C31.5616 13 32.4943 13.6464 32.8603 14.6222L36.002 23V34.25C36.002 34.9404 35.4423 35.5 34.752 35.5H33.502C32.8116 35.5 32.252 34.9404 32.252 34.25V33ZM13.672 23H33.332L30.5195 15.5H16.4845L13.672 23ZM16.627 29.25C17.6625 29.25 18.502 28.4105 18.502 27.375C18.502 26.3395 17.6625 25.5 16.627 25.5C15.5914 25.5 14.752 26.3395 14.752 27.375C14.752 28.4105 15.5914 29.25 16.627 29.25ZM30.377 29.25C31.4125 29.25 32.252 28.4105 32.252 27.375C32.252 26.3395 31.4125 25.5 30.377 25.5C29.3415 25.5 28.502 26.3395 28.502 27.375C28.502 28.4105 29.3415 29.25 30.377 29.25Z" fill="#FFC20E"/></svg>',
    '<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M46.5 23C46.5 41.4 41.9 46 23.5 46C5.1 46 0.5 41.4 0.5 23C0.5 4.6 5.1 0 23.5 0C41.9 0 46.5 4.6 46.5 23Z" fill="#FFC20E" fill-opacity="0.1"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.25 16.75C17.25 13.2982 20.0482 10.5 23.5 10.5C26.9517 10.5 29.75 13.2982 29.75 16.75C29.75 19.7737 27.6027 22.2959 24.75 22.875V29.25C24.75 29.9404 24.1904 30.5 23.5 30.5C22.8096 30.5 22.25 29.9404 22.25 29.25V22.875C19.3972 22.2959 17.25 19.7737 17.25 16.75ZM19.2846 26.2786C19.5789 26.5159 19.75 26.8736 19.75 27.2517V29.25C19.75 31.3211 21.4289 33 23.5 33C25.5711 33 27.25 31.3211 27.25 29.25V27.2517C27.25 26.8736 27.4211 26.5159 27.7154 26.2786C28.0096 26.0414 28.3955 25.95 28.765 26.0301C30.342 26.3722 31.7471 26.8792 32.7919 27.5439C33.7895 28.1785 34.75 29.1565 34.75 30.5C34.75 31.8436 33.7895 32.8215 32.7919 33.4562C31.7471 34.1209 30.342 34.6279 28.765 34.9699C27.1989 35.3096 25.4017 35.5 23.5 35.5C21.5983 35.5 19.8012 35.3096 18.235 34.9699C16.658 34.6279 15.2529 34.1209 14.2082 33.4562C13.2105 32.8215 12.25 31.8436 12.25 30.5C12.25 29.1565 13.2105 28.1785 14.2082 27.5439C15.2529 26.8792 16.658 26.3722 18.235 26.0301C18.6045 25.95 18.9903 26.0414 19.2846 26.2786Z" fill="#FFC20E"/></svg>'
  ].obs;
  RxList<String> periodListCar = ['6', '12', '18', '24', '36'].obs;

  RxList<String> periodList = [
    '6',
    '12',
    '18',
    '24',
    '36',
    '48',
    '60',
    '72',
    '84',
  ].obs;

  RxString? loan_status = '1'.obs; //grant status 1,2,8

  RxBool? isAcceptedTermPolicy = false.obs;

  void calculateLoan(int loanAmount, String calculateType) {
    int total_loan = int.parse(loan_amount.value.text.isNotEmpty
        ? loan_amount.value.text.replaceAll(",", "")
        : '0');
    if (calculateType == 'plus') {
      total_loan += loanAmount;
      loan_amount.value.text = total_loan.toString();
    } else {
      if (loan_amount.value.text.isNotEmpty) {
        loan_amount.value.text = loan_amount.value.text
            .substring(0, loan_amount.value.text.length - 1);
      }
    }
    if (loan_amount.value.text.isNotEmpty) {
      final number =
          int.tryParse(loan_amount.value.text.replaceAll(',', '')) ?? 0;
      loan_amount.value.text = number.toString().replaceAllMapped(
            RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]},',
          );
    }
    update();
  }

  void selectGuarantee(String guarantee, int index) {
    if (selectedGuarantee.value != guarantee) {
      selectedGuarantee.value = guarantee;
      selectedGuaranteeIndex.value = index;
      selectedPeriod.value = '';
      selectedPeriodIndex.value = 0;
    } else {
      selectedGuarantee.value = guarantee;
      selectedGuaranteeIndex.value = index;
    }
    update();
    print(selectedGuarantee.value);
  }

  void selectPeriod(String period, int index) {
    selectedPeriod.value = period;
    selectedPeriodIndex.value = index;
    update();
    print(selectedPeriod.value);
  }

  void setLoanAmount(String amount) {
    String amountStr = amount.replaceAll(',', '');
    final number = int.tryParse(amountStr) ?? 0;
    String formatted = number.toString().replaceAllMapped(
          RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
    if (loan_amount.value.text != formatted) {
      int selectionIndex = formatted.length -
          (loan_amount.value.text.length -
              loan_amount.value.selection.baseOffset);
      if (selectionIndex < 0) selectionIndex = 0;
      loan_amount.value.value = TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: selectionIndex),
      );
    }
  }

  Future<dynamic> saveRequestLoan(context) async {
    try {
      final ProfileController profileCtl = Get.find<ProfileController>();
      final ContractListController contractListCtl =
          Get.find<ContractListController>();

      var phone_firebase = "";

      if (profileCtl.profile.value.phoneFirebase == null ||
          profileCtl.profile.value.phoneFirebase == '') {
        var phone = profileCtl.profile.value.phone.obs.string;
        var phoneSub = phone.substring(1, 10);
        phone_firebase = "+66$phoneSub";
      } else {
        phone_firebase = profileCtl.profile.value.phoneFirebase.obs.string;
      }

      Map data = {
        'firstname': profileCtl.profile.value.firstname.toString(),
        'lastname': profileCtl.profile.value.lastname.toString(),
        'phone': profileCtl.profile.value.phone.toString(),
        'phone_firebase': phone_firebase,
        'line': '-',
        'facebook': '-',
        'idcard': profileCtl.profile.value.idcard.toString(),
        'guarantee_type': selectedGuarantee.value,
        'request_loan': loan_amount.value.text,
        'request_installment': selectedPeriod.value,
        'province': profileCtl.profile.value.province.toString(),
        'district': profileCtl.profile.value.amphur.toString(),
        'branch': '',
        'image': '-',
      };
      print(data);

      AppLoading.loadingVerify(context);
      final response =
          await HttpService.callAPIjwt("POST", Endpoints.saveRequestLoan, data);
      print("API saveRequestLoan : ${Endpoints.saveRequestLoan}");
      print(response);

      if (response["status"] == 200) {
        // todo เช็คสถานะสินเชื่อ
        var chk_status = await contractListCtl.getContactStatus();

        if (chk_status == true) {
          //TODO set dufult status สำหรับรายการขอสินเชื่อล่าสุด
          await Get.put(ContractListController()).setInitStatusData(
              loan_amount.value.text,
              selectedPeriod.value,
              selectedGuarantee.value,
              true);
          AppLoading.Loaderhide(context);
          selectedGuarantee.value = '';
          loan_amount.value.text = '';
          selectedPeriod.value = '';
          isAcceptedTermPolicy!.value = false;
          update();
          Get.off(() => LoanStatus());
        } else {
          AppLoading.Loaderhide(context);
          debugPrint("ไม่สามารถเข้าสู่หน้าสถานะสินเชื่อได้");
          Get.to(() => HomeNavigator());
        }
      } else {
        AppLoading.Loaderhide(context);
        Get.snackbar("เกิดข้อผิดพลาด", "บันทึกข้อมูลไม่สำเร็จ",
            snackPosition: SnackPosition.TOP,
            colorText: configTheme().textTheme.bodyMedium!.color);
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      AppLoading.Loaderhide(context);
      debugPrint("API saveRequestLoan error : $exception");
    }
  }

  RxList<String> guaranteeList = [''].obs;
  RxList<String> guaranteeListDesc = [
    ''
    // menuGetLoanTypeCarDes.tr,
    // menuGetLoanTypeTruckDes.tr,
    // menuGetLoanTypeMotoDes.tr,
    // menuGetLoanTypeLandDes.tr
  ].obs;

  void checkBu(bu) {
    switch (bu) {
      case 'aam':
        guaranteeList = [
          menuGetLoanTypeCar.tr,
          menuGetLoanTypeTruck.tr,
          menuGetLoanTypeMoto.tr,
          menuGetLoanTypeLand.tr
        ].obs;
        guaranteeListDesc = [
          menuGetLoanTypeCarDes.tr,
          menuGetLoanTypeTruckDes.tr,
          menuGetLoanTypeMotoDes.tr,
          menuGetLoanTypeLandDes.tr
        ].obs;
        break;
      case 'rafco':
        guaranteeList = [
          menuGetLoanTypeCar.tr,
          menuGetLoanTypeMoto.tr,
          menuGetLoanTypeThree.tr,
          menuGetLoanTypeLand.tr
        ].obs;
        guaranteeListDesc = [
          menuGetLoanTypeCarDes.tr,
          menuGetLoanTypeMotoDes.tr,
          menuGetLoanTypeThreeDes.tr,
          menuGetLoanTypeLandDes.tr
        ].obs;
        break;
      case 'rplc':
        guaranteeList = [
          menuGetLoanTypeMoto.tr,
          menuGetLoanTypeCar.tr,
          menuGetLoanTypeLand.tr
        ].obs;
        guaranteeListDesc = [
          menuGetLoanTypeMotoDes.tr,
          menuGetLoanTypeCarDes.tr,
          menuGetLoanTypeLandDes.tr
        ].obs;
        break;
    }
  }

  Future<void> AcceptLoanAgreement() async {
    try {
      //todo ส่งข้อมูลไปยัง API
      await resetAcceptTermPolicy();
      Get.to(() => const LoanRequestScreen());
    } catch (error) {
      debugPrint("Error AcceptLoanAgreement : $error");
    }
  }

  void acceptTermPolicy() {
    isAcceptedTermPolicy!.value = !isAcceptedTermPolicy!.value;
    update();
  }

  Future<void> resetAcceptTermPolicy() async {
    isAcceptedTermPolicy!.value = false;
    update();
  }
}