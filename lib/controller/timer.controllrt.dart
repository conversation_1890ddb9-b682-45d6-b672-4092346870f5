
import 'package:get/get.dart';

class TimerController extends GetxController{
  RxBool timeFinish = false.obs;
  RxBool onTime = false.obs;
  RxInt seconds = 0.obs;

  void startTimer(){
    timeFinish.value = false;
    update();
    Timer();
  }

  void stopTimer() {
    timeFinish.value = true;
    update();
  }

  Future<void> Timer() async {
    for (int i = 0; i < 4; i++) {
      if (i == 2) {
        onTime.value = true;
      } else if (i == 0) {
        onTime.value = false;
      }
      if (timeFinish.isFalse) {
        {
          await Future.delayed(Duration(milliseconds: 500), () {
            seconds.value = i;
            update();
            // print('${seconds} sec');
          });
        }
      }
    }
    if (timeFinish.isFalse) {
      Timer();
    }
  }
}




