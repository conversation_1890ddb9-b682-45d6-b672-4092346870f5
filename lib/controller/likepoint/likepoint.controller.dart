import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/service/AppUrl.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../service/endpoint.dart';

/** LikePoint1.0 Controller **/

class LikePointController extends GetxController {
  final ProfileController profileController = Get.find<ProfileController>();

  RxString? likePoint = "0".obs;
  RxString likePointBath = "0".obs;
  RxBool? statusLike = false.obs;
  RxString? totalBalance = RxString('');

  Future<dynamic> getBalanceByPhoneNumber() async {
    try {
      String chkPhone =
          profileController.profile.value.phoneFirebase.obs.string;
      if (chkPhone == "") {
        return false;
      } else {
        Map data = {"phoneNumber": chkPhone};
        final response = await HttpService.post(
            Endpoints.getBalanceByPhoneNumber,
            data,
            HttpService.noKeyRequestHeaders);
        var balance = response;
        if (balance['status'] == 404) {
          Map<String, dynamic> jsonLikePoint = {
            "status": false,
            "likePointConnect": "NotConnect",
            "number": "คุณไม่มี LikePoint",
            "msg": "คุณไม่มี LikePoint"
          };
          likePoint!.value = "0";
          likePointBath.value = "0";
          update();
          return jsonLikePoint;
        } else {
          var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
          Map<String, dynamic> jsonLikePoint = {
            "status": true,
            "likePointConnect": "Connect",
            "number": f.format(balance["balance"]),
            "msg": "LikePoint ${f.format(balance["balance"])} LIKE",
            "available": f.format(balance["availableBalance"])
          };

          // debugPrint("likePoint response => ${balance.toString()}");

          var like_bath_value = int.parse(removeDecimalPart(balance["balance"].toString())) / 100;
          // var like_bath = like_bath_value.toStringAsFixed(2);
          likePointBath.value = f.format(like_bath_value);
          likePoint!.value = jsonLikePoint['number'];
          statusLike!.value = jsonLikePoint['status'];
          totalBalance!.value = jsonLikePoint['number'];
          update();

          // debugPrint("likePoint => ${likePoint!.value}");
          // debugPrint("likePointBath => ${likePointBath.value}");
          return jsonLikePoint;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getLikePoint =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  String removeDecimalPart(String input) {
    final parts = input.split(".");
    return parts[0];
  }
}
