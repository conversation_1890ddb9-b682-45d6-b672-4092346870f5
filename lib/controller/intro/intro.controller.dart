import 'package:get/get.dart';

class IntroController extends GetxController{

  RxInt? indexPage = 0.obs;
  RxBool? isFinalPage = false.obs;

  void setIndexPage(int index) {
    indexPage = index.obs;
    update();
  }

  void setNextage() {
    if(indexPage!.value != 2){
      indexPage!.value = indexPage!.value +1;
      update();
    }
  }
  void setFinalPage() {
    isFinalPage!.value = true;
    update();
  }
}