import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../app_config.dart';

class AppConfigService extends GetxService {
  BuildContext? context;

  // Manual configuration parameters
  Environment? manualEnvironment;
  String? manualCountryConfigCollection;
  String? manualAppTitle;
  String? manualConfigCloudflareAPI;

  AppConfigService({
    this.context,
    this.manualEnvironment,
    this.manualCountryConfigCollection,
    this.manualAppTitle,
    this.manualConfigCloudflareAPI,
  });

  late String countryConfigCollection;
  late Environment environment;
  late String appTitle;
  late String configCloudflareAPI;

  @override
  void onInit() {
    super.onInit();
    print('AppConfigService initializing...');

    // Use manual values if provided, otherwise get from AppConfig
    if (manualEnvironment != null &&
        manualCountryConfigCollection != null &&
        manualAppTitle != null &&
        manualConfigCloudflareAPI != null) {

      environment = manualEnvironment!;
      countryConfigCollection = manualCountryConfigCollection!;
      appTitle = manualAppTitle!;
      configCloudflareAPI = manualConfigCloudflareAPI!;

      print('AppConfigService initialized with manual values');
    } else if (context != null) {
      countryConfigCollection = AppConfig.of(context!).countryConfigCollection;
      environment = AppConfig.of(context!).environment;
      appTitle = AppConfig.of(context!).appTitle;
      configCloudflareAPI = AppConfig.of(context!).pahtConfigCloudflareAPI;

      print('AppConfigService initialized with context values');
    } else {
      print('WARNING: AppConfigService initialized without context or manual values');
      // Set default values to prevent null errors
      environment = Environment.rplc_prod; // Default to RPLC production
      countryConfigCollection = 'rplc';
      appTitle = 'Default App Title';
      configCloudflareAPI = 'RPLCp3';
    }

    print('AppConfigService configuration:');
    print('- environment: $environment');
    print('- countryConfigCollection: $countryConfigCollection');
    print('- appTitle: $appTitle');
    print('- configCloudflareAPI: $configCloudflareAPI');
  }
}
