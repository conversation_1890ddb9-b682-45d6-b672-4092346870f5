import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TransalationController extends GetxController {
  final List locale = [
    {'name': 'English', 'locale': Locale('en', 'US')},
    {'name': 'Thailand', 'locale': Locale('th', 'Th')},
    {'name': 'Cambodian', 'locale': Locale('km', 'Km')},
    {'name': 'Lao', 'locale': Locale('lo', 'La')},
  ];

  RxString location = "".obs;
  RxList listLanguage = [].obs;

  updateLanguage(Locale locale) {
    // Get.back();
    Get.updateLocale(locale);
  }

  checkLocation(index) {
    if (index == 0) {
      location.value = 'EN';
    } else if (index == 1) {
      location.value = "TH";
    } else if (index == 2) {
      location.value = 'KM';
    } else if (index == 3) {
      location.value = 'LO';
    }
    // print('index: $index');
    // print(location.value);
    update();
  }

  showListLanguage(appName) {
    switch (appName) {
      case 'aam':
        listLanguage.value = [
          {'name': 'English', 'locale': Locale('en', 'US')},
          {'name': 'Thailand', 'locale': Locale('th', 'Th')}
        ];
        // location.value = "Thailand";
        update();
        break;
      case 'rafco':
        listLanguage.value = [
          {'name': 'English', 'locale': Locale('en', 'US')},
          {'name': 'Thailand', 'locale': Locale('th', 'Th')},
          {'name': 'Cambodian', 'locale': Locale('km', 'Km')}
        ];
        // location.value = "Cambodian";
        update();
        break;
      case 'rplc':
        listLanguage.value = [
          {'name': 'English', 'locale': Locale('en', 'US')},
          {'name': 'Thailand', 'locale': Locale('th', 'Th')},
          {'name': 'Lao', 'locale': Locale('lo', 'La')}
        ];
        // location.value = "Lao";
        update();
        break;
    }
    // update();
  }


}
