import 'package:AAMG/controller/bill_payment/billPayment.controller.dart';
import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/controller/notification/notify.controller.dart';
import 'package:AAMG/controller/steam.controller.dart';
import 'package:get/get.dart';
import 'AppConfigService.dart';
import 'aampay/aampay.controller.dart';
import 'bill_payment/bankingPayments.controller.dart';
import 'config/appConfig.controller.dart';
import 'config/appVersion.controller.dart';
import 'contract/contractlist.controller.dart';
import 'home/home.controller.dart';
import 'home/policy.controller.dart';
import 'intro/intro.controller.dart';
import 'kyc/kyc.controller.dart';
import 'likepoint/webview.point.controller.dart';
import 'login/login.controller.dart';
import 'mr/mr.controller.dart';
import 'navigator.controller.dart';
import 'news_promotions/news.controller.dart';
import 'notification/notification.controllet.dart';
import 'pincode/pincode.controller.dart';
import 'profile/delete.account.controller.dart';
import 'profile/profile.controller.dart';
import 'register/register.controller.dart';
import 'register/registerAddress.controller.dart';
import 'register/registerVerify.controller.dart';
import 'register/scan.controller.dart';
import 'request_loan/loan.controller.dart';
import 'dailyactiveusers/dailyactiveusers.controller.dart';

class BilndingsApp implements Bindings{
  @override
  void dependencies() {
    //Get.lazyPut(() => Controller(), fenix: true)  **Controller ที่ใช้งานน้อยหรือสามารถลบทิ้งได้เมื่อไม่จำเป็น
    Get.lazyPut(() => AppConfigService(context: Get.context!));
    Get.lazyPut(() => IntroController(), fenix: true);
    Get.lazyPut(() => RegisterController(), fenix: true);
    Get.lazyPut(() => RegisterVerifyController(), fenix: true);
    // Get.lazyPut(() => PincodeController());
    // Get.lazyPut(() => NotifyController());
    Get.lazyPut(() => SteamEvent(), fenix: true);
    // Get.lazyPut(() => ProfileController(), fenix: true);
    Get.lazyPut(()=>ProfileController());
    // Get.lazyPut(() => HomeController());
    Get.lazyPut(() => MRController(), fenix: true);
    Get.lazyPut(() => DeleteAccountController(), fenix: true);
    Get.lazyPut(() => LoginController());
    Get.lazyPut(() => NewsController());
    Get.lazyPut(() => LoanController());
    Get.lazyPut(() => PolicyController());
    Get.lazyPut(() => NotificationController(), fenix: true);
    Get.lazyPut(() => RegisterAddressController());
    Get.lazyPut(() => MyloanController());
    Get.lazyPut(() => ScanController());
    Get.lazyPut(() => AppConfigController());
    Get.lazyPut(() => ContractListController());
    Get.lazyPut(() => KYCController());
    Get.lazyPut(() => AAMPayController(), fenix: false); //ใช้ listener กระบวนการสร้างสัญญา จาก AAMPayController ในหน้าอื่นๆ
    Get.lazyPut(() => ChatInAppController());
    Get.lazyPut(() => SteamEvent());
    Get.lazyPut(() => BankingPaymentController());
    Get.lazyPut(() => BillPaymentController());
    Get.lazyPut(() => WebViewPointController());
    Get.lazyPut(() => AppVersionController());
    Get.lazyPut(() => DailyActiveUsersController(), fenix: true);
    Get.put(LoanController());
  }
}