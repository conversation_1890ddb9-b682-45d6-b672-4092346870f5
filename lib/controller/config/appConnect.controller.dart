import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';

class AppConnectController extends GetxController {
  final controllerBiometric = ValueNotifier<bool>(false);
  final controllerLine = ValueNotifier<bool>(false);
  final controllerFacebook = ValueNotifier<bool>(false);
  final controllerTelegram = ValueNotifier<bool>(false);
  final controllerApple = ValueNotifier<bool>(false);
  final controllerGoogle = ValueNotifier<bool>(false);
  final controllerNotification = ValueNotifier<bool>(false);

  @override
  void onInit() {
    super.onInit();
    checkBiometric();
    checkNotification();
    Future.delayed(Duration.zero, () {
      // checkLine();
      checkFacebook();
      // checkTelegram();
      // checkApple();
      // checkGoogle();
    });
  }

  Future<void> checkBiometric() async {
    print("checkBiometric");
    print(Get.find<AppConfigController>().isOpenBiometrics.value);
    if (Get.find<AppConfigController>().isOpenBiometrics.value) {
      controllerBiometric.value = true;
    } else {
      controllerBiometric.value = false;
    }
    update();
  }

  Future<void> checkLine() async {
    controllerLine.value = true;
  }

  Future<void> checkFacebook() async {
    if (Get.find<ProfileController>().profile.value.facebook_connect.toString().toUpperCase() == 'Y') {
      controllerFacebook.value = true;
    }else{
      controllerFacebook.value = false;
    }
  }

  Future<void> checkTelegram() async {
    controllerTelegram.value = true;
  }

  Future<void> checkApple() async {
    controllerApple.value = true;
  }

  Future<void> checkGoogle() async {
    controllerGoogle.value = true;
  }

  Future<void> checkNotification() async {
    if (Get.find<AppConfigController>().isOpenNotification.value == true) {
      controllerNotification.value = true;
    } else {
      controllerNotification.value = false;
    }
    update();
  }


}
