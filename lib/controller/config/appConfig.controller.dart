import 'dart:io';

import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../models/appconfig_model.dart';
import '../../service/http_service.dart';

class AppConfigController extends GetxController {
  Rx<ResponseAppConfig> appConfig = ResponseAppConfig().obs;
  Rx<ResponseMobileOS> appOS = ResponseMobileOS().obs;
  RxString? deviceOS = RxString('');
  RxBool isOpenBiometrics = false.obs;
  RxBool isOpenNotification = false.obs;
  RxBool isOpenContacts = false.obs;
  var box = GetStorage();
  RxString? currencySymbol = RxString('');
  RxString? currencyName = RxString('');
  RxList? popupList = [].obs;

  @override
  void onInit() async {
    super.onInit();
    setInitialBiometrics();
    checkAppConfig();
    update();
  }

  Future<dynamic> checkAppConfig() async {
    try {
      Map data = {};

      final response =
          await HttpService.callAPICloudflare("POST", "checkAppConfig", data);

      // print(response);
      // print("#########");

      if (response['status'].toString() == "200") {
        appConfig = ResponseAppConfig.fromJson(response['result'][0]).obs;
      }
      update();
      await setCurrencySymbolByAPP();
      await getAppHomePopUp();
    } catch (e) {
      if (kDebugMode) {
        print('checkAppConfig => ${e}');
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getAppHomePopUp() async {
    try {
      Map map = {"version": "new"};

      final jsonResponse = await HttpService.callAPICloudflare("POST", "popupshow", map);

      if (jsonResponse["status"] == 200) {
        popupList!.clear();
        update();
        for (var i = 0; i < jsonResponse["result"].length; i++) {
          popupList!.add(jsonResponse["result"][i]["notify_pic"].toString());
        }
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print('checkAppConfig => ${e}');
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }


  Future<void> setCurrencySymbolByAPP() async {
    try {
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        currencySymbol!.value = "฿";
        currencyName!.value = "บาท";
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        currencySymbol!.value = "\u20AD";
        // currencySymbol!.value = "₭";
        currencyName!.value = "KIP";
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        currencySymbol!.value = "\$";
        currencyName!.value = "\$";
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print('setCurrencySymbolByAPP => ${e}');
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<bool> checkNotificationPermission() async {
    print('checkNotificationPermission');
    // final notificationPermissionStatus = await Permission.notification.status;
    final status = await Permission.notification.status;

    // ถ้าได้รับอนุญาตแล้ว
    if (status.isGranted) {
      // if (await Permission.notification.request().isGranted) {
      debugPrint('notificationPermissionStatus: true');
      isOpenNotification.value = true;
      update();
      return true;
    } else {
      debugPrint('notificationPermissionStatus: false');
      isOpenNotification.value = false;
      update();
      return false;
    }
  }

  Future<bool> checkContractsPermission() async {
    print('checkContractsPermission');
    // final notificationPermissionStatus = await Permission.contacts.status;

    final status = await Permission.contacts.status;

    // ถ้าได้รับอนุญาตแล้ว
    if (status.isGranted) {
      // if (await Permission.contacts.request().isGranted) {
      debugPrint('notificationPermissionStatus: true');
      isOpenContacts.value = true;
      update();
      return true;
    } else {
      debugPrint('notificationPermissionStatus: false');
      isOpenContacts.value = false;
      update();
      return false;
    }
  }

  Future<void> allowContractPermission() async {
    try {
      // ตรวจสอบสถานะการอนุญาตปัจจุบันสำหรับการเข้าถึง Contacts
      final status = await Permission.contacts.status;

      // ถ้าได้รับอนุญาตแล้ว
      if (status.isGranted) {
        debugPrint('Permission already granted');
      }
      // ถ้าไม่อนุญาตหรืออนุญาตอย่างจำกัด
      else if (status.isDenied || status.isLimited) {
        // ร้องขอการอนุญาตใหม่
        final result = await Permission.contacts.request();
        // ถ้าได้รับอนุญาตหลังจากร้องขอ
        if (result.isGranted) {
          debugPrint('Permission granted after request');
        }
        // ถ้าถูกปฏิเสธถาวร
        else if (result.isPermanentlyDenied) {
          debugPrint('Permission is permanently denied');
          // เปิดการตั้งค่าแอปให้ผู้ใช้เปลี่ยนการตั้งค่าด้วยตนเอง
          await openAppSettings();
        } else {
          await openAppSettings();
        }
      }
      // ถ้าถูกปฏิเสธถาวรและไม่ได้ร้องขอใหม่
      else if (status.isPermanentlyDenied) {
        debugPrint('Permission is permanently denied');
        // เปิดการตั้งค่าแอปให้ผู้ใช้เปลี่ยนการตั้งค่าด้วยตนเอง
        if (Platform.isAndroid) {
          await openAppSettings();
        }
      } else {
        await openAppSettings();
      }
    } catch (e) {
      // จัดการข้อผิดพลาดที่อาจเกิดขึ้นในระหว่างกระบวนการร้องขอการอนุญาต
      debugPrint('Error when requesting permissions: $e');
    }
  }

  Future<dynamic> allowContractPermission_() async {
    try {
      await Permission.contacts.request();

      if (await Permission.contacts.request().isGranted) {
        debugPrint('permission granted');
      } else if (await Permission.contacts.request().isPermanentlyDenied) {
        //TODO ยอมรับแล้วแต่ถูกปฏิเสธ ให้เปิดการตั้งค่า
        await openAppSettings();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> allowNotificationPermission_() async {
    try {
      await Permission.notification.request();

      if (await Permission.notification.request().isGranted) {
        debugPrint('permission granted');
      } else if (await Permission.notification.request().isPermanentlyDenied) {
        //TODO ยอมรับแล้วแต่ถูกปฏิเสธ ให้เปิดการตั้งค่า
        await openAppSettings();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> allowNotificationPermission() async {
    try {
      print('allowNotificationPermission');
      // ตรวจสอบสถานะการอนุญาตปัจจุบันสำหรับการแจ้งเตือน
      final status = await Permission.notification.status;

      print('status: $status');
      print(status.isDenied);

      // ถ้าได้รับอนุญาตแล้ว
      if (status.isGranted) {
        debugPrint('Permission already granted');
      }
      // ถ้าไม่อนุญาตหรืออนุญาตอย่างจำกัด
      else if (status.isDenied || status.isLimited) {
        // ร้องขอการอนุญาตใหม่
        final result = await Permission.notification.request();
        print("fdfdffdfdffd");
        print(result);
        // ถ้าได้รับอนุญาตหลังจากร้องขอ
        if (result.isGranted) {
          debugPrint('Permission granted after request');
        }
        // ถ้าถูกปฏิเสธถาวร
        else if (result.isPermanentlyDenied) {
          debugPrint('Permission is permanently denied');
          // เปิดการตั้งค่าแอปให้ผู้ใช้เปลี่ยนการตั้งค่าด้วยตนเอง
          await openAppSettings();
        } else {
          await openAppSettings();
        }
      }
      // ถ้าถูกปฏิเสธถาวรและไม่ได้ร้องขอใหม่
      else if (status.isPermanentlyDenied) {
        debugPrint('Permission is permanently denied');
        // เปิดการตั้งค่าแอปให้ผู้ใช้เปลี่ยนการตั้งค่าด้วยตนเอง
        if (Platform.isAndroid) {
          await openAppSettings();
        }
      } else {
        debugPrint('else');
        // เปิดการตั้งค่าแอปให้ผู้ใช้เปลี่ยนการตั้งค่าด้วยตนเอง
        await openAppSettings();
      }
    } catch (e) {
      // จัดการข้อผิดพลาดที่อาจเกิดขึ้นในระหว่างกระบวนการร้องขอการอนุญาต
      debugPrint('Error when requesting permissions: $e');
    }
  }

  Future<void> requestPermission(Permission permission) async {
    final status = await permission.request();
    print(status);
  }

  Future<dynamic> checkBiometrics() async {
    final LocalAuthentication auth = LocalAuthentication();
    bool canCheckBiometrics = await auth.canCheckBiometrics;
    debugPrint('canCheckBiometrics: $canCheckBiometrics');
    if (!canCheckBiometrics) {
      await openAppSettings();
    } else {
      print('เปิดใช้งานแล้ว');
      await openAppSettings();
    }
  }

  Future<dynamic> checkPermissionBio() async {
    print('kIsWeb: $kIsWeb');
    if (!kIsWeb) {
      final LocalAuthentication auth = LocalAuthentication();
      bool canCheckBiometrics = await auth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        setBiometrics(false);
      }
    }
  }

  Future<void> setInitialBiometrics() async {
    var value = await box.read("isOpenBiometrics");
    isOpenBiometrics.value = value;
    update();
  }

  Future<void> setBiometrics(bool value) async {
    box.write("isOpenBiometrics", value);
    isOpenBiometrics.value = value;
    update();
  }

  Future<bool> checkAndRequestNotificationPermission(
      BuildContext context) async {
    final notificationPermissionStatus = await Permission.notification.status;

    if (notificationPermissionStatus.isGranted) {
      return true;
    } else if (notificationPermissionStatus.isDenied) {
      final newPermissionStatus = await Permission.notification.request();
      if (newPermissionStatus.isGranted) {
        return true;
      } else {
        // เปิดหน้าการตั้งค่าแอปเพื่อให้ผู้ใช้สามารถอนุญาตได้
        _openAppSettings(context);
        return false;
      }
    } else {
      // ในกรณีอื่น ๆ เช่น ถูกจำกัดสิทธิ์จากระบบ
      _openAppSettings(context);
      return false;
    }
  }

  Future<void> _openAppSettings(BuildContext context) async {
    // เปิดหน้าการตั้งค่าแอป
    await openAppSettings().then((_) {
      checkAccessAfterSetting(context);
    });
  }

  Future<void> checkAccessAfterSetting(context) async {
    final notificationPermissionStatus = await Permission.notification.status;
    if (notificationPermissionStatus.isDenied) {
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text('กรุณาเปิดการแจ้งเตือนในการตั้งค่าแอป'),
      //   ),
      // );
    }
  }

  Future<dynamic> accessLocation(context) async {
    PermissionStatus status = await Permission.location.status;

    Permission.locationWhenInUse.request().then((value) => {print(value)});

    if (status.isDenied || status.isPermanentlyDenied) {
      // AppAlert.showAlert(context, 'ไม่สามารถเข้าถึงตำแหน่ง',
      //     'กรุณาเปิดการเข้าถึงตำแหน่งผู้ใช้งาน', 'ตกลง');
      // await alertDialogV3(context, 'ไม่สามารถเข้าถึงตำแหน่ง', 'กรุณาเปิดการเข้าถึงตำแหน่งผู้ใช้งาน');
      openAppSettings();
    }
    if (status.isRestricted) {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.location,
      ].request();
    }

    return status;
  }
}
