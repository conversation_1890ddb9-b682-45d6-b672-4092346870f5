import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import 'appConfig.controller.dart';

class AppVersionController extends GetxController{

  RxString? versionInApp = RxString('');
  RxString? versionInStore = RxString('');

  RxBool? newVersion = false.obs;
  RxBool? hms = false.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    Future.delayed(Duration.zero, () {
      checkVersionInApp().then((value) =>
          checkVersionInStore());
    });
  }

  PackageInfo _packageInfo = PackageInfo(
    appName: 'Unknown',
    packageName: 'Unknown',
    version: 'Unknown',
    buildNumber: 'Unknown',
    buildSignature: 'Unknown',
  );


  Future<dynamic> checkVersionInApp() async{
    try{
      if(!kIsWeb){
        _packageInfo = await PackageInfo.fromPlatform();
        versionInApp!.value = _packageInfo.version;
        update();
        print("versionInApp : ${versionInApp!.value}") ;
      }
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkHuawei()async{
    try{
      // await FlutterHmsGmsAvailability.isHmsAvailable.then((t) {
      //     hms.value = t;
      // });
      update();
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkVersionInStore() async{
    try{
      // if(appConfigCtl.appOS.value.deviceOS == "android"){
      //
      //   final getVersion = AppVersionChecker(appId: "com.aamfinancegroup.aam");
      //   await Future.wait([
      //     getVersion
      //         .checkUpdate()
      //         .then((value) => versionInStore!.value = value.newVersion.toString()),
      //   ]);
      //
      //   // print("versionInStore : ${versionInStore!.value}") ;
      // }else if(appConfigCtl.appOS.value.deviceOS == "ios"){
      //
      //   String url = "https://itunes.apple.com/lookup?id=1521965273";
      //   final response = await apiGetRequest(Uri.parse(url));
      //   versionInStore!.value = response['results'][0]['version'].toString();
      // }else{
      //   // print("web");
      // }
      // update();
      // await checkVersionUpdate();
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic>checkVersionUpdate() async{
    // try{
    //
    //   final profileCtl = Get.put(ProfileController());
    //   final find = '.';
    //   final replaceWith = '';
    //
    //   final versionInAppCheck = versionInApp!.value.replaceAll(find, replaceWith);
    //   final versionInStoreCheck = versionInStore!.value.replaceAll(find, replaceWith);
    //
    //   var versionInAppCheckInt = int.parse(versionInAppCheck);
    //   var versionInStoreCheckInt = int.parse(versionInStoreCheck);
    //
    //
    //   if (versionInAppCheckInt >= versionInStoreCheckInt || profileCtl.profile.value.aamfinance_notify.obs.isNotEmpty!) {
    //     print("ไม่มีเวอร์ชั่นใหม่");
    //     newVersion!.value = false;
    //   } else {
    //     print("มีเวอร์ชั่นใหม่ '${versionInStore!.value}'");
    //     newVersion!.value = true;
    //   }
    //   update();
    //
    // } catch(e){
    //   if (kDebugMode) {
    //     print(e);
    //   }
    //   const GetSnackBar(
    //     title: "เกิดข้อผิดพลาด",
    //     message: "เกิดข้อผิดพลาด",
    //     duration: Duration(seconds: 3),
    //   );
    // }
  }

  urlAndroid() async {
    final Uri url = Uri.parse('https://play.google.com/store/apps/details?id=com.aamfinancegroup.aam');
    if (await launchUrl(url)) {
      await launchUrl(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  urlhuawei() async {
    final Uri url = Uri.parse('https://appgallery.huawei.com/app/C103568577');
    if (await launchUrl(url)) {
      await launchUrl(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  urlIOS() async {
    final Uri url = Uri.parse('https://apps.apple.com/us/app/%E0%B9%80%E0%B8%AD%E0%B9%80%E0%B8%AD%E0%B9%80%E0%B8%AD-%E0%B8%A1-%E0%B8%88-%E0%B8%94%E0%B9%84%E0%B8%9F%E0%B9%81%E0%B8%99%E0%B8%99%E0%B8%8B/id1521965273');
    if (await launchUrl(url)) {
      await launchUrl(url);
    } else {
      throw 'Could not launch $url';
    }
  }



}