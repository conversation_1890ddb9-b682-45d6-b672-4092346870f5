// import 'dart:ffi';

import 'package:AAMG/models/branch/branch_model.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../transalation/translation_key.dart';
import '/models/social_content_model.dart';
import '../AppConfigService.dart';


class BranchController extends GetxController {
  RxList<Branch>? branchDataList = <Branch>[].obs; // ข้อมูลสาขาสำหรับ sort และ search แสดงหน้า UI
  RxList<Branch>? branchInitList = <Branch>[].obs; // ข้อมูลสาขาโหลดครั้งแรก
  RxString selectedZone = ''.obs;
  RxBool isActiveSelectZone = false.obs;
  RxBool isActiveSearchZone = false.obs;
  RxInt itemsToShow = 10.obs; // จำนวนรายการที่ต้องการแสดงทีละ 10
  Rx<TextEditingController> searchController = TextEditingController().obs;
  BitmapDescriptor? customIcon;
  FocusNode searchFocusNode = FocusNode();
  var socialContents = <SocialContentModel>[].obs; 

List<SocialContentModel> _getAAMData() {
    return [
      SocialContentModel(
        appname: "AAM",
        branch: "Bangkok",
        platform: "facebook",
        title: "Facebook",
        content: "AAM จัดไฟแนนซ์",
        url: "https://www.facebook.com/AAM.Financing",
        timestamp: "2024-06-17 13:00:00",
      ),
      SocialContentModel(
          appname: "AAM",
          branch: "Bangkok",
          platform: "tiktok",
          title: "Tiktok",
          content: "AAM จัดไฟแนนซ์",
          url: "https://www.tiktok.com/@aam.financing",
          timestamp: "2024-06-17 13:00:00",
        ),
        SocialContentModel(
          appname: "AAM",
          branch: "Bangkok",
          platform: "line",
          title: "Line Official",
          content: "AAM จัดไฟแนนซ์",
          url: "https://line.me/R/ti/p/~@aamcenter",
          timestamp: "2024-06-17 13:00:00",
        ),
        SocialContentModel(
          appname: "AAM",
          branch: "Bangkok",
          platform: "youtube",
          title: "YouTube",
          content: "AAM จัดไฟแนนซ์",
          url: "https://www.youtube.com/@AAM.Financing",
          timestamp: "2024-06-17 13:00:00",
        ),
    ];
  }

  List<SocialContentModel> _getRPLCData() {
    return [
      SocialContentModel(
        appname: "RPLC",
        branch: "",
        platform: "facebook",
        title: "Facebook",
        content: "RPLC",
        url: "https://www.facebook.com/rplcrpm",
        timestamp: "2024-06-17 13:00:00",
      ),
      SocialContentModel(
          appname: "RPLC",
          branch: "",
          platform: "tiktok",
          title: "Tiktok",
          content: "RPLC",
          url: "https://www.tiktok.com/@rplc_pmm",
          timestamp: "2024-06-17 13:00:00",
        ),
        SocialContentModel(
          appname: "RPLC",
          branch: "",
          platform: "whatsapp",
          title: "whatsapp Official",
          content: "RPLC",
          url: "https://whatsapp.com/channel/0029VawRWSc6mYPVcwJtTO1X",
          timestamp: "2024-06-17 13:00:00",
        ),
    ];
  }

  List<SocialContentModel> _getRAFCOData() {
    return [
      SocialContentModel(
        appname: "RAFCO",
        branch: "",
        platform: "facebook",
        title: "Facebook",
        content: "RAFCO",
        url: "https://www.facebook.com/RAFCOMICROFINANCE",
        timestamp: "RAFCO",
      ),
      SocialContentModel(
          appname: "RAFCO",
          branch: "",
          platform: "tiktok",
          title: "Tiktok",
          content: "RAFCO",
          url: "https://www.tiktok.com/@rafcopnp?lang=th-TH",
          timestamp: "2024-06-17 13:00:00",
        ),
        SocialContentModel(
          appname: "RAFCO",
          branch: "",
          platform: "telegram",
          title: "Telegram",
          content: "RAFCO",
          url: "https://t.me/rafcocambodia",
          timestamp: "2024-06-17 13:00:00",
        ),
       
    ];
  }


  @override
  void onInit() {
    super.onInit();
    Future.delayed(Duration.zero, () {
      print('onInit');
      getBranchData();
    });
  }

  Future<dynamic> getBranchData() async {
    try {
      // print('getBranchData');
      branchDataList!.clear();
      branchInitList!.clear();
      update();
      Map data = {};
      final response =
          await HttpService.callAPIjwt('POST', Endpoints.getBranchData, data);

      // print(Endpoints.getBranchData);
      // print("ddsdsdsdsds");
      // print(response['result']);
      if (response['status'] == 200) {
        // branchDataList!.add(Branch.fromJson(response['result']));
        branchDataList!.value = (response['result'] as List)
            .map((e) => Branch.fromJson(e))
            .toList();
        branchInitList!.value = (response['result'] as List)
            .map((e) => Branch.fromJson(e))
            .toList();
        update();

        // print('branchDataList : ${branchDataList!.length}');
        // print('branchInitList : ${branchInitList!.length}');

        // setIntialSocialMediaData();

      } else {
        debugPrint('ไม่พบข้อมูลสาขา');
        // getBranchData();
      }
    } catch (exception) {
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic>setIntialBranchData() async{
    // print('setIntialBranchData');
    // print(branchInitList!.length);
    branchDataList!.clear();
    if(branchInitList!.length == 0){
      await getBranchData();
    }else{
      branchDataList!.value = branchInitList!;
      update();
    }
  }

 Future<void> setIntialSocialMediaData() async {
    try {
      List<SocialContentModel> data = [];
      final AppConfigService appConfig = Get.find<AppConfigService>();

      switch (appConfig.countryConfigCollection.toString()) {
        case "aam":
          data = _getAAMData();
          break;
        case "rplc":
          data = _getRPLCData();
          break;
        case "rafco":
          data = _getRAFCOData();
          break;
        default:
          if (kDebugMode) {
            print("Unsupported country configuration");
          }
      }

      socialContents.assignAll(data); // อัพเดตค่าให้กับ socialContents
    } catch (exception) {
      if (kDebugMode) {
        print(exception);
      }
    }
  }

  Future<dynamic> searchBranchData(String keyword) async {
    try {
      branchDataList!.clear();
      update();
      Map data = {
        'keyword': keyword,
        'offset': 0,
      };
      print(data);
      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.searchBranchData, data);

      // print(Endpoints.searchBranchData);
      // print(response);
      if (response['status'] == 200) {
        branchDataList!.value =
            (response['result'] as List).map((e) => Branch.fromJson(e)).toList();
        update();
        // print(branchDataList);
      } else {
        debugPrint('ไม่พบข้อมูลสาขาจากการ search : $keyword');
         GetSnackBar(
          message: searchBranchFailed.tr,
          duration: const Duration(seconds: 3),
        );
        setIntialBranchData();
      }
    } catch (exception) {
      setIntialBranchData();
      if (kDebugMode) {
        print(exception);
      }
       GetSnackBar(
        title: "error",
        message: searchBranchFailed.tr,
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sortBranchDataByZone() async {
    try {
      // คัดกรองข้อมูลใน branchDataList ที่ zone ตรงกับ selectedZone
      branchDataList!.value = branchInitList!
          .where((element) => element.zone == selectedZone.value)
          .toList();

      // อัพเดต UI ด้วยการใช้ GetX
      update();
      // print(branchDataList!.first.branchName);
    } catch (exception) {
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void setActivateSelectZone(value) {
    isActiveSelectZone.value = value;
    update();
  }

  void setActivateSearchZone(value) {
    isActiveSearchZone.value = value;
    update();
  }

  void setSelectedZone(String zone) {
    selectedZone.value = zone;
    update();
    sortBranchDataByZone();
  }

  void clearConfigData(){
    selectedZone.value = '';
    isActiveSelectZone.value = false;
    isActiveSearchZone.value = false;
    itemsToShow.value = 10;
    searchController.value.clear();
    update();
  }


}
