import 'dart:async';
import 'dart:convert';
import 'package:AAMG/controller/chatinapp/register.TG.controller.dart';
import 'package:AAMG/controller/notification/notify.controller.dart';
import 'package:AAMG/controller/steam.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/chatinapp_popus.dart';
import 'package:AAMG/view/screen/chatinapp/connectTG.dart';
import 'package:AAMG/view/screen/chatinapp/countdown.dart';
import 'package:AAMG/view/screen/chatinapp/webviewTG.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class ChatInAppController extends GetxController {
  InAppWebViewController? webViewCtrl;
  RxString token = "".obs;
  RxString url = "".obs;
  RxString showCount = "0".obs;
  RxString statusProcess = "".obs;
  final rTGController = Get.put(RegisterTGController());
  final profile = Get.put(ProfileController());

  final box = GetStorage();
  ProfileController profileCtl = Get.find<ProfileController>();
  final steamControll = Get.put(SteamEvent());

  String useCount = '0';
  RxString phoneCode = "".obs;
  bool ckRegister = false;
  String branch_district = "";
  bool chkAlertGroup = false;

  /// ตัวแปรเก็บข้อมูลของผู้ใช้
  RxString phone = "".obs;
  RxString checkRegister = ''.obs;
  RxString codePhoneUser = ''.obs;
  RxString checkLoginPath = ''.obs;

  checkUse(context) async {
    var useCountStorage = await box.read("useCount");
    phone.value = profileCtl.profile.value.phoneFirebase!.toString();
    print("phone.value");
    print(phone.value);
    phoneCode.value = phone.value.replaceFirst("+", "");
    print("phoneCode.value");
    print(phoneCode.value);
    useCount = useCountStorage ?? '0';
    if (useCount == '0') {
      // AppLoader.loaderChat(context);
    } else {
      useCount = '1';
      await box.write("useCount", '1');
    }
  }

  firstCheckTG() async {
    // print("firstCheckTG");
    String phone = profileCtl.profile.value.phoneFirebase!.toString();
    phoneCode.value = phone.replaceAll("+", "");
    // var objData = {
    //   // "phone": "+66917042071"
    //   "phone": profileCtl.profile!.value.phoneFirebase.toString()
    // };

    var body = {
      'phone': phoneCode.value,
    };
    // var res = await HttpService.callAPIFirebaseService(
    //     "https://agilesoftgroup.com/FCS/checkCreateGroup", objData);

    // if(res["status"] == 200){
      final response = await http.post(
        Uri.parse('https://telegram.agilesoftgroup.com/${checkRegister.value}'),
        body: {
          // 'phone': "66917042071",
          'phone': phoneCode.value,
        },
      );
      var data = jsonDecode(response.body);

      try {
        if (data["status"] == "user_not_found") {
          ckRegister = false;
          update();
          return "false";
        }
        if (data["group_id"] != null && data["group_id"].toString().length > 0) {
          print("firstCheckTG ++ ");
          print(data["group_url"]);
          url.value = data["group_url"];
          ckRegister = true;
          timer(0, true);
          update();
          return "success";
        }
        ckRegister = false;
        update();
        return "pending";
      } catch (e) {
        ckRegister = false;
        update();
      }
    // }
    print(ckRegister);
    // }
    return "false";
  }

  void timer(countdown, checkOut) async {
    // continue count when entering app
    var readStorage = await box.read("timeLocal");
    var now = DateTime.now();
    if (readStorage == "0") {
      print("nothing todo");
    } else if (readStorage != null) {
      try {
        var timeLocal = DateTime.parse(readStorage);
        int minutesApart = now.difference(timeLocal).inSeconds;

        print(minutesApart);
        if (minutesApart > 0 && minutesApart < countdown) {
          countdown = minutesApart;
        }
      } catch (e) {
        print("nothing todo");
      }
    }
    await box.write("timeLocal", now.toString());

    const oneSec = Duration(seconds: 1);
    late Timer _timer;

    // stop time
    try {
      if (checkOut == true) {
        showCount = "0".obs;
        update();
        return;
      }
    } catch (e) {
      print("nothing running");
    }

    // main module
    if (showCount == "0") {
      _timer = Timer.periodic(
        oneSec,
        (Timer timer) {
          if (countdown < 1) {
            timer.cancel();
            showCount = "0".obs;
            box.write("timeLocal", "0");
            print('Countdown reached zero!');
          } else {
            countdown -= 1;
            showCount = countdown.toString().obs;
          }
          update();
        },
      );
    } else {
      print("timer is running");
    }
  }

  void changeState(String input) {
    statusProcess = input.obs;
    update();
  }

  changeDataBu(buName) {
    switch (buName) {
      case "aam":
        {
          checkRegister.value = "checkRegister_AAM";
          checkLoginPath.value = "loginViaQR_AAM";
          break;
        }
      case "rafco":
        {
          checkRegister.value = "checkRegister_RAFCO";
          checkLoginPath.value = "loginViaQR_RAFCO";
          break;
        }
      case "rplc":
        {
          checkRegister.value = "checkRegister_RPLC";
          checkLoginPath.value = "loginViaQR_RPLC";
          break;
        }
    }
    update();
  }

  void changeLoginAutoFill() async {
    Future.delayed(const Duration(milliseconds: 1000), () {
      steamControll.changeEvent(steamControll.changeStateChatInApp);
    });
    update();
  }

  void fullProcessRegister(context) async {
    print("fullProcessRegister");
    if (showCount != "0") {
      showDialog(
        context: context,
        builder: (_) => CountDownRegisterPage(),
      );
      return;
    }
    var ckSubRegister = await firstCheckTG();

    print(ckSubRegister);
    if (ckSubRegister == "pending") {
      // Get.to(() => const WebViewTelegram());
      // Alert.alertPenddingGroup(context);
      Popup.popUpChatInApp(context);
      return;
    } else if (ckSubRegister == "success") {
      Get.find<NotifyController>().delNotiChat();
      Get.to(() => const WebViewTelegram());
      return;
    } else if (ckSubRegister == "false") {
      changeState(chatInAppCallingOTP.tr);
      timer(30, false);
      final codeUse;
      if (profile.profile.value.phoneFirebase!.substring(1, 3) == "66") {
        codeUse = "66";
      } else if (profile.profile.value.phoneFirebase!.substring(1, 3) ==
          "856") {
        codeUse = "856";
      } else {
        codeUse = "855";
      }
      // Loader.showTxt(context);

      showDialog(context: context, builder: (_) => Countdown());
      bool res = await rTGController.getCallOTP_TG_FromHome(
          profile.profile.value.phone,
          codeUse,
          profile.profile.value.firstname!,
          profile.profile.value.lastname!,
          profile.profile.value.branch_name!,
          '',
          context);

      // Loader.hide(context);
      timer(0, true);

      if (res) {
        showDialog(
          context: context,
          useSafeArea: false,
          builder: (_) => ConnectTG(),
        );
      } else {
        print("else");
      }
    }
  }
}
