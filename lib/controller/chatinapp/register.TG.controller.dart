import 'dart:convert';

import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/notification/notify.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/chatinapp_popus.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:sentry/sentry.dart';

class RegisterTGController extends GetxController {
  var phone;
  var countryCode;
  var f_name;
  var l_name;
  var machch_on;
  var branchCity;
  var branchDistrict;
  final storage = FlutterSecureStorage();
  final box = GetStorage();
  RxString? bu = "".obs;
  RxString? checkPhone = "".obs;
  RxString? checkPath = "".obs;
  RxString? checkPath2 = "".obs;
  RxString? token = "".obs;

  Future<String> getCallOTP_TG(inputPhone, inputCountry, inputFname, inputLname,
      inputCity, inputDistrict, context) async {
    print("getCallOTP_TG");

    phone = inputPhone;
    countryCode = inputCountry;
    f_name = inputFname;
    l_name = inputLname;
    branchCity = inputCity;
    branchDistrict = inputDistrict;

    // var checkDuplicate = await checkUserDuplicate(context);
    //
    // if (checkDuplicate == false) {
    //   return "checkDuplicate";
    // }

    print("continue");

    Map payload = {"phone": phone, "countrycode": countryCode};

    print(payload);

    var uriParse = Uri.parse("https://telegram.agilesoftgroup.com/register");
    var res = await http.post(uriParse, body: payload);

    final resData = jsonDecode(res.body);

    print(resData);
    if (resData['status'] == "awaiting code") {
      machch_on = resData['machch_on'];
    } else {
      print("else");
      AppLoading.Loaderhide(context);
      // Loader.hide(context);
      return "false";
    }

    update();
    return "true";
  }

  Future<dynamic> getCallOTP_TG_FromHome(inputPhone, inputCountry, inputFname,
      inputLname, inputCity, inputDistrict, context) async {
    // print("getCallOTP_TG");
    try {
      var useThisPhone;

      if (inputPhone.substring(0, 1) == "0") {
        useThisPhone = inputPhone.substring(1, inputPhone.length);
      } else {
        useThisPhone = inputPhone;
      }

      phone = useThisPhone;
      countryCode = inputCountry;
      f_name = inputFname;
      l_name = inputLname;
      branchCity = inputCity;
      branchDistrict = inputDistrict;

      Map payload = {"phone": phone, "countrycode": countryCode};

      print("https://telegram.agilesoftgroup.com/register");
      print(payload);

      var uriParse = Uri.parse("https://telegram.agilesoftgroup.com/register");
      var res = await http.post(uriParse, body: payload);

      final resData = jsonDecode(res.body);

      // print(resData);
      if (resData['status'] == "awaiting code") {
        // storage.write(key: SecureData.localChat, value: "true");
        box.write("localChatButGST", "true");
        machch_on = resData['machch_on'];
      } else {
        print("else");
        // Loader.hide(context);
        return false;
      }
      update();
      return true;
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  Future<bool> resendRegister() async {
    Map payload = {"phone": phone, "countrycode": countryCode};

    // print(payload);

    var uriParse = Uri.parse("https://telegram.agilesoftgroup.com/register");
    var res = await http.post(uriParse, body: payload);

    final resData = jsonDecode(res.body);

    // print(resData);
    if (resData['status'] == "awaiting code") {
      machch_on = resData['machch_on'];
    } else {
      return false;
    }

    update();
    return true;
  }

  Future<dynamic> verifyOTP_TG_FromHome(type, otpCode, context) async {
    try {
      print("verifyOTP_TG_FromHome");
      var useThisPhone;

      if (phone.substring(0, 1) == "0") {
        useThisPhone = phone.replaceFirst("0", "");
      } else {
        useThisPhone = phone;
      }

      Map payload;
      if (type == "otp") {
        payload = {
          "code": otpCode.toString(),
          "f_name": f_name,
          "l_name": l_name,
          "phone": countryCode + useThisPhone,
          "machch_on": machch_on,
          "bu": bu!.value
        };
      } else {
        payload = {
          "password": otpCode.toString(),
          "f_name": f_name,
          "l_name": l_name,
          "phone": countryCode + useThisPhone,
          "machch_on": machch_on,
          "bu": bu!.value
        };
      }

      // print(payload);
      AppLoading.showText(context);
      // Loader.showTxt(context);

      // print("https://telegram.agilesoftgroup.com/mainProcess_RPLC");
      var uriParse =
          Uri.parse("https://telegram.agilesoftgroup.com/${checkPath!.value}");
      var res = await http.post(uriParse, body: payload);

      final resData = jsonDecode(res.body);
      // print("output");
      // print(resData);
      AppLoading.Loaderhide(context);
      // Loader.hide(context);

      if (resData['status'] == 'success') {
        var useThisPohne;
        var chatControl = await Get.find<ChatInAppController>();
        var noti = await Get.find<NotifyController>();
        // var noti = await Get.find<NotifyController>();

        chatControl.timer(90, false);
        chatControl.changeState(chatInAppCreateGroup.tr);

        if (phone.substring(0, 1) == "0") {
          useThisPohne = phone.replaceFirst("0", "");
        } else {
          useThisPohne = phone;
        }

        token!.value = box.read('tokenNotify');

        Map data = {
          "phone": countryCode + useThisPohne,
          "bu": "RAFCOgr",
          "deviceToken": token!.value.toString()
        };

        var uriParse = Uri.parse(
            "https://telegram.agilesoftgroup.com/${checkPath2!.value}");
        http.post(uriParse, body: data);

        // register_old(context);

        await chatControl.firstCheckTG();
        // Get.to(() => const WebViewTelegram());
        // Navigator.pop(context);
        Navigator.pushReplacement(
            context, MaterialPageRoute(builder: (context) => HomeNavigator()));
        Popup.popUpChatInApp(context);
        // Alert.alertPenddingGroup(context);
        // Navigator.pushReplacement(
        //     context,
        //     MaterialPageRoute(
        //         builder: (BuildContext context) => const WebViewTelegram()));
      } else if (resData['status'] == 'error' &&
          resData['error']['error'] == 'awaiting password') {
        return "2step";
      } else if (resData['status'] == 'error' &&
          resData['error']['error'] == 'awaiting email') {
        return 'email';
      } else {
        Get.snackbar('Error', 'OTP is incorrect');
        return "false";
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      return "false";
    }
  }

  changeData(buName) {
    switch (buName) {
      case "aam":
        {
          bu!.value = "AAMgr";
          checkPath!.value = "mainProcess_AAM";
          checkPath2!.value = "mainProcess_AAM_2";
          break;
        }
      case "rafco":
        {
          bu!.value = "RAFCOgr";
          checkPath!.value = "mainProcess_RAFCO";
          checkPath2!.value = "mainProcess_RAFCO_2";
          break;
        }
      case "rplc":
        {
          bu!.value = "RPLCgr";
          checkPath!.value = "mainProcess_RPLC";
          checkPath2!.value = "mainProcess_RPLC_2";
          break;
        }
    }
    update();
  }
}
