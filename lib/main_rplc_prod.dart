import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_storage/get_storage.dart';
import 'package:global_configuration/global_configuration.dart';

import 'app_config.dart';
import 'firebase_options.dart';
import 'main.dart';
import 'service/http_service.dart';

void main() async {
  try {
    // await GlobalConfiguration().loadFromUrl("https://agilesoftgroup.com/AAMGp3-UAT/getEndpoint",queryParameters:{"bu" : "aam"}, headers: HttpService.KeyRequestHeadersEndPoint);
    await HttpService.getAuthEndPoint('rplc', 'RPLCp3');
  } catch (e) {
    // something went wrong while fetching the config from the url ... do something
  }
  // define dev environment here
  var configuredApp = AppConfig(
    environment: Environment.rplc_prod,
    appTitle: 'This is RPLC PROD env',
    countryConfigCollection: 'rplc',
    pahtConfigCloudflareAPI: 'RPLCp3',
    child: MyApp(),
  );
  // run init here

  // await dotenv.load(fileName: ".env");
  await GetStorage.init();
  GetStorage box = GetStorage();
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);


  initScreen = box.read('initScreen') ?? 0;
  await box.write('initScreen', 1); //if already shown -> 1 else 0
  runApp(configuredApp);
}