{"version": "0.2.0", "configurations": [{"name": "AAM(BETA)", "request": "launch", "type": "dart", "program": "lib/main_aam_dev.dart", "args": ["--flavor", "aam_dev"]}, {"name": "AAM", "request": "launch", "type": "dart", "program": "lib/main_aam_prod.dart", "args": ["--flavor", "aam_prod"]}, {"name": "RAFCO(BETA)", "request": "launch", "type": "dart", "program": "lib/main_rafco_dev.dart", "args": ["--flavor", "rafco_dev"]}, {"name": "RAFCO", "request": "launch", "type": "dart", "program": "lib/main_rafco_prod.dart", "args": ["--flavor", "rafco_prod"]}, {"name": "RPLC(BETA)", "request": "launch", "type": "dart", "program": "lib/main_rplc_dev.dart", "args": ["--flavor", "rplc_dev"]}, {"name": "RPLC", "request": "launch", "type": "dart", "program": "lib/main_rplc_prod.dart", "args": ["--flavor", "rplc_prod"]}]}