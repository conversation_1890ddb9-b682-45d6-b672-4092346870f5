import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class TestWeb extends StatefulWidget {
  const TestWeb({Key? key}) : super(key: key);

  @override
  State<TestWeb> createState() => _TestWebState();
}

class _TestWebState extends State<TestWeb> {

  double _progress = 0;

  // late InAppWebViewController inAppWebViewController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();


    // inAppWebViewController = InAppWebViewController(
    //   initialUrl: "https://www.example.com/",
    //   onLoginRequest: (url, username, password) {
    //     // ดักจับสถานะการ login ที่นี่
    //     print("URL: $url");
    //     print("Username: $username");
    //     print("Password: $password");
    //
    //     // ตรวจสอบ username และ password
    //     // ...
    //
    //     // อนุญาตหรือปฏิเสธการ login
    //     return LoginRequestResponse(
    //       shouldAllowLogin: true,
    //     );
    //   },
    // );


  }


  InAppWebViewController? webViewController;
  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
      crossPlatform: InAppWebViewOptions(
        useShouldOverrideUrlLoading: true,
        clearCache: true,
        mediaPlaybackRequiresUserGesture: true,
      ),
      android: AndroidInAppWebViewOptions(
        useHybridComposition: true,
        allowFileAccess: true,
      ),
      ios: IOSInAppWebViewOptions(
        allowsInlineMediaPlayback: true,
      ));
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        child:  InAppWebView(
          initialUrlRequest: URLRequest(url: Uri.parse('https://aicpmotor.web.app/')
          ),
          androidOnPermissionRequest: (controller, origin,
              resources) async {
            return PermissionRequestResponse(
                resources: resources,
                action: PermissionRequestResponseAction.GRANT);
          },
          initialOptions: options,
          // pullToRefreshController: pullToRefreshController,
          onWebViewCreated: (controller) {
            webViewController = controller;
          },
        )
        // child:  InAppWebView(
        //   initialUrlRequest: URLRequest(
        //       url : Uri.parse("https://your-website.com") as WebUri?
        //   ),
        //   onWebViewCreated: (InAppWebViewController controller) {
        //     inAppWebViewController = controller;
        //   },
        //   onProgressChanged: (InAppWebViewController controller,
        //       int progress) {
        //     setState(() {
        //       _progress = progress / 100;
        //     });
        //   },
        // ),


        // child:InAppWebView(
        //
        //   onLoadStart: (controller, url) async {
        //     print("onLoadStart $url");
        //   },
        //   onLoadStop: (controller, url) async {
        //     print("onLoadStop $url");
        //   },
        //   onConsoleMessage: (controller, consoleMessage) async {
        //   },
        //   initialUrlRequest: URLRequest(url: Uri.parse('https://www.aamfinancegroup.com/')),
        //   initialOptions: InAppWebViewGroupOptions(
        //       crossPlatform: InAppWebViewOptions(
        //         javaScriptEnabled: true,
        //         javaScriptCanOpenWindowsAutomatically: true,
        //       )),
        // ),
      ),
    );
  }
}
