import 'dart:async';
import 'dart:convert';

import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
// import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/aam_policy.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/policy_AICP.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';

import '../../componance/themes/app_textstyle.dart';

class HomeAicp extends StatefulWidget {
  const HomeAicp({super.key});

  @override
  State<HomeAicp> createState() => _HomeAicpState();
}

class _HomeAicpState extends State<HomeAicp> {
  final box = GetStorage();
  var phoneUser = "";
  bool isSearch = false;
  InAppWebViewController? webViewController;
  final ProfileController profileController = Get.find<ProfileController>();
  final MRController mrController = Get.put(MRController());
  endCondePhone() {
    phoneUser = base64.encode(
        utf8.encode(profileController.profile.value.phoneFirebase.toString()));
    print("phoneUser");
  } 

  Future<void> checkPolicy() async {
    print("checkPolicy");
    print(isPolicyAccepted);
    final ProfileController profileController = Get.find<ProfileController>();
    if (profileController.profile.value.aicp_agree_status != "Y" ||
        profileController.profile.value.aicp_privacy_status != "Y") {
      isPolicyAccepted = false;
      print(isPolicyAccepted);
    } else if (profileController.profile.value.aicp_agree_status == "Y" ||
        profileController.profile.value.aicp_privacy_status == "Y") {
      isPolicyAccepted = true;
      print(isPolicyAccepted);
    }
    setState(() {});
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    endCondePhone();
    print("isPolicyAccepted");
    print(isPolicyAccepted);
    Future.delayed(Duration.zero, () async {
      // _checkForUpdates();
      isSearch = false;
      checkPolicy();
      // AAMPolicy.alertTermAndPolicy(context);
    });
  }

  @override
  void dispose() {
    super.dispose();
    isSearch = false;
  }

  final String homePageUrl = 'https://aicpmotor.web.app/?phone=';

  bool isHomePage(String? url) {
    return url != null && url.startsWith(homePageUrl);
  }

  bool shouldShowWidget = true;

  @override
  Widget build(BuildContext context) {
    String initialUrl = 'https://aicpmotor.web.app/?phone=$phoneUser';
    return Scaffold(
      body: Stack(
        children: [
          InAppWebView(
            initialUrlRequest: URLRequest(
              url: WebUri('$homePageUrl$phoneUser'),
            ),
            onLoadStart: (controller, url) {
              if (url.toString() == initialUrl) {
                setState(() {
                  coveredUI(context);
                  print("Another page");
                  print(shouldShowWidget);
                  print(url.toString());
                  shouldShowWidget = false; // ซ่อน widget เมื่อไปยังหน้านั้น
                });
              } else {
                setState(() {
                  print("home page");
                  print(shouldShowWidget);
                  print(url.toString());
                  shouldShowWidget = true; // แสดง widget เมื่อไม่อยู่ในหน้านั้น
                });
              }
            },
            // onLoadStart: (controller, url) {
            //   print("started $url");
            //   coveredUI(context);
            // },
            onWebViewCreated: (controller) {
              webViewController = controller;
              // controller.addJavaScriptHandler(
              //   handlerName: 'handler',
              //   callback: (args) {
              //     print("Arguments from JS:");
              //     print(args); // Logs arguments passed from JavaScript
              //     // Perform any additional action based on args
              //   },
              // );
            },
            onLoadStop: (controller, url) {
              if (url != null) {
                print("Finished loading: $url");
                // Additional actions when a page finishes loading
              }
            },
            initialOptions: InAppWebViewGroupOptions(
                crossPlatform: InAppWebViewOptions(
              javaScriptEnabled: true,
              javaScriptCanOpenWindowsAutomatically: true,
            )),
            androidOnPermissionRequest: (controller, origin, resources) async {
              return PermissionRequestResponse(
                  resources: resources,
                  action: PermissionRequestResponseAction.GRANT);
            },
          ),
          Padding(
            padding:  EdgeInsets.only(top: 50.h,),
            child: Container(
               width: 100.w,
                height: 50.h,
               // color: Colors.red,
               // alignment: Alignment.bottomLeft,
                color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  isSearch = false;
                  Get.back();
                },
                child: Center(
                  child: Container(
                    margin: EdgeInsets.only(right: 24.w),
                    width: 30.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.1)),
                    child: Center(
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        size: 18,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Container(
          //   width: Get.width,
          //   height: 106.h,
          //   color: Colors.transparent,
          //   // color: Colors.red,
          //   // color: Color(0xFF2E5477),
          //   alignment: Alignment.bottomCenter,
          //   padding: EdgeInsets.only(
          //       top: 20.h, right: 20.w, bottom: 10.h, left: 20.w),
          //   child:  Row(
          //     // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     crossAxisAlignment: CrossAxisAlignment.end,
          //     // mainAxisAlignment: MainAxisAlignment.end,
          //     children: [
          //
          //       Row(
          //         children: [
          //           // Icon(Icons.chat_outlined),
          //           Align(
          //             alignment: Alignment.bottomCenter,
          //             child: Container(
          //               margin: EdgeInsets.only(left: 60.w),
          //               width: 123.w,
          //               child: SvgPicture.string(
          //                   '<svg width="124" height="40" viewBox="0 0 124 40" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_916_1016)"><path d="M37.09 15.405C36.3912 15.405 35.7543 14.9636 35.5067 14.3015C35.3563 13.9042 35.1971 13.507 35.0202 13.1273C34.7194 12.4829 34.8609 11.7237 35.3563 11.2205C35.9224 10.6555 36.232 9.9051 36.232 9.11057C36.232 8.31604 35.9224 7.56566 35.3563 7.00066L33.0653 4.71419C31.8977 3.54889 30.0048 3.54889 28.8372 4.71419C28.3418 5.20856 27.5722 5.34098 26.9265 5.04966C26.5373 4.8731 26.1393 4.70536 25.7501 4.56411C25.0867 4.31693 24.6444 3.68131 24.6444 2.98389C24.6444 1.34187 23.2999 0 21.6546 0H18.4172C16.7719 0 15.4274 1.34187 15.4274 2.98389C15.4274 3.68131 14.9851 4.31693 14.3217 4.56411C13.9237 4.71419 13.5345 4.8731 13.1453 5.04966C12.4995 5.34981 11.7388 5.20856 11.2346 4.71419C10.067 3.54889 8.17408 3.54889 7.00648 4.71419L4.7155 7.00066C3.54789 8.16597 3.54789 10.0552 4.7155 11.2205C5.21084 11.7149 5.34353 12.4829 5.05163 13.1273C4.87472 13.5158 4.70665 13.9042 4.56512 14.3015C4.31745 14.9636 3.68057 15.405 2.98178 15.405C1.33652 15.405 -0.00799561 16.7469 -0.00799561 18.3889V21.62C-0.00799561 23.262 1.33652 24.6038 2.98178 24.6038C3.68057 24.6038 4.31745 25.0452 4.56512 25.7073C4.7155 26.1046 4.87472 26.5019 5.05163 26.8815C5.35237 27.5259 5.21084 28.2851 4.7155 28.7883C3.54789 29.9537 3.54789 31.8429 4.7155 33.0082L7.00648 35.2946C7.57259 35.8596 8.32446 36.1686 9.12055 36.1686C9.91665 36.1686 10.6685 35.8596 11.2346 35.2946C11.73 34.8003 12.4995 34.6678 13.1453 34.9592C13.5345 35.1357 13.9237 35.3035 14.3217 35.4447C14.9851 35.6919 15.4274 36.3275 15.4274 37.0249C15.4274 38.667 16.7719 40.0088 18.4172 40.0088H21.6546C23.2999 40.0088 24.6444 38.667 24.6444 37.0249C24.6444 36.3275 25.0867 35.6919 25.7501 35.4447C26.1481 35.2946 26.5373 35.1357 26.9265 34.9592C27.5722 34.659 28.333 34.8003 28.8372 35.2946C30.0048 36.4599 31.8977 36.4599 33.0653 35.2946L35.3563 33.0082C35.9224 32.4432 36.232 31.6928 36.232 30.8983C36.232 30.1037 35.9224 29.3533 35.3563 28.7883C34.8609 28.294 34.7282 27.5259 35.0202 26.8815C35.1971 26.5019 35.3651 26.1046 35.5067 25.7073C35.7543 25.0452 36.3912 24.6038 37.09 24.6038C38.7353 24.6038 40.0798 23.262 40.0798 21.62V18.3889C40.0798 16.7469 38.7353 15.405 37.09 15.405ZM38.7795 21.62C38.7795 22.5469 38.0188 23.3061 37.09 23.3061C35.8516 23.3061 34.7194 24.0918 34.286 25.2571C34.1533 25.6191 34.0029 25.9898 33.8349 26.343C33.313 27.473 33.5518 28.8325 34.4275 29.7065C34.7459 30.0243 34.9229 30.448 34.9229 30.8983C34.9229 31.3485 34.7459 31.7722 34.4275 32.09L32.1365 34.3765C31.482 35.0298 30.4028 35.0298 29.7482 34.3765C28.8637 33.4937 27.5103 33.2554 26.3781 33.785C26.0243 33.9528 25.6528 34.1028 25.2901 34.2353C24.1225 34.6678 23.3353 35.789 23.3353 37.0338C23.3353 37.9607 22.5745 38.7199 21.6458 38.7199H18.4083C17.4795 38.7199 16.7188 37.9607 16.7188 37.0338C16.7188 35.7978 15.9316 34.6678 14.764 34.2353C14.4013 34.1028 14.0298 33.9528 13.676 33.785C13.2779 33.5996 12.8445 33.5114 12.4199 33.5114C11.6415 33.5114 10.872 33.8115 10.3058 34.3853C9.98741 34.7032 9.56283 34.8797 9.11171 34.8797C8.66059 34.8797 8.236 34.7032 7.91757 34.3853L5.62658 32.0989C4.97202 31.4456 4.97202 30.3686 5.62658 29.7153C6.51113 28.8325 6.74996 27.4818 6.21923 26.3518C6.05117 25.9987 5.90079 25.6279 5.76811 25.2659C5.33468 24.1006 4.2113 23.3149 2.96409 23.3149C2.03531 23.3149 1.2746 22.5557 1.2746 21.6288V18.3977C1.2746 17.4708 2.03531 16.7115 2.96409 16.7115C4.20246 16.7115 5.33468 15.9258 5.76811 14.7605C5.90079 14.3986 6.05117 14.0366 6.21923 13.6747C6.74112 12.5447 6.50229 11.1852 5.62658 10.3112C4.97202 9.65791 4.97202 8.58089 5.62658 7.92761L7.91757 5.64114C8.57213 4.98786 9.65128 4.98786 10.3058 5.64114C11.1904 6.52395 12.5438 6.7623 13.676 6.23262C14.0298 6.06489 14.4013 5.91481 14.764 5.78239C15.9316 5.34981 16.7188 4.22865 16.7188 2.98389C16.7188 2.05694 17.4795 1.29773 18.4083 1.29773H21.6458C22.5745 1.29773 23.3353 2.05694 23.3353 2.98389C23.3353 4.21982 24.1225 5.34981 25.2901 5.78239C25.6528 5.91481 26.0154 6.06489 26.3781 6.23262C27.5103 6.75348 28.8725 6.51512 29.7482 5.64114C30.4028 4.98786 31.482 4.98786 32.1365 5.64114L34.4275 7.92761C34.7459 8.24542 34.9229 8.66917 34.9229 9.1194C34.9229 9.56963 34.7459 9.99338 34.4275 10.3112C33.543 11.194 33.3041 12.5447 33.8349 13.6747C34.0029 14.0278 34.1533 14.3898 34.286 14.7605C34.7194 15.9258 35.8428 16.7115 37.09 16.7115C38.0188 16.7115 38.7795 17.4708 38.7795 18.3977V21.6288V21.62Z" fill="#FE0000"/><path d="M30.3666 16.8087C30.1631 16.5615 29.9774 16.3319 29.7916 16.1201L28.6063 14.8665C28.0314 14.2927 27.2618 13.9748 26.4392 13.9748C26.3065 13.9748 26.1738 13.9837 26.0411 14.0013C26.1561 13.092 25.8642 12.1386 25.1654 11.4324L23.9094 10.2494C23.7148 10.0728 23.4848 9.89628 23.2371 9.69323C22.3968 9.0223 21.353 8.17481 20.6012 6.92122L20.0439 5.98544L19.4866 6.92122C18.7348 8.18363 17.691 9.0223 16.8507 9.69323C16.603 9.89628 16.373 10.0817 16.1607 10.2671L14.9047 11.45C14.2059 12.1474 13.9228 13.1009 14.0378 14.0102C13.1267 13.8954 12.1626 14.1867 11.4638 14.8841L10.2785 16.1377C10.1016 16.3319 9.92466 16.5615 9.72121 16.8087C9.04895 17.6473 8.19979 18.689 6.94373 19.4394L6.0061 19.9956L6.94373 20.5518C8.20863 21.3022 9.04895 22.3439 9.72121 23.1825C9.92466 23.4297 10.1104 23.6592 10.2962 23.8711L11.4815 25.1247C12.083 25.725 12.8614 26.0163 13.6486 26.0163C13.7813 26.0163 13.914 26.0075 14.0467 25.9899C13.9317 26.8991 14.2147 27.8614 14.9224 28.5588L16.1784 29.7418C16.373 29.9183 16.603 30.0949 16.8507 30.298C17.691 30.9777 18.7348 31.8164 19.4866 33.07L20.0439 34.0057L20.6012 33.07C21.353 31.8076 22.3968 30.9689 23.2371 30.298C23.4848 30.0949 23.7148 29.9095 23.9271 29.7241L25.1831 28.5412C25.8819 27.8438 26.165 26.8903 26.05 25.981C26.1827 25.9987 26.3154 26.0075 26.448 26.0075C27.2618 26.0075 28.0314 25.6897 28.624 25.0982L29.8093 23.8446C29.9862 23.6504 30.1631 23.4209 30.3666 23.1737C31.0388 22.335 31.888 21.2933 33.1441 20.5429L34.0817 19.9868L33.1441 19.4306C31.8792 18.6802 31.0388 17.6385 30.3666 16.7998V16.8087ZM23.9713 19.9073C23.9713 20.7725 23.6882 21.5758 23.2017 22.2203L22.2199 21.2404C21.8838 20.9049 21.7069 20.4723 21.7069 20.0044C21.7069 19.5365 21.8926 19.0951 22.2199 18.7685L23.2813 17.7091C23.7148 18.3359 23.9713 19.104 23.9713 19.9161V19.9073ZM16.1165 19.9073C16.1165 19.0863 16.373 18.3271 16.7976 17.7003L17.8591 18.7597C18.1952 19.0951 18.3721 19.5277 18.3721 19.9956C18.3721 20.4635 18.1863 20.9049 17.8591 21.2315L16.8772 22.2114C16.3996 21.5582 16.1165 20.7636 16.1165 19.8985V19.9073ZM21.2823 17.8239C20.9461 18.1594 20.5127 18.3359 20.0439 18.3359C19.5751 18.3359 19.1328 18.1505 18.8055 17.8239L17.7352 16.7557C18.3898 16.279 19.1859 15.9877 20.0527 15.9877C20.9196 15.9877 21.7157 16.2701 22.3703 16.7469L21.2911 17.8239H21.2823ZM20.4331 19.6072C20.4154 19.7484 20.4066 19.8897 20.4066 20.0309C20.4066 20.1457 20.4154 20.2693 20.4331 20.384C20.3004 20.3664 20.1766 20.3575 20.0439 20.3575C19.9112 20.3575 19.7785 20.3664 19.6547 20.384C19.6724 20.2693 19.6812 20.1457 19.6812 20.0309C19.6812 19.8897 19.6724 19.7484 19.6547 19.6072C19.7874 19.6248 19.9112 19.6336 20.0439 19.6336C20.1766 19.6336 20.3093 19.6248 20.4331 19.6072ZM18.7967 22.1761C19.1328 21.8407 19.5662 21.6641 20.0351 21.6641C20.5039 21.6641 20.9461 21.8495 21.2734 22.1761L22.2464 23.1472C21.6184 23.5798 20.8577 23.827 20.0351 23.827C19.2124 23.827 18.4517 23.571 17.8237 23.1384L18.7967 22.1673V22.1761ZM15.7981 12.377L17.0276 11.2205C17.2045 11.0616 17.4168 10.8939 17.6556 10.6996C18.3544 10.1346 19.2566 9.41074 20.0351 8.39551C20.8135 9.41074 21.7157 10.1346 22.4145 10.6996C22.6445 10.885 22.8568 11.0528 23.0248 11.2028L24.2544 12.3593C24.9443 13.0479 24.9443 14.1602 24.2544 14.8488L23.2813 15.8199C22.388 15.1137 21.2646 14.6899 20.0351 14.6899C18.8055 14.6899 17.6822 15.1137 16.7888 15.8287L15.8069 14.8488C15.117 14.1602 15.117 13.0479 15.7892 12.377H15.7981ZM12.3925 24.2331L11.2338 23.006C11.0746 22.8294 10.9065 22.6175 10.7119 22.3792C10.1458 21.6818 9.42046 20.7813 8.40323 20.0044C9.42046 19.2276 10.1458 18.3271 10.7119 17.6297C10.8977 17.4001 11.0657 17.1883 11.2161 17.0205L12.3749 15.7934C13.0648 15.1048 14.1793 15.1048 14.8693 15.7934L15.86 16.7822C15.1966 17.6562 14.8074 18.742 14.8074 19.9161C14.8074 21.0903 15.232 22.2644 15.9396 23.156L14.8693 24.2242C14.1793 24.9128 13.0648 24.9128 12.3925 24.2419V24.2331ZM24.272 27.6319L23.0425 28.7884C22.8656 28.9473 22.6533 29.115 22.4145 29.3092C21.7157 29.8742 20.8135 30.5981 20.0351 31.6133C19.2566 30.5981 18.3544 29.8742 17.6556 29.3092C17.4256 29.1238 17.2133 28.9561 17.0453 28.806L15.8158 27.6495C15.1258 26.9609 15.1258 25.8486 15.8158 25.16L16.9037 24.0742C17.7795 24.7363 18.8674 25.1335 20.0439 25.1335C21.2203 25.1335 22.3083 24.7363 23.184 24.083L24.2632 25.16C24.9531 25.8486 24.9531 26.9609 24.2809 27.6319H24.272ZM29.3494 22.388C29.1636 22.6175 28.9955 22.8294 28.8452 22.9971L27.6864 24.2242C27.3503 24.5597 26.908 24.7363 26.4392 24.7363C25.9704 24.7363 25.5281 24.5509 25.192 24.2242L24.1217 23.156C24.8293 22.2644 25.2627 21.1432 25.2627 19.9161C25.2627 18.689 24.8647 17.6562 24.2013 16.7822L25.192 15.7934C25.5281 15.458 25.9704 15.2814 26.4392 15.2814C26.908 15.2814 27.3503 15.4668 27.6687 15.7846L28.8275 17.0117C28.9867 17.1883 29.1548 17.4001 29.3494 17.6385C29.9155 18.3359 30.6408 19.2364 31.658 20.0133C30.6408 20.7901 29.9155 21.6906 29.3494 22.388Z" fill="#FE0000"/><path d="M57.4426 15.5374H51.3923L50.4016 17.7179H48.0399L53.2322 6.16199H55.6116L60.8039 17.7179H58.4245L57.4515 15.5374H57.4426ZM54.4175 8.58971L52.268 13.5069H56.5669L54.4175 8.58971Z" fill="white"/><path d="M65.5363 17.7179V6.16199H67.7034V17.7179H65.5363Z" fill="white"/><path d="M83.6253 16.2613C82.4665 17.409 80.9981 17.974 79.229 17.974C78.1941 17.974 77.2742 17.8062 76.4693 17.4708C75.6643 17.1353 75.0186 16.6851 74.5409 16.1201C74.0633 15.5551 73.7006 14.9195 73.4618 14.2309C73.223 13.5423 73.0991 12.8007 73.0991 12.015C73.0991 11.2293 73.223 10.5054 73.4706 9.79034C73.7183 9.07527 74.0898 8.42199 74.5763 7.83934C75.0628 7.25668 75.7174 6.7888 76.5223 6.4445C77.3273 6.10021 78.2295 5.92365 79.2379 5.92365C80.8655 5.92365 82.2896 6.51513 83.4926 7.68926L82.0331 9.09292C81.29 8.36902 80.3613 8.00707 79.2379 8.00707C77.9376 8.00707 76.9469 8.40433 76.257 9.20769C75.5759 10.0022 75.2397 10.9468 75.2486 12.0238C75.2574 13.1009 75.5935 14.0278 76.257 14.787C76.9204 15.5551 77.9111 15.9347 79.2379 15.9347C79.7598 15.9347 80.2728 15.8287 80.8035 15.6257C81.3254 15.4227 81.7766 15.1313 82.1481 14.7605L83.6341 16.2613H83.6253Z" fill="white"/><path d="M90.8167 14.3191V17.7179H88.6318V6.16199C89.2422 6.16199 90.1444 6.16199 91.3562 6.16199C92.5681 6.16199 93.4792 6.16199 94.0807 6.16199C94.6822 6.16199 95.2483 6.2591 95.7613 6.44449C96.2743 6.62988 96.6989 6.88589 97.0351 7.19487C97.3712 7.51268 97.6542 7.86581 97.8842 8.2719C98.1142 8.67799 98.2557 9.10174 98.3088 9.55197C98.3619 10.0022 98.3619 10.4436 98.3088 10.8938C98.2557 11.3441 98.1231 11.7766 97.8842 12.1916C97.6542 12.6065 97.3712 12.9684 97.0439 13.2774C96.7166 13.5952 96.292 13.8424 95.779 14.0366C95.266 14.2308 94.6998 14.328 94.0807 14.328H90.8078L90.8167 14.3191ZM90.8167 12.324H94.0895C94.7529 12.324 95.266 12.1121 95.6463 11.6884C96.0178 11.2646 96.2036 10.7967 96.2036 10.2759C96.2036 9.75501 96.009 9.28712 95.6375 8.86338C95.266 8.43963 94.7529 8.22776 94.0895 8.22776H90.8167V12.324Z" fill="white"/><path opacity="0.2" d="M72.2588 21.5052C70.5517 21.5052 69.1187 22.0702 67.9776 23.209C66.8277 24.339 66.2616 25.8044 66.2616 27.5965C66.2616 28.3734 66.3854 29.115 66.6243 29.8124C66.8631 30.5098 67.2257 31.1454 67.7034 31.7104C68.1811 32.2754 68.8002 32.7257 69.5786 33.0523C70.3571 33.3789 71.2327 33.5467 72.2146 33.5467C73.1964 33.5467 74.0722 33.3789 74.8417 33.0346C75.6113 32.6903 76.2305 32.2401 76.6993 31.6751C77.1681 31.1101 77.5219 30.4745 77.7696 29.7771C78.0084 29.0797 78.1411 28.3469 78.1499 27.5701C78.1588 26.802 78.0438 26.0605 77.7961 25.3542C77.5484 24.648 77.1858 24.0035 76.717 23.4209C76.2393 22.847 75.6201 22.3792 74.8506 22.0349C74.081 21.6906 73.223 21.5052 72.2677 21.4963M112.948 21.7082V33.2818H115.133V29.424H117.353L120.714 33.2818H123.209V33.1317L119.706 29.1856C120.732 28.9737 121.475 28.5411 121.935 27.8702C122.395 27.2081 122.634 26.4312 122.634 25.5484C122.634 24.4891 122.262 23.5886 121.528 22.847C120.785 22.1055 119.768 21.7259 118.468 21.717C117.848 21.717 116.929 21.717 115.708 21.717C114.487 21.717 113.567 21.717 112.966 21.717M91.7189 21.7347H82.1923V23.7122H85.8632V33.2818H88.048V23.7122H91.7189V21.7347ZM60.9101 21.7524H58.4156L54.7978 26.7226L51.18 21.7524H48.6679V33.3083H50.8527V24.904L54.5413 29.936H54.9747L58.7429 24.9216V33.3083H60.9278V21.7524M115.168 27.4641V23.7652H118.494C119.131 23.7652 119.626 23.9506 119.963 24.3125C120.308 24.6745 120.476 25.107 120.476 25.6191C120.476 26.1311 120.308 26.5637 119.971 26.9256C119.635 27.2876 119.14 27.473 118.494 27.473H115.168M72.25 31.5692C70.9939 31.5692 70.0563 31.1807 69.4283 30.4127C68.8002 29.6447 68.473 28.7177 68.4464 27.6407C68.411 26.4666 68.7472 25.4778 69.446 24.6833C70.1448 23.8888 71.1001 23.4915 72.2942 23.4915C73.5326 23.5091 74.4702 23.9329 75.1071 24.7627C75.744 25.5837 76.0535 26.5372 76.0359 27.6142C76.0182 28.6912 75.6909 29.6182 75.0717 30.395C74.4525 31.1719 73.506 31.5603 72.25 31.5603M101.688 31.5603C100.432 31.5603 99.4941 31.1719 98.8661 30.4039C98.2469 29.6358 97.9108 28.7089 97.8842 27.6319C97.8488 26.4577 98.185 25.469 98.8838 24.6745C99.5825 23.8799 100.538 23.4827 101.741 23.4827C102.979 23.5003 103.917 23.9241 104.554 24.7539C105.191 25.5749 105.5 26.5283 105.482 27.6054C105.456 28.6824 105.138 29.6093 104.518 30.3862C103.899 31.1631 102.953 31.5515 101.697 31.5515M101.75 21.4963C100.043 21.4963 98.6095 22.0613 97.4685 23.2002C96.3186 24.3302 95.7524 25.7956 95.7524 27.5877C95.7524 28.3646 95.8763 29.1061 96.1151 29.8036C96.3539 30.501 96.7166 31.1366 97.1943 31.7016C97.6719 32.2666 98.2911 32.7168 99.0695 33.0435C99.8479 33.3701 100.724 33.5378 101.705 33.5378C102.687 33.5378 103.563 33.3701 104.333 33.0258C105.102 32.6815 105.721 32.2313 106.19 31.6663C106.659 31.1013 107.013 30.4657 107.26 29.7683C107.499 29.0708 107.632 28.3381 107.641 27.5612C107.65 26.7932 107.535 26.0516 107.287 25.3454C107.039 24.6391 106.677 23.9947 106.208 23.412C105.73 22.8382 105.111 22.3703 104.341 22.026C103.572 21.6817 102.714 21.4963 101.759 21.4875" fill="white"/></g><defs><clipPath id="clip0_916_1016"><rect width="123.2" height="40" fill="white"/></clipPath></defs></svg>'),
          //             ),
          //           ),
          //           SizedBox(
          //             width: 6,
          //           ),
          //           // AppWidget.boldText(context, "พูดคุยกับประชากิจฯ", 18,
          //           //     const Color(0xFF282828), FontWeight.w400),
          //         ],
          //       ),
          //       SizedBox(
          //         width: 36.w,
          //       )
          //     ],
          //   ),
          // ),
          // Padding(
          //   padding: EdgeInsets.only(top: !isSearch ? 65.h : 55.h, right: 20.w),
          //   child: Align(
          //     alignment: Alignment.topRight,
          //     child: InkWell(
          //       onTap: () {
          //         if (isSearch == false) {
          //           isSearch = true;
          //           print(isSearch);
          //           print("อยู่นี้");
          //         } else if (isSearch == true) {
          //           isSearch = false;
          //           print(isSearch);
          //           print("อยู่นี้");
          //         }
          //         setState(() {});
          //       },
          //       child: !isSearch
          //           ? Container(
          //               width: 30.w,
          //               height: 30.h,
          //               decoration: BoxDecoration(
          //                   shape: BoxShape.circle,
          //                   color: Colors.white.withOpacity(0.1)),
          //               child: Center(
          //                   child: SvgPicture.string(
          //                       '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.98745 11.6443C7.20494 13.6691 3.2788 13.0875 1.22469 10.3448C-0.829414 7.60199 -0.239406 3.73196 2.54311 1.7072C5.32562 -0.317565 9.25176 0.264014 11.3059 3.00678C12.9375 5.18232 12.9375 8.16202 11.3059 10.3376L15.2247 14.2004L13.9063 15.5L9.98745 11.6371V11.6443ZM9.98745 9.97857C11.8449 7.9538 11.6774 4.8305 9.62325 2.99959C7.56914 1.16869 4.40055 1.33383 2.54311 3.35859C0.685671 5.38336 0.853205 8.50666 2.90731 10.3376C4.96142 12.1613 8.13001 11.9962 9.98016 9.9714L9.98745 9.97857Z" fill="white"/></svg>')),
          //             )
          //           : Container(
          //               width: 290.w,
          //               height: 42.h,
          //               decoration: BoxDecoration(
          //                   color: Colors.white,
          //                   borderRadius: BorderRadius.circular(20)),
          //               child: Center(
          //                   child: SvgPicture.string(
          //                       '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.98745 11.6443C7.20494 13.6691 3.2788 13.0875 1.22469 10.3448C-0.829414 7.60199 -0.239406 3.73196 2.54311 1.7072C5.32562 -0.317565 9.25176 0.264014 11.3059 3.00678C12.9375 5.18232 12.9375 8.16202 11.3059 10.3376L15.2247 14.2004L13.9063 15.5L9.98745 11.6371V11.6443ZM9.98745 9.97857C11.8449 7.9538 11.6774 4.8305 9.62325 2.99959C7.56914 1.16869 4.40055 1.33383 2.54311 3.35859C0.685671 5.38336 0.853205 8.50666 2.90731 10.3376C4.96142 12.1613 8.13001 11.9962 9.98016 9.9714L9.98745 9.97857Z" fill="white"/></svg>')),
          //             ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  bool isPolicyAccepted = false; // เพิ่มตัวแปรสำหรับเก็บสถานะ

  void coveredUI(context) async {
    final box = GetStorage();
    final timer = Timer(const Duration(seconds: 15), () {
      print("ปิด timer");
      box.write("openaicp", 'false');
      Navigator.pop(context);
      // ตรวจสอบสถานะการยอมรับนโยบาย
      if (isPolicyAccepted == false) {
        // แสดง Popup ขึ้นมาอีกครั้ง
        showPolicyPopup(context);
      }
    });
    showDialog(
        context: context,
        useSafeArea: false,
        builder: (_) => LoadingScreen()).then((value) {
      timer.cancel();
    });
  }

  showPolicyPopup(BuildContext context) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<PolicyController>(
              init: PolicyController(),
              builder: (policyCtl) {
                return Container(
                    width: Get.width,
                    height: 444.h,
                    decoration: const ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(18),
                          topRight: Radius.circular(18),
                        ),
                      ),
                    ),
                    child: Column(children: [
                      Container(
                          width: 335.w,
                          // height: 206.h,
                          margin: EdgeInsets.only(
                              left: 20.w, right: 20.w, top: 30.h),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 50.h,
                                padding:
                                    const EdgeInsets.only(left: 2, right: 2),
                                decoration: const BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x0A000000),
                                      blurRadius: 18,
                                      offset: Offset(3, 6),
                                      spreadRadius: 0,
                                    )
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 2.w,
                                      height: 50.h,
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryRafco,
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                    ),
                                    SizedBox(width: 12.w),
                                    Container(
                                      // margin: EdgeInsets.only(left: 8.w),
                                      // height: 50.h,
                                      child: Text(
                                        "AICP នៅសេវាកម្មរបស់អ្នក។ សូមចុចយល់ព្រម ដើម្បីបន្ត។",
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818),
                                          // fontSize: 16.sp,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              SizedBox(
                                child: Text(
                                  policyDes.tr,
                                  style: TextStyle(
                                    color: const Color(0x7F1A1818),
                                    fontSize: 14,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 34.h,
                              ),
                              Container(
                                height: 64.h,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      // width: 335,
                                      height: 20,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                              child: Row(
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  policyCtl.acceptTermPolicy();
                                                },
                                                child: Container(
                                                  width: 20.w,
                                                  height: 20.h,
                                                  decoration: ShapeDecoration(
                                                    color: policyCtl
                                                            .isAcceptedTermPolicy!
                                                            .value
                                                        ? AppColors.primaryRafco
                                                        : Colors.transparent,
                                                    shape: OvalBorder(
                                                      side: BorderSide(
                                                          width: 1.w,
                                                          color: AppColors
                                                              .primaryRafco
                                                              .withOpacity(
                                                                  0.25)),
                                                    ),
                                                  ),
                                                  child: SvgPicture.string(
                                                      AppSvgImage.check),
                                                ),
                                              ),
                                              const SizedBox(width: 10),
                                              SizedBox(
                                                // width: 158,
                                                height: 20,
                                                child: Text(
                                                  policyClickTerms.tr,
                                                  style: TextStyle(
                                                    color:
                                                        const Color(0xFF1A1818),
                                                    fontSize: 14,
                                                    fontFamily: TextStyleTheme
                                                        .text_Regular
                                                        .fontFamily,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )),
                                          InkWell(
                                            onTap: () {
                                              AICPPolicy.buildTermsAICP(
                                                  context);
                                            },
                                            child: Container(
                                              color: Colors.transparent,
                                              child: SvgPicture.string(
                                                  AppSvgImage.icon_more_info),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                    Container(
                                      // width: 335,
                                      height: 20,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                              child: Row(
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  policyCtl
                                                      .acceptPrivacyPolicy();
                                                },
                                                child: Container(
                                                  width: 20.w,
                                                  height: 20.h,
                                                  decoration: ShapeDecoration(
                                                    color: policyCtl
                                                            .isAcceptedPrivacyPolicy!
                                                            .value
                                                        ? AppColors.primaryRafco
                                                        : Colors.transparent,
                                                    shape: OvalBorder(
                                                      side: BorderSide(
                                                          width: 1.w,
                                                          color: AppColors
                                                              .primaryRafco
                                                              .withOpacity(
                                                                  0.25)),
                                                    ),
                                                  ),
                                                  child: SvgPicture.string(
                                                      AppSvgImage.check),
                                                ),
                                              ),
                                              const SizedBox(width: 10),
                                              SizedBox(
                                                // width: 192,
                                                height: 20.h,
                                                child: Text(
                                                  policyClickPrivacy.tr,
                                                  style: TextStyle(
                                                    color:
                                                        const Color(0xFF1A1818),
                                                    fontSize: 14,
                                                    fontFamily: TextStyleTheme
                                                        .text_Regular
                                                        .fontFamily,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )),
                                          InkWell(
                                            onTap: () {
                                              AICPPolicy.buildPrivacyPolicyAICP(
                                                  context);
                                              // appConfigService
                                              //     .countryConfigCollection ==
                                              //     "aam"
                                              //     ? buildPrivacyPolicy(context)
                                              //     : appConfigService.countryConfigCollection ==
                                              //     "rafco"
                                              //     ?  RAFCOPolicy.buildTermsAndConditionsRafco(context)
                                              //     : RPLCPolicy.buildPolicyRPLC(context);
                                            },
                                            child: Container(
                                              color: Colors.transparent,
                                              child: SvgPicture.string(
                                                  AppSvgImage.icon_more_info),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                      SizedBox(
                        height: 116.h,
                      ),
                      PrimaryButton(
                          title: policyClickAll.tr,
                          onPressed: () async {
                            var chk = await policyCtl.acceptAllPolicyAICP();
                            print("chk $chk");
                            if (chk) {
                              Navigator.pop(context, true);
                            }
                          },
                          buttonWidth: 327.w,
                          backgroundColor:
                              appConfigService.countryConfigCollection == "aam"
                                  ? AppColors.AAMPurple
                                  : AppColors.textBlackColor,
                          backgroundInactiveColor:
                              AppColors.inActiveButtonColor,
                          textColor: Colors.white,
                          isActive: policyCtl.isAcceptedPrivacyPolicy!.value &&
                                  policyCtl.isAcceptedTermPolicy!.value
                              ? true
                              : false),
                    ]));
              });
        });
  }
}

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        SvgPicture.asset(
          "assets/bg-loading.svg",
          fit: BoxFit.cover,
          height: double.infinity,
          width: double.infinity,
        ),
        Container(
          color: AppColors.indigoPrimary.withOpacity(0.9),
          child: Center(
            child:
                Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              SvgPicture.string(
                  width: 197.6.w,
                  height: 64.28.h,
                  '<svg width="124" height="40" viewBox="0 0 124 40" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_916_1016)"><path d="M37.09 15.405C36.3912 15.405 35.7543 14.9636 35.5067 14.3015C35.3563 13.9042 35.1971 13.507 35.0202 13.1273C34.7194 12.4829 34.8609 11.7237 35.3563 11.2205C35.9224 10.6555 36.232 9.9051 36.232 9.11057C36.232 8.31604 35.9224 7.56566 35.3563 7.00066L33.0653 4.71419C31.8977 3.54889 30.0048 3.54889 28.8372 4.71419C28.3418 5.20856 27.5722 5.34098 26.9265 5.04966C26.5373 4.8731 26.1393 4.70536 25.7501 4.56411C25.0867 4.31693 24.6444 3.68131 24.6444 2.98389C24.6444 1.34187 23.2999 0 21.6546 0H18.4172C16.7719 0 15.4274 1.34187 15.4274 2.98389C15.4274 3.68131 14.9851 4.31693 14.3217 4.56411C13.9237 4.71419 13.5345 4.8731 13.1453 5.04966C12.4995 5.34981 11.7388 5.20856 11.2346 4.71419C10.067 3.54889 8.17408 3.54889 7.00648 4.71419L4.7155 7.00066C3.54789 8.16597 3.54789 10.0552 4.7155 11.2205C5.21084 11.7149 5.34353 12.4829 5.05163 13.1273C4.87472 13.5158 4.70665 13.9042 4.56512 14.3015C4.31745 14.9636 3.68057 15.405 2.98178 15.405C1.33652 15.405 -0.00799561 16.7469 -0.00799561 18.3889V21.62C-0.00799561 23.262 1.33652 24.6038 2.98178 24.6038C3.68057 24.6038 4.31745 25.0452 4.56512 25.7073C4.7155 26.1046 4.87472 26.5019 5.05163 26.8815C5.35237 27.5259 5.21084 28.2851 4.7155 28.7883C3.54789 29.9537 3.54789 31.8429 4.7155 33.0082L7.00648 35.2946C7.57259 35.8596 8.32446 36.1686 9.12055 36.1686C9.91665 36.1686 10.6685 35.8596 11.2346 35.2946C11.73 34.8003 12.4995 34.6678 13.1453 34.9592C13.5345 35.1357 13.9237 35.3035 14.3217 35.4447C14.9851 35.6919 15.4274 36.3275 15.4274 37.0249C15.4274 38.667 16.7719 40.0088 18.4172 40.0088H21.6546C23.2999 40.0088 24.6444 38.667 24.6444 37.0249C24.6444 36.3275 25.0867 35.6919 25.7501 35.4447C26.1481 35.2946 26.5373 35.1357 26.9265 34.9592C27.5722 34.659 28.333 34.8003 28.8372 35.2946C30.0048 36.4599 31.8977 36.4599 33.0653 35.2946L35.3563 33.0082C35.9224 32.4432 36.232 31.6928 36.232 30.8983C36.232 30.1037 35.9224 29.3533 35.3563 28.7883C34.8609 28.294 34.7282 27.5259 35.0202 26.8815C35.1971 26.5019 35.3651 26.1046 35.5067 25.7073C35.7543 25.0452 36.3912 24.6038 37.09 24.6038C38.7353 24.6038 40.0798 23.262 40.0798 21.62V18.3889C40.0798 16.7469 38.7353 15.405 37.09 15.405ZM38.7795 21.62C38.7795 22.5469 38.0188 23.3061 37.09 23.3061C35.8516 23.3061 34.7194 24.0918 34.286 25.2571C34.1533 25.6191 34.0029 25.9898 33.8349 26.343C33.313 27.473 33.5518 28.8325 34.4275 29.7065C34.7459 30.0243 34.9229 30.448 34.9229 30.8983C34.9229 31.3485 34.7459 31.7722 34.4275 32.09L32.1365 34.3765C31.482 35.0298 30.4028 35.0298 29.7482 34.3765C28.8637 33.4937 27.5103 33.2554 26.3781 33.785C26.0243 33.9528 25.6528 34.1028 25.2901 34.2353C24.1225 34.6678 23.3353 35.789 23.3353 37.0338C23.3353 37.9607 22.5745 38.7199 21.6458 38.7199H18.4083C17.4795 38.7199 16.7188 37.9607 16.7188 37.0338C16.7188 35.7978 15.9316 34.6678 14.764 34.2353C14.4013 34.1028 14.0298 33.9528 13.676 33.785C13.2779 33.5996 12.8445 33.5114 12.4199 33.5114C11.6415 33.5114 10.872 33.8115 10.3058 34.3853C9.98741 34.7032 9.56283 34.8797 9.11171 34.8797C8.66059 34.8797 8.236 34.7032 7.91757 34.3853L5.62658 32.0989C4.97202 31.4456 4.97202 30.3686 5.62658 29.7153C6.51113 28.8325 6.74996 27.4818 6.21923 26.3518C6.05117 25.9987 5.90079 25.6279 5.76811 25.2659C5.33468 24.1006 4.2113 23.3149 2.96409 23.3149C2.03531 23.3149 1.2746 22.5557 1.2746 21.6288V18.3977C1.2746 17.4708 2.03531 16.7115 2.96409 16.7115C4.20246 16.7115 5.33468 15.9258 5.76811 14.7605C5.90079 14.3986 6.05117 14.0366 6.21923 13.6747C6.74112 12.5447 6.50229 11.1852 5.62658 10.3112C4.97202 9.65791 4.97202 8.58089 5.62658 7.92761L7.91757 5.64114C8.57213 4.98786 9.65128 4.98786 10.3058 5.64114C11.1904 6.52395 12.5438 6.7623 13.676 6.23262C14.0298 6.06489 14.4013 5.91481 14.764 5.78239C15.9316 5.34981 16.7188 4.22865 16.7188 2.98389C16.7188 2.05694 17.4795 1.29773 18.4083 1.29773H21.6458C22.5745 1.29773 23.3353 2.05694 23.3353 2.98389C23.3353 4.21982 24.1225 5.34981 25.2901 5.78239C25.6528 5.91481 26.0154 6.06489 26.3781 6.23262C27.5103 6.75348 28.8725 6.51512 29.7482 5.64114C30.4028 4.98786 31.482 4.98786 32.1365 5.64114L34.4275 7.92761C34.7459 8.24542 34.9229 8.66917 34.9229 9.1194C34.9229 9.56963 34.7459 9.99338 34.4275 10.3112C33.543 11.194 33.3041 12.5447 33.8349 13.6747C34.0029 14.0278 34.1533 14.3898 34.286 14.7605C34.7194 15.9258 35.8428 16.7115 37.09 16.7115C38.0188 16.7115 38.7795 17.4708 38.7795 18.3977V21.6288V21.62Z" fill="#FE0000"/><path d="M30.3666 16.8087C30.1631 16.5615 29.9774 16.3319 29.7916 16.1201L28.6063 14.8665C28.0314 14.2927 27.2618 13.9748 26.4392 13.9748C26.3065 13.9748 26.1738 13.9837 26.0411 14.0013C26.1561 13.092 25.8642 12.1386 25.1654 11.4324L23.9094 10.2494C23.7148 10.0728 23.4848 9.89628 23.2371 9.69323C22.3968 9.0223 21.353 8.17481 20.6012 6.92122L20.0439 5.98544L19.4866 6.92122C18.7348 8.18363 17.691 9.0223 16.8507 9.69323C16.603 9.89628 16.373 10.0817 16.1607 10.2671L14.9047 11.45C14.2059 12.1474 13.9228 13.1009 14.0378 14.0102C13.1267 13.8954 12.1626 14.1867 11.4638 14.8841L10.2785 16.1377C10.1016 16.3319 9.92466 16.5615 9.72121 16.8087C9.04895 17.6473 8.19979 18.689 6.94373 19.4394L6.0061 19.9956L6.94373 20.5518C8.20863 21.3022 9.04895 22.3439 9.72121 23.1825C9.92466 23.4297 10.1104 23.6592 10.2962 23.8711L11.4815 25.1247C12.083 25.725 12.8614 26.0163 13.6486 26.0163C13.7813 26.0163 13.914 26.0075 14.0467 25.9899C13.9317 26.8991 14.2147 27.8614 14.9224 28.5588L16.1784 29.7418C16.373 29.9183 16.603 30.0949 16.8507 30.298C17.691 30.9777 18.7348 31.8164 19.4866 33.07L20.0439 34.0057L20.6012 33.07C21.353 31.8076 22.3968 30.9689 23.2371 30.298C23.4848 30.0949 23.7148 29.9095 23.9271 29.7241L25.1831 28.5412C25.8819 27.8438 26.165 26.8903 26.05 25.981C26.1827 25.9987 26.3154 26.0075 26.448 26.0075C27.2618 26.0075 28.0314 25.6897 28.624 25.0982L29.8093 23.8446C29.9862 23.6504 30.1631 23.4209 30.3666 23.1737C31.0388 22.335 31.888 21.2933 33.1441 20.5429L34.0817 19.9868L33.1441 19.4306C31.8792 18.6802 31.0388 17.6385 30.3666 16.7998V16.8087ZM23.9713 19.9073C23.9713 20.7725 23.6882 21.5758 23.2017 22.2203L22.2199 21.2404C21.8838 20.9049 21.7069 20.4723 21.7069 20.0044C21.7069 19.5365 21.8926 19.0951 22.2199 18.7685L23.2813 17.7091C23.7148 18.3359 23.9713 19.104 23.9713 19.9161V19.9073ZM16.1165 19.9073C16.1165 19.0863 16.373 18.3271 16.7976 17.7003L17.8591 18.7597C18.1952 19.0951 18.3721 19.5277 18.3721 19.9956C18.3721 20.4635 18.1863 20.9049 17.8591 21.2315L16.8772 22.2114C16.3996 21.5582 16.1165 20.7636 16.1165 19.8985V19.9073ZM21.2823 17.8239C20.9461 18.1594 20.5127 18.3359 20.0439 18.3359C19.5751 18.3359 19.1328 18.1505 18.8055 17.8239L17.7352 16.7557C18.3898 16.279 19.1859 15.9877 20.0527 15.9877C20.9196 15.9877 21.7157 16.2701 22.3703 16.7469L21.2911 17.8239H21.2823ZM20.4331 19.6072C20.4154 19.7484 20.4066 19.8897 20.4066 20.0309C20.4066 20.1457 20.4154 20.2693 20.4331 20.384C20.3004 20.3664 20.1766 20.3575 20.0439 20.3575C19.9112 20.3575 19.7785 20.3664 19.6547 20.384C19.6724 20.2693 19.6812 20.1457 19.6812 20.0309C19.6812 19.8897 19.6724 19.7484 19.6547 19.6072C19.7874 19.6248 19.9112 19.6336 20.0439 19.6336C20.1766 19.6336 20.3093 19.6248 20.4331 19.6072ZM18.7967 22.1761C19.1328 21.8407 19.5662 21.6641 20.0351 21.6641C20.5039 21.6641 20.9461 21.8495 21.2734 22.1761L22.2464 23.1472C21.6184 23.5798 20.8577 23.827 20.0351 23.827C19.2124 23.827 18.4517 23.571 17.8237 23.1384L18.7967 22.1673V22.1761ZM15.7981 12.377L17.0276 11.2205C17.2045 11.0616 17.4168 10.8939 17.6556 10.6996C18.3544 10.1346 19.2566 9.41074 20.0351 8.39551C20.8135 9.41074 21.7157 10.1346 22.4145 10.6996C22.6445 10.885 22.8568 11.0528 23.0248 11.2028L24.2544 12.3593C24.9443 13.0479 24.9443 14.1602 24.2544 14.8488L23.2813 15.8199C22.388 15.1137 21.2646 14.6899 20.0351 14.6899C18.8055 14.6899 17.6822 15.1137 16.7888 15.8287L15.8069 14.8488C15.117 14.1602 15.117 13.0479 15.7892 12.377H15.7981ZM12.3925 24.2331L11.2338 23.006C11.0746 22.8294 10.9065 22.6175 10.7119 22.3792C10.1458 21.6818 9.42046 20.7813 8.40323 20.0044C9.42046 19.2276 10.1458 18.3271 10.7119 17.6297C10.8977 17.4001 11.0657 17.1883 11.2161 17.0205L12.3749 15.7934C13.0648 15.1048 14.1793 15.1048 14.8693 15.7934L15.86 16.7822C15.1966 17.6562 14.8074 18.742 14.8074 19.9161C14.8074 21.0903 15.232 22.2644 15.9396 23.156L14.8693 24.2242C14.1793 24.9128 13.0648 24.9128 12.3925 24.2419V24.2331ZM24.272 27.6319L23.0425 28.7884C22.8656 28.9473 22.6533 29.115 22.4145 29.3092C21.7157 29.8742 20.8135 30.5981 20.0351 31.6133C19.2566 30.5981 18.3544 29.8742 17.6556 29.3092C17.4256 29.1238 17.2133 28.9561 17.0453 28.806L15.8158 27.6495C15.1258 26.9609 15.1258 25.8486 15.8158 25.16L16.9037 24.0742C17.7795 24.7363 18.8674 25.1335 20.0439 25.1335C21.2203 25.1335 22.3083 24.7363 23.184 24.083L24.2632 25.16C24.9531 25.8486 24.9531 26.9609 24.2809 27.6319H24.272ZM29.3494 22.388C29.1636 22.6175 28.9955 22.8294 28.8452 22.9971L27.6864 24.2242C27.3503 24.5597 26.908 24.7363 26.4392 24.7363C25.9704 24.7363 25.5281 24.5509 25.192 24.2242L24.1217 23.156C24.8293 22.2644 25.2627 21.1432 25.2627 19.9161C25.2627 18.689 24.8647 17.6562 24.2013 16.7822L25.192 15.7934C25.5281 15.458 25.9704 15.2814 26.4392 15.2814C26.908 15.2814 27.3503 15.4668 27.6687 15.7846L28.8275 17.0117C28.9867 17.1883 29.1548 17.4001 29.3494 17.6385C29.9155 18.3359 30.6408 19.2364 31.658 20.0133C30.6408 20.7901 29.9155 21.6906 29.3494 22.388Z" fill="#FE0000"/><path d="M57.4426 15.5374H51.3923L50.4016 17.7179H48.0399L53.2322 6.16199H55.6116L60.8039 17.7179H58.4245L57.4515 15.5374H57.4426ZM54.4175 8.58971L52.268 13.5069H56.5669L54.4175 8.58971Z" fill="white"/><path d="M65.5363 17.7179V6.16199H67.7034V17.7179H65.5363Z" fill="white"/><path d="M83.6253 16.2613C82.4665 17.409 80.9981 17.974 79.229 17.974C78.1941 17.974 77.2742 17.8062 76.4693 17.4708C75.6643 17.1353 75.0186 16.6851 74.5409 16.1201C74.0633 15.5551 73.7006 14.9195 73.4618 14.2309C73.223 13.5423 73.0991 12.8007 73.0991 12.015C73.0991 11.2293 73.223 10.5054 73.4706 9.79034C73.7183 9.07527 74.0898 8.42199 74.5763 7.83934C75.0628 7.25668 75.7174 6.7888 76.5223 6.4445C77.3273 6.10021 78.2295 5.92365 79.2379 5.92365C80.8655 5.92365 82.2896 6.51513 83.4926 7.68926L82.0331 9.09292C81.29 8.36902 80.3613 8.00707 79.2379 8.00707C77.9376 8.00707 76.9469 8.40433 76.257 9.20769C75.5759 10.0022 75.2397 10.9468 75.2486 12.0238C75.2574 13.1009 75.5935 14.0278 76.257 14.787C76.9204 15.5551 77.9111 15.9347 79.2379 15.9347C79.7598 15.9347 80.2728 15.8287 80.8035 15.6257C81.3254 15.4227 81.7766 15.1313 82.1481 14.7605L83.6341 16.2613H83.6253Z" fill="white"/><path d="M90.8167 14.3191V17.7179H88.6318V6.16199C89.2422 6.16199 90.1444 6.16199 91.3562 6.16199C92.5681 6.16199 93.4792 6.16199 94.0807 6.16199C94.6822 6.16199 95.2483 6.2591 95.7613 6.44449C96.2743 6.62988 96.6989 6.88589 97.0351 7.19487C97.3712 7.51268 97.6542 7.86581 97.8842 8.2719C98.1142 8.67799 98.2557 9.10174 98.3088 9.55197C98.3619 10.0022 98.3619 10.4436 98.3088 10.8938C98.2557 11.3441 98.1231 11.7766 97.8842 12.1916C97.6542 12.6065 97.3712 12.9684 97.0439 13.2774C96.7166 13.5952 96.292 13.8424 95.779 14.0366C95.266 14.2308 94.6998 14.328 94.0807 14.328H90.8078L90.8167 14.3191ZM90.8167 12.324H94.0895C94.7529 12.324 95.266 12.1121 95.6463 11.6884C96.0178 11.2646 96.2036 10.7967 96.2036 10.2759C96.2036 9.75501 96.009 9.28712 95.6375 8.86338C95.266 8.43963 94.7529 8.22776 94.0895 8.22776H90.8167V12.324Z" fill="white"/><path opacity="0.2" d="M72.2588 21.5052C70.5517 21.5052 69.1187 22.0702 67.9776 23.209C66.8277 24.339 66.2616 25.8044 66.2616 27.5965C66.2616 28.3734 66.3854 29.115 66.6243 29.8124C66.8631 30.5098 67.2257 31.1454 67.7034 31.7104C68.1811 32.2754 68.8002 32.7257 69.5786 33.0523C70.3571 33.3789 71.2327 33.5467 72.2146 33.5467C73.1964 33.5467 74.0722 33.3789 74.8417 33.0346C75.6113 32.6903 76.2305 32.2401 76.6993 31.6751C77.1681 31.1101 77.5219 30.4745 77.7696 29.7771C78.0084 29.0797 78.1411 28.3469 78.1499 27.5701C78.1588 26.802 78.0438 26.0605 77.7961 25.3542C77.5484 24.648 77.1858 24.0035 76.717 23.4209C76.2393 22.847 75.6201 22.3792 74.8506 22.0349C74.081 21.6906 73.223 21.5052 72.2677 21.4963M112.948 21.7082V33.2818H115.133V29.424H117.353L120.714 33.2818H123.209V33.1317L119.706 29.1856C120.732 28.9737 121.475 28.5411 121.935 27.8702C122.395 27.2081 122.634 26.4312 122.634 25.5484C122.634 24.4891 122.262 23.5886 121.528 22.847C120.785 22.1055 119.768 21.7259 118.468 21.717C117.848 21.717 116.929 21.717 115.708 21.717C114.487 21.717 113.567 21.717 112.966 21.717M91.7189 21.7347H82.1923V23.7122H85.8632V33.2818H88.048V23.7122H91.7189V21.7347ZM60.9101 21.7524H58.4156L54.7978 26.7226L51.18 21.7524H48.6679V33.3083H50.8527V24.904L54.5413 29.936H54.9747L58.7429 24.9216V33.3083H60.9278V21.7524M115.168 27.4641V23.7652H118.494C119.131 23.7652 119.626 23.9506 119.963 24.3125C120.308 24.6745 120.476 25.107 120.476 25.6191C120.476 26.1311 120.308 26.5637 119.971 26.9256C119.635 27.2876 119.14 27.473 118.494 27.473H115.168M72.25 31.5692C70.9939 31.5692 70.0563 31.1807 69.4283 30.4127C68.8002 29.6447 68.473 28.7177 68.4464 27.6407C68.411 26.4666 68.7472 25.4778 69.446 24.6833C70.1448 23.8888 71.1001 23.4915 72.2942 23.4915C73.5326 23.5091 74.4702 23.9329 75.1071 24.7627C75.744 25.5837 76.0535 26.5372 76.0359 27.6142C76.0182 28.6912 75.6909 29.6182 75.0717 30.395C74.4525 31.1719 73.506 31.5603 72.25 31.5603M101.688 31.5603C100.432 31.5603 99.4941 31.1719 98.8661 30.4039C98.2469 29.6358 97.9108 28.7089 97.8842 27.6319C97.8488 26.4577 98.185 25.469 98.8838 24.6745C99.5825 23.8799 100.538 23.4827 101.741 23.4827C102.979 23.5003 103.917 23.9241 104.554 24.7539C105.191 25.5749 105.5 26.5283 105.482 27.6054C105.456 28.6824 105.138 29.6093 104.518 30.3862C103.899 31.1631 102.953 31.5515 101.697 31.5515M101.75 21.4963C100.043 21.4963 98.6095 22.0613 97.4685 23.2002C96.3186 24.3302 95.7524 25.7956 95.7524 27.5877C95.7524 28.3646 95.8763 29.1061 96.1151 29.8036C96.3539 30.501 96.7166 31.1366 97.1943 31.7016C97.6719 32.2666 98.2911 32.7168 99.0695 33.0435C99.8479 33.3701 100.724 33.5378 101.705 33.5378C102.687 33.5378 103.563 33.3701 104.333 33.0258C105.102 32.6815 105.721 32.2313 106.19 31.6663C106.659 31.1013 107.013 30.4657 107.26 29.7683C107.499 29.0708 107.632 28.3381 107.641 27.5612C107.65 26.7932 107.535 26.0516 107.287 25.3454C107.039 24.6391 106.677 23.9947 106.208 23.412C105.73 22.8382 105.111 22.3703 104.341 22.026C103.572 21.6817 102.714 21.4963 101.759 21.4875" fill="white"/></g><defs><clipPath id="clip0_916_1016"><rect width="123.2" height="40" fill="white"/></clipPath></defs></svg>'),
              const SizedBox(
                height: 32.5,
              ),
              Text(
                "ព្រោះយើងស្មោះត្រង់និងទទួលខុសត្រូវជានិច្ច",
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.white.withOpacity(0.85),
                ),
                // style: GoogleFonts.notoSansKhmer(
                //     fontSize: 16,
                //     color: AppColors.white.withOpacity(0.85)
                // ),
              ),
              SizedBox(
                height: 130.h,
              ),
            ]),
          ),
        ),
        Positioned(
          child: SizedBox(
            // width: S.width(context),
            // height: S.height(context),
            child: Lottie.asset("assets/json/iconloading.json"),
          ),
        ),
      ],
    ));
  }
}
