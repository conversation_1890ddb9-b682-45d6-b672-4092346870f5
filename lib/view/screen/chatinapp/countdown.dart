
import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Countdown extends StatefulWidget {
  const Countdown({super.key});

  @override
  State<Countdown> createState() => _CountdownState();
}

class _CountdownState extends State<Countdown> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black26.withOpacity(0.5),
      body: GetBuilder<ChatInAppController>(
          builder: (chatControl) {
            if(chatControl.showCount == "0") {
              // if(checker){
              // Future.delayed(Duration (microseconds: 500), () {
              // Navigator.pop(context);
              // });
              // }else{
              //   checker = true;
              // }
            }
            return Center(
              child:  CupertinoAlertDialog(
                title: Text(chatControl.statusProcess + ''),
                content: Text(chatControl.showCount + ' ' + chatInAppSecond.tr),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(accountOk.tr),
                  ),
                ],
              ),
            );
          }
      ),
    );
  }
}

class CountDownRegisterPage extends StatefulWidget {
  const CountDownRegisterPage({Key? key}) : super(key: key);

  @override
  State<CountDownRegisterPage> createState() => _CountDownRegisterPageState();
}

class _CountDownRegisterPageState extends State<CountDownRegisterPage> {

  bool checker = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black26.withOpacity(0.5),
      body: GetBuilder<ChatInAppController>(
          builder: (chatControl) {
            if(chatControl.showCount == "0") {
              // if(checker){
              // Future.delayed(Duration (microseconds: 500), () {
              // Navigator.pop(context);
              // });
              // }else{
              //   checker = true;
              // }
            }
            return Center(
              child:  CupertinoAlertDialog(
                title: Text(chatControl.statusProcess + ''),
                // content: Text(chatControl.showCount + ' ' + register_second.tr),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(accountOk.tr),
                  ),
                ],
              ),
            );
          }
      ),
    );
  }
}