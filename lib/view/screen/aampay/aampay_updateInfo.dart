import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/transalation/translation_key.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/header_widgets/header_general.dart';
import '../kyc/bookbank.dart';
import 'update_digital_address.dart';

class AAMPayUpdateInfo extends StatelessWidget {
  const AAMPayUpdateInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: GetBuilder<AAMPayController>(builder: (aampayCtl) {
      return Stack(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 116.h, horizontal: 24.w),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    Get.to(()=>  UpdateDigitalAddress());
                  },
                  child: Container(
                    width: 327.w,
                    height: 48.h,
                    color: Colors.transparent,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                width: 24.w,
                                height: 24.h,
                                child: SvgPicture.string(
                                    AppSvgImage.location_icon),
                              ),
                              SizedBox(width: 12.w),
                              Text(
                                address.tr,
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  color: const Color(0xFF1A1818),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodySmall!
                                      .fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall!
                                      .fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          width: 18.w,
                          height: 18.h,
                          child: SvgPicture.string(AppSvgImage.icon_more_info),
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  height: 0.30.h,
                  color: const Color(0x331A1818),
                  thickness: 1,
                ),
                GestureDetector(
                  onTap: () {
                    Get.to( KYCBookBankScreen(),
                        transition: Transition.rightToLeft,
                        duration: const Duration(microseconds: 100));
                  },
                  child: Container(
                    width: 327.w,
                    height: 48.h,
                    color: Colors.transparent,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                width: 24.w,
                                height: 24.h,
                                child: SvgPicture.string(
                                    AppSvgImage.bookbank_icon),
                              ),
                              SizedBox(width: 12.w),
                              Text(
                                bankAccount.tr,
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  color: const Color(0xFF1A1818),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodySmall!
                                      .fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall!
                                      .fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          width: 18.w,
                          height: 18.h,
                          child: SvgPicture.string(AppSvgImage.icon_more_info),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          HeaderGeneral(
            title: editAccountInfo.tr,
            firstIcon: SizedBox(
                width: 24.w,
                height: 24.h,
                child: SvgPicture.string(AppSvgImage.close_icon)),
            secondIcon: SizedBox(width: 24.w, height: 24.h),
            firstOnPressed: () {
              Get.back();
            },
            secondOnPressed: () {},
          ),
        ],
      );
    }));
  }
}
