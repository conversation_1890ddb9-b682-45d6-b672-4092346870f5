import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/utils/constant/size.dart';
import '../../componance/widgets/button_widgets/custom_button_widget.dart';
import '../../componance/widgets/common_widgets/circular_icon_widget.dart';
import '../../componance/widgets/common_widgets/custom_bottom_sheet.dart';
import '../../componance/widgets/common_widgets/custom_image_widget.dart';
import '../../componance/widgets/header_widgets/app_bar.dart';
import 'widgets/feedback_bottom_sheet.dart';

class ThanksForAdviceScreen extends StatelessWidget {
  const ThanksForAdviceScreen({super.key});

  @override
  Widget build(BuildContext context) {

    final aamApp =(appConfigService.countryConfigCollection == 'aam');
    final rafcoApp =(appConfigService.countryConfigCollection == 'rafco');
    final rplcApp =(appConfigService.countryConfigCollection == 'rplc');

    return Scaffold(
      appBar: CAppBar(
        actions: [
          CCircularIcon(
            icon: Icons.close,
            backgroundColor: AppColors.light,
            width: CSizes.md,
            height: CSizes.md,
            size: CSizes.iconSm,
            color: AppColors.darkGrey,
            onPressed: () => Get.to(() => const HomePage()),
          ),
        ],
      ),

      body: Padding(
        padding: const EdgeInsets.all(CSizes.defaultSpace),
        child: Column(
          children: [
            //image
            Center(
                child: CImageWidget(
              image: aamApp ? AppImageAssets.aam_feedback_image
                  : rafcoApp ? AppImageAssets.rafco_feedback_image
                  : AppImageAssets.rplc_feedback_image,
              width: CSizes.xxl,
              height: CSizes.xxl,
              padding: EdgeInsets.zero,
            )),
            const SizedBox(height: CSizes.space50 * 2 ,),

            //TEXT
            Text(
              thanksForAdviceText.tr,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleSmall!.apply(color: AppColors.darkerGrey).copyWith(height: 2,),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(top: CSizes.space15, bottom: CSizes.space25, left: CSizes.defaultSpace, right: CSizes.defaultSpace),
        child: CCustomButton(
            label: done.tr,
            backgroundColor: aamApp ? AppColors.AAMPrimaryOrange
                : rafcoApp ? AppColors.primaryRafco_Blue
                : AppColors.primaryRPLC_Grey,
            onPressed: () => Get.to(() => const HomePage()),
                // _buildFeedbackBottomSheet(context)
        ),
      ),
    );
  }

  void _buildFeedbackBottomSheet(BuildContext context) {
    Get.bottomSheet(
      const CustomBottomSheet(
          height: 650,
          child : FeedbackBottomSheetContent()
      ),
      isScrollControlled: true,
    );
  }
}




