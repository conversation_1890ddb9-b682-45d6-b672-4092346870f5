import 'package:AAMG/view/componance/utils/constant/size.dart';
import 'package:AAMG/view/screen/feedback/thanks_for_advice_screen.dart';
import 'package:AAMG/view/screen/feedback/dislike_feedback_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controller/feedback/feedbackController.dart';
import '../../../controller/profile/profile.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/widgets/common_widgets/circular_icon_widget.dart';
import '../../componance/widgets/common_widgets/vertical_icon_text_button.dart';
import '../../componance/widgets/header_widgets/app_bar.dart';
import '../../componance/widgets/common_widgets/custom_image_widget.dart';
import '../home/<USER>';

class FeedbackReactionScreen extends StatelessWidget {
  const FeedbackReactionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final aamApp =(appConfigService.countryConfigCollection == 'aam');
    final rafcoApp =(appConfigService.countryConfigCollection == 'rafco');
    final rplcApp =(appConfigService.countryConfigCollection == 'rplc');

    final controller = Get.put(FeedbackController());
    final profileController = Get.find<ProfileController>();

    return Scaffold(
      appBar: CAppBar(
        actions: [
          CCircularIcon(
            icon: Icons.close,
            backgroundColor: AppColors.light,
            width: CSizes.md,
            height: CSizes.md,
            size: CSizes.iconSm,
            color: AppColors.darkGrey,
            onPressed: () => Get.to(() => const HomePage()),
          ),
        ],
      ),

      body: Padding(
        padding: const EdgeInsets.all(CSizes.defaultSpace),
        child: Column(
          children: [
            //image
            Center(
              child: CImageWidget(
              image : aamApp ? AppImageAssets.aam_feedback_image
                    : rafcoApp ? AppImageAssets.rafco_feedback_image
                    : AppImageAssets.rplc_feedback_image,
              width: CSizes.xxl,
              height: CSizes.xxl,
              padding: EdgeInsets.zero,
            )),
            const SizedBox(height: CSizes.space25,),

            //text
            Text( feedbackTextTitle.tr, style: Theme.of(context).textTheme.titleMedium!.apply(color: AppColors.darkerGrey, fontWeightDelta: 2,)),
            const SizedBox(height: CSizes.space10,),
            Text(feedbackText.tr, textAlign: TextAlign.center, style: Theme.of(context).textTheme.titleSmall!.apply(color: AppColors.darkerGrey).copyWith(height: 2,),),
            const SizedBox(height: CSizes.spaceBtwItems * 6,),

            //choose feedback type
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                CIconTextButtonWidget(
                  svgIcon: AppImageAssets.like_feedback_icon,
                  iconColor: aamApp ? AppColors.AAMPurple : rafcoApp ? AppColors.primaryRafco : AppColors.RPLCYellow,
                  title: likeFeedback.tr,
                  textColor: AppColors.darkGrey,
                  backgroundColor: aamApp ? AppColors.AAMLightPurple : rafcoApp ? AppColors.RAFCOLightRed : AppColors.RPLCLightYellow,
                  borderRadius: CSizes.xl,
                  onTap: () {
                    controller.saveUserMainFeedbackReaction("${profileController.profile.value.firstname} liked this App", "like");
                    Get.to(() => const ThanksForAdviceScreen());
                  },
                ),
                CIconTextButtonWidget(
                  svgIcon: AppImageAssets.unlike_feedback_icon,
                  iconColor: aamApp ? AppColors.AAMPurple : rafcoApp ? AppColors.primaryRafco : AppColors.RPLCYellow,
                  title: unlikeFeedback.tr,
                  textColor: AppColors.darkGrey,
                  backgroundColor: aamApp ? AppColors.AAMLightPurple : rafcoApp ? AppColors.RAFCOLightRed : AppColors.RPLCLightYellow,
                  borderRadius: CSizes.xl,
                  onTap: () {
                  controller.saveUserMainFeedbackReaction("${profileController.profile.value.firstname} disliked this app", "dislike");
                    Get.to(() => const DislikeFeedbackScreen());
                  },
                ),
              ],
            ),


            //bottom text
            const SizedBox(height: CSizes.space35 * 2,),
            Text(takeSuggestionText.tr, textAlign: TextAlign.center, style: Theme.of(context).textTheme.bodySmall!.apply(color: AppColors.darkerGrey,).copyWith(height: 2,),),
          ],
        ),
      ),
    );
  }
}
