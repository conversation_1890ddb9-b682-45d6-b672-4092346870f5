import 'package:AAMG/view/componance/utils/constant/size.dart';
import 'package:AAMG/view/screen/feedback/thanks_for_advice_screen.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controller/feedback/feedbackController.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/widgets/button_widgets/custom_button_widget.dart';
import '../../componance/widgets/common_widgets/choice_chip.dart';
import '../../componance/widgets/common_widgets/circular_icon_widget.dart';
import '../../componance/widgets/common_widgets/text_area_widget.dart';
import '../../componance/widgets/header_widgets/app_bar.dart';
import '../../componance/widgets/common_widgets/custom_image_widget.dart';

class DislikeFeedbackScreen extends StatelessWidget {
  const DislikeFeedbackScreen({super.key});

  @override
  Widget build(BuildContext context) {

    final aamApp =(appConfigService.countryConfigCollection == 'aam');
    final rafcoApp =(appConfigService.countryConfigCollection == 'rafco');
    final rplcApp =(appConfigService.countryConfigCollection == 'rplc');

    final controller = Get.put(FeedbackController());

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            //----APP BAR----
            CAppBar(
              actions: [
                CCircularIcon(
                  icon: Icons.close,
                  backgroundColor: AppColors.light,
                  width: CSizes.md,
                  height: CSizes.md,
                  size: CSizes.iconSm,
                  color: AppColors.darkGrey,
                  onPressed: () => Get.to(() => const HomePage()),
                ),
              ],
            ),
            //-----BODY-----
            Padding(
              padding: const EdgeInsets.all(CSizes.defaultSpace),
              child: Column(
                children: [

                  // Image
                  Center(
                    child: CImageWidget(
                      image: aamApp ? AppImageAssets.aam_thanks_for_advice_image
                          : rafcoApp ? AppImageAssets.rafco_thanks_for_advice_image
                          : AppImageAssets.rplc_thanks_for_advice_image,
                      width: CSizes.xxl,
                      height: CSizes.xxl,
                      padding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(height: CSizes.space25),

                  // Title text
                  Text(thanksForAdviceTitle.tr, style: Theme.of(context).textTheme.titleMedium!.apply(color: AppColors.darkerGrey, fontWeightDelta: 2),),
                  const SizedBox(height: CSizes.space10),
                  Text(tellWhyUnlike.tr, textAlign: TextAlign.center, style: Theme.of(context).textTheme.titleSmall!.apply(color: AppColors.darkerGrey).copyWith(height: 2),),
                  const SizedBox(height: CSizes.spaceBtwItems * 1.5),

                  // Choice Chips
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: CSizes.sm),
                    child: Column(
                      children: [
                        // Choice Chips
                        Obx(() {
                          List<String> dislikeReason = controller.dislikeFeedbackReason;
                          return Wrap(
                            spacing: 2,
                            children: List.generate(5, (index) {
                              return CChoiceChip(
                                text: dislikeReason[index],
                                selected: controller.isSelectedReason[index],
                                onSelected: (value) { controller.toggleChipSelection(index); },
                              );
                            }),
                          );
                        }),
                        const SizedBox(height: CSizes.space5),

                        // Text Area
                        Obx(() {
                          return controller.selectedCount > 1
                              ? TextAreaWidget(
                            controller: controller.feedbackTextController,
                            minLine: 4,
                          )
                              : const SizedBox.shrink();
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(top: CSizes.space15, bottom: CSizes.space25, left: CSizes.defaultSpace, right: CSizes.defaultSpace),
        child: Obx(() {
          return CCustomButton(
            label: submit.tr,
            backgroundColor: controller.selectedCount > 0
                ? ( aamApp ? AppColors.AAMPrimaryOrange
                  : rafcoApp ? AppColors.primaryRafco_Blue
                  : AppColors.primaryRPLC_Grey )
                : ( aamApp ? AppColors.AAMPrimaryOrange
                  : rafcoApp ? AppColors.primaryRafco_Blue
                  : AppColors.primaryRPLC_Grey).withOpacity(0.3),
            borderColor: Colors.transparent,
            onPressed: controller.selectedCount > 0
                ? () {
             controller.saveUserDislikeFeedback();
             //Get.to(() => const ThanksForAdviceScreen());
            }
                : () {},
          );
        }),
      ),
    );
  }


}
