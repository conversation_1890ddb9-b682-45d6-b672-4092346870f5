import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../controller/feedback/feedbackController.dart';
import '../../../../controller/transalation/translation_key.dart';
import '../../../componance/themes/app_colors.dart';
import '../../../componance/themes/theme.dart';
import '../../../componance/utils/AppImageAssets.dart';
import '../../../componance/utils/constant/size.dart';
import '../../../componance/widgets/button_widgets/custom_button_widget.dart';
import '../../../componance/widgets/common_widgets/custom_image_widget.dart';
import '../../home/<USER>';
import 'feedback_selection_list.dart';

class FeedbackBottomSheetContent extends StatelessWidget {
  const FeedbackBottomSheetContent({
    super.key,
  });

  @override
  Widget build(BuildContext context) {

    final aamApp =(appConfigService.countryConfigCollection == 'aam');
    final rafcoApp =(appConfigService.countryConfigCollection == 'rafco');
    final rplcApp =(appConfigService.countryConfigCollection == 'rplc');

    final feedbackController = Get.put(FeedbackController());

    return Column(
      children: [
        //Image
        Stack(
          children: [
            Positioned(
              right: 0,
                top: 0,
                child:SvgPicture.asset(
                  AppImageAssets.feedback_shape,
                  fit: BoxFit.cover,
                  width: 73,
                  height: 34,
                  color:  AppColors.darkGrey,
                ),
            ),
            Center(
                child: CImageWidget(
                  image: aamApp ? AppImageAssets.aam_feedback_image
                      : rafcoApp ? AppImageAssets.rafco_feedback_image
                      : AppImageAssets.rplc_feedback_image,
                  width: CSizes.xl,
                  height: CSizes.xl,
                //  margin: EdgeInsets.symmetric(vertical: CSizes.space20),
                )),
            ]
        ),

        //Text
        Text(feedbackBottomSheetTitle.tr, textAlign: TextAlign.center, style: Theme.of(context).textTheme.titleSmall!.apply(color: AppColors.darkGrey).copyWith(height: 2,)),
        const SizedBox(height: CSizes.space15 ,),
        Text(feedbackBottomSheetText.tr, style: Theme.of(context).textTheme.titleMedium!.apply(color: AppColors.darkerGrey,), textAlign: TextAlign.center,),
        const SizedBox(height: CSizes.space15,),

        //List
        Obx(
              () => Column(
            children: List.generate(4, (index) {
              final textKey = 'feedbackReactionSelection${index + 1}'.tr;

              return FeedbackSelectionList(
                text: textKey,
                onTapLike: () => feedbackController.toggleLike(index),
                onTapUnlike: () => feedbackController.toggleDislike(index),
                isLiked: feedbackController.isLiked[index],
                isDisliked: feedbackController.isDisliked[index],
              );
            }),
          ),
        ),

        const Spacer(),
        Obx(() {
          final isFeedbackSelected = feedbackController.isAllFeedbackSelected();

          return CCustomButton(
            label: submit.tr,
            backgroundColor: isFeedbackSelected
                ? ( aamApp ? AppColors.AAMPrimaryOrange
                : rafcoApp ? AppColors.primaryRafco_Blue
                : AppColors.primaryRPLC_Grey )
                : ( aamApp ? AppColors.AAMPrimaryOrange
                : rafcoApp ? AppColors.primaryRafco_Blue
                : AppColors.primaryRPLC_Grey).withOpacity(0.3),
              onPressed: () {
                if (isFeedbackSelected) {
                  feedbackController.submitFeedback().then((_) {
                    Get.to(() => const HomePage());
                  }).catchError((error) {
                    debugPrint("Error submitting feedback: $error");
                  });
                }
              },
          );
        }),

      ],
    );
  }
}