import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controller/feedback/feedbackController.dart';
import 'feedback_selection_list.dart';

class FeedbackNotiContent extends StatelessWidget {
  const FeedbackNotiContent({super.key});

  @override
  Widget build(BuildContext context) {
    final feedbackController = Get.put(FeedbackController());

    final random = Random();
    final randomIndex = random.nextInt(feedbackController.isLiked.length);

    final message = 'feedbackReactionSelection${randomIndex + 1}'.tr;

    return Obx(
          () {
        final bool? isLiked = feedbackController.isLiked[randomIndex];
        final bool? isDisliked = feedbackController.isDisliked[randomIndex];

        return FeedbackSelectionList(
          text: message,
          showBorder: false,
          padding: EdgeInsets.zero,
          onTapLike: () {
            feedbackController.toggleLike(randomIndex);
            if (isLiked != true ) {
              feedbackController.saveUserFeedbackFromNotification(message, 'Like');
            }
          },
          onTapUnlike: () {
            feedbackController.toggleDislike(randomIndex);
            if (isDisliked != true ) {
              feedbackController.saveUserFeedbackFromNotification(message, 'Dislike');
            }
          },
          isLiked: feedbackController.isLiked[randomIndex],
          isDisliked: feedbackController.isDisliked[randomIndex],
        );
      },
    );
  }
}
