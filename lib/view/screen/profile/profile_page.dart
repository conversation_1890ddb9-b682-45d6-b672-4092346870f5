import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:AAMG/controller/sms/send.request.controller.dart';
import 'package:AAMG/controller/transalation/transalation.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/privacy_policy.dart';
import 'package:AAMG/view/screen/register/register_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_switch/flutter_advanced_switch.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/profile/delete.account.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/terms_condition.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../controller/config/appConfig.controller.dart';
import '../../../controller/config/appConnect.controller.dart';
import '../../../controller/config/appVersion.controller.dart';
import '../../../controller/session/session.controller.dart';
import '../../../controller/social_login/facebook_authentication.dart';
import '../../../controller/social_login/google.controller.dart';
import '../../componance/widgets/kyc/kyc_status_widget.dart';
import '../feedback/feedback_reaction_screen.dart';
import '../home/<USER>';
import 'deleteAccount_verify.dart';
import 'edit_profile.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final HomeController homeCtl = Get.find<HomeController>();
  final AppConnectController appConnectCtl = Get.put(AppConnectController());

  final MRController mrCtl = Get.put(MRController());
  final TransalationController transalationCtl =
      Get.put(TransalationController());
  final AppConfigController appConfigCtl = Get.find<AppConfigController>();
  final ProfileController profileController = Get.find<ProfileController>();
  GetStorage box = GetStorage();
  var isGuest = false.obs;
  final NavigationController navigationController =
      Get.find<NavigationController>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () {
      box.read('isGuest') == true
          ? isGuest.value = true
          : isGuest.value = false;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final isPopupShown = box.read("isSettingPopupShown") ?? false;
      if (!isPopupShown) {
        navigationController.showSettingPopup();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(children: [
      SingleChildScrollView(
        child: Container(
          child: Column(
            children: [
              SizedBox(
                height: 120.h,
              ),
              homeCtl.showUpdateData!.value.isEmpty && !homeCtl.isGuest!.value
                  ? Container(
                      height: 56.h,
                      width: Get.width,
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: const UpdatePersonalDataPage())
                  : Container(),
              Container(
                margin: EdgeInsets.only(left: 24.w, right: 24.w),
                child: Column(children: [
                  homeCtl.isGuest!.value
                      ? _buildProfileGuest(context)
                      : _buildProfile(context),
                  GestureDetector(
                      onTap: () {
                        if (isGuest!.value) {
                          Get.to(() => const RegisterPage());
                        } else {
                          Get.to(() => const EditProfilePage());
                        }
                      },
                      child: _buildEditProfile(context)),
                  _buildProfileMenu(context),
                ]),
              ),
            ],
          ),
        ),
      ),
      headerProfile(context),
    ]));
  }

  headerProfile(context) {
    return Container(
      height: 120.h,
      width: Get.width,
      color: Colors.white.withOpacity(0.9),
      child: Container(
          margin:
              EdgeInsets.only(top: 67.h,  right: 24.w, bottom: 29.h),
          child: Container(
            width: Get.width,
            alignment: Alignment.center,
            child: Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                      child: Text(
                        accountEditProfile.tr,
                        // 'ตั้งค่า',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: configTheme().textTheme.bodyMedium?.color,
                          fontSize:
                          configTheme().primaryTextTheme.titleLarge?.fontSize,
                          fontFamily:
                          configTheme().primaryTextTheme.titleLarge?.fontFamily,
                          fontWeight:
                          configTheme().primaryTextTheme.titleLarge?.fontWeight,
                          height: 0.09,
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  // mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  // crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        height: 46.h,
                        width: 46.w,
                        alignment: Alignment.centerLeft,
                        color: Colors.transparent,
                        child: Center(
                          child: SvgPicture.string(
                            AppSvgImage.back_btn,
                            height: 18.h,
                            width: 18.w,
                          ),
                        ),
                      ),
                    ),

                    // appConfigService.countryConfigCollection.toString() == "aam"
                    //     ? InkWell(
                    //         onTap: () {
                    //           setState(() {
                    //             print('SettingPopup');
                    //             var i = box.read('isSettingPopupShown');
                    //             print(i);
                    //             if (i == true) {
                    //               i = box.write("isSettingPopupShown", null);
                    //               print("iPopUp");
                    //               print(i);
                    //               profileController.indexPageSetting!.value = 0;
                    //               profileController.indexPopupSetting.value = 0;
                    //               print("profileController.indexPageSetting");
                    //               print(profileController.indexPageSetting);
                    //               print(profileController.indexPopupSetting);
                    //               profileController.update();
                    //               navigationController.showSettingPopup();
                    //               setState(() {});
                    //             }
                    //             // print("MR");
                    //             // var i = box.read('isMRPopupShown');
                    //             // print(i);
                    //             // if (i == true) {
                    //             //   i = box.write("isMRPopupShown", null);
                    //             //   print(i);
                    //             //   navigationController.update();
                    //             //   navigationController.showPopupMR();
                    //             // }
                    //           });
                    //         },
                    //         child: Container(
                    //             height: 24.h,
                    //             width: 24.w,
                    //             child: SvgPicture.string(
                    //                 '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3242_15175)"> <path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 8.10001V12.89" stroke="#792AFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 16.5H12.01" stroke="#792AFF" stroke-width="2" stroke-linecap="round"/></g><defs> <clipPath id="clip0_3242_15175"> <rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                    //       )
                    //     : appConfigService.countryConfigCollection.toString() ==
                    //             "rafco"
                    //         ? InkWell(
                    //             onTap: () {
                    //               setState(() {
                    //                 print('NotificationPopUp');
                    //                 var i = box.read('isSettingPopupShown');
                    //                 print(i);
                    //                 if (i == true) {
                    //                   i = box.write("isSettingPopupShown", null);
                    //                   print("iPopUp");
                    //                   print(i);
                    //                   profileController.indexPageSetting!.value = 0;
                    //                   profileController.indexPopupSetting.value = 0;
                    //                   print("profileController.indexPageSetting");
                    //                   print(profileController.indexPageSetting);
                    //                   print(profileController.indexPopupSetting);
                    //                   profileController.update();
                    //                   navigationController.showSettingPopup();
                    //                   setState(() {});
                    //                 }
                    //                 // print("MR");
                    //                 // var i = box.read('isMRPopupShown');
                    //                 // print(i);
                    //                 // if (i == true) {
                    //                 //   i = box.write("isMRPopupShown", null);
                    //                 //   print(i);
                    //                 //   navigationController.update();
                    //                 //   navigationController.showPopupMR();
                    //                 // }
                    //               });
                    //             },
                    //             child: Container(
                    //                 child: SvgPicture.string(
                    //                     '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_67041)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.10001V12.89" stroke="#EA1B23" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#EA1B23" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3898_67041"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                    //           )
                    //         : InkWell(
                    //             onTap: () {
                    //               setState(() {
                    //                 print('NotificationPopUp');
                    //                 var i = box.read('isSettingPopupShown');
                    //                 print(i);
                    //                 if (i == true) {
                    //                   i = box.write("isSettingPopupShown", null);
                    //                   print("iPopUp");
                    //                   print(i);
                    //                   profileController.indexPageSetting!.value = 0;
                    //                   profileController.indexPopupSetting.value = 0;
                    //                   print("profileController.indexPageSetting");
                    //                   print(profileController.indexPageSetting);
                    //                   print(profileController.indexPopupSetting);
                    //                   profileController.update();
                    //                   navigationController.showSettingPopup();
                    //                   setState(() {});
                    //                 }
                    //                 // print("MR");
                    //                 // var i = box.read('isMRPopupShown');
                    //                 // print(i);
                    //                 // if (i == true) {
                    //                 //   i = box.write("isMRPopupShown", null);
                    //                 //   print(i);
                    //                 //   navigationController.update();
                    //                 //   navigationController.showPopupMR();
                    //                 // }
                    //               });
                    //             },
                    //             child: Container(
                    //                 child: SvgPicture.string(
                    //                     '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_32814)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.09998V12.89" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#FFC20E" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_32814"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                    //           )
                  ],
                ),
              ],
            ),
          )),
    );
  }

  _buildLogOut(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 373.h,
            padding: const EdgeInsets.only(bottom: 20),
            decoration: const ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Column(
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 20.h,
                ),
                Image.asset(
                  AppImageAssets.aam_logout,
                  height: 73.h,
                ),
                SizedBox(
                  height: 16.h,
                ),
                Center(
                  child: Text(
                    accountLogout.tr,
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                          configTheme().primaryTextTheme.titleLarge?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.titleLarge?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.titleLarge?.fontWeight,
                    ),
                  ),
                ),
                SizedBox(height: 6.h),
                Center(
                  child: Text(
                    accountLogoutDes.tr,
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.5),
                      fontSize:
                          configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    ),
                  ),
                ),
                SizedBox(
                  height: 19.5.h,
                ),
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.onSecondary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          accountLogoutNotNow.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.**************21),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                GestureDetector(
                  onTap: () {
                    Get.put(SessionController()).logout(context);
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          accountLogoutYes.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: configTheme().colorScheme.onSecondary,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        });
  }

  _buildDeleteAccount(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 445.h,
            padding: const EdgeInsets.only(bottom: 20),
            decoration: const ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  width: 86.w,
                  height: 67.h,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(AppImageAssets.aam_delete),
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                Text(
                  accountDelete.tr,
                  style: TextStyle(
                    color: configTheme().textTheme.bodyMedium?.color,
                    fontSize:
                        configTheme().primaryTextTheme.titleLarge?.fontSize,
                    fontFamily:
                        configTheme().primaryTextTheme.titleLarge?.fontFamily,
                    fontWeight:
                        configTheme().primaryTextTheme.titleLarge?.fontWeight,
                    height: 0.09,
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  accountDeleteDes.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: configTheme()
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withOpacity(0.5),
                    fontSize:
                        configTheme().primaryTextTheme.bodySmall?.fontSize,
                    fontFamily:
                        configTheme().primaryTextTheme.bodySmall?.fontFamily,
                    fontWeight:
                        configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    // height: 0.13,
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          accountDeleteNotNow.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.**************21),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            // height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _buildDeleteAccountDetail(context);
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          accountDeleteYes.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: configTheme().colorScheme.onPrimary,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            // height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        });
  }

  _buildDeleteAccountAbroad(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 445.h,
            padding: EdgeInsets.only(bottom: 20.h),
            decoration: const ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 6.h,
                ),
                Container(
                    margin: EdgeInsets.only(left: 24.w, right: 24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 250.w,
                          height: 254.h,
                          child: Column(
                            children: [
                              Container(
                                height: 20.h,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  accountDelete.tr,
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .titleLarge
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .titleLarge
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .titleLarge
                                        ?.fontWeight,
                                    height: 0.09,
                                  ),
                                ),
                              ),
                              SizedBox(height: 6.h),
                              Container(
                                height: 208.h,
                                alignment: Alignment.topLeft,
                                child: Text(
                                  accountDeleteDes.tr,
                                  textAlign: TextAlign.start,
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(0.5),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontWeight,
                                    // height: 0.13,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          alignment: Alignment.topRight,
                          width: 73.w,
                          height: 38.h,
                          child:
                              SvgPicture.string(AppSvgImage.delete_icon_rplc),
                        ),
                      ],
                    )),
                SizedBox(
                  height: 16.h,
                ),
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          accountDeleteNotNow.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.**************21),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            // height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _buildDeleteAccountDetail(context);
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          accountDeleteYes.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: configTheme().colorScheme.onPrimary,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            // height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        });
  }

    _buildDeleteAccountDetail(context) {
    final SendRequestSMSController smsController =
        Get.put(SendRequestSMSController());
    final DeleteAccountController deleteAccountCtl =
        Get.put(DeleteAccountController());
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: false,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return GetBuilder<DeleteAccountController>(
              init: DeleteAccountController(),
              builder: (deleteAccountCtl) {
                return Container(
                  width: Get.width,
                  height: 812.h,
                  decoration: const ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        height: 120.h,
                        width: Get.width,
                        color: Colors.white.withOpacity(0.9),
                        child: Container(
                            margin: EdgeInsets.only(top: 67.h, bottom: 29.h),
                            child: Stack(
                              children: [
                                //TODO slide page
                                Container(
                                  width: Get.width,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        accountDeleteYes.tr,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .titleLarge
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .titleLarge
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .titleLarge
                                              ?.fontWeight,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    Get.back();
                                  },
                                  child: Container(
                                    height: 46.h,
                                    width: 46.w,
                                    alignment: Alignment.centerLeft,
                                    child: Center(
                                      child: SvgPicture.string(
                                        AppSvgImage.back_btn,
                                        height: 18.h,
                                        width: 18.w,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )),
                      ),
                      Text(
                        accountReason.tr,
                        style: TextStyle(
                          color: configTheme().textTheme.bodyMedium?.color,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontWeight,
                          height: 0,
                          letterSpacing: -0.10,
                        ),
                      ),
                      // Container(
                      //   width: 375.w,
                      //   // height: 22.h,
                      //   child: Row(
                      //     mainAxisSize: MainAxisSize.min,
                      //     mainAxisAlignment: MainAxisAlignment.center,
                      //     crossAxisAlignment: CrossAxisAlignment.center,
                      //     children: [
                      //       Expanded(
                      //         child: Container(
                      //           height: double.infinity,
                      //           padding: const EdgeInsets.symmetric(horizontal: 24),
                      //           child: Row(
                      //             mainAxisSize: MainAxisSize.min,
                      //             mainAxisAlignment: MainAxisAlignment.start,
                      //             crossAxisAlignment: CrossAxisAlignment.center,
                      //             children: [
                      //
                      //             ],
                      //           ),
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      // ),
                      SizedBox(
                        height: 24.5.h,
                      ),
                      Container(
                        width: Get.width,
                        height: 94.h,
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              GestureDetector(
                                  onTap: () {
                                    deleteAccountCtl.setCheckList(
                                        0, accountReasonDetail1.tr);
                                  },
                                  child: _buildCheckListDeleteAccount(
                                      context,
                                      accountReasonDetail1.tr,
                                      0,
                                      deleteAccountCtl.checkList1!.value)),
                              SizedBox(
                                height: 14.h,
                              ),
                              GestureDetector(
                                onTap: () {
                                  deleteAccountCtl.setCheckList(
                                      1, accountReasonDetail2.tr);
                                },
                                child: _buildCheckListDeleteAccount(
                                    context,
                                    accountReasonDetail2.tr,
                                    0,
                                    deleteAccountCtl.checkList2!.value),
                              ),
                              SizedBox(
                                height: 14.h,
                              ),
                              GestureDetector(
                                onTap: () {
                                  deleteAccountCtl.setCheckList(
                                      2, accountReasonDetail3.tr);
                                },
                                child: _buildCheckListDeleteAccount(
                                    context,
                                    accountReasonDetail3.tr,
                                    0,
                                    deleteAccountCtl.checkList3!.value),
                              ),
                            ]),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Container(
                        width: 327.w,
                        height: 67.h,
                        child: Column(
                          children: [
                            Container(
                                width: 327.w,
                                height: 20.h,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  deleteAccountCtl.controllerDeleteAcc.value
                                          .text.isNotEmpty
                                      ? accountReasonInput.tr
                                      : '',
                                  style: TextStyle(
                                    color: const Color(0xFFD7D7D7),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontWeight,
                                    height: 0.10,
                                  ),
                                )),
                            SizedBox(
                              height: 8.h,
                            ),
                            Container(
                              width: 326.w,
                              height: 23.h,
                              child: TextFormField(
                                textAlign: TextAlign.left,
                                cursorWidth: 0,
                                controller:
                                    deleteAccountCtl.controllerDeleteAcc.value,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  labelText: '',
                                  labelStyle: TextStyle(
                                    color: const Color(0xFF0B0B0B),
                                    fontSize: 14.sp,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                    height: 0.11,
                                  ),
                                  hintText: accountReasonInput.tr,
                                  hintStyle: TextStyle(
                                    color: const Color(0xFFD7D7D7),
                                    fontSize: 14.sp,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                    height: 0.11,
                                  ),
                                ),
                                style: TextStyle(
                                  color: const Color(0xFF0B0B0B),
                                  fontSize: 14.sp,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                  height: 0.11,
                                ),
                                onChanged: (value) {
                                  if (value.length > 0) {}
                                },
                              ),
                            ),
                            Container(
                              //   height: 16.h,
                              //   alignment: Alignment.center,
                              child: _buildDivider(),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 312.h,
                      ),
                      GestureDetector(
                        onTap: () async {
                          print('Verify OTP & delete account');
                          //TODO Call function send OTP
                          await deleteAccountCtl.setFormatPhoneCode();
                          smsController
                              .setVerificationPage(const DeleteAccountVerify());
                          smsController.checkOTPConfig(
                              context,
                              Get.find<ProfileController>()
                                  .profile
                                  .value
                                  .phone
                                  .toString());
                          // Get.to(() => const DeleteAccountVerify());
                        },
                        child: Container(
                          width: 327.w,
                          height: 52.h,
                          decoration: ShapeDecoration(
                            color: deleteAccountCtl.checkList1!.value ||
                                    deleteAccountCtl.checkList2!.value ||
                                    deleteAccountCtl.checkList3!.value
                                ? configTheme().colorScheme.onPrimary
                                : const Color(0x191A1818),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                accountDeleteYes.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white
                                      .withOpacity(0.**************21),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontWeight,
                                  height: 0.14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          width: 327.w,
                          height: 52.h,
                          decoration: ShapeDecoration(
                            color: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                accountEditCancel.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: configTheme().colorScheme.onPrimary,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontWeight,
                                  height: 0.14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                );
              });
        });
  }

  Widget _buildCheckListDeleteAccount(
      context, String text, int index, bool isCheck) {
    return Container(
      height: 22.h,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 20.w,
            height: 20.h,
            decoration: ShapeDecoration(
              color: isCheck
                  ? configTheme().colorScheme.primary
                  : Colors.transparent,
              shape: OvalBorder(
                side: BorderSide(
                    width: 1,
                    color:
                        isCheck ? Colors.transparent : const Color(0x261A1818)),
              ),
            ),
            child: Center(
              child: SvgPicture.string(AppSvgImage.check),
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          Text(
            text,
            style: TextStyle(
              color:
                  configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
              fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
              fontFamily: configTheme().primaryTextTheme.bodyMedium?.fontFamily,
              fontWeight: configTheme().primaryTextTheme.bodyMedium?.fontWeight,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileGuest(context) {
    return Container(
      width: Get.width,
      height: 55.h,
      margin: EdgeInsets.only(bottom: 10.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 50.w,
            height: 55.h,
            padding:
                const EdgeInsets.only(top: 5, left: 5, right: 5, bottom: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    width: double.infinity,
                    height: 40,
                    // ถ้าจำเป็นต้องปรับ dynamic height ลบออก
                    padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      image: const DecorationImage(
                        image: NetworkImage(
                            "https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png"),
                        fit: BoxFit.cover,
                      ),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 1.w,
                          strokeAlign: BorderSide.strokeAlignOutside,
                          color: const Color(0xFF1A1818),
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 25.5.w),
          Container(
            alignment: Alignment.centerLeft,
            width: 212.w,
            height: 24.h,
            child: Text(
              accountSettingGuest.tr,
              // 'ผู้เยี่ยมชม',
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                fontFamily:
                    configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                fontWeight:
                    configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                height: 0.10.h,
              ),
              textAlign: TextAlign.left,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildProfile(context) {
    return Container(
      width: Get.width,
      height: 68.h,
      margin: EdgeInsets.only(bottom: 18.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 303.w,
            height: 68.h,
            child: Row(
              children: [
                //TODO profile image
                GetBuilder<ProfileController>(builder: (profileCtl) {
                  return Container(
                    margin: EdgeInsets.only(left: 5.w),
                    width: 59.w,
                    child: Stack(
                      children: [
                        Container(
                          alignment: Alignment.center,
                          width: 50.w,
                          height: 55.h,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Obx(() {
                                final avatarUrl =
                                    profileCtl.profile.value.avatar ?? '';
                                bool isValidAvatar = avatarUrl!.isNotEmpty &&
                                    avatarUrl.toString().toLowerCase() !=
                                        'null';

                                return CircleAvatar(
                                  radius: 20.r,
                                  backgroundColor: Colors.transparent,
                                  child: ClipOval(
                                    child: Image.network(
                                      isValidAvatar
                                          ? avatarUrl
                                          : 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png',
                                      fit: BoxFit.cover,
                                      width: 40,
                                      height: 40,
                                      errorBuilder:
                                          (context, error, stackTrace) => Icon(
                                              Icons.person,
                                              size: 40,
                                              color: Colors.grey),
                                    ),
                                  ),
                                );
                              }),
                              Obx(() {
                                final percent =
                                    profileCtl.percentUpdateProfile.value;
                                final country = appConfigService
                                    .countryConfigCollection
                                    .toString();
                                final mrRank =
                                    mrCtl.mrData.value.mr_rank.toString();

                                Color progressColor;
                                if (country == 'aam') {
                                  progressColor = (mrRank == "GOLD")
                                      ? const Color(0xFFFFD400)
                                      : const Color(0xDDDBC6FF);
                                } else if (country == 'rplc') {
                                  progressColor =
                                      const Color(0xFFFFD400).withOpacity(0.5);
                                } else {
                                  progressColor =
                                      AppColors.primaryRafco.withOpacity(0.75);
                                }

                                return CircularPercentIndicator(
                                  radius: 26.5.r,
                                  lineWidth: 5.0.w,
                                  percent: percent,
                                  progressColor: progressColor,
                                  backgroundColor: const Color(0xEEEDEDED),
                                  animation: true,
                                  circularStrokeCap: CircularStrokeCap.round,
                                );
                              }),
                            ],
                          ),
                        ),
                        Obx(() {
                          return Container(
                              margin: EdgeInsets.only(
                                  top: 38.h, left: 13.w, right: 13.w),
                              width: 24.w,
                              height: 24.h,
                              child:  SvgPicture.string(
                                  mrCtl
                                  .iconProfileLevel.value
                                  .toString()));
                        }),
                        Container(
                          height: 17.h,
                          width: 22.w,
                          margin: EdgeInsets.only(top: 40.h, left: 37.w),
                          alignment: Alignment.lerp(
                              Alignment.topCenter, Alignment.bottomCenter, 0.5),
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              '${profileCtl.percentUpdateProfileText.value.obs}%',
                              //TODO เปอร์เซ็นต์อัปเดตข้อมูล
                              style: TextStyle(
                                color: const Color(0xFF1A1818),
                                fontSize: 10.sp,
                                fontFamily: 'IBM Plex Sans Thai',
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  );
                }),
                SizedBox(width: 12.w),
                //TODO profile name
                GetBuilder<ProfileController>(builder: (profileCtl) {
                  return Container(
                    // color: Colors.red,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                height: 20.h,
                                alignment: Alignment.centerLeft,
                                child: Obx(() {
                                  return Text.rich(
                                    mrCtl.mrData.value.mr_id.obs.string != ""
                                        ? TextSpan(
                                            children: [
                                              TextSpan(
                                                text: '@',
                                                style: TextStyle(
                                                  color: Colors.transparent,
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontWeight,
                                                  height: 0.10,
                                                ),
                                              ),
                                              TextSpan(
                                                text: mrCtl.mrData.value.mr_id
                                                    .obs.string,
                                                style: TextStyle(
                                                  color: Colors.transparent,
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontWeight,
                                                  height: 0.10,
                                                ),
                                              ),
                                            ],
                                          )
                                        : TextSpan(
                                            children: [
                                              TextSpan(
                                                text: '@',
                                                style: TextStyle(
                                                  color: configTheme()
                                                      .colorScheme
                                                      .primary,
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontWeight,
                                                  height: 0.10,
                                                ),
                                              ),
                                              TextSpan(
                                                text: mrCtl.mrData.value.mr_id
                                                    .obs.string,
                                                style: TextStyle(
                                                  color: configTheme()
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color,
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontWeight,
                                                  height: 0.10,
                                                ),
                                              ),
                                            ],
                                          ),
                                  );
                                })),
                            SizedBox(height: 4.h),
                            SizedBox(
                              height: 20.h,
                              child: Center(
                                child: Obx(() {
                                  return Text(
                                    '${profileCtl.profile.value.firstname.obs} ${profileCtl.profile.value.lastname.obs}',
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontWeight,
                                      height: 0.10,
                                    ),
                                  );
                                }),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Obx(() => mrCtl.mrData.value.mr_rank.obs.toString() ==
                                levelPlatinum.tr
                            ? Container(
                                height: 20.h,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 6),
                                decoration: ShapeDecoration(
                                  gradient: const LinearGradient(
                                    begin: Alignment(0.00, -1.00),
                                    end: Alignment(0, 1),
                                    colors: [
                                      Color(0xFF9D9D9D),
                                      Color(0xFF676767),
                                      Color(0xFF3C3B3B),
                                      Color(0xFF1A1818)
                                    ],
                                  ),
                                  shape: RoundedRectangleBorder(
                                    side: BorderSide(
                                        width: 1.w, color: Colors.black),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    'PLATINUM',
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.**************21),
                                      fontSize: 10.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      height: 0.14,
                                    ),
                                  ),
                                ),
                              )
                            : Container(
                                height: 20.h,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 6),
                                decoration: ShapeDecoration(
                                  color: mrCtl.mrData.value.mr_rank.obs
                                              .toString() ==
                                          levelClassic.tr
                                      ? const Color(0x0C792AFF)
                                      : mrCtl.mrData.value.mr_rank.obs
                                                  .toString() ==
                                              levelGold.tr
                                          ? const Color(0xFFFFD400)
                                              .withOpacity(0.5)
                                          : Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                    side: BorderSide(
                                        width: 1.w,
                                        color: mrCtl.mrData.value.mr_rank.obs
                                                    .toString() ==
                                                levelClassic.tr
                                            ? const Color(0x26792AFF)
                                            : mrCtl.mrData.value.mr_rank.obs
                                                        .toString() ==
                                                    levelGold.tr
                                                ? const Color(0xFFFFD400)
                                                : Colors.black),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    mrCtl.mrData.value.mr_rank.obs.toString() ==
                                            levelClassic.tr
                                        ? levelClassic.tr
                                        : mrCtl.mrData.value.mr_rank.obs
                                                    .toString() ==
                                                levelGold.tr
                                            ? levelGold.tr
                                            : '',
                                    style: TextStyle(
                                      color: mrCtl.mrData.value.mr_rank.obs
                                                  .toString() ==
                                              levelClassic.tr
                                          ? const Color(0xFF792AFF)
                                          : const Color(0xFF1A1818),
                                      fontSize: 10.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      height: 0.14,
                                    ),
                                  ),
                                ),
                              )),
                      ],
                    ),
                  );
                })
              ],
            ),
          ),
          Container(
            width: 24.w,
            height: 24.h,
            clipBehavior: Clip.antiAlias,
            decoration: const BoxDecoration(),
            child: SvgPicture.string(AppSvgImage.profile_qr),
          ),
        ],
      ),
    );
  }

  Widget _buildEditProfile(context) {
    return Container(
      width: Get.width,
      height: 42.h,
      padding: const EdgeInsets.all(10),
      decoration: ShapeDecoration(
        gradient: LinearGradient(
          begin: const Alignment(0.00, -1.00),
          end: const Alignment(0, 1),
          colors: [
            Colors.white.withOpacity(0.**************21),
            Colors.white.withOpacity(0.5)
          ],
        ),
        shape: RoundedRectangleBorder(
          side: BorderSide(width: 1.w, color: const Color(0x331A1818)),
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      child: SizedBox(
        // height: 41.h,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appConfigService.countryConfigCollection.toString() == 'aam'
                ? Container(
                    width: 20.w,
                    height: 20.h,
                    clipBehavior: Clip.antiAlias,
                    decoration: const BoxDecoration(),
                    child: SvgPicture.string(AppSvgImage.edit_profile),
                  )
                : Container(),
            SizedBox(width: 10.w),
            Text(
              accountSettingProfile.tr,
              // 'แก้ไขข้อมูลส่วนตัว',
              style: TextStyle(
                color: const Color(0xFF1A1818),
                fontSize: 14,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w400,
                // height: 0.10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileMenu(context) {
    return Container(
        width: Get.width,
        height: 1120.h,
        margin: EdgeInsets.only(top: 24.h),
        child: Column(
          children: [
            //TODO App Setting
            _buildSettingApp(context),
            //TODO Profile Setting
            _buildProfileSetting(context),
            //TODO Help Menu
            _buildHelpCenter(context),
            //TODO Policy Menu
            _buildPolicy(context),
            //TODO Other Setting
            _buildOtherSetting(context),
            //TODO App Version
            _buildAppVersion(context),
          ],
        ));
  }

  Widget _buildSettingApp(context) {
    return Container(
      child: Column(
        children: [
          //TODO title setting
          _buildHeaderMenu(context, accountSettingApp.tr),
          Container(
            width: 327.w,
            height: 48.h,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  // width: 54,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.string(AppSvgImage.translation),
                      SizedBox(width: 9.w),
                      SizedBox(
                        height: 20.h,
                        child: Center(
                          child: Text(
                            accountSettingLanguage.tr,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                              // height: 0.12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    transalationCtl.showListLanguage(appConfigService
                                .countryConfigCollection
                                .toString() ==
                            'aam'
                        ? "aam"
                        : appConfigService.countryConfigCollection.toString() ==
                                'rafco'
                            ? "rafco"
                            : "rplc");
                    buildChangeLanguage(context);
                  },
                  child: Container(
                      child: transalationCtl.location.value == ""
                          ? appConfigService.countryConfigCollection
                                      .toString() ==
                                  'aam'
                              ? const Text("Thailand")
                              : appConfigService.countryConfigCollection
                                          .toString() ==
                                      'rafco'
                                  ? const Text("Cambodia")
                                  : appConfigService.countryConfigCollection
                                              .toString() ==
                                          'rplc'
                                      ? const Text("Laos")
                                      : const Text('')
                          : Text(transalationCtl.location.value)),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              if (isGuest.value) {
                Get.to(() => const RegisterPage());
              } else {
                //set pin
                debugPrint("set pin");
              }
            },
            child: _buildMenuBar(
                context,
                accountSettingPIN.tr,
                SvgPicture.string(
                  AppSvgImage.pin_setting,
                )),
          ),
          Container(
            width: 327.w,
            height: 48.h,
            child: Row(
              // mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  child: Row(
                    children: [
                      SvgPicture.string(AppSvgImage.face_ID),
                      SizedBox(width: 9.w),
                      SizedBox(
                        // width: 299,
                        // height: 26.h,
                        child: Text.rich(
                          TextSpan(
                            children: <InlineSpan>[
                              TextSpan(
                                text: accountSettingBiometric.tr,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,

                                  // height: 0.12,
                                ),
                              ),
                              // const TextSpan(
                              //   text: "\n",
                              // ),
                              TextSpan(
                                text: accountSettingBioDes.tr,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.5),
                                  fontSize: 10.sp,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                  // height: 0.14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                GetBuilder<AppConnectController>(builder: (appConnectCtl) {
                  return Container(
                    color: Colors.transparent,
                    child: AdvancedSwitch(
                      activeColor: configTheme().colorScheme.primary,
                      inactiveColor: const Color(0xFFD3D5DD),
                      width: 23.w,
                      height: 14.h,
                      borderRadius: BorderRadius.circular(200),
                      controller: appConnectCtl.controllerBiometric,
                      initialValue: appConnectCtl.controllerBiometric.value,
                      onChanged: (value) async {
                        print('biometric: $value');
                        if (isGuest.value) {
                          Get.to(() => const RegisterPage());
                        }
                        if (!kIsWeb) {
                          await appConfigCtl.checkBiometrics();
                          appConfigCtl.checkPermissionBio();
                        }
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
          _buildDivider()
        ],
      ),
    );
  }

  Widget _buildProfileSetting(context) {
    final KYCController kycController = Get.put(KYCController());
    return Container(
      child: Column(
        children: [
          _buildHeaderMenu(context, accountSetting.tr),
          //TODO KYC Sumsub
          Container(
            width: 327.w,
            height: 48.h,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.string(AppSvgImage.user_kyc),
                      SizedBox(width: 9.w),
                      SizedBox(
                        height: 20.h,
                        child: Center(
                          child: Text(
                            accountSettingKYC.tr,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: 12.sp,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                              // height: 0.12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // const SizedBox(width: 144),
                kycController.sumsubData.value.sumsub_passed.obs == true
                    ? KYCStatusWidget(
                        title: statusVerify.tr,
                        onPressed: () {},
                        backgroundColor: const Color(0xFFE4FFE9),
                        textColor: const Color(0xFF2B9A22),
                      )
                    : kycController.sumsubData.value.sumsub_status.obs
                                    .toString() ==
                                "pending" ||
                            kycController.sumsubData.value.sumsub_status.obs
                                    .toString() ==
                                ""
                        ? KYCStatusWidget(
                            title: statusPending.tr,
                            onPressed: () {},
                            backgroundColor: const Color(0xFFFFF6CC),
                            textColor: const Color(0xFFFFC600),
                          )
                        : kycController.sumsubData.value.sumsub_status.obs
                                    .toString() ==
                                "problem"
                            ? KYCStatusWidget(
                                title: statusReject.tr,
                                onPressed: () {},
                                backgroundColor: const Color(0xFFFFE6E7),
                                textColor: const Color(0xFFFF3B30),
                              )
                            : Container(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                      child: Center(
                                        child: Text(
                                          accountSettingKYCDes.tr,
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color,
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyLarge
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyLarge
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyLarge
                                                ?.fontWeight,
                                            decorationColor: Colors.black,
                                            decoration:
                                                TextDecoration.underline,
                                            // height: 0.08,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
              ],
            ),
          ),
          //TODO KYC Bank
          Container(
            width: 327.w,
            height: 48.h,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.string(AppSvgImage.bank_kyc),
                      SizedBox(width: 9.w),
                      SizedBox(
                        height: 20.h,
                        child: Center(
                          child: Text(
                            accountSettingBank.tr,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                              // height: 0.12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // const SizedBox(width: 144),
                kycController.bookbankData.value.bookbank_status.obs ==
                        "success"
                    ? KYCStatusWidget(
                        title: statusVerify.tr,
                        onPressed: () {},
                        backgroundColor: const Color(0xFFE4FFE9),
                        textColor: const Color(0xFF2B9A22),
                      )
                    : kycController.bookbankData.value.bookbank_status.obs ==
                            "pending"
                        ? KYCStatusWidget(
                            title: statusPending.tr,
                            onPressed: () {},
                            backgroundColor: const Color(0xFFFFF6CC),
                            textColor: const Color(0xFFFFC600),
                          )
                        : kycController
                                    .bookbankData.value.bookbank_status.obs ==
                                "problem"
                            ? KYCStatusWidget(
                                title: statusReject.tr,
                                onPressed: () {},
                                backgroundColor: const Color(0xFFFFE6E7),
                                textColor: const Color(0xFFFF3B30),
                              )
                            : Container(),
              ],
            ),
          ),
          // _buildMenuBar(
          //     context,
          //     'บัญชีธนาคาร',
          //     SvgPicture.string(
          //       AppSvgImage.bank_kyc,
          //     )),
          //TODO Social media
          //TODO line Connect
          _buildSocialMedia(context, SvgPicture.string(AppSvgImage.line_chat),
              accountSettingLine.tr, appConnectCtl.controllerLine),
          //TODO Facebook Connect
          _buildSocialMedia(context, SvgPicture.string(AppSvgImage.facebook),
              accountSettingFacebook.tr, appConnectCtl.controllerFacebook),
          //TODO Telegram Connect
          _buildSocialMedia(context, SvgPicture.string(AppSvgImage.telegram),
              accountSettingTG.tr, appConnectCtl.controllerTelegram),
          //TODO Apple Connect
          _buildSocialMedia(context, SvgPicture.string(AppSvgImage.apple),
              accountSettingApple.tr, appConnectCtl.controllerApple),
          //TODO Google Connect
          _buildSocialMedia(context, SvgPicture.string(AppSvgImage.google),
              accountSettingGoogle.tr, appConnectCtl.controllerGoogle),
          //TODO Delete Account
          GestureDetector(
            onTap: () {
              if (isGuest.value) {
                Get.to(() => const RegisterPage());
              } else {
                if (appConfigService.countryConfigCollection.toString() ==
                    "aam") {
                  _buildDeleteAccount(context);
                } else {
                  _buildDeleteAccountAbroad(context);
                }
              }
            },
            child: _buildMenuBar(
                context,
                accountSettingDelete.tr,
                SvgPicture.string(
                  AppSvgImage.delete_account,
                )),
          ),
          _buildDivider()
        ],
      ),
    );
  }

  Widget _buildHelpCenter(context) {
    return Column(
      children: [
        _buildHeaderMenu(context, accountSettingHelp.tr),
        Container(
          width: 327.w,
          // height: 48.h,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.string(AppSvgImage.chat),
              SizedBox(width: 9.w),
              SizedBox(
                // height: 26.h,
                child: Center(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: accountSettingHelpTitle.tr,
                          style: TextStyle(
                            color: const Color(0xFF1A1818),
                            fontSize: 12.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                            // height: 0.12,
                          ),
                        ),
                        // const TextSpan(text: "\n"),
                        TextSpan(
                          text: accountSettingHelpDes.tr,
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: 10.sp,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontWeight,
                            // height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        _buildDivider()
      ],
    );
  }

  Widget _buildPolicy(context) {
    final country = appConfigService.countryConfigCollection.toString();
    return Container(
      child: Column(
        children: [
          _buildHeaderMenu(context, accountSettingTerm.tr),
          GestureDetector(
            onTap: () {
              country == 'aam'
                  ? PrivacyPolicyWidget.buildPrivacyPolicyAAM(context)
                  : country == 'rplc'
                      ? PrivacyPolicyWidget.buildPrivacyPolicyRPLC(context)
                      : country == 'rafco'
                          ? PrivacyPolicyWidget.buildPrivacyPolicyRAFCO(context)
                          : Container();
            },
            child: _buildMenuBar(context, accountSettingPolicy.tr,
                SvgPicture.string(AppSvgImage.privacy_policy)),
          ),
          GestureDetector(
            onTap: () {
              print('click');
              country == 'aam'
                  ? TermsAndConditionWidget.buildTermsAndConditionsAAM(context)
                  : country == 'rplc'
                      ? TermsAndConditionWidget.buildTermsAndConditionsRPLC(
                          context)
                      : country == 'rafco'
                          ? TermsAndConditionWidget
                              .buildTermsAndConditionsRAFCO(context)
                          : Container();
            },
            child: _buildMenuBar(context, accountSettingCondition.tr,
                SvgPicture.string(AppSvgImage.policy)),
          ),
          _buildDivider()
        ],
      ),
    );
  }

  Widget _buildOtherSetting(context) {
    return Container(
      child: Column(
        children: [
          _buildHeaderMenu(context, accountSettingAnother.tr),
          GetBuilder<AppConnectController>(builder: (appConnectCtl) {
            return Column(
              children: [
                GestureDetector(
                  onTap: () => Get.to(() => FeedbackReactionScreen()),
                  child: _buildMenuBar(context, feedbackMenu.tr,
                      SvgPicture.string(AppSvgImage.privacy_policy)),
                ),
                _buildSocialMedia(
                    context,
                    SvgPicture.string(AppSvgImage.notify),
                    accountSettingNotification.tr,
                    appConnectCtl.controllerNotification),
              ],
            );
          }),
          GestureDetector(
            onTap: () {
              if (isGuest.value) {
                Get.to(() => const RegisterPage());
              } else {
                _buildLogOut(context);
              }
            },
            child: _buildMenuBar(context, accountSettingLogout.tr,
                SvgPicture.string(AppSvgImage.logout)),
          ),
          _buildDivider()
        ],
      ),
    );
  }

  Widget _buildAppVersion(context) {
    final AppVersionController appVersionCtl = Get.put(AppVersionController());
    return Container(
      width: 327.w,
      height: 48.h,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Obx(() {
                  return Text(
                    'Version ${appVersionCtl.versionInApp!.value.toString()}',
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.5),
                      fontSize: 10.sp,
                      fontFamily:
                          configTheme().primaryTextTheme.labelSmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.labelSmall?.fontWeight,
                      height: 0.24,
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderMenu(context, String title) {
    return Container(
      width: 327.w,
      height: 48.h,
      color: Colors.transparent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 20.h,
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  color: configTheme()
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.5),
                  fontSize:
                      configTheme().primaryTextTheme.labelMedium?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.labelMedium?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.labelMedium?.fontWeight,
                  // height: 0.12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialMedia(context, Widget icon, String title, _controller) {
    return GestureDetector(
      onTap: () {
        if (isGuest.value) {
          Get.to(() => const RegisterPage());
        }
        if (title == accountSettingFacebook.tr) {
          //TODO facebook connect function
          print('facebook connect');
          if (_controller.value == false) {
            print('FacebookAuthentication');
            FacebookAuthentication().signUp(context);
          }
        } else if (title == accountSettingGoogle.tr) {
          Get.lazyPut(() => GoogleController());
          Get.find<GoogleController>().signInWithGoogle();
        } else {
          poppupClose();
        }
      },
      child: Container(
        width: 327.w,
        height: 48.h,
        color: Colors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  icon,
                  SizedBox(width: 9.w),
                  SizedBox(
                    height: 20.h,
                    child: Center(
                      child: Text(
                        title,
                        style: TextStyle(
                          color: configTheme().textTheme.bodyMedium?.color,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontWeight,
                          // height: 0.12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              color: Colors.transparent,
              child: AdvancedSwitch(
                activeColor: configTheme().colorScheme.primary.withOpacity(1.0),
                inactiveColor: const Color(0xFFD3D5DD),
                width: 23.w,
                height: 14.h,
                enabled: title == accountSettingLine.tr ||
                        title == accountSettingFacebook.tr ||
                        title == accountSettingTG.tr ||
                        title == accountSettingApple.tr ||
                        title == accountSettingGoogle.tr && _controller.value
                    ? false
                    : true,
                disabledOpacity: 1.0,
                borderRadius: BorderRadius.circular(200),
                controller: _controller,
                initialValue: _controller.value,
                onChanged: (value) async {
                  if (isGuest.value) {
                    Get.to(() => const RegisterPage());
                  }
                  if (title == accountSettingNotification.tr) {
                    await openAppSettings();
                  }
                  if (title == accountSettingFacebook.tr) {
                    //TODO facebook connect function
                    print('facebook connect');
                    if (_controller.value == false) {
                      print('FacebookAuthentication');
                      FacebookAuthentication().signUp(context);
                    }
                  }
                  print('${_controller} : $value');
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildMenuBar(context, String title, Widget icon) {
    return Container(
      width: 327.w,
      height: 48.h,
      color: Colors.transparent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          icon,
          SizedBox(width: 9.w),
          SizedBox(
            height: 20.h,
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  color: configTheme().textTheme.bodyMedium?.color,
                  fontSize: configTheme().primaryTextTheme.labelSmall?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.labelSmall?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.labelSmall?.fontWeight,
                  // height: 0.12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      alignment: Alignment.bottomCenter,
      child: Divider(
        height: 0.5.h,
        thickness: 0.5.h,
        color: Colors.black.withOpacity(0.2),
      ),
    );
  }

  buildChangeLanguage(context) {
    return showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
              height: 250.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              // color: Colors.teal,
              child: ListView.builder(
                itemCount: transalationCtl.listLanguage.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      transalationCtl.updateLanguage(
                          transalationCtl.listLanguage[index]['locale']);
                      transalationCtl.location.value =
                          transalationCtl.listLanguage[index]['name'];
                      transalationCtl.update();
                      Get.back();
                    },
                    child: Container(
                      height: 48.h,
                      // color: Colors.teal,
                      margin:
                          EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
                      // decoration: BoxDecoration(
                      //   border: Border(
                      //     bottom: BorderSide(
                      //       color: Colors.black.withOpacity(0.2),
                      //       width: 0.5.h,
                      //     ),
                      //   ),
                      // ),
                      child: Center(
                        child: Text(
                          transalationCtl.listLanguage[index]['name']
                              .toString(),
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontWeight,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ));
        });
  }

  poppupClose() {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Under development"),
        content: const Text("Under development"),
        actions: [
          TextButton(
            onPressed: () {
              // Delete item logic here
              Navigator.pop(context);
            },
            child: const Text("Ok"),
          ),
        ],
      ),
    );
  }
}
