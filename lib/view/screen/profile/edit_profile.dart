import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:AAMG/controller/profile/edit.profile.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';

import '../../../controller/register/registerAddress.controller.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({Key? key}) : super(key: key);

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final EditProfileController editProfileController =
      Get.put(EditProfileController());
  final MRController mrCtl = Get.put(MRController());
  final RegisterAddressController addressCtl =
      Get.put(RegisterAddressController());
  var getAddress = Get.find<RegisterAddressController>().checkConfigAddress();
  final ProfileController profileCtl = Get.find<ProfileController>();

  @override
  void initState() {
    super.initState();
    profileCtl.getProfile();
    addressCtl.checkConfigAddress();

  }
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileController>(
      init: ProfileController(),
      // initState: (_){
      //   profileCtl.getProfile();
      // },
        builder: (profileControl){
        return Scaffold(
        //   floatingActionButton: FloatingActionButton(
        //     onPressed: () {
        //       addressCtl.getSubDistrictDataRPLC();
        //     },
        //     child: const Icon(Icons.add),
        //   ),
        // backgroundColor: Colors.white,
        body: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              margin: EdgeInsets.only(top: 137.h),
              child: Column(
                children: [
                  _buildProfileImage(context),
                  _buildProfileMenu(context)
                ],
              ),
            ),
            headerEditProfile(context),
          ],
        ));});
  }

  Widget headerEditProfile(context) {
    return Container(
      height: 120.h,
      width: Get.width,
      color: Colors.white.withOpacity(0.9),
      child: GetBuilder<ProfileController>(builder: (profileCtl) {
        return Container(
            margin: EdgeInsets.only(left: 24.w, right: 24.w),
            child: Stack(
              children: [
                //TODO slide page
                Container(
                  height: 24.h,
                  width: Get.width,
                  // color: Colors.red,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                    top: 67.h,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        accountEdit.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: const Color(0xFF1A1818),
                          fontSize: 16,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w700,
                          height: 0.09,
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  // color: Colors.blue,
                  width: Get.width,
                  height: 20.h,
                  margin: EdgeInsets.only(top: 93.h),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        // width: 160,
                        height: 20.h,
                        child: Center(
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: accountEditProfileAll.tr,
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(0.5),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                    height: 0.12,
                                  ),
                                ),
                                TextSpan(
                                  text: ' ',
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                    height: 0.12,
                                  ),
                                ),
                                TextSpan(
                                  text:
                                      '${profileCtl.percentUpdateProfileText} %',
                                  style: TextStyle(
                                    color: configTheme().colorScheme.primary,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                    height: 0.12,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Container(
                        // color: Color(0xFF792AFF),
                        height: 20.h,
                        child: LinearPercentIndicator(
                          width: 48.0.w,
                          lineHeight: 5.h,
                          percent: profileCtl.percentUpdateProfile.value,
                          //TODO % ความครบถ้วนของข้อมูล
                          curve: Curves.linear,
                          barRadius: const Radius.circular(10),
                          animation: true,
                          backgroundColor: configTheme()
                              .colorScheme
                              .primary
                              .withOpacity(0.2),
                          progressColor:
                              configTheme().colorScheme.primary.withOpacity(1),
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // print("profile ${profileCtl.profile.value.email.obs.string}");
                    editProfileController.isEdit!.value = true;
                    editProfileController.isEmailValid!.value = false;
                    editProfileController.selectEmail!.value = "";
                    // print("profile ${editProfileController.isEmailValid!.value}");
                    // print("profile ${editProfileController.isEdit!.value}");
                    setState(() {});
                  },
                  child: Container(
                      alignment: Alignment.centerRight,
                      margin: EdgeInsets.only(top: 60.h),
                      child: SvgPicture.string(
                        appConfigService.countryConfigCollection == "aam"
                            ? AppSvgImage.edit_profile
                            : '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_51310)"><path d="M21 12V17.4C21 19.38 19.38 21 17.4 21H6.59998C4.61998 21 3 19.38 3 17.4V6.60001C3 4.62001 4.61998 3 6.59998 3H12" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M18.5001 9.16977L14.4001 13.2697C14.2201 13.4497 14.0101 13.5498 13.7601 13.5698L11.3101 13.7997C10.6701 13.8597 10.1301 13.3098 10.1901 12.6698L10.4101 10.2898C10.4301 10.0398 10.5301 9.81975 10.7101 9.64975L14.8501 5.50977L18.5001 9.16977V9.16977Z" stroke="#1A1818" stroke-width="1.5" stroke-linejoin="round"/><path d="M20.7001 6.98002L18.5001 9.17003L14.8501 5.51002L17.0401 3.32002C17.4401 2.92002 18.1001 2.92002 18.5001 3.32002L20.7001 5.51002C21.0901 5.91002 21.0901 6.58002 20.7001 6.98002V6.98002Z" stroke="#1A1818" stroke-width="1.5" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3898_51310"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>',
                        width: 24.w,
                        height: 24.h,
                      )),
                ),
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    height: 46.h,
                    width: 46.w,
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.only(
                      top: 60.h,
                    ),
                    child: Center(
                      child: SvgPicture.string(
                        AppSvgImage.back_btn,
                      ),
                    ),
                  ),
                ),
              ],
            ));
      }),
    );
  }

  Widget _buildProfileImage(context) {
    return Container(
      // width: 57.w,
      // height: 85.h,
      child: GetBuilder<ProfileController>(builder: (profileCtl) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Obx(() {
              return Container(
                width: 55,
                height: 55,
                padding: EdgeInsets.only(top: 11, left: 11, right: 11),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  image: DecorationImage(
                    image: NetworkImage(profileCtl.profile.value.avatar
                            .toString()
                            .isNotEmpty && profileCtl.profile.value.avatar
                        .toString().toLowerCase() != 'null'
                        ? profileCtl.profile.value.avatar.obs.string
                        : 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png'),
                    fit: BoxFit.cover,
                  ),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1.w,
                      color: Color(0xFF1A1818),
                    ),
                    borderRadius: BorderRadius.circular(50.r),
                  ),
                ),
              );
            }),
            SizedBox(
              height: 10.h,
            ),
            GestureDetector(
              onTap: () {
                buildUploadImage(context);
                print('upload image');
              },
              child: Container(
                height: 17.h,
                color: Colors.transparent,
                // color: Colors.red,
                child: Center(
                  child: Text(
                    // "อัพโหลดรูปภาพ",
                    accountEditUploadImg.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: configTheme().colorScheme.primary,
                      fontSize: 12.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w700,
                      // height: 0.12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildProfileMenu(context) {
    return Container(
      width: 327.w,
      // color: Colors.teal,
      // height: 288.h,
      margin: EdgeInsets.only(top: 10.h, left: 24.w, right: 24.w),
      child: GetBuilder<ProfileController>(builder: (profileCtl) {
        return Column(
          children: [
            GestureDetector(
              onTap: () {
                _buildEditMoreData(context);
              },
              child: Container(
                height: 48.h,
                width: 327.w,
                // color: Colors.orange,
                child: _buildListProfileMenu(
                    context,
                    'name',
                    accountEditProfileName.tr,
                    '${profileCtl.profile.value.firstname.obs.string} ${profileCtl.profile.value.lastname.obs.string}'),
              ),
            ),
            _buildDivider(),
            GestureDetector(
              onTap: () {
                _buildEditMoreData(context);
              },
              child: _buildListProfileMenu(
                  context,
                  'referral',
                  accountEditProfileIDMr.tr,
                  mrCtl.mrData.value.mr_id.obs.string != ""
                      ? "-"
                      : mrCtl.mrData.value.mr_id.obs.string),
            ),
            _buildDivider(),
            GestureDetector(
                onTap: () {
                  _buildUpdateData(context, 'email');
                },
                child: Container(
                  height: 48.h,
                  width: 327.w,
                  // color: Colors.pink,
                  child: _buildListProfileMenu(
                      context,
                      'email',
                      accountEditProfileEmail.tr,
                      editProfileController.isEdit!.value == true
                          ? editProfileController.selectEmail!.value
                          : profileCtl.profile.value.email.obs.string),
                )),
            _buildDivider(),
            GestureDetector(
              onTap: () {
                _buildEditMoreData(context);
              },
              child: _buildListProfileMenu(
                  context,
                  'phone',
                  accountEditProfilePhone.tr,
                  profileCtl.profile.value.phone.obs.string.substring(0, 3) +
                      "-" +
                      profileCtl.profile.value.phone.obs.string.substring(
                          3, profileCtl.profile.value.phone.obs.string.length)),
            ),
            _buildDivider(),
            _buildListProfileMenu(
                context,
                'idcard',
                accountEditProfileIDCard.tr,
                profileCtl.profile.value.idcard.obs.string),
            _buildDivider(),
            _buildListProfileMenu(
                context,
                'address',
                accountEditProfileAddress.tr,
                editProfileController.isEdit!.value == true
                    ? ""
                    : appConfigService.countryConfigCollection == 'aam'? '${profileCtl.numHome.obs.string ?? ""} ม.${profileCtl.numMoo.obs.string ?? ""} ต.${profileCtl.profile.value.tumbol.obs.string} อ.${profileCtl.profile.value.amphur.obs.string} จ.${profileCtl.profile.value.province.obs.string}' : '${profileCtl.numHome.obs.string ?? ""} ${profileCtl.numMoo.obs.string ?? ""} ${profileCtl.profile.value.tumbol.obs.string} ${profileCtl.profile.value.amphur.obs.string} ${profileCtl.profile.value.province.obs.string}'),
          ],
        );
      }),
    );
  }

  _buildEditMoreData(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        builder: (context) {
          return appConfigService.countryConfigCollection.toString() == 'aam'
              ? Container(
                  width: Get.width,
                  height: 395.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    // mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 15.h,
                      ),
                      Container(
                        child: SvgPicture.string(
                            '<svg width="44" height="5" viewBox="0 0 44 5" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="44" height="5" rx="2.5" fill="#1A1818" fill-opacity="0.2"/></svg>'),
                      ),
                      SizedBox(
                        height: 30.h,
                      ),
                      Container(
                        child: SvgPicture.string(
                            '<svg width="78" height="70" viewBox="0 0 78 70" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.15" d="M39.5 70C58.83 70 74.5 54.33 74.5 35C74.5 15.67 58.83 0 39.5 0C20.17 0 4.5 15.67 4.5 35C4.5 54.33 20.17 70 39.5 70Z" fill="url(#paint0_linear_3624_30024)"/><path opacity="0.5" d="M39.5002 57.7982C52.0914 57.7982 62.2986 47.5909 62.2986 34.9997C62.2986 22.4084 52.0914 12.2012 39.5002 12.2012C26.9089 12.2012 16.7017 22.4084 16.7017 34.9997C16.7017 47.5909 26.9089 57.7982 39.5002 57.7982Z" fill="url(#paint1_linear_3624_30024)"/><g clip-path="url(#clip0_3624_30024)"><path fill-rule="evenodd" clip-rule="evenodd" d="M44.45 61.088C43.5766 59.8807 42.3103 58.8563 41.9993 57.404C41.6296 55.6876 42.7248 53.9589 42.5667 52.2124C42.3668 50.0131 40.3182 48.491 39.2657 46.5467C38.2326 44.6432 38.7917 44.0473 38.8026 41.8862C38.8148 38.6305 36.3391 34.3919 32.6981 35.31C29.0026 36.243 29.4238 39.6226 29.9316 42.1776C30.2951 43.9965 29.983 44.2401 29.4486 46.0151C28.8002 48.1606 26.9646 50.1713 27.5762 52.327C27.9541 53.6604 29.2055 54.6097 29.6079 55.9373C30.0628 57.4412 29.3363 59.008 29.0092 60.5429C28.317 63.7763 28.9265 66.5243 31.1766 69.3354C31.4513 69.624 31.7397 69.8884 32.0434 70.1341C35.3571 70.1354 39.0524 70.172 43.0706 70.2393C43.1287 70.1874 43.1851 70.1336 43.2426 70.078C45.5521 67.8222 46.4148 63.7987 44.4491 61.0879L44.45 61.088Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M34.0193 38.3448C33.9717 38.1954 33.8087 38.1136 33.656 38.1625C33.5051 38.2115 33.4225 38.3729 33.472 38.5224C33.7183 39.2726 33.9165 40.0549 34.08 40.8683C34.0017 40.7908 33.9198 40.7149 33.8352 40.6397C33.4617 40.309 33.03 40.0151 32.548 39.7586C32.4081 39.6824 32.2345 39.7364 32.1577 39.8742C32.0834 40.0139 32.1371 40.1849 32.2762 40.261C32.7208 40.4971 33.1127 40.7659 33.4529 41.0657C33.7743 41.3513 34.0512 41.6647 34.2828 42.0095C34.5601 43.7655 34.7031 45.6476 34.8177 47.6292C34.5118 47.0982 34.179 46.6207 33.822 46.1952C33.2024 45.4543 32.5043 44.867 31.7275 44.4361C31.5885 44.3573 31.4134 44.4068 31.3357 44.5445C31.2589 44.6806 31.3062 44.8548 31.4453 44.9309C32.1578 45.3276 32.8018 45.8714 33.3794 46.5597C33.9519 47.2432 34.4569 48.0722 34.893 49.0419L34.9215 49.5792C34.9828 50.7601 35.047 51.9722 35.1303 53.2101C35.1297 53.2216 35.13 53.2331 35.1339 53.2457C35.2215 54.5387 35.3354 55.8613 35.4945 57.2075C34.9224 56.2246 34.2971 55.3524 33.6181 54.5855C32.6251 53.4616 31.5182 52.5692 30.2964 51.9093C30.1573 51.8331 29.983 51.8854 29.9062 52.0223C29.831 52.161 29.882 52.3336 30.0211 52.4089C31.1819 53.0392 32.2382 53.8891 33.1857 54.9602C34.1315 56.0303 34.972 57.3209 35.7091 58.8302C35.9705 60.6182 36.3275 62.4421 36.8313 64.2947L36.8337 64.301C37.0807 65.2098 37.3632 66.1257 37.6874 67.0455C36.8555 66.2619 35.9668 65.5755 35.0157 64.9869C33.6915 64.165 32.2589 63.5359 30.7146 63.0987C30.5622 63.0546 30.403 63.1421 30.3594 63.294C30.314 63.4458 30.4033 63.6035 30.5565 63.6476C32.0507 64.0709 33.4378 64.679 34.711 65.4682C35.9712 66.2497 37.1254 67.2126 38.1756 68.3545C38.4174 68.9681 38.6778 69.5844 38.9596 70.201L39.5966 70.2077C38.6505 68.1776 37.9387 66.163 37.396 64.1791C37.361 62.7834 37.5988 61.4034 38.111 60.0382C38.6305 58.6548 39.4312 57.2795 40.5161 55.9205C40.6137 55.797 40.5927 55.6162 40.4698 55.5205C40.3453 55.422 40.1645 55.4429 40.0652 55.5663C38.9455 56.9749 38.1147 58.3974 37.5743 59.8382C37.2673 60.6568 37.053 61.481 36.9334 62.3073C36.2309 59.1791 35.9102 56.1385 35.7097 53.2431C36.109 52.0396 36.5387 51.0271 36.9996 50.2039C37.4569 49.3841 37.9407 48.7578 38.451 48.3295C38.5707 48.2267 38.5854 48.0476 38.4816 47.9272C38.3787 47.8086 38.1972 47.7923 38.0784 47.8952C37.512 48.372 36.9852 49.0493 36.4955 49.9276C36.1901 50.4744 35.8984 51.1007 35.6177 51.8091C35.5732 51.0443 35.5345 50.2905 35.4952 49.5482L35.4642 48.9727C35.4648 48.9612 35.4643 48.9532 35.4631 48.9417C35.2574 45.0081 35.028 41.4284 34.0167 38.3447L34.0193 38.3448Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M34.5677 62.145C33.5968 61.5607 32.4086 61.2186 31.759 60.2981C30.9902 59.2105 31.2337 57.6846 30.6024 56.5154C29.8054 55.0411 27.9242 54.5822 26.6062 53.5374C25.3184 52.5119 25.5324 51.9357 24.8928 50.426C23.9271 48.1531 20.9332 45.9284 18.6717 47.6359C16.3766 49.3717 17.6815 51.6024 18.7994 53.2322C19.5966 54.3921 19.4525 54.654 19.6092 56.047C19.7999 57.7323 19.1229 59.6726 20.1951 60.9936C20.8554 61.8118 22.0112 62.1054 22.689 62.9121C23.4552 63.8252 23.4162 65.1303 23.6472 66.2969C23.9583 67.8669 24.5815 69.1428 25.6183 70.1981C28.4983 70.149 31.8366 70.1409 35.5646 70.1669C35.9147 69.7428 36.2047 69.2696 36.416 68.7614C37.3495 66.5126 36.7462 63.4558 34.5668 62.1449L34.5677 62.145Z" fill="url(#paint2_linear_3624_30024)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.619 49.2992C20.5415 49.2085 20.4043 49.1998 20.3144 49.2784C20.2228 49.3552 20.214 49.4912 20.2915 49.5828C20.6875 50.0316 21.0609 50.5199 21.4175 51.0384C21.3395 51.0079 21.2588 50.979 21.1771 50.9518C20.8195 50.8318 20.4312 50.7518 20.0175 50.7139C19.8992 50.7026 19.7945 50.7911 19.782 50.9109C19.7724 51.0282 19.8599 51.1327 19.9782 51.1441C20.3608 51.1795 20.7137 51.2506 21.0406 51.3602C21.3508 51.4628 21.636 51.6004 21.9006 51.7733C22.6201 52.9142 23.282 54.1833 23.9534 55.529C23.5816 55.2515 23.2086 55.0156 22.8324 54.8228C22.1774 54.4893 21.5158 54.2856 20.8455 54.2125C20.7264 54.2002 20.6191 54.2869 20.6068 54.4031C20.5917 54.5219 20.6792 54.6282 20.7973 54.6431C21.4125 54.708 22.0249 54.8977 22.631 55.2076C23.2353 55.5173 23.8336 55.9445 24.4274 56.4919L24.6087 56.858C25.0032 57.6627 25.411 58.4876 25.8387 59.3258C25.8437 59.3341 25.8469 59.3413 25.8509 59.3495C26.299 60.2251 26.7742 61.1119 27.2868 62.0032C26.5957 61.4871 25.8981 61.0628 25.1975 60.7287C24.1709 60.2372 23.1314 59.9408 22.0837 59.8379C21.9627 59.8264 21.8571 59.9149 21.8448 60.0329C21.8351 60.1502 21.9226 60.2565 22.0409 60.2678C23.0361 60.3654 24.0261 60.6496 25.0094 61.1168C25.9872 61.5846 26.9573 62.2379 27.9241 63.0744C28.6396 64.2408 29.4328 65.4077 30.3401 66.5484L30.3434 66.5539L30.3452 66.554C30.7873 67.114 31.2573 67.6684 31.76 68.2148C30.9453 67.9112 30.1207 67.6956 29.2819 67.5626C28.1159 67.3801 26.9288 67.3631 25.7232 67.5116C25.6027 67.5258 25.5185 67.6331 25.532 67.7516C25.5465 67.8692 25.6565 67.9537 25.7771 67.9394C26.9457 67.7952 28.0917 67.8119 29.2151 67.9896C30.3261 68.1648 31.4192 68.4959 32.4908 68.9834C32.8842 69.379 33.2921 69.7699 33.72 70.1565L34.3777 70.159C32.9663 68.9324 31.757 67.6345 30.6997 66.3019C30.2578 65.3416 30.0091 64.3088 29.9588 63.2081C29.905 62.0904 30.0553 60.8972 30.4033 59.6317C30.4343 59.5164 30.3662 59.3978 30.2498 59.3671C30.1335 59.3355 30.0138 59.4029 29.9828 59.5182C29.6209 60.8264 29.4693 62.062 29.5243 63.2267C29.5554 63.8863 29.653 64.5218 29.8162 65.134C28.3898 63.1601 27.2566 61.1364 26.2513 59.1777C26.1711 58.2243 26.1677 57.3916 26.242 56.6817C26.3161 55.9761 26.4639 55.4 26.6919 54.9511C26.7448 54.8449 26.7006 54.7142 26.5925 54.6618C26.4862 54.6085 26.3552 54.6515 26.3014 54.7577C26.0513 55.255 25.8856 55.8833 25.808 56.6392C25.7577 57.1104 25.7432 57.6322 25.7581 58.2068C25.4989 57.6879 25.2457 57.1747 24.9975 56.6688L24.8033 56.2754C24.801 56.2691 24.796 56.2618 24.7936 56.2555C23.4735 53.5758 22.245 51.1479 20.6164 49.2982L20.619 49.2992Z" fill="white"/><path d="M42.3293 37.5494C41.2421 40.9429 39.9916 44.266 38.4998 47.5148C37.3322 49.9844 36.074 52.4843 34.2224 54.6077C33.4823 55.4209 32.6924 56.1447 31.5861 56.5902L30.4912 53.967C30.6979 53.8663 30.9429 53.6977 31.1867 53.4761C32.0123 52.7033 32.6248 51.7328 33.2092 50.7132C35.5125 46.4776 36.9877 41.7322 38.274 37.0887L38.4296 36.4883L42.3306 37.5494H42.3293Z" fill="#F9C2C5"/><path d="M40.2476 52.1446L40.4007 63.618C40.6648 70.9778 40.9685 79.916 41.7712 87.2368L43.2642 100.864L47.3361 101.615L48.1554 92.0553C48.3876 89.344 48.4501 86.6213 48.3429 83.9024L48.1018 77.7737L48.6735 59.9764L52.776 79.8732C53.1014 81.5574 54.0138 83.0767 55.355 84.1668L70.8605 96.5578L72.3701 92.7513L60.0445 76.6282L58.0577 52.1421H40.2476V52.1446Z" fill="#792AFF"/><path d="M42.1938 13.3851C43.0909 11.6014 44.7689 10.2093 46.7124 9.66299C48.809 9.0739 51.171 9.48173 52.9409 10.7379C54.4352 11.8003 55.4471 13.3662 56.191 15.0504C57.0843 16.4387 57.8078 17.9404 58.8261 19.2432C61.4803 22.6418 65.9313 24.4531 68.1861 28.121C69.8718 30.8638 70.0364 34.4563 68.6097 37.3375C67.1831 40.22 64.2099 42.2994 60.9814 42.6758C57.5705 43.0723 54.1391 41.6449 51.5002 39.4761C49.2364 37.6157 47.5214 35.2631 45.9174 32.8501C45.8153 32.6978 45.6902 32.3668 45.546 32.2585C45.4235 32.1666 45.1734 32.183 45.0152 32.1452C44.6439 32.0584 44.2904 31.9035 43.9739 31.6959C43.3397 31.2805 42.8433 30.6498 42.6404 29.9223C42.3788 28.9883 42.5664 28.0002 42.6175 27.0322C42.7298 24.8786 42.1491 22.7563 41.7395 20.6379C41.2789 18.2538 41.0683 15.6281 42.1976 13.3825L42.1938 13.3851Z" fill="#1A1818"/><path d="M39.228 47.4219C39.2472 46.9146 39.274 46.4061 39.2982 45.8976C39.385 44.1354 39.5266 42.3794 39.7244 40.6336C40.083 37.3785 40.5615 33.9963 41.6717 30.9325C42.1592 29.6209 42.8291 28.3546 43.8334 27.3627C44.3004 26.9096 44.8351 26.5144 45.4501 26.1909C45.5407 26.143 45.6339 26.0977 45.727 26.0549C46.0614 25.9001 46.411 25.773 46.7709 25.6723C48.0495 25.316 49.4468 25.2997 50.7752 25.5804C51.1567 25.6609 51.5319 25.7679 51.8969 25.8976C52.554 26.1304 53.1767 26.4426 53.7382 26.8253C54.7871 27.5541 55.683 28.5472 56.3912 29.6939C58.6753 33.6086 58.9395 39.0778 58.9165 44.0158C58.9038 45.8347 58.8655 47.6535 58.7966 49.4749C58.7672 50.2654 58.8438 51.0508 58.5924 51.8237C58.3678 52.5173 57.9697 53.1479 57.4822 53.6388C56.7 54.4305 55.7085 54.8799 54.717 55.1103C53.3669 55.4262 51.99 55.3645 50.6272 55.3028C48.9121 55.2248 47.1971 55.1455 45.4795 55.0675C44.8593 55.0385 44.2391 55.0108 43.619 54.9819C42.8521 54.9466 42.0571 54.9038 41.354 54.5035C40.2527 53.8792 39.5713 52.4782 39.3607 51.0949C39.3556 51.0609 39.3518 51.0269 39.3467 50.9929C39.1808 49.7971 39.1834 48.6102 39.2267 47.4207L39.228 47.4219Z" fill="url(#paint3_linear_3624_30024)"/><path d="M45.7056 26.3318C45.7056 28.0424 47.1003 29.4282 48.8205 29.4282C50.5406 29.4282 51.9366 28.0424 51.9366 26.3318C51.9366 26.1857 51.9264 26.041 51.906 25.9C51.5372 25.7704 51.1595 25.6634 50.7767 25.5828C49.4457 25.3021 48.0484 25.3185 46.7685 25.6747C46.4074 25.7754 46.0565 25.9025 45.7183 26.0574C45.7107 26.148 45.7056 26.2399 45.7056 26.333V26.3318Z" fill="white"/><path d="M50.9132 25.5816C50.7141 24.7282 50.6771 23.8484 50.709 23.078C50.7639 21.7689 51.0216 20.7783 51.0216 20.7783L46.6677 21.9854C47.1552 23.287 46.8706 24.5407 46.6473 25.199C46.5452 25.4986 46.4546 25.6735 46.4546 25.6735L50.9132 25.5816Z" fill="#F9C2C5"/><path d="M46.3939 25.9579C46.3939 26.6427 47.43 28.4678 48.7074 28.4678C49.9847 28.4678 51.0209 26.6427 51.0209 25.9579C51.0209 25.827 50.9826 25.7011 50.9124 25.5828C49.4322 25.3021 47.8792 25.3185 46.4538 25.6747C46.4143 25.7666 46.3926 25.861 46.3926 25.9592L46.3939 25.9579Z" fill="#F9C2C5"/><g style="mix-blend-mode:multiply"><path d="M46.6455 25.1987C46.7782 25.2201 46.9135 25.2314 47.0526 25.2314C48.3121 25.2314 49.6277 24.3428 50.7085 23.079C50.7634 21.7699 51.0212 20.7793 51.0212 20.7793L46.6672 21.9864C47.1547 23.288 46.8701 24.5417 46.6468 25.2L46.6455 25.1987Z" fill="#C1725B"/></g><path d="M42.7095 16.4165C42.7095 19.2776 44.177 23.4704 46.9996 23.4704C49.8223 23.4704 52.9308 19.2776 52.9308 16.4165C52.9308 13.5554 50.6428 11.2368 47.8201 11.2368C44.9975 11.2368 42.7095 13.5567 42.7095 16.4165Z" fill="#F9C2C5"/><path d="M51.9327 17.1166C51.5537 17.7636 51.6149 18.4974 52.0692 18.7567C52.5235 19.0147 53.1985 18.7001 53.5775 18.0531C53.9565 17.4061 53.8953 16.6722 53.441 16.4129C52.9867 16.1536 52.3117 16.4696 51.9327 17.1166Z" fill="#F9C2C5"/><path d="M46.5675 21.3245C46.5675 21.3245 45.0771 21.2175 44.5781 20.9116C44.5781 20.9116 45.3438 23.5952 46.5675 21.3245Z" fill="white"/><path d="M42.8074 17.7728C42.5496 15.4995 43.4467 16.3189 43.9278 16.9294C44.2915 17.3914 45.3378 19.1876 47.682 19.0655C50.0274 18.9434 51.7067 18.1189 52.9305 16.3378C54.1529 14.5567 49.3855 9.9082 49.3855 9.9082L43.5781 12.4508L41.918 16.8262L42.8061 17.7728H42.8074Z" fill="#1A1818"/><path d="M55.7305 14.911C56.2486 15.5076 56.8764 15.9306 57.642 16.1483C56.8292 15.6146 56.1567 14.8745 55.7075 14.0186C55.7356 14.3383 55.7024 14.5913 55.7305 14.911Z" fill="#1A1818"/><path d="M59.1978 20.7438C60.0527 21.3518 61.0723 21.6954 62.12 21.5897C61.0417 21.2473 60.054 20.6268 59.282 19.8086C59.2667 20.1573 59.2118 20.3952 59.1978 20.7438Z" fill="#1A1818"/><path d="M65.7064 26.5112C66.8701 26.8624 67.8961 27.4477 68.8225 28.2256C68.136 27.1469 67.1751 26.2406 66.0522 25.6138C65.9437 25.9196 65.8135 26.2054 65.7051 26.5112H65.7064Z" fill="#1A1818"/><path d="M43.5112 16.4766C43.9451 17.3564 44.5359 18.1192 45.225 18.6919C44.8677 18.269 44.6673 17.6333 44.5895 17.0329C44.2297 16.7875 43.8711 16.722 43.5112 16.4766Z" fill="#1A1818"/><path d="M47.5192 18.2153C47.107 18.384 46.6834 18.6257 46.2725 18.7943C47.2346 19.3822 48.4354 19.5156 49.4971 19.1506C48.7263 19.2626 47.9377 18.8623 47.4069 18.2644L47.5192 18.2153Z" fill="#1A1818"/><path d="M41.5723 19.251C41.8339 20.4657 41.8351 21.7357 41.5735 22.9504C42.0597 22.5854 42.3647 21.9447 42.3507 21.343C42.3315 20.5563 41.8415 19.9924 41.5723 19.251Z" fill="#1A1818"/><path d="M46.3936 9.78167C46.7866 9.37888 47.3085 9.10196 47.8661 9C47.417 9.37888 47.1579 9.84083 47.0239 10.4085C46.8147 10.1681 46.6028 10.0234 46.3936 9.78167Z" fill="#1A1818"/><path d="M46.7408 9.86829C45.9037 9.47305 44.8165 9.68074 44.1899 10.3567C44.9798 10.2157 45.7557 10.2396 46.5277 10.4574C46.5724 10.2283 46.6949 10.0974 46.7395 9.86829H46.7408Z" fill="#1A1818"/><path d="M61.1297 35.1287C61.5941 37.7078 62.347 40.3625 62.6035 43.0864C62.6699 43.863 62.6814 44.6548 62.5971 45.4365C62.1403 49.8911 58.3721 51.8497 54.4979 53.0103C54.1074 53.1437 53.5677 53.2683 53.1708 53.3854L52.4077 51.0894C53.158 50.7873 54.0015 50.4286 54.7212 50.0598C56.546 49.1095 58.6222 47.8444 58.7523 45.6781C58.8353 44.2835 58.358 42.7516 57.905 41.333C57.3806 39.742 56.7668 38.0741 56.2142 36.3547L61.1309 35.1274L61.1297 35.1287Z" fill="#F9C2C5"/><path d="M51.9058 25.8984C57.0458 27.244 58.8195 27.6808 61.1037 35.0947L56.2482 36.429L51.9058 25.8984Z" fill="url(#paint4_linear_3624_30024)"/><path d="M38.4463 36.4948C39.7466 32.0628 43.1473 26.8441 45.7186 26.0562L42.3523 37.6327L38.4463 36.4948Z" fill="url(#paint5_linear_3624_30024)"/><path d="M56.1218 36.6199C56.067 36.6199 56.0159 36.5847 55.9993 36.5306L54.551 31.8808C54.5306 31.8141 54.5676 31.7436 54.6365 31.7222C54.7041 31.7021 54.7756 31.7398 54.7973 31.8065L56.2456 36.4563C56.266 36.523 56.229 36.5935 56.1601 36.6149C56.1474 36.6187 56.1346 36.6199 56.1218 36.6199Z" fill="white"/><path d="M39.3412 45.2566C39.3412 45.2566 39.3373 45.2566 39.3361 45.2566C39.2646 45.2541 39.2097 45.1949 39.2136 45.1244C39.5734 37.0924 41.0116 32.6151 42.1549 30.2726C43.4055 27.7098 44.568 27.0717 44.6164 27.0452C44.679 27.0125 44.7568 27.0352 44.79 27.0968C44.8232 27.1585 44.8002 27.2353 44.7377 27.268C44.7262 27.2743 43.5816 27.9138 42.3655 30.4224C41.2412 32.7423 39.8261 37.1793 39.4688 45.1357C39.4662 45.2037 39.4088 45.2566 39.3412 45.2566Z" fill="white"/><path d="M69.0014 95.2221C68.9734 95.2221 68.9453 95.2133 68.921 95.1944L55.4343 84.5682C54.0523 83.4794 53.0901 81.9387 52.7264 80.2306L48.435 60.0909L47.6081 56.5211C47.5928 56.4532 47.6349 56.3852 47.7051 56.3688C47.7753 56.3537 47.8429 56.3952 47.8595 56.4645L48.6877 60.0368L52.9791 80.1777C53.3313 81.8279 54.2603 83.3158 55.5963 84.3693L69.0831 94.9956C69.138 95.0396 69.1469 95.1189 69.1035 95.173C69.078 95.2045 69.041 95.2209 69.0027 95.2209L69.0014 95.2221Z" fill="#1A1818"/><path d="M43.0771 99.2893C43.012 99.2893 42.9571 99.2414 42.9495 99.176C42.9354 99.0526 41.5905 86.741 40.9741 79.2553C40.3578 71.7671 40.1217 54.9516 40.1191 54.7817C40.1191 54.7112 40.1753 54.6546 40.2455 54.6533H40.2467C40.3169 54.6533 40.3744 54.7087 40.3756 54.7779C40.3782 54.9466 40.613 71.7545 41.2293 79.2339C41.8457 86.7158 43.1906 99.0237 43.2047 99.147C43.2123 99.2163 43.1613 99.2792 43.0911 99.2868C43.086 99.2868 43.0822 99.2868 43.0771 99.2868V99.2893Z" fill="white"/><path d="M58.6237 66.1642L58.6236 66.1643L58.6301 66.167C60.7894 67.0916 63.3013 66.1245 64.252 63.9984C64.2561 63.9912 64.2598 63.9838 64.2632 63.9761C65.2069 61.8317 64.2103 59.3418 62.0426 58.413L62.0427 58.4129L62.036 58.4103L21.2618 42.2973C21.1369 42.2479 20.9953 42.3061 20.9411 42.429L18.7195 47.4702C18.6645 47.595 18.7196 47.7408 18.8434 47.798L58.6237 66.1642Z" fill="#FF9300" fill-opacity="0.5" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><path d="M10.9804 36.0465C10.909 36.1001 10.8716 36.1877 10.8822 36.2763C10.8928 36.3649 10.9499 36.4411 11.0319 36.4763L17.1928 39.1171C19.3742 40.0526 20.3692 42.551 19.4233 44.6962L19.4232 44.6963C18.4764 46.8447 15.9412 47.8321 13.7594 46.896L13.7594 46.896L7.59849 44.2552C7.51707 44.2203 7.42325 44.231 7.35189 44.2835C7.28052 44.336 7.24226 44.4224 7.25131 44.5105C7.58956 47.8031 9.67147 50.8219 12.9651 52.2344L12.9652 52.2344C17.8132 54.3128 23.4551 52.1221 25.5644 47.3348C27.6746 42.5457 25.4476 36.9782 20.5978 34.899L20.5978 34.899C17.3046 33.488 13.6487 34.045 10.9804 36.0465Z" fill="url(#paint6_linear_3624_30024)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><path d="M60.6947 63.605C61.4802 63.2996 61.866 62.424 61.5564 61.6492C61.2469 60.8743 60.3591 60.4938 59.5736 60.7991C58.7881 61.1045 58.4023 61.9802 58.7119 62.755C59.0215 63.5298 59.9092 63.9104 60.6947 63.605Z" fill="white"/><path d="M52.7814 50.9336C49.0106 51.3918 44.6324 53.8778 47.2471 55.3983C49.8618 56.9176 53.3952 53.3277 53.3952 53.3277L52.7814 50.9336Z" fill="#F9C2C5"/><path d="M31.5697 56.5989C32.4693 56.1281 32.3391 52.863 31.8427 51.7578C31.4344 50.849 30.2209 49.2617 29.4042 49.9968C28.2838 51.0051 27.3637 53.3954 27.8435 54.887C28.3233 56.3786 29.3365 57.7682 31.5709 56.5989H31.5697Z" fill="#F9C2C5"/><path d="M37.4996 17.9833C37.4996 12.4696 33.0226 8 27.4998 8C21.977 8 17.5 12.4696 17.5 17.9833C17.5 23.0958 21.3495 27.3102 26.3126 27.8965L26.8794 28.8765L27.564 30.0602L28.2486 28.8765L28.8253 27.8792C33.7214 27.2319 37.5 23.0482 37.5 17.9833H37.4996Z" fill="url(#paint7_linear_3624_30024)"/><path d="M26.6925 18.708L26.4578 15.1999C26.4137 14.5165 26.3916 14.0257 26.3916 13.728C26.3916 13.323 26.498 13.0068 26.7107 12.7795C26.9234 12.5526 27.2034 12.439 27.5504 12.439C27.9707 12.439 28.252 12.5841 28.3942 12.8747C28.536 13.1653 28.6069 13.5837 28.6069 14.1307C28.6069 14.4528 28.5895 14.7803 28.5557 15.1122L28.2402 18.7226C28.2059 19.1524 28.1326 19.4819 28.02 19.7112C27.9073 19.9409 27.7214 20.0553 27.4626 20.0553C27.2038 20.0553 27.0151 19.9444 26.9123 19.7222C26.8095 19.5 26.7367 19.1618 26.6925 18.708ZM27.5071 23.5268C27.2085 23.5268 26.9482 23.4305 26.726 23.2374C26.5035 23.0447 26.3924 22.775 26.3924 22.4281C26.3924 22.1253 26.4987 21.8678 26.7114 21.6554C26.9241 21.4431 27.1845 21.3369 27.4925 21.3369C27.8006 21.3369 28.0633 21.4431 28.2811 21.6554C28.4985 21.8678 28.6077 22.1253 28.6077 22.4281C28.6077 22.7699 28.4978 23.0384 28.2776 23.2339C28.0574 23.4293 27.8002 23.5268 27.5071 23.5268Z" fill="white"/></g><defs><linearGradient id="paint0_linear_3624_30024" x1="39.5" y1="0" x2="39.5" y2="70" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint1_linear_3624_30024" x1="39.5002" y1="12.2012" x2="39.5002" y2="57.7982" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><linearGradient id="paint2_linear_3624_30024" x1="27.6855" y1="47.3965" x2="26.5225" y2="70.2441" gradientUnits="userSpaceOnUse"><stop stop-color="#FF9300" stop-opacity="0.15"/><stop offset="1" stop-color="#FF9300" stop-opacity="0.4"/></linearGradient><linearGradient id="paint3_linear_3624_30024" x1="49.0597" y1="25.3857" x2="49.0597" y2="55.3527" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint4_linear_3624_30024" x1="56.5047" y1="25.8984" x2="56.5047" y2="36.429" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint5_linear_3624_30024" x1="42.0825" y1="26.0562" x2="42.0825" y2="37.6327" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint6_linear_3624_30024" x1="16.8055" y1="34.3643" x2="16.8055" y2="52.7694" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint7_linear_3624_30024" x1="27.5" y1="8" x2="27.5" y2="30.0602" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><clipPath id="clip0_3624_30024"><rect width="71" height="62" fill="white" transform="translate(6.5 8)"/></clipPath></defs></svg>'),
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      Text(accountEditMore.tr,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize:
                                configTheme().textTheme.titleLarge?.fontSize,
                            fontFamily:
                                configTheme().textTheme.titleLarge?.fontFamily,
                            fontWeight:
                                configTheme().textTheme.titleLarge?.fontWeight,
                            // height: 0.12,
                          )),
                      SizedBox(
                        height: 10.h,
                      ),
                      Center(
                        child: Text(
                          accountEditMoreDes.tr,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize:
                                configTheme().textTheme.titleLarge?.fontSize,
                            fontFamily:
                                configTheme().textTheme.titleLarge?.fontFamily,
                            fontWeight:
                                configTheme().textTheme.titleLarge?.fontWeight,
                            // height: 0.12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(
                        height: 26.h,
                      ),
                      Container(
                        width: 327.w,
                        height: 52.h,
                        decoration: BoxDecoration(
                          color: configTheme().colorScheme.primary,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              child: SvgPicture.string(
                                  '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.3259 5.77772L18.7023 6.1944V6.1944L19.3259 5.77772ZM19.3259 16.2223L18.7023 15.8056H18.7023L19.3259 16.2223ZM18.2223 17.3259L17.8056 16.7023V16.7023L18.2223 17.3259ZM14 17.9986L13.9967 17.2486C13.5838 17.2504 13.25 17.5857 13.25 17.9986H14ZM14 18L14.6708 18.3354C14.7229 18.2313 14.75 18.1164 14.75 18H14ZM10 18H9.25C9.25 18.1164 9.27711 18.2313 9.32918 18.3354L10 18ZM10 17.9986H10.75C10.75 17.5857 10.4162 17.2504 10.0033 17.2486L10 17.9986ZM5.77772 17.3259L6.1944 16.7023H6.1944L5.77772 17.3259ZM4.67412 16.2223L5.29772 15.8056H5.29772L4.67412 16.2223ZM4.67412 5.77772L5.29772 6.1944L4.67412 5.77772ZM5.77772 4.67412L6.1944 5.29772L5.77772 4.67412ZM18.2223 4.67412L17.8056 5.29772V5.29772L18.2223 4.67412ZM20.75 11C20.75 9.61115 20.7509 8.51305 20.6619 7.63794C20.5716 6.7503 20.3826 6.00926 19.9495 5.36104L18.7023 6.1944C18.9433 6.55507 19.0914 7.02071 19.1696 7.78975C19.2491 8.57133 19.25 9.57993 19.25 11H20.75ZM19.9495 16.639C20.3826 15.9907 20.5716 15.2497 20.6619 14.3621C20.7509 13.4869 20.75 12.3889 20.75 11H19.25C19.25 12.4201 19.2491 13.4287 19.1696 14.2102C19.0914 14.9793 18.9433 15.4449 18.7023 15.8056L19.9495 16.639ZM18.639 17.9495C19.1576 17.6029 19.6029 17.1576 19.9495 16.639L18.7023 15.8056C18.4652 16.1605 18.1605 16.4652 17.8056 16.7023L18.639 17.9495ZM14.0033 18.7486C15.0786 18.7439 15.9608 18.7236 16.6925 18.6225C17.4362 18.5197 18.0728 18.3278 18.639 17.9495L17.8056 16.7023C17.4904 16.9129 17.0947 17.0526 16.4871 17.1366C15.8674 17.2223 15.0791 17.2439 13.9967 17.2486L14.0033 18.7486ZM14.75 18V17.9986H13.25V18H14.75ZM13.5652 20.5466L14.6708 18.3354L13.3292 17.6646L12.2236 19.8757L13.5652 20.5466ZM10.4348 20.5466C11.0797 21.8364 12.9203 21.8364 13.5652 20.5466L12.2236 19.8757C12.1315 20.06 11.8685 20.06 11.7764 19.8757L10.4348 20.5466ZM9.32918 18.3354L10.4348 20.5466L11.7764 19.8757L10.6708 17.6646L9.32918 18.3354ZM9.25 17.9986V18H10.75V17.9986H9.25ZM5.36104 17.9495C5.9272 18.3278 6.56377 18.5197 7.30753 18.6225C8.03924 18.7236 8.92145 18.7439 9.99673 18.7486L10.0033 17.2486C8.92085 17.2439 8.1326 17.2223 7.51292 17.1366C6.90531 17.0526 6.50959 16.9129 6.1944 16.7023L5.36104 17.9495ZM4.05052 16.639C4.39707 17.1576 4.84239 17.6029 5.36104 17.9495L6.1944 16.7023C5.83953 16.4652 5.53484 16.1605 5.29772 15.8056L4.05052 16.639ZM3.25 11C3.25 12.3889 3.24909 13.4869 3.33812 14.3621C3.42841 15.2497 3.61739 15.9907 4.05052 16.639L5.29772 15.8056C5.05673 15.4449 4.90865 14.9793 4.83041 14.2102C4.75091 13.4287 4.75 12.4201 4.75 11H3.25ZM4.05052 5.36104C3.61739 6.00926 3.42841 6.7503 3.33812 7.63794C3.24909 8.51305 3.25 9.61115 3.25 11H4.75C4.75 9.57993 4.75091 8.57133 4.83041 7.78975C4.90865 7.02071 5.05673 6.55507 5.29772 6.1944L4.05052 5.36104ZM5.36104 4.05052C4.84239 4.39707 4.39707 4.84239 4.05052 5.36104L5.29772 6.1944C5.53484 5.83953 5.83953 5.53484 6.1944 5.29772L5.36104 4.05052ZM11 3.25C9.61115 3.25 8.51305 3.24909 7.63794 3.33812C6.7503 3.42841 6.00926 3.61739 5.36104 4.05052L6.1944 5.29772C6.55507 5.05673 7.02071 4.90865 7.78975 4.83041C8.57133 4.75091 9.57993 4.75 11 4.75V3.25ZM13 3.25H11V4.75H13V3.25ZM18.639 4.05052C17.9907 3.61739 17.2497 3.42841 16.3621 3.33812C15.4869 3.24909 14.3889 3.25 13 3.25V4.75C14.4201 4.75 15.4287 4.75091 16.2102 4.83041C16.9793 4.90865 17.4449 5.05673 17.8056 5.29772L18.639 4.05052ZM19.9495 5.36104C19.6029 4.84239 19.1576 4.39707 18.639 4.05052L17.8056 5.29772C18.1605 5.53484 18.4652 5.83953 18.7023 6.1944L19.9495 5.36104Z" fill="white"/><circle cx="16" cy="11" r="1" fill="white"/><circle cx="12" cy="11" r="1" fill="white"/><circle cx="8" cy="11" r="1" fill="white"/></svg>'),
                            ),
                            Text(
                              accountEditContact.tr,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: configTheme()
                                    .textTheme
                                    .titleLarge
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .textTheme
                                    .titleLarge
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .textTheme
                                    .titleLarge
                                    ?.fontWeight,
                                // height: 0.12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            accountEditCancel.tr,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize:
                                  configTheme().textTheme.titleLarge?.fontSize,
                              fontFamily: configTheme()
                                  .textTheme
                                  .titleLarge
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .textTheme
                                  .titleLarge
                                  ?.fontWeight,
                              // height: 0.12,
                            ),
                          ))
                    ],
                  ),
                )
              : Container(
                  height: 271.h,
                  width: Get.width,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r),
                    ),
                  ),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 15.h,
                      ),
                      Container(
                        child: SvgPicture.string(
                            '<svg width="44" height="5" viewBox="0 0 44 5" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="44" height="5" rx="2.5" fill="#1A1818" fill-opacity="0.2"/></svg>'),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 24.w, right: 24.w, top: 20.h, bottom: 16.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  accountEditMore.tr,
                                  style: TextStyle(
                                    color: Color(0xFF1A1818),
                                    fontSize: configTheme()
                                        .textTheme
                                        .titleLarge
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .textTheme
                                        .titleLarge
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .textTheme
                                        .titleLarge
                                        ?.fontWeight,
                                  ),
                                ),
                                Text(
                                  accountEditMoreDes.tr,
                                  style: TextStyle(
                                    color: Color(0xFF1A1818).withOpacity(0.75),
                                    fontSize: configTheme()
                                        .textTheme
                                        .bodySmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .textTheme
                                        .bodySmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .textTheme
                                        .bodySmall
                                        ?.fontWeight,
                                  ),
                                )
                              ],
                            ),
                            Container(
                                child: SvgPicture.string(
                                    '<svg width="73" height="39" viewBox="0 0 73 39" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_4389_9577)"><path d="M49 27V32.4C49 34.38 47.38 36 45.4 36H34.6C32.62 36 31 34.38 31 32.4V21.6C31 19.62 32.62 18 34.6 18H40" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M46.5001 24.1698L42.4001 28.2697C42.2201 28.4497 42.0101 28.5498 41.7601 28.5698L39.3101 28.7997C38.6701 28.8597 38.1301 28.3098 38.1901 27.6698L38.4101 25.2898C38.4301 25.0398 38.5301 24.8197 38.7101 24.6497L42.8501 20.5098L46.5001 24.1698V24.1698Z" stroke="#1A1818" stroke-width="1.5" stroke-linejoin="round"/><path d="M48.7001 21.98L46.5001 24.17L42.8501 20.51L45.0401 18.32C45.4401 17.92 46.1001 17.92 46.5001 18.32L48.7001 20.51C49.0901 20.91 49.0901 21.58 48.7001 21.98V21.98Z" stroke="#1A1818" stroke-width="1.5" stroke-linejoin="round"/></g><g opacity="0.3"><path d="M65.9786 3.27487C66.1283 3.42366 66.3269 3.50664 66.5336 3.50664C66.7402 3.50664 66.9389 3.42366 67.0885 3.27487C67.1673 3.20242 67.2304 3.11328 67.2736 3.01337C67.3169 2.91346 67.3392 2.80508 67.3392 2.69545C67.3392 2.58581 67.3169 2.47743 67.2736 2.37752C67.2304 2.27761 67.1673 2.18847 67.0885 2.11602L65.3127 0.261854C65.2434 0.179566 65.158 0.113653 65.0623 0.0685026C64.9666 0.0233522 64.8628 0 64.7578 0C64.6528 0 64.549 0.0233522 64.4534 0.0685026C64.3577 0.113653 64.2723 0.179566 64.2029 0.261854C64.1241 0.334303 64.061 0.423449 64.0178 0.523357C63.9745 0.623265 63.9521 0.731644 63.9521 0.841281C63.9521 0.950917 63.9745 1.0593 64.0178 1.1592C64.061 1.25911 64.1241 1.34826 64.2029 1.42071L65.9786 3.27487Z" fill="#1A1818" fill-opacity="0.2"/><path d="M71.6392 9.18503C71.7888 9.33381 71.9875 9.4168 72.1941 9.4168C72.4007 9.4168 72.5994 9.33381 72.749 9.18503C72.8278 9.11258 72.8909 9.02343 72.9342 8.92353C72.9774 8.82362 72.9998 8.71524 72.9998 8.6056C72.9998 8.49597 72.9774 8.38759 72.9342 8.28768C72.8909 8.18777 72.8278 8.09862 72.749 8.02617L70.9733 6.17201C70.9039 6.08972 70.8185 6.02381 70.7228 5.97866C70.6272 5.93351 70.5234 5.91016 70.4184 5.91016C70.3134 5.91016 70.2096 5.93351 70.1139 5.97866C70.0182 6.02381 69.9328 6.08972 69.8635 6.17201C69.6415 6.51967 69.5305 7.09909 69.8635 7.33086L71.6392 9.18503Z" fill="#1A1818" fill-opacity="0.2"/><path d="M64.8682 9.41803C65.074 9.41367 65.2712 9.33129 65.4231 9.18626L67.1988 7.3321C67.3396 7.17708 67.4169 6.97079 67.4142 6.75715C67.4115 6.54351 67.3291 6.33941 67.1844 6.18833C67.0397 6.03725 66.8442 5.95113 66.6396 5.94831C66.435 5.9455 66.2374 6.0262 66.089 6.17324L64.3133 8.02741C64.2345 8.09986 64.1713 8.189 64.1281 8.28891C64.0849 8.38882 64.0625 8.4972 64.0625 8.60684C64.0625 8.71647 64.0849 8.82485 64.1281 8.92476C64.1713 9.02467 64.2345 9.11381 64.3133 9.18626C64.4652 9.33129 64.6624 9.41367 64.8682 9.41803Z" fill="#1A1818" fill-opacity="0.2"/><path d="M70.417 3.50664C70.6228 3.50228 70.82 3.4199 70.9719 3.27487L72.7476 1.42071C72.8265 1.34826 72.8896 1.25911 72.9328 1.1592C72.9761 1.0593 72.9984 0.950917 72.9984 0.841281C72.9984 0.731644 72.9761 0.623265 72.9328 0.523357C72.8896 0.423449 72.8265 0.334303 72.7476 0.261854C72.6783 0.179566 72.5929 0.113653 72.4972 0.0685026C72.4015 0.0233522 72.2977 0 72.1927 0C72.0877 0 71.9839 0.0233522 71.8883 0.0685026C71.7926 0.113653 71.7072 0.179566 71.6378 0.261854L69.8621 2.11602C69.7833 2.18847 69.7202 2.27761 69.6769 2.37752C69.6337 2.47743 69.6113 2.58581 69.6113 2.69545C69.6113 2.80508 69.6337 2.91346 69.6769 3.01337C69.7202 3.11328 69.7833 3.20242 69.8621 3.27487C69.9279 3.35988 70.0134 3.42583 70.1106 3.46641C70.2077 3.50699 70.3133 3.52084 70.417 3.50664Z" fill="#1A1818" fill-opacity="0.2"/></g><path opacity="0.3" d="M67.7586 33.2882C68.8427 33.271 69.8779 32.8137 70.6446 32.0131C71.4113 31.2125 71.8493 30.1316 71.8657 28.9995C71.8657 27.8621 71.433 26.7713 70.6628 25.967C69.8925 25.1628 68.8478 24.7109 67.7586 24.7109C66.6693 24.7109 65.6246 25.1628 64.8543 25.967C64.0841 26.7713 63.6514 27.8621 63.6514 28.9995C63.6501 29.5631 63.7554 30.1214 63.9614 30.6423C64.1673 31.1632 64.4698 31.6366 64.8515 32.0351C65.2331 32.4336 65.6864 32.7494 66.1853 32.9645C66.6842 33.1795 67.2188 33.2895 67.7586 33.2882ZM67.7586 26.4492C68.2416 26.4492 68.7138 26.5988 69.1155 26.879C69.5171 27.1593 69.8302 27.5576 70.0151 28.0236C70.1999 28.4896 70.2483 29.0024 70.154 29.4971C70.0598 29.9918 69.8272 30.4462 69.4856 30.8029C69.144 31.1596 68.7088 31.4025 68.235 31.5009C67.7613 31.5993 67.2702 31.5488 66.8239 31.3557C66.3776 31.1627 65.9961 30.8358 65.7277 30.4164C65.4594 29.997 65.3161 29.504 65.3161 28.9995C65.3217 28.3249 65.5808 27.6796 66.0376 27.2026C66.4945 26.7256 67.1125 26.455 67.7586 26.4492Z" fill="#1A1818" fill-opacity="0.2"/><g opacity="0.3"><path d="M10.8185 11.0377H26.0264C26.1324 11.0449 26.2388 11.0293 26.3388 10.9918C26.4388 10.9544 26.5304 10.896 26.608 10.8201C26.6855 10.7442 26.7473 10.6525 26.7896 10.5507C26.8318 10.4489 26.8536 10.3391 26.8536 10.2282C26.8536 10.1172 26.8318 10.0075 26.7896 9.90565C26.7473 9.80385 26.6855 9.71217 26.608 9.6363C26.5304 9.56042 26.4388 9.50197 26.3388 9.46454C26.2388 9.42711 26.1324 9.41151 26.0264 9.4187H10.8185C10.7158 9.41613 10.6137 9.43536 10.5184 9.47521C10.423 9.51507 10.3364 9.57473 10.2637 9.65057C10.1911 9.72642 10.134 9.81687 10.0958 9.91645C10.0576 10.016 10.0392 10.1227 10.0417 10.2299C9.93068 10.6968 10.3746 11.0377 10.8185 11.0377Z" fill="#1A1818" fill-opacity="0.2"/><path d="M0.827209 11.0377H5.71125C5.81729 11.0449 5.92361 11.0293 6.02365 10.9918C6.12368 10.9544 6.2153 10.896 6.29283 10.8201C6.37036 10.7442 6.43216 10.6525 6.47441 10.5507C6.51666 10.4489 6.53846 10.3391 6.53846 10.2282C6.53846 10.1172 6.51666 10.0075 6.47441 9.90565C6.43216 9.80385 6.37036 9.71217 6.29283 9.6363C6.2153 9.56042 6.12368 9.50197 6.02365 9.46454C5.92361 9.42711 5.81729 9.41151 5.71125 9.4187H0.827209C0.721171 9.41151 0.614849 9.42711 0.514816 9.46454C0.414784 9.50197 0.323168 9.56042 0.245635 9.6363C0.168101 9.71217 0.106299 9.80385 0.0640489 9.90565C0.021799 10.0075 0 10.1172 0 10.2282C0 10.3391 0.021799 10.4489 0.0640489 10.5507C0.106299 10.6525 0.168101 10.7442 0.245635 10.8201C0.323168 10.896 0.414784 10.9544 0.514816 10.9918C0.614849 11.0293 0.721171 11.0449 0.827209 11.0377Z" fill="#1A1818" fill-opacity="0.2"/><path d="M12.2609 15.6772C12.2633 15.5699 12.2449 15.4633 12.2067 15.3637C12.1686 15.2641 12.1114 15.1737 12.0388 15.0978C11.9662 15.022 11.8795 14.9623 11.7842 14.9225C11.6888 14.8826 11.5867 14.8634 11.484 14.866H0.827209C0.721171 14.8588 0.614849 14.8744 0.514816 14.9118C0.414784 14.9492 0.323168 15.0077 0.245635 15.0836C0.168101 15.1594 0.106299 15.2511 0.0640489 15.3529C0.021799 15.4547 0 15.5645 0 15.6755C0 15.7864 0.021799 15.8962 0.0640489 15.998C0.106299 16.0998 0.168101 16.1915 0.245635 16.2673C0.323168 16.3432 0.414784 16.4017 0.514816 16.4391C0.614849 16.4765 0.721171 16.4921 0.827209 16.4849H11.484C11.5864 16.4875 11.6882 16.4684 11.7834 16.4287C11.8785 16.3891 11.965 16.3297 12.0376 16.2542C12.1102 16.1788 12.1674 16.0887 12.2058 15.9896C12.2441 15.8904 12.2629 15.7841 12.2609 15.6772Z" fill="#1A1818" fill-opacity="0.2"/><path d="M16.0352 14.8665C15.9292 14.8593 15.8229 14.8749 15.7228 14.9123C15.6228 14.9497 15.5312 15.0082 15.4536 15.0841C15.3761 15.1599 15.3143 15.2516 15.2721 15.3534C15.2298 15.4552 15.208 15.565 15.208 15.6759C15.208 15.7869 15.2298 15.8967 15.2721 15.9985C15.3143 16.1003 15.3761 16.192 15.4536 16.2678C15.5312 16.3437 15.6228 16.4022 15.7228 16.4396C15.8229 16.477 15.9292 16.4926 16.0352 16.4854H19.4765C19.5825 16.4926 19.6888 16.477 19.7889 16.4396C19.8889 16.4022 19.9805 16.3437 20.0581 16.2678C20.1356 16.192 20.1974 16.1003 20.2396 15.9985C20.2819 15.8967 20.3037 15.7869 20.3037 15.6759C20.3037 15.565 20.2819 15.4552 20.2396 15.3534C20.1974 15.2516 20.1356 15.1599 20.0581 15.0841C19.9805 15.0082 19.8889 14.9497 19.7889 14.9123C19.6888 14.8749 19.5825 14.8593 19.4765 14.8665H16.0352Z" fill="#1A1818" fill-opacity="0.2"/></g><defs><clipPath id="clip0_4389_9577"><rect width="24" height="24" fill="white" transform="translate(28 15)"/></clipPath></defs></svg>'))
                          ],
                        ),
                      ),
                      Container(
                        width: 327.w,
                        height: 52.h,
                        decoration: BoxDecoration(
                          color: configTheme().colorScheme.primary,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              child: SvgPicture.string(
                                  '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.3259 5.77772L18.7023 6.1944V6.1944L19.3259 5.77772ZM19.3259 16.2223L18.7023 15.8056H18.7023L19.3259 16.2223ZM18.2223 17.3259L17.8056 16.7023V16.7023L18.2223 17.3259ZM14 17.9986L13.9967 17.2486C13.5838 17.2504 13.25 17.5857 13.25 17.9986H14ZM14 18L14.6708 18.3354C14.7229 18.2313 14.75 18.1164 14.75 18H14ZM10 18H9.25C9.25 18.1164 9.27711 18.2313 9.32918 18.3354L10 18ZM10 17.9986H10.75C10.75 17.5857 10.4162 17.2504 10.0033 17.2486L10 17.9986ZM5.77772 17.3259L6.1944 16.7023H6.1944L5.77772 17.3259ZM4.67412 16.2223L5.29772 15.8056H5.29772L4.67412 16.2223ZM4.67412 5.77772L5.29772 6.1944L4.67412 5.77772ZM5.77772 4.67412L6.1944 5.29772L5.77772 4.67412ZM18.2223 4.67412L17.8056 5.29772V5.29772L18.2223 4.67412ZM20.75 11C20.75 9.61115 20.7509 8.51305 20.6619 7.63794C20.5716 6.7503 20.3826 6.00926 19.9495 5.36104L18.7023 6.1944C18.9433 6.55507 19.0914 7.02071 19.1696 7.78975C19.2491 8.57133 19.25 9.57993 19.25 11H20.75ZM19.9495 16.639C20.3826 15.9907 20.5716 15.2497 20.6619 14.3621C20.7509 13.4869 20.75 12.3889 20.75 11H19.25C19.25 12.4201 19.2491 13.4287 19.1696 14.2102C19.0914 14.9793 18.9433 15.4449 18.7023 15.8056L19.9495 16.639ZM18.639 17.9495C19.1576 17.6029 19.6029 17.1576 19.9495 16.639L18.7023 15.8056C18.4652 16.1605 18.1605 16.4652 17.8056 16.7023L18.639 17.9495ZM14.0033 18.7486C15.0786 18.7439 15.9608 18.7236 16.6925 18.6225C17.4362 18.5197 18.0728 18.3278 18.639 17.9495L17.8056 16.7023C17.4904 16.9129 17.0947 17.0526 16.4871 17.1366C15.8674 17.2223 15.0791 17.2439 13.9967 17.2486L14.0033 18.7486ZM14.75 18V17.9986H13.25V18H14.75ZM13.5652 20.5466L14.6708 18.3354L13.3292 17.6646L12.2236 19.8757L13.5652 20.5466ZM10.4348 20.5466C11.0797 21.8364 12.9203 21.8364 13.5652 20.5466L12.2236 19.8757C12.1315 20.06 11.8685 20.06 11.7764 19.8757L10.4348 20.5466ZM9.32918 18.3354L10.4348 20.5466L11.7764 19.8757L10.6708 17.6646L9.32918 18.3354ZM9.25 17.9986V18H10.75V17.9986H9.25ZM5.36104 17.9495C5.9272 18.3278 6.56377 18.5197 7.30753 18.6225C8.03924 18.7236 8.92145 18.7439 9.99673 18.7486L10.0033 17.2486C8.92085 17.2439 8.1326 17.2223 7.51292 17.1366C6.90531 17.0526 6.50959 16.9129 6.1944 16.7023L5.36104 17.9495ZM4.05052 16.639C4.39707 17.1576 4.84239 17.6029 5.36104 17.9495L6.1944 16.7023C5.83953 16.4652 5.53484 16.1605 5.29772 15.8056L4.05052 16.639ZM3.25 11C3.25 12.3889 3.24909 13.4869 3.33812 14.3621C3.42841 15.2497 3.61739 15.9907 4.05052 16.639L5.29772 15.8056C5.05673 15.4449 4.90865 14.9793 4.83041 14.2102C4.75091 13.4287 4.75 12.4201 4.75 11H3.25ZM4.05052 5.36104C3.61739 6.00926 3.42841 6.7503 3.33812 7.63794C3.24909 8.51305 3.25 9.61115 3.25 11H4.75C4.75 9.57993 4.75091 8.57133 4.83041 7.78975C4.90865 7.02071 5.05673 6.55507 5.29772 6.1944L4.05052 5.36104ZM5.36104 4.05052C4.84239 4.39707 4.39707 4.84239 4.05052 5.36104L5.29772 6.1944C5.53484 5.83953 5.83953 5.53484 6.1944 5.29772L5.36104 4.05052ZM11 3.25C9.61115 3.25 8.51305 3.24909 7.63794 3.33812C6.7503 3.42841 6.00926 3.61739 5.36104 4.05052L6.1944 5.29772C6.55507 5.05673 7.02071 4.90865 7.78975 4.83041C8.57133 4.75091 9.57993 4.75 11 4.75V3.25ZM13 3.25H11V4.75H13V3.25ZM18.639 4.05052C17.9907 3.61739 17.2497 3.42841 16.3621 3.33812C15.4869 3.24909 14.3889 3.25 13 3.25V4.75C14.4201 4.75 15.4287 4.75091 16.2102 4.83041C16.9793 4.90865 17.4449 5.05673 17.8056 5.29772L18.639 4.05052ZM19.9495 5.36104C19.6029 4.84239 19.1576 4.39707 18.639 4.05052L17.8056 5.29772C18.1605 5.53484 18.4652 5.83953 18.7023 6.1944L19.9495 5.36104Z" fill="white"/><circle cx="16" cy="11" r="1" fill="white"/><circle cx="12" cy="11" r="1" fill="white"/><circle cx="8" cy="11" r="1" fill="white"/></svg>'),
                            ),
                            Text(
                              accountEditContact.tr,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: configTheme()
                                    .textTheme
                                    .titleLarge
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .textTheme
                                    .titleLarge
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .textTheme
                                    .titleLarge
                                    ?.fontWeight,
                                // height: 0.12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            accountEditCancel.tr,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize:
                                  configTheme().textTheme.titleLarge?.fontSize,
                              fontFamily: configTheme()
                                  .textTheme
                                  .titleLarge
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .textTheme
                                  .titleLarge
                                  ?.fontWeight,
                              // height: 0.12,
                            ),
                          ))
                    ],
                  ),
                );
        });
  }

  Widget _buildListProfileMenu(
      context, String menu, String title, String value) {
    return Container(
      width: 327.w,
      height: 48.h,
      // color: Colors.yellow,
      // color: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (value.length > 0 && !value.contains('null')) {
            print('if');
            print("menu: $menu");
          } else {
            print('else');
            print("menu: $menu");
            _buildUpdateData(context, menu);
          }
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: 20.h,
              child: Center(
                child: Text(
                  title,
                  style: TextStyle(
                    color: Color(0xFF1A1818),
                    fontSize: 12.sp,
                    fontFamily: TextStyleTheme.text_Regular.fontFamily,
                    fontWeight: FontWeight.w400,
                    height: 0.12,
                  ),
                ),
              ),
            ),
            value.length > 0 && !value.contains('null')
                ? Container(
                    height: 20.h,
                    width: 200.w,
                    // color: Colors.teal,
                    child: Text(
                      value,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                      softWrap: true,
                      style: TextStyle(
                        color: Color(0x7F1A1818),
                        fontSize: 12.sp,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w400,
                        // height: 0.12,
                      ),
                    ),
                  )
                : _buildUpdateData(context, menu)
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateData(context, String menu) {
    final profileController = Get.find<ProfileController>();
    if (menu == 'email' || menu == 'idcard' || menu == 'address') {
      return GestureDetector(
        onTap: () {
          if (menu == 'email') {
            _buildUpdateEmail(context);
          } else if (menu == 'idcard') {
            _buildUpdateIdCard(context);
          } else if (menu == 'address') {
            _buildUpdateAddress(context);
          }
        },
        child: Container(
          // color: Colors.red,
          width: 150.w,
          height: 20.h,
          child: Text(
            menu == 'email'
                ? editProfileController.isEdit!.value == true
                    ? accountEditEmail.tr
                    : editProfileController.isEmailValid!.value == false
                        ? accountAddEmail.tr
                        : editProfileController.selectEmail?.value ?? ''
                : menu == 'idcard'
                    ? accountAddIDCard.tr
                    : menu == 'address'
                        ? editProfileController.isEdit!.value == true
                            ? accountEditAdd.tr
                            : accountAddAddress.tr
                        : '',
            textAlign: TextAlign.right,
            style: TextStyle(
              color: Color(0xFF1A1818),
              fontSize: 14.sp,
              fontFamily: TextStyleTheme.text_Regular.fontFamily,
              fontWeight: FontWeight.w700,
              decoration: TextDecoration.underline,
              decorationColor: Color(0xFF1A1818),
              // height: 0.08,
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  _buildUpdateEmail(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (context) {
          return Stack(
            children: [
              Container(
                width: Get.width,
                height: 513.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.0.r),
                    topRight: Radius.circular(12.0.r),
                  ),
                  // color: Colors.teal,
                  color: Color(0xFFFFFFFF),
                ),
                child: Padding(
                  padding: EdgeInsets.only(top: 20.h, left: 24.w, right: 24.w),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            accountEditProfileEmail.tr,
                            style: TextStyle(
                              color: Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w600,
                              height: 0.10,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Container(
                              alignment: Alignment.topRight,
                              child: SvgPicture.string(AppSvgImage.close_btn),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      Divider(
                        color: Color(0xFF1A1818).withOpacity(0.2),
                        thickness: 1,
                      ),
                    ],
                  ),
                ),
              ),
              // SizedBox(
              //   height: 12.h,
              // ),
              Padding(
                padding: EdgeInsets.only(top: 66.h, left: 24.w, right: 24.w),
                child: Container(
                  height: 150.h,
                  // color: Colors.red,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                            width: Get.width,
                            height: 48.h,
                            decoration: BoxDecoration(
                              color: Color(0xFFF9F9F9),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: TextFormField(
                              showCursor: true,
                              style: TextStyle(
                                color: Color(0xFF1A1818),
                                fontSize: 14.sp,
                                fontFamily:
                                    TextStyleTheme.text_Regular.fontFamily,
                                fontWeight: FontWeight.w400,
                                // height: 0.14,
                              ),
                              cursorColor:
                                  configTheme().colorScheme.onSecondary,
                              keyboardType: TextInputType.emailAddress,
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.only(left: 16.w),
                                hintText: accountInputEmail.tr,
                                hintStyle: TextStyle(
                                  color: Color(0xFF1A1818).withOpacity(0.5),
                                  fontSize: 14.sp,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                  // height: 0.14,
                                ),
                              ),
                              onChanged: (value) {
                                if (value.length > 0 && value.contains('@')) {
                                  print('value : $value');
                                  editProfileController.setIsEmailValid(
                                      true, value);
                                } else {
                                  editProfileController.setIsEmailValid(
                                      false, value);
                                }
                              },
                            )),
                        SizedBox(
                          height: 12.h,
                        ),
                        Obx(() {
                          return GestureDetector(
                            onTap: () {
                              if (editProfileController.isEmailValid!.value) {
                                print('save');
                                editProfileController.updateProfile(
                                    context, 'email');
                              }
                            },
                            child: Container(
                              width: Get.width,
                              height: 52.h,
                              decoration: ShapeDecoration(
                                color:
                                    !editProfileController.isEmailValid!.value
                                        ? Color(0x191A1818).withOpacity(0.1)
                                        : configTheme()
                                            .colorScheme
                                            .primary
                                            .withOpacity(1),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Container(
                                width: 36.w,
                                decoration: ShapeDecoration(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50.r),
                                  ),
                                ),
                                child: SizedBox(
                                  width: 36.w,
                                  height: 20.h,
                                  child: Center(
                                    child: Text(
                                      accountSave.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.white
                                            .withOpacity(0.****************),
                                        fontSize: 14.sp,
                                        fontFamily: TextStyleTheme
                                            .text_Regular.fontFamily,
                                        fontWeight: FontWeight.w600,
                                        height: 0.14,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        });
  }

  _buildUpdateIdCard(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 513.h,
            color: Color(0xFFFFFFFF),
            child: Container(
              // width: 327.w,
              // height: 158.h,
              margin: EdgeInsets.only(top: 20.h, left: 24.w, right: 24.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: Get.width,
                    height: 34.h,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 24.h,
                          child: Center(
                            child: Text(
                              accountIDCard.tr,
                              style: TextStyle(
                                color: Color(0xFF1A1818),
                                fontSize: 14.sp,
                                fontFamily:
                                    TextStyleTheme.text_Regular.fontFamily,
                                fontWeight: FontWeight.w600,
                                height: 0.10,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.topRight,
                            child: SvgPicture.string(AppSvgImage.close_btn),
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildDivider(),
                  SizedBox(height: 12.h),
                  Container(
                    width: Get.width,
                    height: 48.h,
                    decoration: ShapeDecoration(
                      color: Color(0xFFF9F9F9),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: TextFormField(
                      textAlign: TextAlign.start,
                      cursorWidth: 2.w,
                      cursorColor: configTheme().colorScheme.primary,
                      // cursorHeight: 24.h,
                      // controller: ,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 14.w),
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        // labelText: '',
                        labelStyle: TextStyle(
                          color: Color(0xFF1A1818),
                          fontSize: 12.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w700,
                          height: 0.12,
                        ),
                        hintText: '0-0000-00-000-00-0',
                        hintStyle: TextStyle(
                          color: Color(0x7F1A1818),
                          fontSize: 12.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w400,
                          height: 0.12,
                        ),
                      ),
                      onChanged: (value) {
                        if (appConfigService.countryConfigCollection
                                .toString() ==
                            'aam') {
                          if (value.length == 13) {
                            editProfileController.setIsIdCardValid(true, value);
                          } else {
                            editProfileController.setIsIdCardValid(
                                false, value);
                          }
                        } else if (value.length > 0) {
                          editProfileController.setIsIdCardValid(true, value);
                        } else {
                          editProfileController.setIsIdCardValid(false, value);
                        }
                      },
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                        LengthLimitingTextInputFormatter(13),
                      ],
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Obx(() {
                    return GestureDetector(
                      onTap: () {
                        if (editProfileController.isIdCardValid!.value) {
                          print('save');
                          editProfileController.updateProfile(
                              context, 'idcard');
                        }
                      },
                      child: Container(
                        width: Get.width,
                        height: 52.h,
                        decoration: ShapeDecoration(
                          color: !editProfileController.isIdCardValid!.value
                              ? Color(0x191A1818).withOpacity(0.1)
                              : configTheme()
                                  .colorScheme
                                  .primary
                                  .withOpacity(1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Container(
                          width: 36.w,
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(50.r),
                            ),
                          ),
                          child: SizedBox(
                            width: 36.w,
                            height: 20.h,
                            child: Center(
                              child: Text(
                                accountSave.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white
                                      .withOpacity(0.****************),
                                  fontSize: 14.sp,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w600,
                                  height: 0.14,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          );
        });
  }

  _buildUpdateAddress(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return SingleChildScrollView(
            child: Container(
              width: Get.width,
              height: Get.height,
              color: Colors.white.withOpacity(0.9),
              child: Column(
                // mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    height: 120.h,
                    width: Get.width,
                    color: Colors.white.withOpacity(0.9),
                    child: Container(
                        margin: EdgeInsets.only(
                            top: 67.h, left: 24.w, right: 24.w, bottom: 29.h),
                        child: Stack(
                          children: [
                            //TODO slide page
                            Container(
                              width: Get.width,
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    accountEditProfileAddress.tr,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 16,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      height: 0.09,
                                    ),
                                  )
                                ],
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                Get.back();
                                editProfileController
                                    .controllerAddress.value.text = "";
                                editProfileController.controllerMoo.value.text =
                                    "";
                                editProfileController
                                    .selectedSubDistrict!.value = "";
                                editProfileController.selectedDistrict!.value =
                                    "";
                                editProfileController.selectedProvince!.value =
                                    "";
                              },
                              child: Container(
                                height: 46.h,
                                width: 46.w,
                                alignment: Alignment.centerLeft,
                                child: Center(
                                  child: SvgPicture.string(
                                    AppSvgImage.back_btn,
                                    height: 18.h,
                                    width: 18.w,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                  ),
                  Obx(() {
                    return Container(
                      width: Get.width,
                      height: Get.height - 130.h,
                      // color: Colors.teal,
                      margin: EdgeInsets.only(left: 24.w, right: 24.w),
                      child: Column(
                        children: [
                          appConfigService.countryConfigCollection.toString() ==
                                  'aam'
                              ? Column(
                                  children: [
                                    _buildAddressItem(context, 'address',
                                        accountAddressNo.tr, ''),
                                    _buildDivider(),
                                    _buildAddressItem(context, 'moo',
                                        accountAddressMoo.tr, ''),
                                    _buildDivider(),
                                    _buildAddressItem(
                                        context,
                                        'province',
                                        accountAddressProvince.tr,
                                        editProfileController
                                                .selectedProvince!.value.isEmpty
                                            ? ""
                                            : editProfileController
                                                .selectedProvince!.value),
                                    _buildDivider(),
                                    _buildAddressItem(
                                        context,
                                        'district',
                                        accountAddressDistrict.tr,
                                        editProfileController
                                            .selectedDistrict!.value),
                                    _buildDivider(),
                                    _buildAddressItem(
                                        context,
                                        'sub-district',
                                        accountAddressSubDistrict.tr,
                                        editProfileController
                                            .selectedSubDistrict!.value),
                                    _buildDivider(),
                                  ],
                                )
                              : Column(
                                  children: [
                                    _buildAddressItem(context, 'address',
                                        accountAddressNo.tr, ''),
                                    _buildDivider(),
                                    _buildAddressItem(context, 'moo',
                                        accountAddressMoo.tr, ''),
                                    _buildDivider(),
                                    _buildAddressItem(
                                        context,
                                        'province',
                                        accountAddressProvince.tr,
                                        editProfileController
                                            .selectedProvince!.value),
                                    _buildDivider(),
                                    _buildAddressItem(
                                        context,
                                        'district',
                                        accountAddressDistrict.tr,
                                        editProfileController
                                            .selectedDistrict!.value),
                                    _buildDivider(),
                                  ],
                                ),
                          Spacer(),
                          _buildSaveAddress(context),
                          SizedBox(
                            height: 33.h,
                          )
                        ],
                      ),
                    );
                  })
                ],
              ),
            ),
          );
        });
  }

  _buildSaveAddress(context) {
    return GetBuilder<ProfileController>(
        builder: (controller) => GestureDetector(
              onTap: () {
                print('save #');
                (appConfigService.countryConfigCollection.toString() ==
                    'aam' &&
                    // เช็คการกรอกข้อมูลสำหรับ 'aam'
                    editProfileController
                        .selectedProvince!.value.isNotEmpty &&
                    editProfileController
                        .selectedDistrict!.value.isNotEmpty &&
                    editProfileController
                        .selectedSubDistrict!.value.isNotEmpty) ||
                    ((appConfigService.countryConfigCollection
                        .toString() ==
                        'rafco' ||
                        appConfigService.countryConfigCollection
                            .toString() ==
                            'rplc') &&
                        // เช็คการกรอกข้อมูลสำหรับ 'rafco' หรือ 'rplc' โดยไม่ต้องเช็ค selectedSubDistrict
                        editProfileController
                            .selectedProvince!.value.isNotEmpty &&
                        editProfileController
                            .selectedDistrict!.value.isNotEmpty)
                    ? setState(() {
                  editProfileController
                      .updateProfile(context, 'address')
                      .then((value) {
                    profileCtl.getProfile();
                    controller.update(); // บังคับให้ GetBuilder Rebuild
                     editProfileController.selectedProvince!.value = "";
                        editProfileController.selectedDistrict!.value = "";
                        editProfileController.selectedSubDistrict!.value ="";
                        editProfileController.controllerAddress.value.text = "";
                        editProfileController.controllerMoo.value.text = "";

                  });
                })
                : Get.snackbar(
                    'กรุณากรอกข้อมูลให้ครบถ้วน',
                    'กรุณากรอกข้อมูลให้ครบถ้วน',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                    margin: EdgeInsets.all(10),
                    borderRadius: 10,
                    duration: Duration(seconds: 3),
                  );
              },
              child: Container(
                // Outer container (main button shape)
                width:
                    327.w, // Width based on device width (assuming extension)
                height: 52.h,
                // color: Colors.teal,
                decoration: BoxDecoration(
                  color: (appConfigService.countryConfigCollection.toString() ==
                                  'aam' &&
                              // เช็คการกรอกข้อมูลสำหรับ 'aam'
                              editProfileController
                                  .selectedProvince!.value.isNotEmpty &&
                              editProfileController
                                  .selectedDistrict!.value.isNotEmpty &&
                              editProfileController
                                  .selectedSubDistrict!.value.isNotEmpty) ||
                          ((appConfigService.countryConfigCollection
                                          .toString() ==
                                      'rafco' ||
                                  appConfigService.countryConfigCollection
                                          .toString() ==
                                      'rplc') &&
                              // เช็คการกรอกข้อมูลสำหรับ 'rafco' หรือ 'rplc' โดยไม่ต้องเช็ค selectedSubDistrict
                              editProfileController
                                  .selectedProvince!.value.isNotEmpty &&
                              editProfileController
                                  .selectedDistrict!.value.isNotEmpty)
                      ? configTheme()
                          .colorScheme
                          .onSecondary // สีถ้ามีการกรอกข้อมูลตามประเทศ
                      : Color(0x191A1818), // สีถ้าไม่มีการกรอกข้อมูล
                  // สีถ้าไม่มีการกรอกข้อมูล
                  // Transparent with border color ,
                  borderRadius: BorderRadius.circular(12), // Rounded corners
                ), // Height based on device height (assuming extension)
                margin: EdgeInsets.only(
                    top: 352.h), // Top margin based on device height
                child: SizedBox(
                  // Ensures child (text) fits within the inner container
                  height: 20.h, // Adjusts text height based on device height
                  child: Center(
                    // Centers the text within the inner container
                    child: Text(
                      accountSave
                          .tr, // Text content from translation (assuming extension)
                      textAlign: TextAlign.center, // Center-aligned text
                      style: TextStyle(
                        color: Colors.white.withOpacity(
                            0.****************), // White text with slight transparency
                        fontSize: 14
                            .sp, // Font size based on device size (assuming extension)
                        fontFamily: TextStyleTheme
                            .text_Regular.fontFamily, // Font family
                        fontWeight: FontWeight.w600, // Bold text
                        height: 0.14, // Line height ratio
                      ),
                    ),
                  ),
                ),
              ),
            ));
  }

  Widget _buildAddressItem(context, String menu, String title, String value) {
    return GetBuilder<ProfileController>(
      init: ProfileController(),
        builder: (contoller) {
      return Container(
        // width: Get.width,
        // color: Colors.teal,
        height: 48.h,
        child: InkWell(
          onTap: () {
            if (menu == 'address') {
              _buildUpdate(
                  context,
                  accountAddressNo.tr,
                  accountAddressInputNo.tr,
                  editProfileController.controllerAddress.value,
                  false);
            } else if (menu == 'moo') {
              _buildUpdate(
                  context,
                  accountAddressMoo.tr,
                  accountAddressInputMoo.tr,
                  editProfileController.controllerMoo.value,
                  false);
            } else if (menu == 'province') {
              _buildUpdateProvince(context, accountAddressProvince.tr,
                  editProfileController.controllerProvince);
              print('province');
              print(editProfileController.selectedProvince);
            } else if (menu == 'district') {
              _buildUpdateProvince(context, accountAddressDistrict.tr,
                  editProfileController.selectedDistrict);
            } else if (menu == 'sub-district') {
              print(menu);
              _buildUpdateProvince(context, accountAddressSubDistrict.tr,
                  editProfileController.selectedSubDistrict);

              // _buildUpdateProvince(context, accountAddressSubDistrict.tr,
              //     editProfileController.selectedSubDistrict);
              // _buildUpdate(
              //     context,
              //     accountAddressSubDistrict.tr,
              //     accountAddressSubDistrict.tr,
              //     editProfileController.selectedSubDistrict,
              //     false);
              // if (appConfigService.countryConfigCollection.toString() == 'aam' && appConfigService.countryConfigCollection.toString() == 'rplc') {
              //
              // }
            }
            setState(() {});
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 20.h,
                child: Center(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: 12.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w400,
                      height: 0.12,
                    ),
                  ),
                ),
              ),
              value.length > 0
                  ? SizedBox(
                      height: 20.h,
                      child: Center(
                        child: Text(
                          value,
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            color: Color(0x7F1A1818).withOpacity(0.5),
                            fontSize: 14.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                            height: 0.08,
                          ),
                        ),
                      ),
                    )
                  : _buildAddAddress(context, menu, value),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildAddAddress(context, String menu, String value) {
    return GestureDetector(
      onTap: () {
        print('menu : $menu');
        // if (menu == 'address') {
        //   _buildUpdate(context, accountAddressNo.tr, accountAddressInputNo.tr,
        //       editProfileController.controllerAddress, false);
        // } else if (menu == 'moo') {
        //   _buildUpdate(context, accountAddressMoo.tr, accountAddressInputMoo.tr,
        //       editProfileController.controllerMoo, false);
        // } else if (menu == 'province') {
        //   _buildUpdateProvince(context, accountAddressProvince.tr,
        //       editProfileController.controllerProvince);
        // } else if (menu == 'district') {
        //   _buildUpdateProvince(context, accountAddressDistrict.tr,
        //       editProfileController.controllerDistrict);
        // } else if (menu == 'sub-district') {
        //   _buildUpdateProvince(context, accountAddressSubDistrict.tr,
        //       editProfileController.controllerSubDistrict);
        // }
      },
      child: Container(
        // color: Colors.red,
        width: 200.w,
        height: 20.h,
        alignment: Alignment.centerRight,
        child: menu == 'province'
            ? Text(
                editProfileController.controllerProvince.value.text.isEmpty
                    ? accountAddress.tr
                    : editProfileController.controllerProvince.value.text,
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: Color(0xFF1A1818),
                  fontSize: 14.sp,
                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                  fontWeight: FontWeight.w700,
                  decorationColor: Color(0xFF1A1818),
                  decoration: menu == 'province'
                      ? TextDecoration.underline
                      : TextDecoration.none,
                  // height: 0.08,
                ),
              )
            : menu == 'address'
                ? Text(
                    editProfileController.controllerAddress.value.text.isEmpty
                        ? "-"
                        : editProfileController.controllerAddress.value.text)
                : menu == 'moo'
                    ? Text(
                        editProfileController.controllerMoo.value.text.isEmpty
                            ? "-"
                            : editProfileController.controllerMoo.value.text)
                    : menu == 'district'
                        ? Text(editProfileController
                                .controllerDistrict.value.text.isEmpty
                            ? "-"
                            : editProfileController
                                .controllerDistrict.value.text)
                        : menu == 'sub-district'
                            ? Text(editProfileController
                                    .controllerSubDistrict.value.text.isEmpty
                                ? "-"
                                : editProfileController
                                    .controllerSubDistrict.value.text)
                            : Text(''),
      ),
    );
  }

  _buildUpdate(context, String title, String hint, controller, bool isSave) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Container(
              width: Get.width,
              height: 513.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                color: Colors.white,
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  left: 24.w,
                  right: 24.w,
                  top: 20.h,
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w600,
                            height: 0.10,
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.topRight,
                            child: SvgPicture.string(AppSvgImage.close_btn),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    Divider(
                      color: Color(0xFF1A1818).withOpacity(0.20),
                      thickness: 1,
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Container(
                        width: Get.width,
                        height: 48.h,
                        decoration: BoxDecoration(
                          color: Color(0xFFF9F9F9),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextFormField(
                          showCursor: true,
                          controller: controller,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                            // height: 0.14,
                          ),
                          cursorColor: configTheme().colorScheme.onSecondary,
                          // keyboardType: TextInputType.emailAddress,
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.only(left: 16.w),
                            hintText: hint,
                            hintStyle: TextStyle(
                              color: Color(0xFF1A1818).withOpacity(0.5),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w400,
                              // height: 0.14,
                            ),
                          ),
                          onChanged: (value) {
                            if (value.length > 0) {
                              setState(() {
                                isSave = true;
                              });
                            } else {
                              setState(() {
                                isSave = false;
                              });
                            }
                            // editProfileController.controllerAddress.value.text = value;
                          },
                        )),
                    SizedBox(
                      height: 12.h,
                    ),
                    GestureDetector(
                      onTap: () {
                        if (isSave) {
                          Navigator.pop(context);
                          print(
                              "edit address ${editProfileController.controllerAddress.value.text}");
                          print('save address');
                        }
                      },
                      child: Container(
                        width: Get.width,
                        height: 52.h,
                        decoration: ShapeDecoration(
                          color: !isSave
                              ? Color(0x191A1818).withOpacity(0.1)
                              : configTheme()
                                  .colorScheme
                                  .primary
                                  .withOpacity(1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            accountSave.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color:
                                  Colors.white.withOpacity(0.****************),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w600,
                              height: 0.14,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  _buildUpdateProvince(context, String type, controller) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            height: 624.h,
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 375.w,
                  height: 48.h,
                  // color: Colors.teal,
                  // margin: EdgeInsets.only(top: 28.h),
                  child: Center(
                    child: Text(
                      type == accountAddressProvince.tr
                          ? accountAddress.tr
                          : type == accountAddressDistrict.tr
                              ? signUpChooseDistrict.tr
                              : accountAddressSubDistrict.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize: 16.sp,
                        fontFamily: 'NotoSansThai',
                        fontWeight: FontWeight.w600,
                        // height: 0.19.h,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50.w,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: const Color(0x191A1818),
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 524.h,
                  child: ListView.builder(
                      itemCount: type == accountAddressProvince.tr
                          ? addressCtl.citiesData.length
                          : type == accountAddressDistrict.tr
                              ? addressCtl.districtsData.length
                              : type == accountAddressSubDistrict.tr
                                  ? addressCtl.subDistrictsData.length
                                  : addressCtl.subDistrictsData.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(left: 24.w, right: 24.w),
                          child: InkWell(
                            onTap: () {
                              editProfileController.setDropdown(
                                  context, type, index);
                              if (type == accountAddressProvince.tr) {
                                editProfileController.selectedProvince!.value =
                                    addressCtl.citiesData[index].cityNameLocal
                                        .toString();
                                editProfileController.selectedDistrict!.value =
                                    '';
                                editProfileController
                                    .selectedSubDistrict!.value = '';
                              } else if (type == accountAddressDistrict.tr) {
                                editProfileController.selectedDistrict!.value =
                                    addressCtl.districtsData[index].districtNameLocal.toString();
                                editProfileController.selectedSubDistrict!.value = '';
                              } else if (type == accountAddressSubDistrict.tr) {
                                editProfileController
                                        .selectedSubDistrict!.value =
                                    addressCtl.subDistrictsData[index]
                                        .subDistrictNameLocal
                                        .toString();
                              }
                              setState(() {});
                            },
                            child: Container(
                                width: 327.w,
                                height: 52.h,
                                decoration: BoxDecoration(
                                  // color: Colors.red,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: const Color(0x191A1818),
                                      width: 0.5.w,
                                    ),
                                  ),
                                ),
                                child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      type == accountAddressProvince.tr
                                          ? addressCtl
                                              .citiesData[index].cityNameLocal
                                              .toString()
                                          : type == accountAddressDistrict.tr
                                              ? addressCtl.districtsData[index]
                                                  .districtNameLocal
                                                  .toString()
                                              : type ==
                                                      accountAddressSubDistrict
                                                          .tr
                                                  ? addressCtl
                                                      .subDistrictsData[index]
                                                      .subDistrictNameLocal
                                                      .toString()
                                                  : addressCtl
                                          .subDistrictsData[index]
                                          .subDistrictNameLocal
                                          .toString(),
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 16.sp,
                                        fontFamily: 'NotoSansThai',
                                        fontWeight: FontWeight.w400,
                                        // height: 0.19.h,
                                      ),
                                    ))),
                          ),
                        );
                      }),
                )
              ],
            ),
          );
        });
  }

  buildUploadImage(context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext bc) {
          return Container(
            height: 202.h,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 14.h,
                ),
                Container(
                  height: 5.h,
                  width: 44.w,
                  decoration: BoxDecoration(
                    color: Color(0xFF1A1818).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                SizedBox(
                  height: 38.h,
                ),
                InkWell(
                  onTap: () {
                    editProfileController.pickImage();
                    Get.back();
                  },
                  child: Container(
                    height: 52.h,
                    margin: EdgeInsets.only(left: 24.w, right: 24.w),
                    // width: 44.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      color:
                          configTheme().colorScheme.primary.withOpacity(0.05),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          height: 20.h,
                          width: 20.w,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(
                                  'assets/profile/uploadImg.png',
                                ),
                                scale: 1
                                // fit: BoxFit.cover,
                                ),
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Text(
                          accountEditUploadDevice.tr,
                          style: TextStyle(
                            color: configTheme()
                                .colorScheme
                                .primary
                                .withOpacity(0.75),
                            fontSize: 14.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                            // height: 0.12,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                TextButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: Text(
                      accountEditCancel.tr,
                      style: TextStyle(
                        color: configTheme().colorScheme.primary,
                        fontSize: 14.sp,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w600,
                        // height: 0.12,
                      ),
                    ))
              ],
            ),
          );
        });
  }

  Widget _buildDivider() {
    return Container(
      alignment: Alignment.bottomCenter,
      child: Divider(
        height: 0.5.h,
        // thickness: 0.5.h,
        color: Colors.black.withOpacity(0.2),
      ),
    );
  }
}
