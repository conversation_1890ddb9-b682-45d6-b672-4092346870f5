import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/screen/login/login_page.dart';

import '../../../controller/session/session.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/theme.dart';

class DeleteAccountSuccess extends StatelessWidget {
  const DeleteAccountSuccess({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration:
                BoxDecoration(gradient:
                appConfigService.countryConfigCollection.toString() == 'aam'
                    ? AppColorsGradient.appBgAAMGradient
                    : appConfigService.countryConfigCollection.toString() ==
                    'rafco'
                    ? AppColorsGradient.appBgRAFCOGradient
                    : AppColorsGradient.appBgRPLCGradient),
          ),
          Column(
            children: [
              Center(
                child: Container(
                    width: 73.90.w,
                    height: 40.h,
                    margin: EdgeInsets.only(top: 81.0.h),
                    child:
                        Image.asset('assets/register/icon/regis_success.png')),
              ),
              SizedBox(
                height: 24.h,
              ),
              Text(
                accountSuccess.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 20.sp,
                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(
                height: 10.h,
              ),
              Text(
                accountSuccessDes.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 12.sp,
                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Spacer(),
              // Container(
              //   // height: 81.h
              //   child: Center(
              //     child: Text.rich(
              //       TextSpan(
              //         children: <InlineSpan>[
              //           TextSpan(
              //             text: 'ยืนยันลบบัญชี\n\n\n\n',
              //             style: TextStyle(
              //               color: Colors.white.withOpacity(0.****************),
              //               fontSize: 20.sp,
              //               fontFamily: TextStyleTheme.text_Regular.fontFamily,
              //               fontWeight: FontWeight.w700,
              //               height: 0.07.h,
              //             ),
              //           ),
              //           TextSpan(
              //             text: '\n',
              //           ),
              //           TextSpan(
              //             text: 'เรียบร้อย\n\n\n\n',
              //             style: TextStyle(
              //               color: Colors.white.withOpacity(0.****************),
              //               fontSize: 20.sp,
              //               fontFamily: TextStyleTheme.text_Regular.fontFamily,
              //               fontWeight: FontWeight.w700,
              //               height: 0.07.h,
              //             ),
              //           ),
              //           TextSpan(
              //             text: '\n',
              //           ),
              //           TextSpan(
              //             text: 'เอเอเอ็ม จัดไฟแนนซ์ หวังว่าเราให้ได้รับใช้\n\n\n\n',
              //             style: TextStyle(
              //               color: Colors.white.withOpacity(0.****************),
              //               fontSize: 12.sp,
              //               fontFamily: TextStyleTheme.text_Regular.fontFamily,
              //               fontWeight: FontWeight.w400,
              //               height: 0.18.h,
              //             ),
              //           ),
              //           TextSpan(
              //             text: '\n',
              //           ),
              //            TextSpan(
              //             text: 'และบริการคุณอีกครั้ง',
              //             style: TextStyle(
              //               color: Colors.white.withOpacity(0.****************),
              //               fontSize: 12.sp,
              //               fontFamily: TextStyleTheme.text_Regular.fontFamily,
              //               fontWeight: FontWeight.w400,
              //               height: 0.18.h,
              //             ),
              //           ),
              //         ],
              //       ),
              //       textAlign: TextAlign.center,
              //     ),
              //   ),
              // ),
              GestureDetector(
                onTap: () {
                  Get.put(SessionController())
                      .logout(context); //TODO: Destroy Session And Logout
                },
                child: Container(
                  width: 327.w,
                  height: 52.h,
                  // margin: EdgeInsets.only(top: 486.0.h),
                  decoration: ShapeDecoration(
                    color: Color(0xBF1A1818),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      accountOk.tr,
                      // textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w500,
                        height: 0.14.h,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 48.h,
              ),
            ],
          )
        ],
      ),
    );
  }
}
