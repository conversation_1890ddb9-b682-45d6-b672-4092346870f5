import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/screen/register/camera_scan.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/AppBackgound.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/screen/register/register_info.dart';

import '../../../controller/register/register.controller.dart';
import '../../../controller/register/scan.controller.dart';
import '../../componance/themes/app_colors_gradient.dart';

class RegisterScanScreen extends StatefulWidget {
  const RegisterScanScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScanScreen> createState() => _RegisterScanScreenState();
}

class _RegisterScanScreenState extends State<RegisterScanScreen> {
  final RegisterController registerCtl = Get.find<RegisterController>();
  final ScanController cameraCtl = Get.put(ScanController());

  @override
  void initState() {
    super.initState();
    print('RegisterScanScreen');
  }

  @override
  void dispose() {
    // TODO: implement dispose
    cameraCtl.cameraController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: GetBuilder<ScanController>(
      init: ScanController(),
      builder: (controller) {
        return Stack(
          children: [
            /// พื้นหลัง
            AppBackground.backgroundPrimaryColor(context),
            /// เช็คกล้อง
            controller.isCameraReady!.value
                ? Container(
                    height: Get.height,
                    width: Get.width,
                    child: CameraPreview(controller.cameraController!))
                : Center(child: const CircularProgressIndicator()),

            // Container(
            //     width: Get.width,
            //     height: Get.height,
            //     decoration: BoxDecoration(
            //         gradient: LinearGradient(
            //       begin: Alignment.topRight,
            //       end: Alignment.bottomLeft,
            //       colors: [
            //         const Color(0xAAA169FF).withOpacity(0.1),
            //         const Color(0x77792AFF).withOpacity(0.1)
            //       ],
            //       stops: [0.0, 1.0],
            //     ))),
            /// ส่วนของกรอบสแกน
            InkWell(
              onTap: () async {
                try {
                  AppLoading.loadingVerify(context);
                  final image = await cameraCtl.cameraController!.takePicture();
                  // await _cameraController!.stopImageStream();
                  var img = cameraCtl.cameraController!.pausePreview();

                  // image.readAsBytes();
                  // print(image.path);
                  print(image.readAsBytes());
                  cameraCtl.imgBlob.value = image.path;
                  cameraCtl.imgXFile = image;
                  cameraCtl.update();
                  cameraCtl.getOCRData(context);
                } catch (e) {
                  print(e);
                }

                // AppLoading.generalLoading(context);
                // final image = await controller.cameraController!.takePicture();
                // // var img = controller.cameraController!.pausePreview();
                // // print(image.readAsBytes());
                // controller.imgBlob.value = image.path;
                // controller.imgXFile = image;
                // AppLoading.Loaderhide(context);
              },
              child: Container(
                width: 280.w,
                height: 186.h,
                margin: EdgeInsets.only(top: 176.h, left: 47.w),
                decoration: ShapeDecoration(
                  color: Colors.black.withOpacity(0.20000000298023224),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18),
                  ),
                ),
                child: Stack(
                  children: [
                    Container(
                      alignment: Alignment.topLeft,
                      child: SvgPicture.string(
                        AppSvgImage.scan_frame1,
                      ),
                    ),
                    Container(
                      alignment: Alignment.topRight,
                      child: SvgPicture.string(
                        AppSvgImage.scan_frame2,
                      ),
                    ),
                    Container(
                      alignment: Alignment.bottomLeft,
                      child: SvgPicture.string(
                        AppSvgImage.scan_frame3,
                      ),
                    ),
                    Container(
                      alignment: Alignment.bottomRight,
                      child: SvgPicture.string(
                        AppSvgImage.scan_frame4,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            /// ส่วนของข้อความ
            Container(
              margin: EdgeInsets.only(top: 676.h),
              alignment: Alignment.center,
              height: 46.h,
              child: Text(
                'วางหน้าบัตรประชาชน ให้อยู่ในกรอบ\nเพื่อทำการสแกน',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // GetBuilder<CameraControllers>(builder: (cameraCtl) {
            //   if (!cameraCtl.isCameraReady!.value) {
            //     return const Center(
            //       child: CircularProgressIndicator(),
            //     );
            //   } else {
            //     return Padding(
            //       padding: EdgeInsets.only(top: Get.height * 0.002),
            //       child: SizedBox(
            //           height: Get.height,
            //           child: CameraPreview(cameraCtl.controller!)),
            //     );
            //   }
            // }),

            _buildHeader(),
          ],
        );
      },
    ));
  }

  Widget _buildHeader() {
    return Container(
      width: Get.width,
      height: 106.h,
      child: Stack(
        children: [
          Container(
            width: Get.width,
            height: 106.h,
            padding: EdgeInsets.only(
              top: 71.h,
              left: 132.w,
              right: 113.w,
              bottom: 11.h,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 24.h,
                  child: Text(
                    'สแกนบัตรประชาชน',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              registerCtl.setScanIdcard(true);
            },
            child: Container(
              margin: EdgeInsets.only(left: 24.w, top: 60.h),
              child: SizedBox(
                  height: 24.h,
                  width: 24.w,
                  child: SvgPicture.string(AppSvgImage.back_btn)
              )
            ),
          ),
        ],
      ),
    );
  }
}
