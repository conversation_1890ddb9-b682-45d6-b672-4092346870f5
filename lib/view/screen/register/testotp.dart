import 'package:flutter/material.dart';

class OTPInput extends StatefulWidget {
  final int length;
  final Function(String) onCompleted;
  final bool autoFocus;

  const OTPInput({
    Key? key,
    required this.length,
    required this.onCompleted,
    this.autoFocus = false,
  }) : super(key: key);

  @override
  State<OTPInput> createState() => _OTPInputState();
}

class _OTPInputState extends State<OTPInput> {
  final List<TextEditingController> controllers = [];
  final FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < widget.length; i++) {
      controllers.add(TextEditingController());
    }
  }

  @override
  void dispose() {
    focusNode.dispose();
    for (var controller in controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body:   Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(
          widget.length,
              (index) => _OTPInputBox(
            controller: controllers[index],
            focusNode: index == 0 ? focusNode : null,
            autoFocus: widget.autoFocus && index == 0,
            onChanged: (value) {
              if (value.length == 1 && index < widget.length - 1) {
                FocusScope.of(context).nextFocus();
              } else if (index == widget.length - 1) {
                String otp = '';
                for (var controller in controllers) {
                  otp += controller.text;
                }
                widget.onCompleted(otp);
              }
            },
          ),
        ),
      )
    );


  }
}

class _OTPInputBox extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode? focusNode;
  final bool autoFocus;
  final Function(String) onChanged;

  const _OTPInputBox({
    Key? key,
    required this.controller,
    this.focusNode,
    this.autoFocus = false,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        autofocus: autoFocus,
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        cursorColor: Theme.of(context).primaryColor,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        onChanged: onChanged,
      ),
    );
  }
}
