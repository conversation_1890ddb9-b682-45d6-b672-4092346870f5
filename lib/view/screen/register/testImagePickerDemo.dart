import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:tflite_v2/tflite_v2.dart';

class ImagePickerDemo extends StatefulWidget {
  const ImagePickerDemo({super.key});

  @override
  _ImagePickerDemoState createState() => _ImagePickerDemoState();
}

class _ImagePickerDemoState extends State<ImagePickerDemo> {
  final ImagePicker _picker = ImagePicker();
  XFile? _image;
  File? file;
  var _recognitions;
  var v = "";
  // var dataList = [];
  @override
  void initState() {
    super.initState();

    // print("Detect Object#####");
    // loadmodel().then((value) {
    //   print("Model Loaded");
    //   print(value);
    //   setState(() {});
    // });
  }

  // loadmodel() async {
  //   await Tflite.loadModel(
  //     model: "assets/detect_object/model_unquant.tflite",
  //     labels: "assets/detect_object/labels.txt",
  //
  //   );
  // }

  // Future<void> _pickImage() async {
  //   try {
  //     final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
  //     setState(() {
  //       _image = image;
  //       file = File(image!.path);
  //     });
  //     detectimage(file!);
  //   } catch (e) {
  //     print('Error picking image: $e');
  //   }
  // }

  // Future detectimage(File image) async {
  //   int startTime = new DateTime.now().millisecondsSinceEpoch;
  //   var recognitions = await Tflite.runModelOnImage(
  //     path: image.path,
  //     numResults: 6,
  //     threshold: 0.05,
  //     imageMean: 127.5,
  //     imageStd: 127.5,
  //   );
  //   setState(() {
  //     _recognitions = recognitions;
  //     v = recognitions.toString();
  //     // dataList = List<Map<String, dynamic>>.from(jsonDecode(v));
  //   });
  //   print("//////////////////////////////////////////////////");
  //   print(_recognitions);
  //   // print(dataList);
  //   print("//////////////////////////////////////////////////");
  //   int endTime = new DateTime.now().millisecondsSinceEpoch;
  //   print("Inference took ${endTime - startTime}ms");
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Flutter TFlite'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            if (_image != null)
              Image.file(
                File(_image!.path),
                height: 200,
                width: 200,
                fit: BoxFit.cover,
              )
            else
              Text('No image selected'),
            SizedBox(height: 20),
            // ElevatedButton(
            //   // onPressed: _pickImage,
            //   child: Text('Pick Image from Gallery'),
            // ),
            SizedBox(height: 20),
            Text(v),
          ],
        ),
      ),
    );
  }
}