import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:AAMG/controller/register/registerVerify.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';

import '../../../controller/register/register.controller.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/utils/AppSvgImage.dart';

class RegisterVerify extends StatefulWidget {
  const RegisterVerify({Key? key}) : super(key: key);

  @override
  State<RegisterVerify> createState() => _RegisterVerifyState();
}

class _RegisterVerifyState extends State<RegisterVerify> {
  final FocusNode focusNode = FocusNode();
  final RegisterVerifyController regisVerifyCtl =
  Get.find<RegisterVerifyController>();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () {
      regisVerifyCtl.startOTPTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
        color: Color(0xFF1A1818),
        fontSize: 20,
        fontFamily: 'NotoSansThai',
        fontWeight: FontWeight.w400,
        height: 0,
      ),
      decoration: const BoxDecoration(),
    );
    final preFilledWidget = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 24.w,
          height: 2.5.h,
          decoration: BoxDecoration(
            color: Color(0xFFFF1A1818).withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
    return Scaffold(
      body: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(top: 81.h),
            child:
            GetBuilder<RegisterVerifyController>(
              init: Get.put(RegisterVerifyController()),
                builder: (regisVerifyCtl) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/register/icon/verify.png',
                        fit: BoxFit.fill,
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 28.h),
                    // color: Colors.red,
                    height: 55.h,
                    child: Center(
                      child: Obx(() {
                        return Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: signUpOTP.tr,
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: 20.sp,
                                  fontFamily: 'NotoSansThai',
                                  fontWeight: FontWeight.w700,
                                  // height: 0.07.h,
                                ),
                              ),
                              // TextSpan(text: '\n'),
                              TextSpan(
                                text: '${signUpSendPhone.tr} ${regisVerifyCtl.phone_code.value}${regisVerifyCtl.phone_register!.value.substring(1,3)}-${regisVerifyCtl.phone_register!.value.substring(3)}',
                                style: TextStyle(
                                  color: Color(0x7F1A1818),
                                  fontSize: 12.sp,
                                  fontFamily: 'NotoSansThai',
                                  fontWeight: FontWeight.w400,
                                  // height: 0.18.h,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        );
                      }),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                  ),
                  SizedBox(
                    width: 254.w,
                    child: Column(
                      children: [
                        SizedBox(
                          // color: Colors.brown,
                          width: 242.w,
                          height: 30.h,
                          child: Obx(() {
                            return Pinput(
                              // androidSmsAutofillMethod:
                              // AndroidSmsAutofillMethod.smsRetrieverApi,
                              pinAnimationType: PinAnimationType.scale,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              defaultPinTheme: defaultPinTheme,
                              // preFilledWidget: preFilledWidget,
                              length: 6,
                              controller: regisVerifyCtl.otpController.value,
                              focusNode: focusNode,
                              // listenForMultipleSmsOnAndroid: true,
                              showCursor: false,
                              closeKeyboardWhenCompleted: true,
                              animationCurve: Curves.easeInOut,
                              separatorBuilder: (index) => SizedBox(width: 22),
                              hapticFeedbackType: HapticFeedbackType.vibrate,
                              onCompleted: (pin) async {
                                print("YourCodeOTP = $pin");
                              },
                              onChanged: (value) {
                                print("YourCodeOTP = $value");
                                regisVerifyCtl.checkOtpFormat(context,value);
                              },
                            );
                          }),
                        ),
                        Obx(() {
                          return SizedBox(
                              width: 254.w,
                              child: Image.asset(
                                regisVerifyCtl.isAlert!.value &&
                                    regisVerifyCtl.isVerify!.value == false &&
                                    regisVerifyCtl.otpController.value.text
                                        .length == 6
                                    ? 'assets/register/icon/OTP_error.png'
                                    : regisVerifyCtl.otpController.value.text
                                    .isNotEmpty
                                    ? appConfigService.countryConfigCollection.toString() == "aam"? AppImageAssets.aam_line_otp :appConfigService.countryConfigCollection.toString() == "rplc" ?AppImageAssets.rplc_line_otp : AppImageAssets.rafco_line_otp
                                    : 'assets/register/icon/OTP_defalt.png',
                                fit: BoxFit.fill,
                              ));
                        }),
                      ],
                    ),
                  ),
                  Container(
                    height: 74.h,
                    child: Stack(
                      children: [
                        Obx(() {
                          return regisVerifyCtl.isAlert!.value &&
                              regisVerifyCtl.isVerify!.value == false &&
                              regisVerifyCtl.otpController.value.text.length ==
                                  6
                              ? Container(
                            height: 20.h,
                            alignment: Alignment.center,
                            margin:
                            EdgeInsets.only(top: 30.h, bottom: 24.h),
                            child: Center(
                              child: Text(
                                signUpCilkRefCodeAgain.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Color(0xFFFF3B30),
                                  fontSize: 12.sp,
                                  fontFamily: 'NotoSansThai',
                                  fontWeight: FontWeight.w400,
                                  height: 0.14.h,
                                ),
                              ),
                            ),
                          )
                              : Container();
                        }),
                        Obx(() {
                          return regisVerifyCtl.isVerify!.value
                              ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 14.w,
                                height: 14.h,
                                alignment: Alignment.center,
                                margin: EdgeInsets.only(
                                    top: 30.h, bottom: 30.h),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    backgroundColor: Color(0xFFFF9300),
                                    valueColor:
                                    AlwaysStoppedAnimation<Color>(
                                        appConfigService.countryConfigCollection.toString() == "aam"? Color(0x77792AFF)
                                            :appConfigService.countryConfigCollection.toString() == "rplc" ?Color(0xFF6A7165)
                                            : Color(0x2222409A)
                                            .withOpacity(1.0)),
                                    strokeWidth: 1.0,
                                  ),
                                ),
                              ),
                            ],
                          )
                              : Container();
                        })
                      ],
                    ),
                  ),
                  Obx(() {
                    return SizedBox(
                        height: 40.h,
                        child: regisVerifyCtl.counter_OTP.value != 0
                            ? Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text:
                                ' ${signUpRefCode.tr} ${regisVerifyCtl.refCode!.value}',
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: 12.sp,
                                  fontFamily: 'NotoSansThai',
                                  fontWeight: FontWeight.w400,
                                  height: 0.18.h,
                                ),
                              ),
                              TextSpan(
                                text: signUpRefCodeAgain.tr,
                                style: TextStyle(
                                  color: Color(0x7F1A1818),
                                  fontSize: 12.sp,
                                  fontFamily: 'NotoSansThai',
                                  fontWeight: FontWeight.w400,
                                  height: 0.18.h,
                                ),
                              ),
                              TextSpan(
                                text: regisVerifyCtl.counter_OTP.value
                                    .toString(),
                                style: TextStyle(
                                  color: Color(0xFFFF9300),
                                  fontSize: 12.sp,
                                  fontFamily: 'NotoSansThai',
                                  fontWeight: FontWeight.w700,
                                  height: 0.18.h,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        )
                            : Text(
                          signUpRefCodeError.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0x7F1A1818),
                            fontSize: 12,
                            fontFamily:
                            TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                          ),
                        ));
                  }),
                  GestureDetector(
                    onTap: () {
                      //TODO : ส่ง OTP ไปใหม่
                      print("Resend OTP");
                      if (regisVerifyCtl.counter_OTP.value == 0) {
                        regisVerifyCtl.checkResendOTPConfig(context);
                      }
                    },
                    child: Container(
                      width: 105.w,
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: regisVerifyCtl.counter_OTP.value == 0
                            ?  appConfigService.countryConfigCollection.toString() == 'aam'? AppColors.AAMPurple: appConfigService.countryConfigCollection.toString() == "rplc" ? AppColors.primaryRPLC_Grey : AppColors.primaryRafco_Blue
                            : Color(0x0C1A1818),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Container(
                        width: 105.w,
                        height: 46.h,
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: SizedBox(
                          // width: 73.w,
                          // height: 14.h,
                          child: Center(
                            child: Text(
                              signUpRefCodeAgain.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color:
                                regisVerifyCtl.counter_OTP.value ==
                                    0
                                    ? Colors.white
                                    : Color(0x331A1818),
                                fontSize: 12.sp,
                                fontFamily: 'NotoSansThai',
                                fontWeight: FontWeight.w600,
                                // height: 0.18.h,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              );
            }),
          ),
          headerVerify(context)
        ],
      ),
    );
  }

  headerVerify(context) {
    return Container(
        margin: EdgeInsets.only(top: 60.h, left: 24.w, right: 24.w),
        height: 46.h,
        width: 327.w,
        child: Stack(
          children: [
            GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: SizedBox(
                    height: 24.h,
                    width: 24.w,
                    child: SvgPicture.string(AppSvgImage.back_btn)
                )
            ),
          ],
        ));
  }
}