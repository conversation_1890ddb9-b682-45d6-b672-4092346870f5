import 'package:AAMG/controller/register/scan.controller.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../componance/AppBackgound.dart';

class CameraScan extends StatelessWidget {
   CameraScan({Key? key}) : super(key: key);
  final GlobalKey _triggerKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<ScanController>(
        init: ScanController(),
        builder: (controller) {
          print('########## ${controller.isCameraInitialized.value}');
          return controller.isCameraInitialized.value
              ? GestureDetector(
                  key: _triggerKey,
                  onTap: () {
                    _detectWidgetPosition(context);
                  },
                child: Stack(
                    children: [
                      AppBackground.backgroundPrimaryColor(context),

                      CameraPreview(controller.cameraController!),
                      Positioned(
                        // top: (controller.y) * 700,
                        // right: (controller.x) * 500,
                        child: Container(
                          // width: controller.w * 100 * context.width / 100,
                          // height: controller.h * 100 * context.height / 100,
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "Scan QR Code",
                                style: TextStyle(color: Colors.white, fontSize: 20),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
              )
              : Center(
                  child: Container(),
                );
        },
      ),
    );
  }

  void _detectWidgetPosition(context) async {
    final renderBox = _triggerKey.currentContext?.findRenderObject() as RenderBox;
    final screenSize = MediaQuery.of(context).size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final x = offset.dx;
    final y = offset.dy;

    // ตรวจสอบว่าตำแหน่งอยู่ในเฟรมกล้องหรือไม่
    if (x >= 0 && x <= screenSize.width && y >= 0 && y <= screenSize.height) {

      print('x: $x, y: $y');
      // _takePicture();
    }
  }
}
