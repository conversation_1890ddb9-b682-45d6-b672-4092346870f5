import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/terms_condition.dart';
import 'package:AAMG/view/screen/register/register_info.dart';
import '../../../controller/AppConfigService.dart';
import '../../../controller/config/appVersion.controller.dart';
import '../../../controller/profile/delete.account.controller.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/widgets/app_pop_up/alert_popup.dart';
import '../../componance/widgets/app_pop_up/phone_code.dart';
import '../../componance/widgets/app_pop_up/register_popups.dart';
import '../login/login_page.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final RegisterController registerCtl = Get.put(RegisterController());
  final AppVersionController appVersionCtl = Get.put(AppVersionController());

  String Lang = Get.locale.toString();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () {
      registerCtl.setInitPhoneCode();
    });
  }

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));

    return Scaffold(
      body: Stack(
        children: [
          appConfigService.countryConfigCollection.toString() == 'rafco'
              ? Container(
            margin: EdgeInsets.only(top: 58.h),
            child: Image.asset(AppImageAssets.rafco_bg),
          )
              : Container(),
          Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w),
            child: Stack(
              children: [
                // TODO App bar
                Container(
                    margin: EdgeInsets.only(top: 60.h),
                    height: 46.h,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Get.back();
                          },
                          child: SizedBox(
                              height: 24.h,
                              width: 24.w,
                              child: SvgPicture.string(AppSvgImage.back_btn)
                          ),
                        ),
                        SizedBox(
                          width: 72.w,
                          height: 34.h,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Obx(() {
                                return Text(
                                  'Version ${appVersionCtl.versionInApp!
                                      .value}',
                                  style: TextStyle(
                                    color: configTheme().primaryColorDark,
                                    fontSize: 10.sp,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                    height: 0.24.h,
                                  ),
                                );
                              }),
                            ],
                          ),
                        )
                      ],
                    )),
                //TODO Title & logo
                Container(
                  margin: EdgeInsets.only(top: 137.h),
                  width: 327.w,
                  height: 134.h,
                  child: Stack(
                    children: [
                      appConfigService.countryConfigCollection == 'aam'
                          ? AAMLogo(context)
                          : appConfigService.countryConfigCollection == 'rafco'
                          ? RafcoLogo(context)
                          : RPLCLogo(context),
                      Positioned(
                        left: -0.50.w,
                        top: 112.h,
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: "${signUpRegis.tr} ",
                                style: TextStyle(
                                  color: configTheme().primaryColorDark,
                                  // color: configTheme().textTheme.labelSmall?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontWeight,
                                  height: 0,
                                ),
                              ),
                              TextSpan(
                                text: signUpPhone.tr,
                                style: TextStyle(
                                  color: configTheme().primaryColorDark,
                                  // color: configTheme().textTheme.labelSmall?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                  height: 0,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                //TODO phone input
                GetBuilder<RegisterController>(
                    init: RegisterController(),
                    builder: (registerController) {
                      return Container(
                        margin: EdgeInsets.only(top: 287.h),
                        // width: 327.w,
                        height: 118.h,
                        color: Colors.white,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              // width: 326.w,
                              height: 54.h,
                              decoration: ShapeDecoration(
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                    width: 0.50.w,
                                    // strokeAlign: BorderSide.strokeAlignInside,
                                    color: registerController
                                        .isPhoneValid!.value ==
                                        false
                                        ? configTheme()
                                        .inputDecorationTheme
                                        .border
                                        ?.borderSide
                                        .color ??
                                        AppColors.textBlackColor
                                            .withOpacity(0.5)
                                        : configTheme()
                                        .inputDecorationTheme
                                        .focusedBorder
                                        ?.borderSide
                                        .color ??
                                        AppColors.textBlackColor
                                            .withOpacity(0.5),
                                  ),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  appConfigService.countryConfigCollection !=
                                      'aam'
                                      ? Container(
                                    height: 36.h,
                                    child: Row(
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            PhoneCodeWidget
                                                .PhoneCodePopUp(
                                                context, 'register');
                                          },
                                          child: Container(
                                            width: 51.w,
                                            // height: 28.h,
                                            color: Colors.transparent,
                                            // alignment: Alignment.centerLeft,
                                            // margin: EdgeInsets.only(left: 21.w),
                                            child: Obx(() {
                                              return Center(
                                                child: FittedBox(
                                                  fit: Get.width > 1000 ? BoxFit.scaleDown : BoxFit.fill,
                                                  child: Text(
                                                    registerCtl
                                                        .phone_code.value,
                                                    style: TextStyle(
                                                      color: const Color(
                                                          0xFF1A1818),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodyLarge
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodyLarge
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodyLarge
                                                          ?.fontWeight,
                                                      // height: 0.14.h,
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }),
                                          ),
                                        ),
                                        VerticalDivider(
                                          color: Color(0xFF1A181880)
                                              .withOpacity(0.5),
                                          thickness: 0.5,
                                          endIndent: 1,
                                          indent: 1,
                                          width: 0.5,
                                        )
                                        // Container(
                                        //   alignment: Alignment.center,
                                        //   child: Image.asset(
                                        //       'assets/register/Line.png'),
                                        // ),
                                      ],
                                    ),
                                  )
                                      : Container(),
                                  SizedBox(
                                    width: 20.w,
                                  ),
                                  Expanded(
                                      child: TextFormField(
                                        showCursor: true,
                                        keyboardType: TextInputType.number,
                                        minLines: 1,
                                        autofocus: false,
                                        focusNode: registerCtl.textFieldFocus,
                                        controller:
                                        registerCtl.phone_regis.value,
                                        onTap: () {
                                          // Open the keyboard when the TextField is tapped
                                          FocusScope.of(context).requestFocus(
                                              registerCtl.textFieldFocus);
                                          // registerCtl.checkFormatedPhone(
                                          //     registerCtl
                                          //         .phone_regis.value.text);
                                        },
                                        inputFormatters: [
                                          LengthLimitingTextInputFormatter(
                                              13),
                                        ],
                                        decoration: InputDecoration(
                                            border: InputBorder.none,
                                            focusedBorder: InputBorder.none,
                                            // Remove default border
                                            labelText: signInPhone.tr,
                                            labelStyle: TextStyle(
                                              color: const Color(0xFF1A1818)
                                                  .withOpacity(0.3),
                                              fontSize: 12.sp,
                                              fontFamily: 'NotoSansThai',
                                              fontWeight: FontWeight.w400,
                                              height: 0.14.h,
                                            ),
                                            hintText: '080-0000000',

                                            // Hint text
                                            hintStyle: TextStyle(
                                              color: const Color(0x331A1818)
                                                  .withOpacity(0.2),
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.w400,
                                              // height: 0.14,
                                              fontFamily: 'NotoSansThai',
                                            )),
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818)
                                              .withOpacity(1.0),
                                          fontSize: 14.sp,
                                          fontFamily: 'NotoSansThai',
                                          fontWeight: FontWeight.w700,
                                        ),
                                        // onSaved: (value) {
                                        //   registerController
                                        //       .checkFormatedPhone(registerCtl
                                        //       .phone_regis.value.text);
                                        // },
                                        // onTapOutside: (value) {
                                        //   registerController
                                        //       .checkFormatedPhone(registerCtl
                                        //       .phone_regis.value.text);
                                        // }
                                      )),
                                ],
                              ),
                            ),
                            SizedBox(height: 12.h),

                            /// ปุ่มสมัครสมาชิก
                            GestureDetector(
                              onTap: () async {
                                if (registerCtl
                                    .phone_regis.value.text.isNotEmpty) {
                                  AppLoading.loadingVerify(context);
                                  //TODO check Delete Account
                                  var chkAcc =
                                  await Get.put(DeleteAccountController())
                                      .checkDeleteAccount(registerCtl
                                      .phone_code.value ,
                                      registerCtl.phone_regis.value.text );
                                  //TODO ถ้ามีสถานะ Delete Account
                                  if (chkAcc == true) {
                                    await registerCtl.checkFormatedPhone(
                                        registerCtl.phone_regis.value.text);
                                    AppLoading.Loaderhide(context);
                                    if (registerCtl.isOldUser!.value == true) {
                                      RegisterPopUp.AlertOldUser(context);
                                    } else {
                                      //TODO ไปสมัครบัญชีต่อ
                                      Get.to(const RegisterInfo());
                                    }
                                  } else if (chkAcc["status"].toString() ==
                                      'false') {
                                    AppLoading.Loaderhide(context);
                                    //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
                                    AlertPopup.AlertDeleteAccount(context);
                                  } else {
                                    AppLoading.Loaderhide(context);
                                    debugPrint(
                                        "Something went wrong try again");
                                  }
                                  print(
                                      'isOldUserif: ${registerController
                                          .isOldUser!.value}');
                                } else {
                                  debugPrint("phone number is empty");
                                }
                              },
                              child: Container(
                                width: 327.w,
                                height: 52.h,
                                decoration: ShapeDecoration(
                                  gradient: AppColorsGradient.buttonGradient,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      signUpRegis.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.white
                                            .withOpacity(0.8999999761581421),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontWeight,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                //TODO intro policy
                GestureDetector(
                  onTap: () {
                    final country =
                    appConfigService.countryConfigCollection.toString();
                    country == 'aam'
                        ? TermsAndConditionWidget.buildTermsAndConditionsAAM(
                        context)
                        : country == 'rplc'
                        ? TermsAndConditionWidget
                        .buildTermsAndConditionsRPLC(context)
                        : country == 'rafco'
                        ? TermsAndConditionWidget
                        .buildTermsAndConditionsRAFCO(context)
                        : Container();
                  },
                  child: Container(
                    // width: 327.w,
                    height: 48.h,
                    margin: EdgeInsets.only(top: 423.h),
                    child: Container(
                      // height: 48.h,
                      alignment: Alignment.center,
                      child:FittedBox(
                        fit: Get.width > 1000 ? BoxFit.scaleDown : BoxFit.fill,
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text:
                                "${signUpDescription.tr} ${appConfigService
                                    .countryConfigCollection == "aam" &&
                                    Lang == "th_TH"
                                    ? "AAM you agree to our"
                                    : appConfigService.countryConfigCollection ==
                                    "rafco" && Lang == 'en_US'
                                    ? "RAFCO you agree to our"
                                    : appConfigService.countryConfigCollection ==
                                    "rplc" && Lang == 'en_US'
                                    ? "RPLC you agree to our"
                                    : ""}",
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.5),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                  // height: 0.17.h,
                                ),
                              ),
                              // TextSpan(
                              //   text: '\n\n',
                              // ),
                              TextSpan(
                                text: signUpPolicy.tr,
                                style: TextStyle(
                                  color:
                                  configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontFamily,
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.underline,
                                  // height: 0.17.h,
                                ),
                              ),
                              TextSpan(
                                text: signUpHere.tr,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.5),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                  height: 0.17.h,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                //TODO มีบัญชีอยู่แล้ว ไป login
                GestureDetector(
                  onTap: () {
                    print('login');
                    Get.to(const LoginPage());
                  },
                  child: Container(
                    width: 327.w,
                    height: 28.h,
                    margin: EdgeInsets.only(top: 736.h),
                    color: Colors.transparent,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: '${signUpAcc.tr} ',
                                style: TextStyle(
                                  color:
                                  configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                  height: 0.19.h,
                                ),
                              ),
                              TextSpan(
                                text: signUpHave.tr,
                                style: TextStyle(
                                  color:
                                  configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontFamily,
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.underline,
                                  height: 0.14.h,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  AAMLogo(context) {
    return Stack(
      children: [
        Positioned(
          left: 0,
          top: 0,
          child: SizedBox(
            width: 40.w,
            height: 26.17.h,
            child: SvgPicture.string(
              AppSvgImage.aam_logo,
              fit: BoxFit.fill,
            ),
          ),
        ),
        Positioned(
          left: -0.50.w,
          top: 33.h,
          child: SizedBox(
            width: 115.w,
            height: 68.h,
            child: Image.asset(
              AppImageAssets.aam_regis_logo,
              fit: BoxFit.fill,
            ),
          ),
        ),
      ],
    );
  }

  RafcoLogo(context) {
    return Container(
      width: 114.13,
      height: 101,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(),
      child: Image.asset(AppImageAssets.rafco_logo_login),
    );
  }

  RPLCLogo(context) {
    return SizedBox(
      width: 115,
      height: 47.66,
      child: Image.asset(
        AppImageAssets.rplc_regis_logo,
        fit: BoxFit.fill,
      ),
    );
  }
}