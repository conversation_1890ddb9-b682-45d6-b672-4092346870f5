import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/register/register_scan_referral.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/register/registerAddress.controller.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import 'camera_scan.dart';
import 'register_scan.dart';
import 'register_verify.dart';
import 'testImagePickerDemo.dart';

class RegisterInfo extends StatefulWidget {
  const RegisterInfo({Key? key}) : super(key: key);

  @override
  State<RegisterInfo> createState() => _RegisterInfoState();
}

class _RegisterInfoState extends State<RegisterInfo> {
  final RegisterController registerController = Get.put(RegisterController());
  final RegisterAddressController regisAddressController =
  Get.put(RegisterAddressController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    regisAddressController.checkConfigAddress();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<RegisterController>(
          init: RegisterController(),
          builder: (registerCtl) {
        return Container(
          height: Get.height,
          width: Get.width,
          child: Column(
            // crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // SizedBox(height: 134.h,),
              headerRegister(context),
              Expanded(
                child: Swiper(
                  itemBuilder: (BuildContext context, int index) {
                    //ถ้าผ่านขั้นตอนแสกน ID Card มาแล้ว
                    // if (registerController.isScanIdCard!.value) {
                    //   index = registerController.indexPage!.value;
                    // }

                    index = registerController.indexPage!.value;

                    print("index $index");

                    if (index == 0) {
                      return registerStepReferal(context);
                    } else if (index == 1) {
                      if (registerController.isScanIdCard!.value) {
                        return registerStepName(context);
                      } else {
                        return registerStepScanName(context);
                      }
                    } else if (index == 2) {
                      return registerStepAddress(context);
                    } else {
                      return Container();
                    }
                  },
                  itemCount: 3,
                  onIndexChanged: (int index) {
                    print("ลำดับที่ $index");
                    registerCtl.setIndexPage(index);
                  },
                  loop: false,
                ),
              )
            ],
          ),
        );
      }),
    );
  }

  headerRegister(context) {
    //TODO slide page
    return Padding(
        padding: EdgeInsets.only(top: 60.h, left: 24.w, right: 24.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
                onTap: () {
                  // Get.back();
                  Navigator.pop(context);
                },
                child: SizedBox(
                    height: 24.h,
                    width: 24.w,
                    child: SvgPicture.string(AppSvgImage.back_btn)
                )
            ),
            Row(
              children: [
                // SizedBox(width: 21.w),
                Obx(() {
                  return Opacity(
                    opacity: registerController.indexPage!.value == 0 ? 1 : 0.4,
                    child: Container(
                      width: registerController.indexPage!.value == 0 ? 15 : 4,
                      height: 4,
                      decoration: ShapeDecoration(
                        color: appConfigService.countryConfigCollection
                            .toString() ==
                            "aam"
                            ? AppColors.AAMPrimaryOrange
                            : appConfigService.countryConfigCollection
                            .toString() ==
                            "rplc"
                            ? AppColors.primaryRPLC_Grey
                            : AppColors.primaryRafco_Blue,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                      ),
                    ),
                  );
                }),
                SizedBox(width: 6.w),
                Opacity(
                  opacity: registerController.indexPage!.value == 1 ? 1 : 0.4,
                  child: Container(
                    width: registerController.indexPage!.value == 1 ? 15 : 4,
                    height: 4,
                    decoration: ShapeDecoration(
                      color:
                      appConfigService.countryConfigCollection.toString() ==
                          "aam"
                          ? AppColors.AAMPrimaryOrange
                          : appConfigService.countryConfigCollection
                          .toString() ==
                          "rplc"
                          ? AppColors.primaryRPLC_Grey
                          : AppColors.primaryRafco_Blue,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
                SizedBox(width: 6.w),
                Opacity(
                  opacity: registerController.indexPage!.value == 2 ? 1 : 0.4,
                  child: Container(
                    width: registerController.indexPage!.value == 2 ? 15 : 4,
                    height: 4,
                    decoration: ShapeDecoration(
                      color:
                      appConfigService.countryConfigCollection.toString() ==
                          "aam"
                          ? AppColors.AAMPrimaryOrange
                          : appConfigService.countryConfigCollection
                          .toString() ==
                          "rplc"
                          ? AppColors.primaryRPLC_Grey
                          : AppColors.primaryRafco_Blue,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),
            Container(
              height: 46.h,
              width: 46.w,
            )
          ],
        ));
  }

  registerStepReferal(context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return GetBuilder<RegisterController>(
        init: Get.find<RegisterController>(),
        builder: (registerCtl) {
      return SingleChildScrollView(
        child: Column(
          children: [
            appConfigService.countryConfigCollection.toString() == 'aam'
                ? Container(
              margin: EdgeInsets.only(top: 55.h),
              child: SvgPicture.string(
                  appConfigService.countryConfigCollection.toString() ==
                      'aam'
                      ? AppSvgImage.aam_icon_referal
                      : ""),
            )
                : Container(
              margin: EdgeInsets.only(top: 55.h),
            ),
            SizedBox(height: 10.h),
            Text.rich(
              strutStyle: StrutStyle(
                height: 0.17.h,
              ),
              textAlign: TextAlign.center,
              TextSpan(
                children: [
                  TextSpan(
                    text: signUpReferCode.tr,
                    style: TextStyle(
                      color: const Color(0xFF1A1818),
                      fontSize: 20.sp,
                      fontFamily: TextStyleTheme.text_Bold.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: "${signUpRefCodeDescription.tr}\n",
                    style: TextStyle(
                      color: const Color(0xFF1A1818),
                      fontSize: 18.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  TextSpan(
                    text: signUpRefCodeDescription2.tr,
                    style: TextStyle(
                      color: const Color(0xFF1A1818),
                      fontSize: 14.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w400,
                    ),
                  )
                ],
              ),
            ),
            Container(
              width: 325.w,
              height: 54.h,
              margin: EdgeInsets.only(top: 26.h),
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    width: 1.w,
                    color: registerCtl.referalAlert!.value &&
                        registerCtl.referalCode.value.text.isNotEmpty
                        ? AppColors.errorColor
                        : registerCtl.referalComplete!.value
                        ? appConfigService.countryConfigCollection
                        .toString() ==
                        "aam"
                        ? AppColors.AAMPurple
                        : appConfigService.countryConfigCollection
                        .toString() ==
                        "rplc"
                        ? AppColors.primaryRPLC_Yellow
                        : AppColors.primaryRafco_Blue
                        : Colors.black.withOpacity(0.2),
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      alignment: Alignment.center,
                      width: Get.width,
                      child: Obx(
                            () {
                              print("referalCode"); 
                              print(registerCtl.referalCode.value.text);
                          return  TextFormField(
                        textAlign: TextAlign.center,
                        cursorWidth: 0,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(15),
                        ],
                        controller: registerCtl.referalCode.value,
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          labelStyle: TextStyle(
                            color: const Color(0xFF1A1818).withOpacity(0.3),
                            fontSize: 12,
                            fontFamily: 'NotoSansThai-Bold',
                            fontWeight: FontWeight.w400,
                            decoration: TextDecoration.none,
                          ),
                          hintText: signUpInputRefCode.tr,
                          hintStyle: TextStyle(
                            color: Colors.black.withOpacity(1),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'NotoSansThai',
                            decoration: TextDecoration.none,
                          ),
                        ),
                        style: TextStyle(
                          color: Colors.black.withOpacity(1),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'NotoSansThai',
                          decoration: TextDecoration.none,
                        ),
                        onTapOutside: (value) {
                          print("onTapOutside");
                          print(registerCtl.referalCode.value.text);
                          if(registerCtl.referalCode.value.text.length >= 6) {
                            print("onTapOutside");
                            registerCtl.checkReferalCode(context, registerCtl.referalCode.value.text);
                          }else if( registerCtl.referalCode.value.text.isEmpty){
                            registerCtl.setReferalCode(false);
                          }
                        },
                        onSaved: (value) {
                          print("onSaved");
                          print(registerCtl.referalCode.value.text);
                          if(value.toString().length >= 6) {
                            registerCtl.checkReferalCode(context, value);
                          }else if( registerCtl.referalCode.value.text.isEmpty){
                            registerCtl.setReferalCode(false);
                          }
                        },
                      );
                        },
                      )
                     
                    ),
                  ),
                  Container(
                    width: 52.w,
                    child: registerCtl.referalLoading!.value
                        ?
                    //TODO Loading..
                    Container(
                      alignment: Alignment.centerRight,
                      width: 52.w,
                      child: AppLoading.textFieldLoading(),
                    )
                        :registerCtl.referalCode.value.text.isEmpty
                    //TODO icon Scan referal
                        ?GestureDetector(
                      onTap: ()async{
                         var result = await Get.to(const RegisterScanReferral());
                         if(result != null){
                          print('result : $result');
                          registerCtl.setRefCode(result);
                        }
                      },
                      child: SizedBox(
                          width: 52.w,
                          height: 52.sh,
                          // color: Colors.red,
                          child: Image.asset(
                              'assets/register/icon/scan_referal.png')),
                    ):Container(),
                  ),
                ],
              ),
            ),
            Obx(() {
              return registerCtl.referalAlert!.value &&
                  registerCtl.referalCode.value.text.isNotEmpty
                  ? Container(
                margin: EdgeInsets.only(top: 21.h),
                height: 20.h,
                alignment: Alignment.center,
                child: Text(
                  signUpInputRefCodeAgain.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: const Color(0xFFFF3B30),
                    fontSize: 12.sp,
                    fontFamily: 'NotoSansThai',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              )
                  : Container(
                margin: EdgeInsets.only(top: 21.h),
                height: 20.h,
              );
            }),
            SizedBox(
              height: 278.h,
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: InkWell(
                onTap: () {
                  if (registerCtl.referalCode.value.text.isEmpty || registerCtl.referalComplete!.value == false) {
                    registerCtl.skipReferalPage();
                  } else {
                    registerCtl.completeReferalPage();
                  }
                },
                child: Container(
                  width: 71.w,
                  height: 52.h,
                  decoration: ShapeDecoration(
                    color: appConfigService.countryConfigCollection
                        .toString() ==
                        'aam'
                        ? AppColors.AAMPurple
                        : appConfigService.countryConfigCollection.toString() ==
                        'rplc'
                        ? AppColors.primaryRPLC_Yellow
                        : AppColors.primaryRafco_Blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Obx(() {
                    return Text(
                      registerCtl.referalComplete!.value
                          ? signUpNext.tr
                          : signUpSkipReferCode.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  registerStepScanName(context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return GetBuilder<RegisterController>(
        init: RegisterController(),
        builder: (registerCtl) {
      return Container(
        height: Get.height,
        margin: EdgeInsets.only(left: 24.w, right: 24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          // mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 46.h,
              child: SvgPicture.string(
                appConfigService.countryConfigCollection.toString() == "aam"
                    ? AppSvgImage.aam_icon_name
                    : appConfigService.countryConfigCollection.toString() ==
                    "rplc"
                    ? AppSvgImage.rplc_icon_name
                    : AppSvgImage.rafco_icon_name,
                allowDrawingOutsideViewBox: true,
              ),
            ),
            Text.rich(
              strutStyle: StrutStyle(
                height: 0.17.h,
              ),
              textAlign: TextAlign.center,
              TextSpan(
                children: [
                  TextSpan(
                    text: signUpName.tr,
                    style: TextStyle(
                      color: const Color(0xFF1A1818),
                      fontSize: 20.sp,
                      fontFamily: TextStyleTheme.text_Bold.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  // TextSpan(
                  //   text: signUpNameDataDescription.tr,
                  //   style: TextStyle(
                  //     color: const Color(0xFF1A1818),
                  //     fontSize: 14.sp,
                  //     fontFamily:
                  //     TextStyleTheme.text_Regular.fontFamily,
                  //     fontWeight: FontWeight.w400,
                  //   ),
                  // ),
                ],
              ),
            ),
            Spacer(),
            InkWell(
                onTap: () {
                  // Get.to(const ImagePickerDemo());
                  // Get.to( CameraScan());
                  Get.to(const RegisterScanScreen());
                },
                child: Container(
                  width: Get.width,
                  height: 56.h,
                  decoration: ShapeDecoration(
                    color: appConfigService.countryConfigCollection
                        .toString() ==
                        "aam"
                        ? AppColors.primaryAAM
                        : appConfigService.countryConfigCollection.toString() ==
                        "rplc"
                        ? AppColors.primaryRPLC_Yellow
                        : AppColors.primaryRafco_Blue,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50.w,
                        strokeAlign: BorderSide.strokeAlignOutside,
                        color: const Color(0x331A1818),
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            alignment: Alignment.center,
                            child: Text(
                              signUpScanID.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: appConfigService.countryConfigCollection
                                    .toString() ==
                                    "rplc"
                                    ? Color(0xFF1A1818)
                                    : Colors.white
                                    .withOpacity(0.8999999761581421),
                                fontSize: 14,
                                fontFamily:
                                TextStyleTheme.text_Regular.fontFamily,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                              width: 56.w,
                              height: 56.h,
                              alignment: Alignment.centerRight,
                              child: Image.asset(
                                'assets/register/icon/scan_referal.png',
                                color: appConfigService.countryConfigCollection
                                    .toString() ==
                                    "rplc"
                                    ? Color(0xFF1A1818)
                                    : Colors.white,
                              )),
                        ],
                      ),
                    ],
                  ),
                )),
            Container(
              margin: EdgeInsets.only(top: 14.h),
              child: InkWell(
                onTap: () {
                  registerCtl.skipScanIdCard();
                },
                child: PrimaryButton(
                  title: signUpInputData.tr,
                  onPressed: () {
                    registerCtl.skipScanIdCard();
                  },
                  backgroundColor: appConfigService.countryConfigCollection
                      .toString() ==
                      'aam'
                      ? Color(0x19792AFF)
                      : appConfigService.countryConfigCollection.toString() ==
                      'rafco'
                      ? Color(0x2622419A)
                      : Color(0x2622419A),
                  textColor: appConfigService.countryConfigCollection
                      .toString() ==
                      "aam"
                      ? Color(0xBF792AFF)
                      : appConfigService.countryConfigCollection.toString() ==
                      "rplc"
                      ? Color(0xFF6A7165)
                      : Color(0xFF22409A),
                  height: 0.h,
                  buttonWidth: 327.0.w,
                  isActive: true,
                ),
              ),
            ),
            SizedBox(
              height: 60.h,
            ),
          ],
        ),
      );
    });
  }

  registerStepName(context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return GetBuilder<RegisterController>(
        init: RegisterController(),
        builder: (registerCtl) {
      return SingleChildScrollView(
        child: Column(
          children: [
            Container(
                margin: EdgeInsets.only(top: 55.h),
                height: 46.h,
                child: SvgPicture.string(
                  appConfigService.countryConfigCollection.toString() == "aam"
                      ? AppSvgImage.aam_icon_name
                      : appConfigService.countryConfigCollection.toString() ==
                      "rplc"
                      ? AppSvgImage.rplc_icon_name
                      : AppSvgImage.rafco_icon_name,
                )),
            Text.rich(
              strutStyle: StrutStyle(
                height: 0.17.h,
              ),
              textAlign: TextAlign.center,
              TextSpan(
                children: [
                  TextSpan(
                    text: signUpName.tr,
                    style: TextStyle(
                      color: const Color(0xFF1A1818),
                      fontSize: 20.sp,
                      fontFamily: TextStyleTheme.text_Bold.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  TextSpan(
                    text: signUpNameDataDescription.tr,
                    style: TextStyle(
                      color: const Color(0xFF1A1818),
                      fontSize: 14.sp,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            // TODO name
            Container(
              width: 326.w,
              height: 54.h,
              margin: EdgeInsets.only(top: 26.h),
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    width: 1.w,
                    color: registerCtl.nameComplete!.value
                        ? appConfigService.countryConfigCollection.toString() ==
                        "aam"
                        ? AppColors.AAMPurple
                        : appConfigService.countryConfigCollection
                        .toString() ==
                        "rplc"
                        ? AppColors.primaryRPLC_Yellow
                        : AppColors.primaryRafco_Blue
                        : Colors.black.withOpacity(0.2),
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Container(
                // color: Colors.red,
                  width: 200,
                  // width: Get.width,
                  margin: EdgeInsets.only(
                      left: registerCtl.nameComplete!.value ? 20.w : 0),
                  child: TextFormField(
                    textAlign: registerCtl.nameComplete!.value
                        ? TextAlign.start
                        : TextAlign.center,
                    cursorWidth: 0,
                    controller: registerCtl.first_name.value,
                    minLines: 1,
                    autofocus: true,
                    focusNode: registerCtl.textFieldFNameFocus,
                    onTap: () {
                      // Open the keyboard when the TextField is tapped
                      FocusScope.of(context)
                          .requestFocus(registerCtl.textFieldFNameFocus);
                    },
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        // Remove default border
                        labelText: registerCtl.nameComplete!.value
                            ? signUpInputDataName.tr
                            : '',
                        labelStyle: TextStyle(
                          color: const Color(0xFF1A1818).withOpacity(0.3),
                          fontSize: 12.sp,
                          fontFamily: 'NotoSansThai',
                          height: 0.14.h,
                          fontWeight: FontWeight.w400,
                        ),
                        alignLabelWithHint: true,
                        hintText: signUpInputDataName.tr,
                        // Hint text
                        hintStyle: TextStyle(
                          color: const Color(0x331A1818).withOpacity(1),
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'NotoSansThai',
                          height: 0.14.h,
                        )),
                    style: TextStyle(
                      color: const Color(0xFF1A1818).withOpacity(1.0),
                      fontSize: 14.sp,
                      fontFamily: 'NotoSansThai',
                      fontWeight: FontWeight.w700,
                      height: 0.14.h,
                    ),
                    onChanged: (value) {
                      if (value.length > 0) {
                        registerCtl.setName(true);
                      } else {
                        registerCtl.setName(false);
                      }
                    },
                  )),
            ),
            //TODO lastname
            Container(
              width: 326.w,
              height: 54.h,
              margin: const EdgeInsets.only(top: 18),
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    width: 1.w,
                    // strokeAlign: BorderSide.strokeAlignInside,
                    color: registerCtl.lastNameComplete!.value
                        ? appConfigService.countryConfigCollection.toString() ==
                        "aam"
                        ? AppColors.AAMPurple
                        : appConfigService.countryConfigCollection
                        .toString() ==
                        "rplc"
                        ? AppColors.primaryRPLC_Yellow
                        : AppColors.primaryRafco_Blue
                        : Colors.black.withOpacity(0.2),
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: registerCtl.lastNameComplete!.value
                    ? MainAxisAlignment.start
                    : MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                      width: 200,
                      margin: EdgeInsets.only(
                          left:
                          registerCtl.lastNameComplete!.value ? 20.w : 0.w),
                      child: TextFormField(
                        textAlign: registerCtl.lastNameComplete!.value
                            ? TextAlign.start
                            : TextAlign.center,
                        cursorWidth: 0,
                        controller: registerCtl.last_name.value,
                        minLines: 1,
                        autofocus: true,
                        focusNode: registerCtl.textFieldlLNameFocus,
                        onTap: () {
                          // Open the keyboard when the TextField is tapped
                          FocusScope.of(context)
                              .requestFocus(registerCtl.textFieldlLNameFocus);
                        },
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            // Remove default border
                            labelText: registerCtl.lastNameComplete!.value
                                ? signUpInputDataSurname.tr
                                : '',
                            labelStyle: TextStyle(
                              color: const Color(0xFF1A1818).withOpacity(0.3),
                              fontSize: 12.sp,
                              fontFamily: 'NotoSansThai',
                              fontWeight: FontWeight.w400,
                              height: 0.19.h,
                            ),
                            hintText: signUpInputDataSurname.tr,
                            hintTextDirection: TextDirection.rtl,
                            // Hint text
                            hintStyle: TextStyle(
                              color: const Color(0x331A1818).withOpacity(1),
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400,
                              height: 0.14,
                              fontFamily: 'NotoSansThai',
                            )),
                        style: TextStyle(
                          color: const Color(0xFF1A1818).withOpacity(1.0),
                          fontSize: 14.sp,
                          fontFamily: 'NotoSansThai',
                          fontWeight: FontWeight.w700,
                          height: 0.14.h,
                        ),
                        onChanged: (value) {
                          if (value.length > 0) {
                            registerCtl.setLastName(true);
                          } else {
                            registerCtl.setLastName(false);
                          }
                        },
                      )),
                ],
              ),
            ),
            SizedBox(
              height: 278.h,
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: InkWell(
                onTap: () {
                  registerController.checkName();
                },
                child: Container(
                  // margin: EdgeInsets.only(bottom: 60.h),
                  width: 71.w,
                  height: 52.h,
                  decoration: ShapeDecoration(
                    color: appConfigService.countryConfigCollection
                        .toString() ==
                        'aam'
                        ? AppColors.AAMPurple
                        : appConfigService.countryConfigCollection.toString() ==
                        'rplc'
                        ? AppColors.primaryRPLC_Yellow
                        : AppColors.primaryRafco_Blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),

                  child: Center(
                    child: Text(
                      signUpNext.tr,
                      // textAlign: TextAlign.center,
                      style: TextStyle(
                        color: appConfigService.countryConfigCollection
                            .toString() ==
                            "rplc"
                            ? Color(0xFF1A1818)
                            : Colors.white,
                        fontSize: 14,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // SizedBox(height: 60.h,)
          ],
        ),
      );
    });
  }

  registerStepAddress(context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return GetBuilder<RegisterController>(
        init: RegisterController(),
        builder: (registerCtl) {
      return Column(
        children: [
          appConfigService.countryConfigCollection.toString() == 'aam'
              ? Container(
            margin: EdgeInsets.only(top: 55.h),
            child: SvgPicture.string(AppSvgImage.aam_icon_address),
          )
              : Container(
            margin: EdgeInsets.only(top: 55.h),
          ),
          Container(
              height: 32.h,
              alignment: Alignment.center,
              child: Text(
                signUpAddress.tr,
                style: TextStyle(
                  color: const Color(0xFF1A1818),
                  fontSize: 20.sp,
                  fontFamily: TextStyleTheme.text_Bold.fontFamily,
                  fontWeight: FontWeight.w700,
                ),
              )),
          SizedBox(height: 10.h),
          Container(
            height: 24.h,
            child: Text(
              signUpAddressDescription.tr,
              style: TextStyle(
                color: Color(0x7F1A1818),
                fontSize: 14,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          //TODO Province
          Container(
            width: 326.w,
            height: 54.h,
            margin: EdgeInsets.only(top: 30.h, left: 24.w, right: 24.w),
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  width: 0.50.w,
                  color: registerCtl.ProvinceComplete!.value
                      ? appConfigService.countryConfigCollection.toString() ==
                      "aam"
                      ? AppColors.AAMPurple
                      : appConfigService.countryConfigCollection
                      .toString() ==
                      "rplc"
                      ? AppColors.primaryRPLC_Yellow
                      : AppColors.primaryRafco_Blue
                      : Colors.black.withOpacity(0.2),
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: InkWell(
              onTap: () {
                _buildUpdateProvince(context,accountAddressProvince.tr , registerCtl.provinceController);
                // _buildBottomSheetPutParam("Province");
              },
              child: Stack(
                children: [
                  Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          child: Text(
                            registerController.selectedProvince!.value.isEmpty
                                ? signUpChooseProvince.tr
                                : registerController.selectedProvince!.value,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: const Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily: 'NotoSansThai',
                              fontWeight: FontWeight.w400,
                              height: 0.14.h,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Center(
                        child: Container(
                          margin: EdgeInsets.only(right: 20.62.w),
                          child: SvgPicture.string(
                            '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                            allowDrawingOutsideViewBox: true,
                            width: 11.w,
                            height: 6.h,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          //TODO District
          Container(
            width: 326.w,
            height: 54.h,
            margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 18),
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  width: 0.50.w,
                  // strokeAlign: BorderSide.strokeAlignInside,
                  color: registerCtl.DistrictComplete!.value
                      ? appConfigService.countryConfigCollection.toString() ==
                      "aam"
                      ? AppColors.AAMPurple
                      : appConfigService.countryConfigCollection
                      .toString() ==
                      "rplc"
                      ? AppColors.primaryRPLC_Yellow
                      : AppColors.primaryRafco_Blue
                      : Colors.black.withOpacity(0.2),
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: InkWell(
              onTap: () {
                _buildUpdateProvince(context,accountAddressDistrict.tr , registerCtl.districtController);
                // _buildBottomSheetPutParam("District");
              },
              child: Stack(
                children: [
                  Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          child: Text(
                            registerController.selectedDistrict!.value.isEmpty
                                ? signUpChooseDistrict.tr
                                : registerController.selectedDistrict!.value,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: const Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily: 'NotoSansThai',
                              fontWeight: FontWeight.w400,
                              height: 0.14.h,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Center(
                        child: Container(
                          margin: EdgeInsets.only(right: 20.62.w),
                          child: SvgPicture.string(
                            '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                            allowDrawingOutsideViewBox: true,
                            width: 11.w,
                            height: 6.h,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 278.h,
          ),
          InkWell(
            onTap: () async{
              print("DSsddsdssdsd");
               print(registerController.registerData.value.toJson());
              // Get.to(const RegisterVerify());
              registerController.checkAddressData(context);
            },
            child: Container(
              width: 71.w,
              height: 52.h,
              decoration: ShapeDecoration(
                color:
                appConfigService.countryConfigCollection.toString() == 'aam'
                    ? AppColors.AAMPurple
                    : appConfigService.countryConfigCollection.toString() ==
                    'rplc'
                    ? AppColors.primaryRPLC_Yellow
                    : AppColors.primaryRafco_Blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                signUpNext.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: appConfigService.countryConfigCollection.toString() ==
                      "rplc"
                      ? Color(0xFF1A1818)
                      : Colors.white,
                  fontSize: 14,
                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          )
        ],
      );
    });
  }
  //
  // Future<dynamic> _buildBottomSheetPutParam(type) {
  //   return showModalBottomSheet(
  //       context: context,
  //       isScrollControlled: true,
  //       backgroundColor: Colors.white.withOpacity(1.0),
  //       shape: const RoundedRectangleBorder(
  //         borderRadius: BorderRadius.vertical(
  //           top: Radius.circular(18.0),
  //         ),
  //       ),
  //       builder: (context) {
  //         return Container(
  //           child: scrollInput(type),
  //         );
  //       });
  // }
  //
  // scrollInput(type) {
  //   final regisAddresssCtl = Get.find<RegisterAddressController>();
  //   return Container(
  //     width: 375.w,
  //     height: 624.h,
  //     clipBehavior: Clip.antiAlias,
  //     decoration: ShapeDecoration(
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(12.r),
  //           topRight: Radius.circular(12.r),
  //         ),
  //       ),
  //       shadows: [
  //         const BoxShadow(
  //           color: Color(0x19000000),
  //           blurRadius: 20,
  //           offset: Offset(0, 4),
  //           spreadRadius: 0,
  //         )
  //       ],
  //       gradient: LinearGradient(
  //         begin: Alignment.topCenter,
  //         end: Alignment.bottomCenter,
  //         colors: [
  //           Colors.white.withOpacity(1),
  //           Colors.white.withOpacity(0.9),
  //         ],
  //       ),
  //     ),
  //     child: Column(
  //       children: [
  //         Column(
  //           mainAxisSize: MainAxisSize.min,
  //           mainAxisAlignment: MainAxisAlignment.start,
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Container(
  //               width: 375.w,
  //               height: 48.h,
  //               margin: EdgeInsets.only(top: 28.h),
  //               child: Center(
  //                 child: Text(
  //                   type == "Province"
  //                       ? signUpChooseProvince.tr
  //                       : signUpChooseDistrict.tr,
  //                   textAlign: TextAlign.center,
  //                   style: TextStyle(
  //                     color: const Color(0xFF1A1818),
  //                     fontSize: 16.sp,
  //                     fontFamily: 'NotoSansThai',
  //                     fontWeight: FontWeight.w600,
  //                     height: 0.19.h,
  //                   ),
  //                 ),
  //               ),
  //             ),
  //             Container(
  //               width: 375.w,
  //               decoration: ShapeDecoration(
  //                 shape: RoundedRectangleBorder(
  //                   side: BorderSide(
  //                     width: 0.50.w,
  //                     strokeAlign: BorderSide.strokeAlignCenter,
  //                     color: const Color(0x191A1818),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //         Container(
  //             alignment: Alignment.topCenter,
  //             height: 548,
  //             child: ListWheelScrollView(
  //               squeeze: 0.8,
  //               // ช่องว่างระหว่างลิส
  //               diameterRatio: 50,
  //               perspective: 0.01,
  //               itemExtent: 52.h,
  //               magnification: 1.6,
  //               useMagnifier: false,
  //               physics: const FixedExtentScrollPhysics(),
  //               children: List.generate(
  //                   type == "Province"
  //                       ? regisAddresssCtl.citiesData.length
  //                       : regisAddresssCtl.districtsData.length, (index) {
  //                 return InkWell(
  //                   onTap: () {
  //                     registerController.setDropdown(context, type, index);
  //                     print("index $index");
  //                   },
  //                   child: Container(
  //                     width: 327.w,
  //                     height: 52.h,
  //                     decoration: BoxDecoration(
  //                       border: Border(
  //                         bottom: BorderSide(
  //                           color: const Color(0x191A1818),
  //                           width: 0.5.w,
  //                         ),
  //                       ),
  //                     ),
  //                     child: Center(
  //                       child: Text(
  //                         type == "Province"
  //                             ? regisAddresssCtl.citiesData[index].cityName
  //                                 .toString()
  //                             : regisAddresssCtl
  //                                 .districtsData[index].districtName
  //                                 .toString(),
  //                         style: TextStyle(
  //                           color: const Color(0xFF1A1818),
  //                           fontSize: 14.sp,
  //                           fontFamily: 'NotoSansThai',
  //                           fontWeight: FontWeight.w400,
  //                           height: 0.11.h,
  //                         ),
  //                         textAlign: TextAlign.center,
  //                       ),
  //                     ),
  //                   ),
  //                 );
  //               }),
  //             )),
  //       ],
  //     ),
  //   );
  // }

  _buildUpdateProvince(context, String type, controller) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            height: 624.h,
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 375.w,
                  height: 48.h,
                  // color: Colors.teal,
                  // margin: EdgeInsets.only(top: 28.h),
                  child: Center(
                    child: Text(
                      type == accountAddressProvince.tr
                          ? accountAddress.tr
                          : type == accountAddressDistrict.tr?
                      signUpChooseDistrict.tr
                          : accountAddressSubDistrict.tr ,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize: 16.sp,
                        fontFamily: 'NotoSansThai',
                        fontWeight: FontWeight.w600,
                        // height: 0.19.h,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50.w,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: const Color(0x191A1818),
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 524.h,
                  child: ListView.builder(
                      itemCount: type == accountAddressProvince.tr
                          ? regisAddressController.citiesData.length
                          : type == accountAddressDistrict.tr
                          ? regisAddressController.districtsData.length
                          : 0,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(left: 24.w, right: 24.w),
                          child: InkWell(
                            onTap: () {
                              registerController.setDropdown(context, type, index);
                              if(type == accountAddressProvince.tr){
                                // registerController.idCity = regisAddressController.citiesData[index].cityId!;
                                registerController.selectedProvince!.value = regisAddressController.citiesData[index].cityNameLocal.toString();
                                registerController.selectedDistrict!.value = '';
                                // registerController.selectedSubDistrict!.value = '';
                              } else if(type == accountAddressDistrict.tr){
                                registerController.selectedDistrict!.value = regisAddressController.districtsData[index].districtNameLocal.toString();
                                // editProfileController.selectedSubDistrict!.value = '';
                                // } else if(type == accountAddressSubDistrict.tr){
                                //   editProfileController.selectedSubDistrict!.value = addressCtl.subDistrictsData[index].subDistrictName.toString();
                              }
                              setState(() {});
                            },
                            child: Container(
                                width: 327.w,
                                height: 52.h,
                                decoration: BoxDecoration(
                                  // color: Colors.red,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: const Color(0x191A1818),
                                      width: 0.5.w,
                                    ),
                                  ),
                                ),
                                child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      type == accountAddressProvince.tr
                                          ? regisAddressController
                                          .citiesData[index].cityNameLocal
                                          .toString()
                                          : type == accountAddressDistrict.tr
                                          ? regisAddressController.districtsData[index]
                                          .districtNameLocal
                                          .toString()
                                          : '',
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 16.sp,
                                        fontFamily: 'NotoSansThai',
                                        fontWeight: FontWeight.w400,
                                        // height: 0.19.h,
                                      ),
                                    ))),
                          ),
                        );
                      }),
                )
              ],
            ),
          );
        });
  }
}