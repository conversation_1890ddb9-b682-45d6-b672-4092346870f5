import 'package:AAMG/controller/bill_payment/bankingPayments.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:lottie/lottie.dart';

import '../../../controller/bill_payment/billPayment.controller.dart';
import '../../../controller/config/appConfig.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/dashLine.dart';
import '../../componance/widgets/header_widgets/header_general.dart';
import '../loan_screen/bill_history_screen.dart';

class PaymentSuccess extends StatelessWidget {
  PaymentSuccess({super.key});

  final ContractListController contractCtl = Get.find<ContractListController>();
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Get.off(HomeNavigator());
        Get.find<BillPaymentController>().resetData();
        Get.find<BankingPaymentController>().resetData();
        return true;
      },
      child: Scaffold(
        body: Stack(
          children: [
            GetBuilder<BillPaymentController>(
                autoRemove: true,
                builder: (billCtl) {
                  return Column(
                    children: [
                      Container(
                        padding: EdgeInsets.only(
                            top: 136.h, left: 74.w, right: 74.w),
                        child: Container(
                          height: 205.h,
                          width: Get.width,
                          child: Lottie.asset(appConfigService
                                      .countryConfigCollection ==
                                  'aam'
                              ? 'assets/animation/AAMSuccessAnimate.json'
                              : appConfigService.countryConfigCollection ==
                                      'rafco'
                                  ? 'assets/animation/RAFCOSuccessAnimate.json'
                                  : 'assets/animation/AAMSuccessAnimate.json'),
                        ),
                      ),
                      Container(
                        height: 172.h,
                        width: Get.width,
                        margin:
                            EdgeInsets.only(top: 30.h, left: 24.w, right: 24.w),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                                width: 1.w,
                                color: configTheme()
                                    .appBarTheme
                                    .titleTextStyle!
                                    .color!
                                    .withOpacity(0.08)),
                            borderRadius: BorderRadius.circular(14.r),
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 14.w, vertical: 12.h),
                          child: Column(
                            children: [
                              //TODO รายละเอียดสัญญา
                              SizedBox(
                                height: 42.h,
                                width: Get.width,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        homeNoContract.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(1),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          SizedBox(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                SizedBox(
                                                  height: 24.h,
                                                  child: Text(
                                                    contractCtl.getGuaranteeTypeName(
                                                        Get.find<
                                                                BillPaymentController>()
                                                            .selectedContractIndex
                                                            .value),

                                                    // contractCtl
                                                    //     .contractList[Get.find<
                                                    //     BillPaymentController>()
                                                    //     .selectedContractIndex
                                                    //     .value]
                                                    //     .guarantee_type
                                                    //     .toString() ==
                                                    //     "1"
                                                    //     ? contractCtl.guaranteeList[0]
                                                    //     : contractCtl
                                                    //     .contractList[Get.find<
                                                    //     BillPaymentController>()
                                                    //     .selectedContractIndex
                                                    //     .value]
                                                    //     .guarantee_type
                                                    //     .toString() ==
                                                    //     "2"
                                                    //     ? contractCtl.guaranteeList[1]
                                                    //     : appConfigService
                                                    //     .countryConfigCollection ==
                                                    //     'aam' ||
                                                    //     appConfigService
                                                    //         .countryConfigCollection ==
                                                    //         'rafco' &&
                                                    //         contractCtl
                                                    //             .contractList[Get
                                                    //             .find<
                                                    //             BillPaymentController>()
                                                    //             .selectedContractIndex
                                                    //             .value]
                                                    //             .guarantee_type
                                                    //             .toString() ==
                                                    //             "3"
                                                    //     ? contractCtl.guaranteeList[3]
                                                    //     : contractCtl.guaranteeList[2],
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(1),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontWeight,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 18.h,
                                                  child: Text(
                                                    contractCtl
                                                        .contractList[Get.find<
                                                                BillPaymentController>()
                                                            .selectedContractIndex
                                                            .value]
                                                        .ctt_code
                                                        .toString(),
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontWeight,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            width: 12.w,
                                          ),
                                          Container(
                                            alignment: Alignment.centerRight,
                                            height: 34.h,
                                            width: 34.w,
                                            child: SvgPicture.string(
                                              contractCtl
                                                          .contractList[Get.find<
                                                                  BillPaymentController>()
                                                              .selectedContractIndex
                                                              .value]
                                                          .guarantee_type
                                                          .toString() ==
                                                      "1"
                                                  ? contractCtl
                                                      .guaranteeImgList![0]
                                                  : contractCtl
                                                              .contractList[Get.find<
                                                                      BillPaymentController>()
                                                                  .selectedContractIndex
                                                                  .value]
                                                              .guarantee_type
                                                              .toString() ==
                                                          "2"
                                                      ? contractCtl
                                                          .guaranteeImgList![1]
                                                      : contractCtl
                                                                  .contractList[
                                                                      Get.find<BillPaymentController>()
                                                                          .selectedContractIndex
                                                                          .value]
                                                                  .guarantee_type
                                                                  .toString() ==
                                                              "3"
                                                          ? contractCtl
                                                              .guaranteeImgList![2]
                                                          : contractCtl.guaranteeImgList![3],
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 9.h,
                              ),
                              DashedLine(
                                dashWidth: 3.0, // ความยาว dash
                                dashHeight: 1.0.h, // ความหนา dash
                                dashSpace: 1.0, // ช่องว่างระหว่างแต่ละ dash
                                color: configTheme()
                                    .appBarTheme
                                    .titleTextStyle!
                                    .color!
                                    .withOpacity(0.08), // สีของ dash
                              ),
                              SizedBox(
                                height: 9.h,
                              ),
                              //TODO วันที่ทำรายการ
                              SizedBox(
                                height: 24.h,
                                width: Get.width,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      transactionDate.tr,
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color
                                            ?.withOpacity(1),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                      ),
                                    ),
                                    Text(
                                      '${billCtl.transectionDate.value}/${billCtl.transectionTime.value}',
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color
                                            ?.withOpacity(1),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontWeight,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              DashedLine(
                                dashWidth: 3.0, // ความยาว dash
                                dashHeight: 1.0.h, // ความหนา dash
                                dashSpace: 1.0, // ช่องว่างระหว่างแต่ละ dash
                                color: configTheme()
                                    .appBarTheme
                                    .titleTextStyle!
                                    .color!
                                    .withOpacity(0.08), // สีของ dash
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              //TODO จำนวนเงิน
                              SizedBox(
                                height: 38.h,
                                width: Get.width,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text.rich(
                                      textAlign: TextAlign.start,
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text:
                                                '${amount.tr} (${Get.find<AppConfigController>().currencyName!.value.toString()})\n',
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color
                                                  ?.withOpacity(0.5),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontWeight,
                                              height: 1.5.h,
                                            ),
                                          ),
                                          TextSpan(
                                            text: payAmount.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color
                                                  ?.withOpacity(1),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontWeight,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Container(
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                          billCtl.amountController.value.text,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color
                                                ?.withOpacity(1),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  );
                }),
            Padding(
                padding: EdgeInsets.only(top: 712.h, left: 24.w, right: 24.w),
                child: PrimaryButton(
                  isActive: true,
                  buttonWidth: 327.w,
                  textColor: Colors.white,
                  backgroundColor: configTheme().highlightColor,
                  onPressed: () {
                    Get.off(HomeNavigator());
                    Get.find<BillPaymentController>().resetData();
                    Get.find<BankingPaymentController>().resetData();
                  },
                  title: payment_success_btn.tr,
                )),
            HeaderGeneral(
              title: payment_success.tr,
              firstIcon: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: SvgPicture.string(AppSvgImage.back_btn)),
              secondIcon: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: SvgPicture.string(AppSvgImage.aam_clock_myloan)),
              firstOnPressed: () {
                Get.off(HomeNavigator());
                Get.find<BillPaymentController>().resetData();
                Get.find<BankingPaymentController>().resetData();
              },
              secondOnPressed: () async {
                //TODO history transaction
                Get.find<MyloanController>().setDataMyLoanOtherPage(context,
                    Get.find<BillPaymentController>().bil_ctt_code.value);

                await Get.find<MyloanController>()
                    .checkLoanDetail(context)
                    .then((value) {
                  Get.to(() => const BillHistoryScreen(),
                      transition: Transition.rightToLeft);
                });
              },
            )
          ],
        ),
      ),
    );
  }
}
