import 'package:AAMG/controller/bill_payment/bankingPayments.controller.dart';
import 'package:AAMG/view/componance/AppBackgound.dart';
import 'package:AAMG/view/componance/widgets/header_widgets/header_general.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/transalation/translation_key.dart';
import '../../componance/utils/AppSvgImage.dart';

class PaymentBanking extends StatefulWidget {
  PaymentBanking({super.key});

  @override
  State<PaymentBanking> createState() => _PaymentBankingState();
}

class _PaymentBankingState extends State<PaymentBanking> {
  final BankingPaymentController bankingPaymentController =
      Get.put(BankingPaymentController());
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero).then((value) {
      bankingPaymentController.setInitailContractData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GetBuilder<BankingPaymentController>(
          autoRemove: true,
          builder: (bankPaymentCtl) {
            return Stack(
              children: [
                Container(
                  child: AppBackground.backgroundPrimaryColor(context),
                ),
                HeaderGeneral(
                    title: '',
                    backgroundColor: Colors.transparent,
                    firstIcon: SizedBox(
                        width: 24.w,
                        height: 24.h,
                        child: SvgPicture.string(AppSvgImage.back_btn)),
                    secondIcon: Container(),
                    firstOnPressed: () {
                      Get.back();
                    },
                    secondOnPressed: () {}),
              ],
            );
          }),
    );
  }
}

class PaymentBankingPopUp {
  static TimeOut(BuildContext context) {
    final BankingPaymentController bankPaymentCtl =
        Get.find<BankingPaymentController>();
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            return false;
          },
          child: Dialog(
            child: Container(
              height: MediaQuery.of(context).size.height * 0.25,
              width: MediaQuery.of(context).size.width * 0.8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30.0),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text(time_Out.tr,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                      )),
                  Padding(
                    padding: const EdgeInsets.only(left: 18.0, right: 18.0),
                    child: Text(
                      token_timeout.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 18.sp,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      InkWell(
                        onTap: () {
                          bankPaymentCtl.setCountTimer(false);
                          Get.back();
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.3,
                          height: 52.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.0),
                              border: Border.all(
                                  color: Color(0xFF707070), width: 1.w)),
                          child: Center(
                            child: Text(
                              try_again.tr,
                              style: TextStyle(
                                  color: Color(0xFF707070), fontSize: 18.sp),
                            ),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Get.off(() => HomeNavigator());
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.3,
                          height: 52.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.0),
                              color: Color(0xFFE81E25)),
                          child: Center(
                            child: Text(
                              homepage.tr,
                              style: TextStyle(
                                  color: Colors.white, fontSize: 18.sp),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
