import 'package:AAMG/controller/bill_payment/billPayment.controller.dart';
import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/animation/shimmer_effect.dart';
import '../../componance/widgets/header_widgets/header_general.dart';
import 'payment_banking.dart';
import 'payment_qrcode.dart';

class PaymentCheckout extends StatelessWidget {
  final int index;
  PaymentCheckout(this.index ,{super.key});

  final ContractListController contractCtl = Get.find<ContractListController>();
  final BillPaymentController billCtl = Get.find<BillPaymentController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: 320,
            margin: EdgeInsets.only(top: 120.h, left: 24.w, right: 24.w),
            child: Column(
              children: <Widget>[
                Container(
                  // height: 138,
                  width: Get.width,
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                          width: 1.w,
                          color: configTheme()
                              .appBarTheme
                              .titleTextStyle!
                              .color!
                              .withOpacity(0.08)),
                      borderRadius: BorderRadius.circular(14.r),
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: 14.w, vertical: 14.h),
                    child: Column(
                      children: <Widget>[
                        SizedBox(
                          height: 42.h,
                          child: Row(
                            mainAxisAlignment:
                            MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              Container(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  homeNoContract.tr,
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(1),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .bodyMedium
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .bodyMedium
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .bodyMedium
                                        ?.fontWeight,
                                  ),
                                ),
                              ),
                              // TODO ประเภทสัญญา
                              SizedBox(
                                child: Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                      child: Column(
                                        crossAxisAlignment:
                                        CrossAxisAlignment.end,
                                        mainAxisAlignment:
                                        MainAxisAlignment.center,
                                        children: [
                                          Get.find<ContractListController>()
                                              .contractList
                                              .length ==
                                              0
                                              ? AnimatedShimmer(
                                              width: 100.w)
                                              : SizedBox(
                                            height: 24.h,
                                            child: Text(
                                                contractCtl.getGuaranteeTypeName(Get
                                                    .find<
                                                    BillPaymentController>()
                                                    .selectedContractIndex
                                                    .value),
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withOpacity(
                                                    1),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontWeight,
                                              ),
                                            ),
                                          ),
                                          Get.find<ContractListController>()
                                              .contractList
                                              .length ==
                                              0
                                              ? AnimatedShimmer(
                                              width: 100.w)
                                              : SizedBox(
                                            height: 18.h,
                                            child: Text(
                                              contractCtl
                                                  .contractList[index]
                                                  .ctt_code
                                                  .toString(),
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withOpacity(
                                                    0.5),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .labelSmall
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .labelSmall
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .labelSmall
                                                    ?.fontWeight,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      width: 12.w,
                                    ),
                                    Get.find<ContractListController>()
                                        .contractList
                                        .length ==
                                        0
                                        ? Container(width: 100.w)
                                        : Container(
                                      alignment:
                                      Alignment.centerRight,
                                      height: 34.h,
                                      width: 34.w,
                                      child: SvgPicture.string(
                                        contractCtl
                                            .contractList[index]
                                            .ctt_code
                                            .toString()
                                            .substring(6, 8) ==
                                            "DT"
                                            ? AppSvgImage.icon_aampay
                                            :contractCtl
                                            .contractList[index]
                                            .ctt_code
                                            .toString()
                                            .substring(6, 8) ==
                                            "DL"
                                            ? AppSvgImage.rplcpay_icon
                                            : contractCtl
                                            .contractList[index]
                                            .guarantee_type
                                            .toString() ==
                                            "1"
                                            ? contractCtl
                                            .guaranteeImgList![
                                        0]
                                            : contractCtl
                                            .contractList[index]
                                            .guarantee_type
                                            .toString() ==
                                            "2"
                                            ? contractCtl
                                            .guaranteeImgList![
                                        1]
                                            : contractCtl
                                            .contractList[index]
                                            .guarantee_type
                                            .toString() ==
                                            "3"
                                            ? contractCtl
                                            .guaranteeImgList![
                                        2]
                                            : contractCtl
                                            .guaranteeImgList![3],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 11.h,
                        ),
                        Divider(
                          color: configTheme()
                              .dividerColor
                              .withOpacity(0.08),
                          thickness: 1.h,
                        ),
                        SizedBox(
                          height: 11.h,
                        ),
                        SizedBox(
                          height: 46.h,
                          child: Column(
                            children: [
                              Container(
                                height: 18.h,
                                alignment: Alignment.centerRight,
                                child: Text(
                                  '${amount.tr} (${Get.find<AppConfigController>().currencyName!.value.toString()})',
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(0.5),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 4.h,
                              ),
                              SizedBox(
                                height: 24.h,
                                child: Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment:
                                  CrossAxisAlignment.center,
                                  children: <Widget>[
                                    SizedBox(
                                      child: Center(
                                        child: Text(
                                          payAmount.tr,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color
                                                ?.withOpacity(1),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      child: Center(
                                        child: Text(
                                          billCtl.amountController.value
                                              .text,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color
                                                ?.withOpacity(1),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  // height: 170.h,
                  width: Get.width,
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                          width: 1.w,
                          color: configTheme()
                              .appBarTheme
                              .titleTextStyle!
                              .color!
                              .withOpacity(0.08)),
                      borderRadius: BorderRadius.circular(14.r),
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: 14.w, vertical: 14.h),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(
                          height: 26.h,
                          child: Text(
                            payment_channels.tr,
                            style: TextStyle(
                              color: configTheme()
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withOpacity(1),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontWeight,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 11.h,
                        ),
                        Divider(
                          color: configTheme()
                              .dividerColor
                              .withOpacity(0.08),
                          thickness: 1.h,
                        ),
                        SizedBox(
                          height: 11.h,
                        ),
                        appConfigService.countryConfigCollection ==
                            'rafco'
                            ? Column(
                          children: [
                            SizedBox(
                              height: 12.h,
                            ),
                            //TODO การชำระเงินผ่านธนาคาร  mobile banking
                            InkWell(
                              onTap: () {
                                debugPrint('mobile banking');
                                Get.to(() => PaymentBanking());
                              },
                              child: SizedBox(
                                height: 40.h,
                                child: Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment
                                      .spaceBetween,
                                  crossAxisAlignment:
                                  CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      children: <Widget>[
                                        SvgPicture.string(
                                          AppSvgImage
                                              .banking_payment_icon_rafco,
                                        ),
                                        SizedBox(
                                          width: 10.w,
                                        ),
                                        SizedBox(
                                          child: Text(
                                            payment_banking.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color
                                                  ?.withOpacity(1),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontWeight,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      alignment:
                                      Alignment.centerRight,
                                      child: SvgPicture.string(
                                        AppSvgImage
                                            .arrow_right_icon,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )
                            : //TODO การชำระเงินผ่าน QR code
                        InkWell(
                          onTap: () async {
                            debugPrint('QR code');
                            await billCtl.setTimeQRDataExpired();
                            if (billCtl.qrCode_Url!.value != null &&
                                billCtl.qrCode_Url!.value != "" &&
                                billCtl.qrCode_Url!.value.toString().trim().isNotEmpty) {

                              print('qrCode_Url ::  ${billCtl.qrCode_Url!.value}');
                              Get.to(() => PaymentQrcode(),
                                  transition: Transition.rightToLeft);
                            }else{
                              print('qrCode_Url ::  ${billCtl.qrCode_Url!.value}');
                            }

                          },
                          child: SizedBox(
                            height: 40.h,
                            child: Row(
                              mainAxisAlignment:
                              MainAxisAlignment.spaceBetween,
                              crossAxisAlignment:
                              CrossAxisAlignment.center,
                              children: [
                                Row(
                                  children: <Widget>[
                                    SvgPicture.string(
                                      appConfigService
                                          .countryConfigCollection ==
                                          'aam'
                                          ? AppSvgImage
                                          .qr_payment_icon_aam
                                          : appConfigService
                                          .countryConfigCollection ==
                                          'rafco'
                                          ? AppSvgImage
                                          .qr_payment_icon_rafco
                                          : AppSvgImage
                                          .qr_payment_icon_rplc,
                                    ),
                                    SizedBox(
                                      width: 10.w,
                                    ),
                                    SizedBox(
                                      child: Text(
                                        payment_qr.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(1),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  child: SvgPicture.string(
                                    AppSvgImage.arrow_right_icon,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          HeaderGeneral(
            title: billPaymentMenu.tr,
            firstIcon: SizedBox(
                width: 24.w,
                height: 24.h,
                child: SvgPicture.string(AppSvgImage.back_btn)),
            secondIcon: SizedBox(width: 24.w, height: 24.h),
            firstOnPressed: () {
              Get.back();
            },
            secondOnPressed: () {},
          )
        ],
      ),
    );
  }

  String checkFormatGuarunteetype(String value) {
    if (value.contains("สินเชื่อ")) {
      return value.replaceAll("สินเชื่อ", "");
    } else {
      return value;
    }
  }
}
