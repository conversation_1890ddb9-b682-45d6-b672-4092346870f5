import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../../controller/bill_payment/billPayment.controller.dart';
import '../../../controller/contract/contractlist.controller.dart';
import '../../../controller/contract/myloan.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import '../../componance/widgets/header_widgets/header_general.dart';
import '../home/<USER>';
import '../loan_screen/bill_history_screen.dart';

class PaymentOnprocess extends StatelessWidget {
  PaymentOnprocess({super.key});
  final ContractListController contractCtl = Get.find<ContractListController>();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Get.off(HomeNavigator());
        Get.find<BillPaymentController>().resetData();
        Get.find<BillPaymentController>().resetData();
        return true;
      },
      child: Scaffold(
        body: Stack(
          children: [
            GetBuilder<BillPaymentController>(
                autoRemove: true,
                builder: (billCtl) {
                  return Column(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.only(top: 136.h, left: 74.w, right: 74.w),
                        child: Container(
                          height: 200.h,
                          width: Get.width,
                          child: Stack(
                            children: [
                              SvgPicture.string(
                                appConfigService.countryConfigCollection == 'aam'
                                    ? AppSvgImage.onProcess_bg_aam
                                    : appConfigService.countryConfigCollection ==
                                            'rafco'
                                        ? AppSvgImage.onProcess_bg_rafco
                                        : AppSvgImage.onProcess_bg_rplc,
                                fit: BoxFit.contain,
                              ),
                              Container(
                                height: 144.h,
                                width: 122.w,
                                margin: EdgeInsets.only(
                                    top: 33.h,
                                    bottom: 23.h,
                                    left: 53.w,
                                    right: 53.w),
                                child: Lottie.asset(appConfigService
                                            .countryConfigCollection ==
                                        'aam'
                                    ? 'assets/animation/AAMSlipLoop.json'
                                    : appConfigService.countryConfigCollection ==
                                            'rafco'
                                        ? 'assets/animation/RAFCOSlipLoop.json'
                                        : 'assets/animation/AAMSlipLoop.json'),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        // height: 82.h,
                        width: Get.width,
                        margin:
                            EdgeInsets.only(top: 60.h, left: 24.w, right: 24.w),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              height: 20.h,
                              width: Get.width,
                              alignment: Alignment.center,
                              child: Text(
                                onProcessTitle.tr,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(1),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontWeight,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Container(
                              height: 52.h,
                              width: Get.width,
                              alignment: Alignment.center,
                              child: Text(
                                onProcessDesc.tr,
                                maxLines: 2,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.75),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontWeight,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  );
                }),
            Padding(
                padding: EdgeInsets.only(top: 712.h, left: 24.w, right: 24.w),
                child: PrimaryButton(
                  isActive: true,
                  buttonWidth: 327.w,
                  textColor: Colors.white,
                  backgroundColor: configTheme().highlightColor,
                  onPressed: () {
                    Get.off(HomeNavigator());
                    Get.find<BillPaymentController>().resetData();
                    Get.find<BillPaymentController>().resetData();
                  },
                  title: payment_success_btn.tr,
                )),
            HeaderGeneral(
              title: payment_success.tr,
              firstIcon: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: SvgPicture.string(AppSvgImage.back_btn)),
              secondIcon: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: SvgPicture.string(AppSvgImage.aam_clock_myloan)),
              firstOnPressed: () {

                Get.off(HomeNavigator());
                Get.find<BillPaymentController>().resetData();
                Get.find<BillPaymentController>().resetData();

              },
              secondOnPressed: () async {
                //TODO history transaction
                Get.find<MyloanController>().setDataMyLoanOtherPage(context,
                    Get.find<BillPaymentController>().bil_ctt_code.value);

                await Get.find<MyloanController>()
                    .checkLoanDetail(context)
                    .then((value) {
                  Get.to(() => const BillHistoryScreen(),
                      transition: Transition.rightToLeft);
                });
              },
            )
          ],
        ),
      ),
    );
  }
}
