import 'package:AAMG/controller/bill_payment/billPayment.controller.dart';
import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/service/AppService.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:AAMG/view/screen/bill_payment/payment_checkout.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/animation/shimmer_effect.dart';
import '../../componance/widgets/header_widgets/header_general.dart';

class SelectPaymentScreen extends StatelessWidget {
  SelectPaymentScreen({super.key});

  final billCtl = Get.put(BillPaymentController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
      child: Stack(
        children: [
          Column(
            children: [
              //TODO loan list
              GetBuilder<BillPaymentController>(
                  init: BillPaymentController(),
                  autoRemove: true,
                  builder: (billPaymentCtl) {
                    if (Get.find<ContractListController>()
                            .contractList
                            .length ==
                        0) {
                      return CircularProgressIndicator();
                    } else {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        child: Container(
                          margin: EdgeInsets.only(top: 120.h),
                          height: 498.h,
                          width: Get.width,
                          child: Column(
                            children: [
                              //TODO loan details & choose payment
                              SizedBox(
                                height: 153.h,
                                width: Get.width,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 21.h,
                                      width: Get.width,
                                      child: Text(
                                        homeNoContract.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(1),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 14.h),
                                    //TODO loan dropdown
                                    _buildDropDownContract(context),
                                    SizedBox(height: 14.h),
                                    SizedBox(
                                        height: 46.h,
                                        width: Get.width,
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Expanded(
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  SizedBox(
                                                      height: 46.h,
                                                      child: Text(
                                                        billDueDate.tr,
                                                        style: TextStyle(
                                                          color: configTheme()
                                                              .textTheme
                                                              .bodyMedium
                                                              ?.color
                                                              ?.withOpacity(
                                                                  0.5),
                                                          fontSize: configTheme()
                                                              .primaryTextTheme
                                                              .labelSmall
                                                              ?.fontSize,
                                                          fontFamily: configTheme()
                                                              .primaryTextTheme
                                                              .labelSmall
                                                              ?.fontFamily,
                                                          fontWeight: configTheme()
                                                              .primaryTextTheme
                                                              .labelSmall
                                                              ?.fontWeight,
                                                        ),
                                                      )),
                                                  SizedBox(
                                                      height: 46.h,
                                                      child: Text(
                                                        transactionDate.tr,
                                                        style: TextStyle(
                                                          color: configTheme()
                                                              .textTheme
                                                              .bodyMedium
                                                              ?.color
                                                              ?.withOpacity(
                                                                  0.5),
                                                          fontSize: configTheme()
                                                              .primaryTextTheme
                                                              .labelSmall
                                                              ?.fontSize,
                                                          fontFamily: configTheme()
                                                              .primaryTextTheme
                                                              .labelSmall
                                                              ?.fontFamily,
                                                          fontWeight: configTheme()
                                                              .primaryTextTheme
                                                              .labelSmall
                                                              ?.fontWeight,
                                                        ),
                                                      )),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Get.find<ContractListController>()
                                                              .contractList
                                                              .length ==
                                                          0
                                                      ? AnimatedShimmer(
                                                          width: 100.w)
                                                      : SizedBox(
                                                          height: 46.h,
                                                          child: Text(
                                                            // Get.find<
                                                            //         ContractListController>()
                                                            //     .contractList[Get.find<
                                                            //                 BillPaymentController>()
                                                            //             .selectedContractIndex
                                                            //             .value ??
                                                            //         0]
                                                            //     .due_date
                                                            //     .toString(),
                                                            billPaymentCtl
                                                                .dueDateLocalFormat
                                                                .value,
                                                            style: TextStyle(
                                                              color: configTheme()
                                                                  .textTheme
                                                                  .bodyMedium
                                                                  ?.color
                                                                  ?.withOpacity(
                                                                      1),
                                                              fontSize: configTheme()
                                                                  .primaryTextTheme
                                                                  .bodyMedium
                                                                  ?.fontSize,
                                                              fontFamily: configTheme()
                                                                  .primaryTextTheme
                                                                  .bodyMedium
                                                                  ?.fontFamily,
                                                              fontWeight: configTheme()
                                                                  .primaryTextTheme
                                                                  .bodyMedium
                                                                  ?.fontWeight,
                                                            ),
                                                          )),
                                                  SizedBox(
                                                      height: 46.h,
                                                      child: Text(
                                                        billPaymentCtl
                                                            .transectionDate
                                                            .value,
                                                        style: TextStyle(
                                                          color: configTheme()
                                                              .textTheme
                                                              .bodyMedium
                                                              ?.color
                                                              ?.withOpacity(1),
                                                          fontSize: configTheme()
                                                              .primaryTextTheme
                                                              .bodyMedium
                                                              ?.fontSize,
                                                          fontFamily: configTheme()
                                                              .primaryTextTheme
                                                              .bodyMedium
                                                              ?.fontFamily,
                                                          fontWeight: configTheme()
                                                              .primaryTextTheme
                                                              .bodyMedium
                                                              ?.fontWeight,
                                                        ),
                                                      )),
                                                ],
                                              ),
                                            )
                                          ],
                                        )),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 15.5.h,
                              ),
                              Divider(
                                height: 1.h,
                                color: configTheme()
                                    .appBarTheme
                                    .titleTextStyle!
                                    .color!
                                    .withOpacity(0.08),
                              ),
                              SizedBox(
                                height: 15.5.h,
                              ),
                              SizedBox(
                                height: 313.h,
                                width: Get.width,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 21.h,
                                      width: Get.width,
                                      child: Text(
                                        choosebillAmount.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(1),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 14.h),
                                    InkWell(
                                      onTap: () {
                                        billPaymentCtl
                                            .setSelectPayAmount(false);
                                      },
                                      child: Container(
                                        decoration: ShapeDecoration(
                                          color: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                                width: 1.w,
                                                color: billPaymentCtl
                                                            .isSpectifyAmount
                                                            .value ==
                                                        false
                                                    ? configTheme()
                                                        .highlightColor
                                                        .withOpacity(1)
                                                    : configTheme()
                                                        .appBarTheme
                                                        .titleTextStyle!
                                                        .color!
                                                        .withOpacity(0.1)),
                                            borderRadius:
                                                BorderRadius.circular(14.r),
                                          ),
                                        ),
                                        height: 132.h,
                                        width: Get.width,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 14.w, vertical: 16.h),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                  child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  billPaymentCtl
                                                          .isSpectifyAmount
                                                          .value
                                                      ? Container(
                                                          height: 24.h,
                                                          width: 24.w,
                                                          decoration:
                                                              BoxDecoration(
                                                            shape: BoxShape
                                                                .circle, // กำหนดรูปทรงให้เป็นวงกลม
                                                            border: Border.all(
                                                                width: 1.w,
                                                                color: configTheme()
                                                                    .highlightColor
                                                                    .withOpacity(
                                                                        0.5)),
                                                          ),
                                                        )
                                                      : SizedBox(
                                                          height: 24.h,
                                                          width: 24.w,
                                                          child: Center(
                                                              child: SvgPicture
                                                                  .string(
                                                            appConfigService
                                                                        .countryConfigCollection ==
                                                                    'aam'
                                                                ? AppSvgImage
                                                                    .choose_amount_aam
                                                                : appConfigService
                                                                            .countryConfigCollection ==
                                                                        'rplc'
                                                                    ? AppSvgImage
                                                                        .choose_amount_rplc
                                                                    : AppSvgImage
                                                                        .choose_amount_rafco,
                                                          )),
                                                        ),
                                                  SizedBox(width: 8.w),
                                                  Text(
                                                    paidAmount.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.75),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ],
                                              )),
                                              SizedBox(height: 40.h),
                                              Container(
                                                  height: 36.h,
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.end,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Get.find<ContractListController>()
                                                                  .contractList
                                                                  .length ==
                                                              0
                                                          ? AnimatedShimmer(
                                                              width: 100.w)
                                                          : Container(
                                                              alignment:
                                                                  Alignment
                                                                      .center,
                                                              height: 36.h,
                                                              child: Text(
                                                                AppService.formatCurrencyWithDecimal(Get
                                                                        .find<
                                                                            ContractListController>()
                                                                    .contractList[
                                                                        billPaymentCtl.selectedContractIndex.value ??
                                                                            0]
                                                                    .nextpay
                                                                    .toString()),
                                                                style:
                                                                    TextStyle(
                                                                  color: billPaymentCtl
                                                                          .isSpectifyAmount
                                                                          .value
                                                                      ? configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color
                                                                          ?.withOpacity(
                                                                              0.5)
                                                                      : configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color
                                                                          ?.withOpacity(
                                                                              1),
                                                                  fontSize:
                                                                      24.sp,
                                                                  fontFamily: configTheme()
                                                                      .primaryTextTheme
                                                                      .displayLarge
                                                                      ?.fontFamily,
                                                                  fontWeight: configTheme()
                                                                      .primaryTextTheme
                                                                      .displayLarge
                                                                      ?.fontWeight,
                                                                ),
                                                              ),
                                                            ),
                                                      SizedBox(
                                                        width: 8.w,
                                                      ),
                                                      Container(
                                                        alignment:
                                                            Alignment.center,
                                                        child: Text(
                                                          Get.find<AppConfigController>().currencyName!.value.toString(),
                                                          // menuGetLoanBath.tr,
                                                          style: TextStyle(
                                                            color: billPaymentCtl
                                                                    .isSpectifyAmount
                                                                    .value
                                                                ? configTheme()
                                                                    .textTheme
                                                                    .bodyMedium
                                                                    ?.color
                                                                    ?.withOpacity(
                                                                        0.5)
                                                                : configTheme()
                                                                    .textTheme
                                                                    .bodyMedium
                                                                    ?.color
                                                                    ?.withOpacity(
                                                                        0.5),
                                                            fontSize: configTheme()
                                                                .primaryTextTheme
                                                                .labelSmall
                                                                ?.fontSize,
                                                            fontFamily: configTheme()
                                                                .primaryTextTheme
                                                                .labelSmall
                                                                ?.fontFamily,
                                                            fontWeight: configTheme()
                                                                .primaryTextTheme
                                                                .labelSmall
                                                                ?.fontWeight,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 14.h),
                                    appConfigService.countryConfigCollection ==
                                            'aam'
                                        ?
                                        //TODO specify amount
                                        InkWell(
                                            onTap: () {
                                              billPaymentCtl
                                                  .setSelectPayAmount(true);
                                            },
                                            child: Container(
                                              decoration: ShapeDecoration(
                                                color: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  side: BorderSide(
                                                      width: 1.w,
                                                      color: billPaymentCtl
                                                              .isSpectifyAmount
                                                              .value
                                                          ? configTheme()
                                                              .highlightColor
                                                              .withOpacity(1)
                                                          : configTheme()
                                                              .appBarTheme
                                                              .titleTextStyle!
                                                              .color!
                                                              .withOpacity(
                                                                  0.1)),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          14.r),
                                                ),
                                              ),
                                              height: 132.h,
                                              width: Get.width,
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 14.w,
                                                    vertical: 16.h),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Expanded(
                                                        child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        billPaymentCtl
                                                                    .isSpectifyAmount
                                                                    .value ==
                                                                false
                                                            ? Container(
                                                                height: 24.h,
                                                                width: 24.w,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  shape: BoxShape
                                                                      .circle, // กำหนดรูปทรงให้เป็นวงกลม
                                                                  border: Border.all(
                                                                      width:
                                                                          1.w,
                                                                      color: configTheme()
                                                                          .highlightColor
                                                                          .withOpacity(
                                                                              0.5)),
                                                                ),
                                                              )
                                                            : SizedBox(
                                                                height: 24.h,
                                                                width: 24.w,
                                                                child: Center(
                                                                    child: SvgPicture
                                                                        .string(
                                                                  appConfigService
                                                                              .countryConfigCollection ==
                                                                          'aam'
                                                                      ? AppSvgImage
                                                                          .choose_amount_aam
                                                                      : appConfigService.countryConfigCollection ==
                                                                              'rplc'
                                                                          ? AppSvgImage
                                                                              .choose_amount_rplc
                                                                          : AppSvgImage
                                                                              .choose_amount_rafco,
                                                                )),
                                                              ),
                                                        SizedBox(width: 8.w),
                                                        Text(
                                                          specifyAmount.tr,
                                                          style: TextStyle(
                                                            color: configTheme()
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.color
                                                                ?.withOpacity(
                                                                    0.75),
                                                            fontSize: configTheme()
                                                                .primaryTextTheme
                                                                .bodySmall
                                                                ?.fontSize,
                                                            fontFamily: configTheme()
                                                                .primaryTextTheme
                                                                .bodySmall
                                                                ?.fontFamily,
                                                            fontWeight: configTheme()
                                                                .primaryTextTheme
                                                                .bodySmall
                                                                ?.fontWeight,
                                                          ),
                                                        ),
                                                      ],
                                                    )),
                                                    SizedBox(height: 40.h),
                                                    Container(
                                                        height: 36.h,
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .end,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Container(
                                                              alignment: Alignment
                                                                  .centerRight,
                                                              height: 36.h,
                                                              width: 200.w,
                                                              // margin: EdgeInsets.only(right: 28.w),
                                                              child:
                                                                  TextFormField(
                                                                // readOnly:
                                                                //     true, // TODO ปิดไปก่อน
                                                                textAlign:
                                                                    TextAlign
                                                                        .end,
                                                                controller:
                                                                    billPaymentCtl
                                                                        .amountController
                                                                        .value,
                                                                // focusNode: billPaymentCtl
                                                                //     .amountFocusNode,
                                                                style:
                                                                    TextStyle(
                                                                  color: billPaymentCtl
                                                                              .isSpectifyAmount
                                                                              .value ==
                                                                          false
                                                                      ? configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color
                                                                          ?.withOpacity(
                                                                              0.5)
                                                                      : configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color
                                                                          ?.withOpacity(
                                                                              1),
                                                                  fontSize:
                                                                      24.sp,
                                                                  fontFamily: configTheme()
                                                                      .primaryTextTheme
                                                                      .displayLarge
                                                                      ?.fontFamily,
                                                                  fontWeight: configTheme()
                                                                      .primaryTextTheme
                                                                      .displayLarge
                                                                      ?.fontWeight,
                                                                ),
                                                                keyboardType:
                                                                    TextInputType
                                                                        .number,
                                                                inputFormatters: [
                                                                  FilteringTextInputFormatter
                                                                      .allow(RegExp(
                                                                          r'[0-9]')), // Allow only numbers
                                                                  // LengthLimitingTextInputFormatter(
                                                                  //     10),
                                                                ],
                                                                decoration:
                                                                    InputDecoration(
                                                                  border:
                                                                      InputBorder
                                                                          .none,
                                                                  labelStyle:
                                                                      TextStyle(
                                                                    color: billPaymentCtl.isSpectifyAmount.value ==
                                                                            false
                                                                        ? configTheme()
                                                                            .textTheme
                                                                            .bodyMedium
                                                                            ?.color
                                                                            ?.withOpacity(
                                                                                0.5)
                                                                        : configTheme()
                                                                            .textTheme
                                                                            .bodyMedium
                                                                            ?.color
                                                                            ?.withOpacity(1),
                                                                    fontSize:
                                                                        24.sp,
                                                                    fontFamily: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontFamily,
                                                                    fontWeight: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontWeight,
                                                                  ),
                                                                ),
                                                                onTap: () {
                                                                  billPaymentCtl
                                                                      .setSelectPayAmount(
                                                                          true);
                                                                },
                                                                onFieldSubmitted:
                                                                    (value) {
                                                                  print(
                                                                      'onFieldSubmitted : ${value}');
                                                                  billPaymentCtl
                                                                      .setAmount(
                                                                          value);
                                                                },
                                                                onTapOutside:
                                                                    (value) {
                                                                  print(
                                                                      'onTapOutside : ${value}');
                                                                  billPaymentCtl.setAmount(int.parse(
                                                                      billPaymentCtl
                                                                          .amountController
                                                                          .value
                                                                          .text));
                                                                },
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              width: 8.w,
                                                            ),
                                                            Container(
                                                              alignment: Alignment
                                                                  .bottomRight,
                                                              child: Text(
                                                                menuGetLoanBath
                                                                    .tr,
                                                                style:
                                                                    TextStyle(
                                                                  color: billPaymentCtl
                                                                          .isSpectifyAmount
                                                                          .value
                                                                      ? configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color
                                                                          ?.withOpacity(
                                                                              0.5)
                                                                      : configTheme()
                                                                          .textTheme
                                                                          .bodyMedium
                                                                          ?.color
                                                                          ?.withOpacity(
                                                                              0.5),
                                                                  fontSize: configTheme()
                                                                      .primaryTextTheme
                                                                      .labelSmall
                                                                      ?.fontSize,
                                                                  fontFamily: configTheme()
                                                                      .primaryTextTheme
                                                                      .labelSmall
                                                                      ?.fontFamily,
                                                                  fontWeight: configTheme()
                                                                      .primaryTextTheme
                                                                      .labelSmall
                                                                      ?.fontWeight,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        )),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          )
                                        : Container(),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    }
                  }),
              //TODO button
              Padding(
                padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 94.h),
                child: PrimaryButton(
                  title: billPaymentMenu.tr,
                  buttonWidth: Get.width,
                  backgroundColor: configTheme()
                      .buttonTheme
                      .colorScheme!
                      .secondary
                      .withOpacity(1.0),
                  isActive: true,
                  textColor: configTheme().buttonTheme.colorScheme!.onPrimary,
                  onPressed: () async {
                    var myLoanCtl;
                    if (Get.isRegistered<MyloanController>()) {
                      myLoanCtl = Get.find<MyloanController>();
                    } else {
                      myLoanCtl = Get.put(MyloanController());
                    }

                    myLoanCtl.setIndexMyloan(Get.find<BillPaymentController>()
                        .selectedContractIndex
                        .value);
                    myLoanCtl.getLoanDetailForBillPayment(context);
                    Get.to(
                        () => PaymentCheckout(Get.find<BillPaymentController>()
                            .selectedContractIndex
                            .value),
                        transition: Transition.rightToLeft);
                  },
                ),
              ),
            ],
          ),
          HeaderGeneral(
            title: billPaymentMenu.tr,
            firstIcon: SizedBox(
                width: 24.w,
                height: 24.h,
                child: SvgPicture.string(AppSvgImage.back_btn)),
            secondIcon: SizedBox(width: 24.w, height: 24.h),
            firstOnPressed: () {
              Get.find<BillPaymentController>().resetData();
              Get.back();
            },
            secondOnPressed: () {},
          )
        ],
      ),
    ));
  }

  Widget _buildDropDownContract(context) {
    return GetBuilder<ContractListController>(builder: (contractCtl) {
      return Container(
        width: 327.w,
        height: 58.h,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 1.w, color: const Color(0x141A1818)),
            borderRadius: BorderRadius.circular(14.r),
          ),
        ),
        child: InkWell(
          onTap: () {
            print('click');
            _buildBottomSheetContractList(context);
          },
          child: Stack(
            children: [
              Row(
                children: [
                  Container(
                    width: 34.w,
                    height: 34.h,
                    margin: EdgeInsets.only(
                        top: 12.h, left: 14.w, right: 12.w, bottom: 12.h),
                    child:
                        Get.find<ContractListController>()
                                    .contractList
                                    .length ==
                                0
                            ? Container(width: 100.w)
                            : SvgPicture.string(
                                contractCtl
                                            .contractList[Get.find<BillPaymentController>()
                                                .selectedContractIndex
                                                .value]
                                            .ctt_code
                                            .toString()
                                            .substring(6, 8) ==
                                        "DT"
                                    ? AppSvgImage.icon_aampay
                                :contractCtl
                                    .contractList[Get.find<BillPaymentController>()
                                    .selectedContractIndex
                                    .value]
                                    .ctt_code
                                    .toString()
                                    .substring(6, 8) ==
                                    "DL"
                                    ? AppSvgImage.rplcpay_icon
                                    : contractCtl
                                                .contractList[
                                                    Get.find<BillPaymentController>()
                                                        .selectedContractIndex
                                                        .value]
                                                .guarantee_type
                                                .toString() ==
                                            "1"
                                        ? contractCtl.guaranteeImgList![0]
                                        : contractCtl
                                                    .contractList[Get.find<BillPaymentController>()
                                                        .selectedContractIndex
                                                        .value]
                                                    .guarantee_type
                                                    .toString() ==
                                                "2"
                                            ? contractCtl.guaranteeImgList![1]
                                            : contractCtl
                                                        .contractList[Get.find<BillPaymentController>()
                                                            .selectedContractIndex
                                                            .value]
                                                        .guarantee_type
                                                        .toString() ==
                                                    "3"
                                                ? contractCtl.guaranteeImgList![2]
                                                : contractCtl.guaranteeImgList![3],
                              ),
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(vertical: 8.h),
                    height: 42.h,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Get.find<ContractListController>()
                                    .contractList
                                    .length ==
                                0
                            ? AnimatedShimmer(width: 100.w)
                            : SizedBox(
                                height: 18.h,
                                child: Center(
                                  child: Text(

                                    // contractCtl.contractList[Get.find<BillPaymentController>().selectedContractIndex.value].ctt_code
                                    //             .toString()
                                    //             .substring(6, 8) ==
                                    //         "DT"
                                    //     ? "เอเอเอ็ม เปย์"
                                    //     : contractCtl
                                    //                 .contractList[
                                    //                     Get.find<BillPaymentController>()
                                    //                         .selectedContractIndex
                                    //                         .value]
                                    //                 .guarantee_type
                                    //                 .toString() ==
                                    //             "1"
                                    //         ? contractCtl.guaranteeList[0]
                                    //         : contractCtl
                                    //                     .contractList[Get.find<BillPaymentController>()
                                    //                         .selectedContractIndex
                                    //                         .value]
                                    //                     .guarantee_type
                                    //                     .toString() ==
                                    //                 "2"
                                    //             ? contractCtl.guaranteeList[1]
                                    //             : appConfigService.countryConfigCollection == 'aam' ||
                                    //                     appConfigService.countryConfigCollection ==
                                    //                             'rafco' &&
                                    //                         contractCtl.contractList[Get.find<BillPaymentController>().selectedContractIndex.value].guarantee_type.toString() == "3"
                                    //                 ? contractCtl.guaranteeList[2]
                                    //                 : contractCtl.guaranteeList[1],
                                    contractCtl.getGuaranteeTypeName(Get.find<BillPaymentController>()
                                        .selectedContractIndex
                                        .value),


                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color
                                          ?.withOpacity(1),
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontWeight,
                                    ),
                                  ),
                                ),
                              ),
                        Get.find<ContractListController>()
                                    .contractList
                                    .length ==
                                0
                            ? AnimatedShimmer(width: 100.w)
                            : SizedBox(
                                height: 18.h,
                                child: Center(
                                  child: Text(
                                    contractCtl
                                        .contractList[
                                            Get.find<BillPaymentController>()
                                                .selectedContractIndex
                                                .value]
                                        .ctt_code
                                        .toString(),
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color
                                          ?.withOpacity(0.5),
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontWeight,
                                    ),
                                  ),
                                ),
                              ),
                      ],
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                    child: Container(
                      margin: EdgeInsets.only(right: 15.w),
                      child: SvgPicture.string(
                        '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        color: configTheme().textTheme.bodyMedium?.color,
                        width: 11.w,
                        height: 6.h,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  _buildBottomSheetContractList(context) {
    AppConfigService appConfigService = Get.find<AppConfigService>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: configTheme().colorScheme.background.withOpacity(1.0),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(18.0),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 600.h,
            padding:  EdgeInsets.only(bottom: 20,),
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.background,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: SizedBox(
              // height: 550.h,
              child: Column(
                children: [
                  Container(
                    height: 34.h,
                    alignment: Alignment.center,
                    child: SvgPicture.string(AppSvgImage.close_bar),
                  ),
                  SizedBox(
                    height: 26.h,
                  ),
                  SizedBox(
                    height: 500.h,
                    child: SingleChildScrollView(
                      child: ListView.builder(
                        itemBuilder: (context, index) {
                          return _listLoanMenu(
                              context,
                              Get.find<ContractListController>().getGuaranteeTypeName(index),
                              // Get.find<ContractListController>()
                              //             .contractList[index]
                              //             .ctt_code
                              //             .toString()
                              //             .substring(6, 8) ==
                              //         "DT"
                              //     ? "เอเอเอ็ม เปย์"
                              //     : Get.find<ContractListController>()
                              //                 .contractList[index]
                              //                 .guarantee_type
                              //                 .toString() ==
                              //             "1"
                              //         ? Get.find<ContractListController>()
                              //             .guaranteeList[0]
                              //             .toString()
                              //         : Get.find<ContractListController>()
                              //                     .contractList[index]
                              //                     .guarantee_type
                              //                     .toString() ==
                              //                 "2"
                              //             ? Get.find<ContractListController>()
                              //                 .guaranteeList[1]
                              //                 .toString()
                              //             : appConfigService
                              //                             .countryConfigCollection ==
                              //                         'aam' ||
                              //                     appConfigService
                              //                                 .countryConfigCollection ==
                              //                             'rafco' &&
                              //                         Get.find<ContractListController>()
                              //                                 .contractList[index]
                              //                                 .guarantee_type
                              //                                 .toString() ==
                              //                             "3"
                              //                 ? Get.find<ContractListController>()
                              //                     .guaranteeList[2]
                              //                     .toString()
                              //                 : Get.find<ContractListController>()
                              //                     .guaranteeList[1]
                              //                     .toString(),
                              Get.find<ContractListController>()
                                  .contractList[index]
                                  .ctt_code
                                  .toString(),
                              SvgPicture.string(
                                Get.find<ContractListController>()
                                            .contractList[index]
                                            .ctt_code
                                            .toString()
                                            .substring(6, 8) ==
                                        "DT"
                                    ? AppSvgImage.icon_aampay
                                :Get.find<ContractListController>()
                                    .contractList[index]
                                    .ctt_code
                                    .toString()
                                    .substring(6, 8) ==
                                    "DL"
                                    ? AppSvgImage.rplcpay_icon
                                    : Get.find<ContractListController>()
                                                .contractList[index]
                                                .guarantee_type
                                                .toString() ==
                                            "1"
                                        ? Get.find<ContractListController>()
                                            .guaranteeImgList![0]
                                        : Get.find<ContractListController>()
                                                    .contractList[index]
                                                    .guarantee_type
                                                    .toString() ==
                                                "2"
                                            ? Get.find<ContractListController>()
                                                .guaranteeImgList![1]
                                            : Get.find<ContractListController>()
                                                        .contractList[index]
                                                        .guarantee_type
                                                        .toString() ==
                                                    "3"
                                                ? Get.find<ContractListController>()
                                                    .guaranteeImgList![2]
                                                : Get.find<ContractListController>()
                                                    .guaranteeImgList![3],
                              ), () {
                            Get.find<BillPaymentController>().setInitailContract(
                                index,
                                Get.find<ContractListController>()
                                    .contractList[index]
                                    .ctt_code
                                    .toString());
                            Navigator.pop(context);
                          });
                        },
                        itemCount:
                            Get.find<ContractListController>().contractList.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  _listLoanMenu(context, String title, String sub_title, Widget icon,
      VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 376.w,
        height: 80.h,
        child: Row(
          children: [
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 46.w,
              child: icon,
            ),
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 270.w,
              // height: 44.h,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 270.w,
                    child: Text(
                      title,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Container(
                    width: 269.w,
                    child: Container(
                      height: 20.h,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 180.w,
                            child: Text(
                              sub_title,
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
