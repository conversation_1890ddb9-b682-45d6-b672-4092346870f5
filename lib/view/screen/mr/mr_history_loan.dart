import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/mr/referfriend.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:get/get.dart';

import 'mr_register.dart';

class MrHistoryLoan extends StatefulWidget {
  const MrHistoryLoan({super.key});

  @override
  State<MrHistoryLoan> createState() => _MrHistoryLoanState();
}

class _MrHistoryLoanState extends State<MrHistoryLoan> {
  @override
  Widget build(BuildContext context) {
    // String numCount = "";

    // List listHistory = [
    //   {
    //     "name": "ปุญญพัตน์",
    //     "mrNum": "T7HRKV",
    //     "status": mrWait.tr,
    //     'image': ""
    //   },
    //   {"name": "สุวัฒน์", "mrNum": "T7H56A", "status": mrSuccess.tr, 'image': ""},
    //   {
    //     "name": "ธนวันต์",
    //     "mrNum": "@AAMMR001",
    //     "status": "ปิดไม่ได้",
    //     'image': ""
    //   },
    //   {"name": "มัทนา", "mrNum": "@AAMMR003", "status": mrSuccess.tr, 'image': ""},
    //   {
    //     "name": "ศักดิ์กรินทร์",
    //     "mrNum": "@AAMMR11",
    //     "status": mrWait.tr,
    //     'image': ""
    //   },
    //   {
    //     "name": "ปุญญพัตน์",
    //     "mrNum": "T7HRKV",
    //     "status": mrWait.tr,
    //     'image': ""
    //   },
    //   {"name": "สุวัฒน์", "mrNum": "T7H56A", "status":mrSuccess.tr, 'image': ""},
    //   {
    //     "name": "ธนวันต์",
    //     "mrNum": "@AAMMR001",
    //     "status": "ปิดไม่ได้",
    //     'image': ""
    //   },
    //   {"name": "มัทนา", "mrNum": "@AAMMR003", "status": mrSuccess.tr, 'image': ""},
    //   {
    //     "name": "ศักดิ์กรินทร์",
    //     "mrNum": "@AAMMR11",
    //     "status": mrWait.tr,
    //     'image': ""
    //   },
    // ];
    return GetBuilder<MRController>(
        init: MRController(),
        builder: (mrController) => Scaffold(
                body: InkWell(
              onTap: () {
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: Stack(children: [
                mrController.mrReferral.length == 0
                    ? Container(
                        color: Colors.white,
                        // color: Colors.red,
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: 16.w, right: 16.w, top: 120.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                mrHave.tr,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.50),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontWeight,
                                ),
                              ),
                              Text(
                                "0",
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: 24.sp,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontWeight,
                                ),
                              ),
                              SizedBox(
                                height: 24.h,
                              ),
                              Divider(
                                height: 1,
                                thickness: 1,
                                color: Color(0xFF1A1818).withOpacity(0.08),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              // buildHeadRefer(),
                              buildListHistory(),
                              Spacer(),

                              ///Note
                              Container(
                                height: 98.h,
                                width: Get.width,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: configTheme()
                                          .colorScheme
                                          .primary
                                          .withOpacity(0.08),
                                      width: 1),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(16)),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.all(14.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      appConfigService
                                                  .countryConfigCollection ==
                                              "aam"
                                          ? Container(
                                              child: SvgPicture.string(
                                                  '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3430_45422)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#995DFE" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3430_45422"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'))
                                          : appConfigService
                                                      .countryConfigCollection ==
                                                  "rafco"
                                              ? Container(
                                                  child: SvgPicture.string(
                                                      '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_66556)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#22409A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#22409A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#22409A" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3898_66556"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'))
                                              : Container(
                                                  child: SvgPicture.string(
                                                      '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_31713)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#6A7165" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#6A7165" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#6A7165" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_31713"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                                      SizedBox(
                                        width: 8.w,
                                      ),
                                      Expanded(
                                        child: Text(
                                          mrPoint.tr,
                                          style: TextStyle(
                                            overflow: TextOverflow.visible,
                                            color: configTheme()
                                                .colorScheme
                                                .primary,
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontWeight,
                                            height: 1.2.h,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 14.h,
                              ),
                              InkWell(
                                onTap: () {
                                  if (mrController.mrData.value.mr_id == null) {
                                    debugPrint("mr_id is null");
                                    Get.to(() => MrRegister());
                                  } else {
                                    Get.to(() => ReferFriend());
                                  }
                                },
                                child: Container(
                                  height: 52.h,
                                  width: Get.width,
                                  decoration: BoxDecoration(
                                    color:
                                        configTheme().colorScheme.onSecondary,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(16)),
                                  ),
                                  child: Center(
                                    child: Text(
                                      mrReferFriend.tr,
                                      style: TextStyle(
                                        color: configTheme().primaryColorLight,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .titleMedium
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .titleMedium
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .titleMedium
                                            ?.fontWeight,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 48.h,
                              ),
                            ],
                          ),
                        ),
                      )
                    : Container(
                        margin: EdgeInsets.only(top: 332.h),
                        child: /// list history
                            Column(
                          children: [
                            Container(
                              // height: Get.height - 88.h,
                              height: 344.h,
                              // padding: EdgeInsets.only(left: 16.w, right: 16.w,),
                              child: ListView.builder(
                                // scrollDirection: Axis.vertical,
                                padding: EdgeInsets.zero,
                                // itemExtent: 16.h,
                                itemCount:
                                    mrController.filtered_mrReferral.length,
                                // shrinkWrap: true,
                                // padding: EdgeInsets.only(top: 16.h,bottom: 16.h),
                                // physics: NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  return Container(
                                    height: 56.h,
                                    width: Get.width,
                                    // color: Colors.teal,
                                    child: Row(
                                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          height: 46.h,
                                          width: 46.w,
                                          clipBehavior: Clip.antiAlias,
                                          decoration: ShapeDecoration(
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(20)),
                                            ),
                                            color: Colors.white,
                                          ),
                                          child: Image.network(
                                            'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png',
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 12.w,
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(mrController
                                                .filtered_mrReferral[index]
                                                .cust_name
                                                .toString()),
                                            Text(mrController
                                                .filtered_mrReferral[index]
                                                .refcode
                                                .toString()),
                                          ],
                                        ),
                                        Spacer(),
                                        Container(
                                          height: 26.h,
                                          decoration: BoxDecoration(
                                            color: mrController
                                                        .filtered_mrReferral[
                                                            index]
                                                        .status ==
                                                    "ปิดไม่ได้"
                                                ? Color(0xFFFFE6E7)
                                                : mrController
                                                            .filtered_mrReferral[
                                                                index]
                                                            .status ==
                                                        mrSuccess.tr
                                                    ? Color(0xFFE9FFE3)
                                                    : Color(0xFFFFF7CD),
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(6)),
                                          ),
                                          child: Padding(
                                            padding: EdgeInsets.only(
                                                left: 4.w, right: 4.w),
                                            child: Center(
                                                child: Text(
                                              mrController
                                                  .filtered_mrReferral[index]
                                                  .status
                                                  .toString(),
                                              style: TextStyle(
                                                color: mrController
                                                            .filtered_mrReferral[
                                                                index]
                                                            .status ==
                                                        "ปิดไม่ได้"
                                                    ? Color(0xFFFF3B30)
                                                    : mrController
                                                                .filtered_mrReferral[
                                                                    index]
                                                                .status ==
                                                            mrSuccess.tr
                                                        ? Color(0xFF34C76F)
                                                        : Color(0xFFFF9300),
                                              ),
                                            )),
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                            SizedBox(
                              height: 31.h,
                            ),
                            InkWell(
                              onTap: () {
                                if (mrController.mrData.value.mr_id == null) {
                                  debugPrint("mr_id is null");
                                  // Get.to(() => MrRegister());
                                } else {
                                  Get.to(() => ReferFriend());
                                }
                              },
                              child: Container(
                                height: 52.h,
                                margin:
                                    EdgeInsets.only(left: 16.w, right: 16.w,bottom: 20.h),
                                width: Get.width,
                                decoration: BoxDecoration(
                                  color: configTheme().colorScheme.primary,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(16)),
                                ),
                                child: Center(
                                  child: Text(
                                    mrReferFriend.tr,
                                    style: TextStyle(
                                      color: configTheme().primaryColorLight,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .titleMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .titleMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .titleMedium
                                          ?.fontWeight,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                            // SizedBox(
                            //   height: 48.h,
                            // ),
                          ],
                        ),
                      ),
                Container(
                    height: mrController.mrReferral.length == 0 ? 120.h : 333.h,
                    color: Colors.white,
                    // color: Colors.green,
                    child: Column(children: [
                      Container(
                        color: Colors.white,
                        height: 106.h,
                        child: Padding(
                          padding: EdgeInsets.only(top: 44.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                  // Get.back();
                                },
                                child: Container(
                                  height: 50.h,
                                  width: 50.w,
                                  child: Center(
                                      child: SvgPicture.string(
                                          '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3430_45429)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3430_45429"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                                ),
                              ),
                              Text(
                                mrLoan.tr,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontWeight,
                                ),
                              ),
                              Container(
                                height: 50.h,
                                width: 50.w,
                              )
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 14.h),
                      mrController.mrReferral.length == 0
                          ? Container()
                          : buildHeadRefer(),
                    ])),
              ]),
            )));
  }

  buildHeadRefer() {
    final MRController mrController = Get.find<MRController>();
    // final mrController = Get.put(MRController());
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Container(
        // color: Colors.teal,
        height: 209.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              mrHave.tr,
              style: TextStyle(
                color: configTheme()
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.50),
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:
                    configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:
                    configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
            ),
            Text(
              mrController.mrReferral.length.toString(),
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: 24.sp,
                fontFamily:
                    configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:
                    configTheme().primaryTextTheme.bodyMedium?.fontWeight,
              ),
            ),
            SizedBox(
              height: 20.h,
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: Color(0xFF1A1818).withOpacity(0.08),
            ),
            SizedBox(
              height: 10.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(mrWait.tr,
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.50),
                      fontSize:
                          configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    )),
                Text(mrController.pending!.value.toString(),
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                          configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    )),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(mrSuccess.tr,
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.50),
                      fontSize:
                          configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    )),
                Text(mrController.close!.value.toString(),
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                          configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    )),
              ],
            ),
            SizedBox(
              height: 10.h,
            ),
            Container(
              height: 48.h,
              width: Get.width,
              decoration: BoxDecoration(
                border: Border.all(
                    color: Color(0xFF1A1818).withOpacity(0.08), width: 1),
                // color: Color(0xFF995DFE),
                borderRadius: BorderRadius.all(Radius.circular(32)),
              ),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 24.w, right: 16.w),
                    height: 24.h,
                    width: 24.w,
                    child: Center(
                        child: SvgPicture.string(
                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 20L15.9497 15.9497M15.9497 15.9497C17.2165 14.683 18 12.933 18 11C18 7.13401 14.866 4 11 4C7.13401 4 4 7.13401 4 11C4 14.866 7.13401 18 11 18C12.933 18 14.683 17.2165 15.9497 15.9497Z" stroke="#1A1818" stroke-opacity="0.2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                  ),

                  /// ค้นหา
                  Expanded(
                    child: TextFormField(
                      style: TextStyle(
                        color: Color(0xFF1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall
                            ?.fontWeight,
                      ),
                      controller: mrController.searchController,
                      decoration: InputDecoration(
                        // prefixIcon: Icon(Icons.search_rounded),
                        // icon: Icon(Icons.search),
                        hintText: search.tr,
                        hintStyle: TextStyle(
                          color: Color(0xFF1A1818).withOpacity(0.5),
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontWeight,
                        ),
                        border: InputBorder.none,
                        // contentPadding: EdgeInsets.only(left: 16.w),
                      ),
                      onChanged: (value) {
                        if (value.length == 0) {
                          mrController.searchMrReferralLoan("");
                        } else if (value.length >= 3) {
                          mrController.searchMrReferralLoan(value);
                        }
                        // setState(() {
                        //   mrController.searchMR(value);
                        //   print("search");
                        //   print(value);
                        // });
                      },
                      onTapOutside: (value) {
                        if (mrController.searchController.text.length == 0) {
                          mrController.searchMrReferralLoan("");
                        } else if (mrController.searchController.text.length >=
                            3) {
                          mrController.searchMrReferralLoan(
                              mrController.searchController.text);
                        }
                      },
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildListHistory() {
    return Column(
      children: [
        SizedBox(
          height: 34.h,
        ),
        appConfigService.countryConfigCollection == "aam"
            ? Center(
                child: Container(
                  height: 40.h,
                  width: 37.w,
                  child: Center(
                      child: SvgPicture.string(
                          '<svg width="38" height="41" viewBox="0 0 38 41" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M37.4993 18.4996C37.4993 8.28246 29.2168 0 18.9996 0C8.78246 0 0.5 8.28246 0.5 18.4996C0.5 27.9735 7.62163 35.7831 16.8033 36.8696L17.8519 38.6855L19.1184 40.8789L20.3849 38.6855L21.4517 36.8375C30.5095 35.6381 37.5 27.8854 37.5 18.4996H37.4993Z" fill="#792AFF" fill-opacity="0.08"/><path d="M17.5069 19.8426L17.0726 13.3418C16.991 12.0753 16.9502 11.1659 16.9502 10.6143C16.9502 9.86372 17.1469 9.27784 17.5404 8.85664C17.9339 8.43618 18.4521 8.22559 19.094 8.22559C19.8716 8.22559 20.3919 8.49448 20.6549 9.03299C20.9173 9.57151 21.0484 10.3468 21.0484 11.3605C21.0484 11.9573 21.0164 12.5643 20.9537 13.1793L20.37 19.8696C20.3066 20.666 20.1711 21.2767 19.9627 21.7015C19.7543 22.1271 19.4103 22.3391 18.9315 22.3391C18.4528 22.3391 18.1037 22.1337 17.9135 21.7219C17.7234 21.3102 17.5878 20.6835 17.5062 19.8426H17.5069ZM19.0139 28.7722C18.4615 28.7722 17.9799 28.5936 17.5689 28.2358C17.1571 27.8788 16.9517 27.3789 16.9517 26.7362C16.9517 26.1751 17.1484 25.6977 17.5419 25.3042C17.9354 24.9107 18.4171 24.714 18.9869 24.714C19.5568 24.714 20.0428 24.9107 20.4458 25.3042C20.848 25.6977 21.0499 26.1751 21.0499 26.7362C21.0499 27.3694 20.8466 27.8671 20.4392 28.2293C20.0319 28.5914 19.5568 28.7722 19.0146 28.7722H19.0139Z" fill="#792AFF" fill-opacity="0.15"/></svg>')),
                ),
              )
            : appConfigService.countryConfigCollection == "rafco"
                ? Center(
                    child: Container(
                        height: 40.h,
                        width: 37.w,
                        child: Center(
                            child: SvgPicture.string(
                                '<svg width="38" height="41" viewBox="0 0 38 41" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M37.4993 18.4996C37.4993 8.28246 29.2168 0 18.9996 0C8.78246 0 0.5 8.28246 0.5 18.4996C0.5 27.9735 7.62163 35.7831 16.8033 36.8696L17.8519 38.6855L19.1184 40.8789L20.3849 38.6855L21.4517 36.8375C30.5095 35.6381 37.5 27.8854 37.5 18.4996H37.4993Z" fill="#EA1B23" fill-opacity="0.08"/><path d="M17.5059 19.8426L17.0716 13.3418C16.99 12.0753 16.9492 11.1659 16.9492 10.6143C16.9492 9.86372 17.146 9.27784 17.5395 8.85664C17.933 8.43618 18.4511 8.22559 19.0931 8.22559C19.8706 8.22559 20.3909 8.49448 20.654 9.03299C20.9163 9.57151 21.0475 10.3468 21.0475 11.3605C21.0475 11.9573 21.0154 12.5643 20.9527 13.1793L20.369 19.8696C20.3056 20.666 20.1701 21.2767 19.9617 21.7015C19.7533 22.1271 19.4093 22.3391 18.9306 22.3391C18.4518 22.3391 18.1028 22.1337 17.9126 21.7219C17.7224 21.3102 17.5868 20.6835 17.5052 19.8426H17.5059ZM19.0129 28.7722C18.4606 28.7722 17.9789 28.5936 17.5679 28.2358C17.1562 27.8788 16.9507 27.3789 16.9507 26.7362C16.9507 26.1751 17.1474 25.6977 17.5409 25.3042C17.9344 24.9107 18.4161 24.714 18.9859 24.714C19.5558 24.714 20.0418 24.9107 20.4448 25.3042C20.8471 25.6977 21.0489 26.1751 21.0489 26.7362C21.0489 27.3694 20.8456 27.8671 20.4383 28.2293C20.0309 28.5914 19.5558 28.7722 19.0136 28.7722H19.0129Z" fill="#EA1B23" fill-opacity="0.25"/></svg>'))),
                  )
                : Center(
                    child: Container(
                        height: 40.h,
                        width: 37.w,
                        child: Center(
                            child: SvgPicture.string(
                                '<svg width="38" height="41" viewBox="0 0 38 41" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M37.4993 18.4996C37.4993 8.28246 29.2168 0 18.9996 0C8.78246 0 0.5 8.28246 0.5 18.4996C0.5 27.9735 7.62163 35.7831 16.8033 36.8696L17.8519 38.6855L19.1184 40.8789L20.3849 38.6855L21.4517 36.8375C30.5095 35.6381 37.5 27.8854 37.5 18.4996H37.4993Z" fill="#FFC20E" fill-opacity="0.15"/><path d="M17.5059 19.8426L17.0716 13.3418C16.99 12.0753 16.9492 11.1659 16.9492 10.6143C16.9492 9.86372 17.146 9.27784 17.5395 8.85664C17.933 8.43618 18.4511 8.22559 19.0931 8.22559C19.8706 8.22559 20.3909 8.49448 20.654 9.03299C20.9163 9.57151 21.0475 10.3468 21.0475 11.3605C21.0475 11.9573 21.0154 12.5643 20.9527 13.1793L20.369 19.8696C20.3056 20.666 20.1701 21.2767 19.9617 21.7015C19.7533 22.1271 19.4093 22.3391 18.9306 22.3391C18.4518 22.3391 18.1028 22.1337 17.9126 21.7219C17.7224 21.3102 17.5868 20.6835 17.5052 19.8426H17.5059ZM19.0129 28.7722C18.4606 28.7722 17.9789 28.5936 17.5679 28.2358C17.1562 27.8788 16.9507 27.3789 16.9507 26.7362C16.9507 26.1751 17.1474 25.6977 17.5409 25.3042C17.9344 24.9107 18.4161 24.714 18.9859 24.714C19.5558 24.714 20.0418 24.9107 20.4448 25.3042C20.8471 25.6977 21.0489 26.1751 21.0489 26.7362C21.0489 27.3694 20.8456 27.8671 20.4383 28.2293C20.0309 28.5914 19.5558 28.7722 19.0136 28.7722H19.0129Z" fill="#FFC20E" fill-opacity="0.5"/></svg>'))),
                  ),
        SizedBox(
          height: 16.h,
        ),
        Center(
          child: Text(
            mrNoRefer.tr,
            style: TextStyle(
              color:
                  configTheme().textTheme.bodyMedium?.color?.withOpacity(0.35),
              fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
              fontFamily: configTheme().primaryTextTheme.bodySmall?.fontFamily,
              fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
