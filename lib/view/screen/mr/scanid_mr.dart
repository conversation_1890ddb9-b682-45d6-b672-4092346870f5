import 'package:AAMG/controller/register/scan.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppBackgound.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';

import '../../../controller/mr/mr.controller.dart';

class ScanIdMr extends StatefulWidget {
  const ScanIdMr({super.key});

  @override
  State<ScanIdMr> createState() => _ScanIdMrState();
}

class _ScanIdMrState extends State<ScanIdMr> {
  final ScanController cameraCtl = Get.put(ScanController());
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  String? qrText;
  bool _isDialogShown = false;
  final MRController mrController = Get.find<MRController>();
  @override
  void initState() {
    super.initState();
    print('RegisterScanScreen');
  }

  @override
  void dispose() {
    // TODO: implement dispose
    cameraCtl.cameraController!.dispose();
    controller?.dispose();
    super.dispose();
  }

  @override
  void reassemble() {
    super.reassemble();
    if (controller != null) {
      controller!.pauseCamera();
      controller!.resumeCamera();
    }
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      if (!_isDialogShown && scanData.code != null) {
        setState(() {
          qrText = scanData.code;
        });
        _isDialogShown = true; // Set the flag to true when dialog is shown
        // _showQrDialog(scanData.code!);
        mrController.setRefCodeData(scanData.code!).then((value) {
          mrController.addReferalCode();
        });
      }
    });
  }

  void _showQrDialog(String qrData) {
    // Delay to ensure the QR scan event is finished before showing the dialog
    Future.delayed(Duration(milliseconds: 200), () {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          print('qrData: $qrData');
          return AlertDialog(
            content: Text(qrData),
            actions: [


              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close the dialog immediately
                  Clipboard.setData(ClipboardData(text: qrData));
                  Future.delayed(Duration(milliseconds: 100), () {
                    Get.snackbar("Success", "Copy ID success");
                  });
                  setState(() {
                    _isDialogShown = false; // Reset the dialog flag
                  });
                },
                child: Text('Copy'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close the dialog immediately
                  setState(() {
                    _isDialogShown = false; // Reset the dialog flag
                  });
                },
                child: Text('Close'),
              ),
            ],
          );
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isNoOpen = false;
    return Scaffold(
      body: GetBuilder<ScanController>(
        init: ScanController(),
        builder: (controllerQR) {
          return Stack(
            children: [
              // Background
              AppBackground.backgroundPrimaryColor(context),

              // If the camera is ready, display QRView and overlays
              if (controllerQR.isCameraReady!.value)
                Stack(
                  children: [
                    // Purple overlay with a transparent hole
                    ClipPath(
                        clipper: HoleClipper(),
                        child: Container(color: Colors.transparent)),


                    // QR Scanner positioned to align with the transparent hole
                    controllerQR.isCameraReady!.value ?
                    Positioned(
                      top: 176.h,
                      left: 47.w,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(18),
                        child: SizedBox(
                          width: 280.w,
                          height: 280.h,
                          child: QRView(
                            key: qrKey,
                            onQRViewCreated: _onQRViewCreated,
                          ),
                        ),
                      ),
                    ) : const Center(child: CircularProgressIndicator()),

                    // Decorative corner frames
                    Positioned(
                      top: 176.h,
                      left: 47.w,
                      child: InkWell(
                        onTap: () async {
                          try {
                            print('open camera');
                            print(qrText);
                          } catch (e) {
                            print(e);
                          }
                        },
                        child:SizedBox(
                          width: 280.w,
                          height: 280.h,
                          child: Stack(
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: SvgPicture.string(AppSvgImage.scan_frame1),
                              ),
                              Align(
                                alignment: Alignment.topRight,
                                child: SvgPicture.string(AppSvgImage.scan_frame2),
                              ),
                              Align(
                                alignment: Alignment.bottomLeft,
                                child: SvgPicture.string(AppSvgImage.scan_frame3),
                              ),
                              Align(
                                alignment: Alignment.bottomRight,
                                child: SvgPicture.string(AppSvgImage.scan_frame4),
                              ),
                            ],
                          ),
                        ),
                      ),),

                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 50),
                        child: Text(
                          mrPlaceQr.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: configTheme().colorScheme.background,
                              fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                              fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight),
                        ),
                      ),
                    ),
                  ],
                )
              else
                const Center(child: CircularProgressIndicator()),

              // Header
              _buildHeader(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 106.h,
      child: Padding(
        padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 44.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () {
                Navigator.pop(context);
                // Get.back();
              },
              child: Container(
                height: 50.h,
                width: 50.w,
                child: Center(
                  child: SvgPicture.string(
                      '<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="46" y="46" width="46" height="46" rx="23" transform="rotate(-180 46 46)" fill="white" fill-opacity="0.7"/><g clip-path="url(#clip0_3408_19577)"><path d="M25.25 29.5L18.75 23L25.25 16.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3408_19577"><rect width="24" height="24" fill="white" transform="translate(11 11)"/></clipPath></defs></svg>'),
                ),
              ),
            ),
            Text(
              mrScanCode.tr,
              style: TextStyle(
                color: configTheme().colorScheme.background,
                fontSize: configTheme().primaryTextTheme.titleMedium?.fontSize,
                fontWeight: configTheme().primaryTextTheme.titleMedium?.fontWeight,
              ),
            ),
            SizedBox(
              height: 50.h,
              width: 50.w,
            )
          ],
        ),
      ),
    );
  }
}

class HoleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path()
    // Full overlay area
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
    // Hole area with rounded corners
      ..addRRect(
        RRect.fromRectAndCorners(
          Rect.fromLTWH(47.w, 176.h, 280.w, 280.h),
          topLeft: const Radius.circular(18),
          topRight: const Radius.circular(18),
          bottomLeft: const Radius.circular(18),
          bottomRight: const Radius.circular(18),
        ),
      );

    return path..fillType = PathFillType.evenOdd; // Cut out the transparent hole
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

