import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class ReferFriend extends StatefulWidget {
  const ReferFriend({super.key});

  @override
  State<ReferFriend> createState() => _ReferFriendState();
}

class _ReferFriendState extends State<ReferFriend> {
  List active = [
    {'head': mrLoanService.tr, 'subhead': '${mrPointReceive.tr} ${Get.find<WebViewPointController>().namePoint.value}'},
    {'head': mrRenew.tr, 'subhead': '${mrPointReceive.tr} ${Get.find<WebViewPointController>().namePoint.value}'},
  ];
  String selected = '';
  final loanCtl = Get.put(LoanController());
  final RegisterController registerController = Get.put(RegisterController());
  final RegisterAddressController regisAddressController = Get.put(RegisterAddressController());
  final MRController mrCtl = Get.put(MRController());
  int currentIndex = 0;
  changePage() {
    setState(() {
      currentIndex++;
    });
  }

  backPage() {
    setState(() {
      currentIndex--;
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    regisAddressController.checkConfigAddress();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MRController>(
        init: MRController(),
        builder: (mrController) => Scaffold(
              // floatingActionButton: FloatingActionButton(
              //   onPressed: () {
              //     Get.put(RegisterAddressController()).getProvinceData();
              //     // mrController.getMRData();
              //   },
              //   child: Icon(Icons.add),
              // ),
              backgroundColor: const Color(0xFFF9F9F9),
              body: Stack(
                // mainAxisAlignment: MainAxisAlignment.start,
                // crossAxisAlignment: CrossAxisAlignment.start,
                // alignment: Alignment.topCenter,
                children: [
                  Container(
                    height: 106.h,
                    width: Get.width,
                    // color: Colors.black,
                    // color: Color(0xffF9F9F9),
                    child: Padding(
                      padding: EdgeInsets.only(right: 12.w, top: 60.h, left: 12.w),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          // crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            appConfigService.countryConfigCollection.toString() == "aam" ||
                                    appConfigService.countryConfigCollection.toString() == "rplc"
                                ? InkWell(
                                    onTap: () {
                                      Get.back();
                                      mrController.firstNameController.value.clear();
                                      mrController.lastNameController.value.clear();
                                      mrController.phoneController.value.clear();
                                      mrController.selectedProvince!.value = '';
                                      mrController.selectedDistrict!.value = '';
                                      loanCtl.selectedGuarantee!.value = '';
                                      mrController.imageList.clear();
                                    },
                                    child: Container(
                                      height: 38.h,
                                      width: 38.w,
                                      child: Center(
                                          child: SvgPicture.string(
                                              '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3499_20491)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3499_20491"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                                    ),
                                  )
                                : InkWell(
                              onTap: () {
                                Get.back();

                                if (mrController.firstNameController.value.text.isNotEmpty) {
                                  mrController.firstNameController.value.clear();
                                }

                                if (mrController.lastNameController.value.text.isNotEmpty) {
                                  mrController.lastNameController.value.clear();
                                }

                                if (mrController.phoneController.value.text.isNotEmpty) {
                                  mrController.phoneController.value.clear();
                                }

                                if (mrController.selectedProvince!.value.isNotEmpty) {
                                  mrController.selectedProvince!.value = '';
                                }

                                if (mrController.selectedDistrict!.value.isNotEmpty) {
                                  mrController.selectedDistrict!.value = '';
                                }

                                if (loanCtl.selectedGuarantee!.value.isNotEmpty) {
                                  loanCtl.selectedGuarantee!.value = '';
                                }

                                if (mrController.imageList.isNotEmpty) {
                                  mrController.imageList.clear();
                                }
                              },

                              child: Container(
                                        height: 38.h,
                                        width: 38.w,
                                        child: Center(
                                            child: SvgPicture.string(
                                                '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_64341)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#EA1B23" stroke-opacity="0.75" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3898_64341"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'))),
                                  ),
                            Text(
                              mrReferFriend.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: configTheme().textTheme.titleMedium!.fontSize,
                                fontWeight: configTheme().textTheme.titleMedium!.fontWeight,
                                color: const Color(0xff1A1818),
                              ),
                            ),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: appConfigService.countryConfigCollection.toString() == "aam"
                                  ? Container(
                                      height: 48.h,
                                      width: 106.w,
                                      child: SvgPicture.string(
                                          '<svg width="106" height="48" viewBox="0 0 106 48" fill="none" xmlns="http://www.w3.org/2000/svg"> <g opacity="0.8" clip-path="url(#clip0_3499_20375)"> <path opacity="0.15" d="M58.316 67.8205C76.1637 67.8205 90.6321 53.3099 90.6321 35.4103C90.6321 17.5106 76.1637 3 58.316 3C40.4684 3 26 17.5106 26 35.4103C26 53.3099 40.4684 67.8205 58.316 67.8205Z" fill="#FF9300"/> <path opacity="0.5" d="M58.3158 56.5217C69.9415 56.5217 79.366 47.0698 79.366 35.4102C79.366 23.7506 69.9415 14.2986 58.3158 14.2986C46.6901 14.2986 37.2656 23.7506 37.2656 35.4102C37.2656 47.0698 46.6901 56.5217 58.3158 56.5217Z" fill="url(#paint0_linear_3499_20375)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M73 16.8974C66.8729 23.6784 58.0564 29.0157 47.431 31.1975C45.6162 28.1562 44.1371 25.0259 43 21.8609C52.2939 20.6059 59.9912 16.4166 65.2751 11C67.5698 13.177 70.1511 15.1616 73 16.8974Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M72.091 16.9951C66.1582 23.3457 57.7939 28.3476 47.7612 30.5005C46.1838 27.8427 44.8607 25.1152 43.8027 22.3576C52.7212 20.992 60.1474 16.9542 65.3593 11.7432C67.3877 13.6557 69.6368 15.4203 72.091 16.9951Z" fill="url(#paint1_linear_3499_20375)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M64.0395 15.7308C63.4026 16.2537 62.5677 16.47 61.7988 16.3763C58.5098 18.7708 54.6701 20.7483 50.3818 22.1078C50.1239 22.8074 49.4642 23.4085 48.4962 23.6597C47.5257 23.9134 46.5361 24.1357 45.5273 24.3257C46.1307 25.66 46.7976 26.9835 47.5257 28.2902C48.5933 28.057 49.6417 27.7913 50.6709 27.4908C51.6653 27.2035 52.7389 27.2275 53.6733 27.4956C58.3754 25.8139 62.5869 23.4674 66.2023 20.6653C66.2658 19.9741 66.6425 19.2697 67.301 18.7071C68.1755 17.9618 69.008 17.1877 69.8008 16.3871C68.6049 15.5144 67.463 14.5972 66.3798 13.6367C65.6385 14.3592 64.8564 15.0576 64.0383 15.7308H64.0395Z" fill="#F9F9F9"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M61.5438 20.2243C62.9028 21.6848 62.7781 23.7476 60.956 24.7778C59.1387 25.808 56.4782 25.2129 55.3195 23.5336C54.1583 21.8567 54.7641 19.9021 56.375 19.0823C57.9944 18.2661 60.1727 18.7565 61.5438 20.2243Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M53.1938 24.6832C53.6329 25.3443 53.273 26.1557 52.3398 26.4707C51.409 26.7856 50.2874 26.4526 49.8892 25.7566C49.4909 25.0606 49.9348 24.2949 50.8248 24.0148C51.7172 23.7359 52.7548 24.0232 53.1938 24.6832Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M65.9024 17.347C66.4746 17.8591 66.417 18.7282 65.7165 19.2824C65.0183 19.8378 63.964 19.8306 63.4266 19.2776C62.882 18.7246 63.0295 17.8712 63.6953 17.3591C64.3622 16.847 65.3254 16.8386 65.9012 17.3482L65.9024 17.347Z" fill="#792AFF" fill-opacity="0.5"/> <path d="M59.8309 21.3224C60.0291 21.3836 60.1891 21.5126 60.311 21.7093C60.5093 22.0293 60.5628 22.3424 60.4716 22.6488C60.3803 22.9551 60.1571 23.2189 59.802 23.4403L59.6763 23.5186L59.9799 24.0085L59.7285 24.1652L59.425 23.6753L58.48 24.2643L56.6274 21.2747C56.5882 21.2114 56.5509 21.17 56.5156 21.1503C56.4802 21.1309 56.4382 21.1154 56.3894 21.1043C56.359 21.1002 56.3344 21.0924 56.3157 21.081C56.2969 21.0695 56.2838 21.0502 56.276 21.0226C56.2502 20.951 56.2462 20.8715 56.2641 20.7841C56.2819 20.6967 56.321 20.6341 56.3814 20.5965C56.4449 20.5569 56.5206 20.5514 56.6079 20.58L57.2563 20.1759L56.9495 19.6809L57.2009 19.5242L57.5076 20.0192L57.6181 19.9503C57.9431 19.7478 58.253 19.6724 58.5477 19.7244C58.8422 19.7764 59.0753 19.9408 59.2467 20.2174C59.3727 20.4206 59.4244 20.609 59.4018 20.7825C59.3793 20.956 59.2995 21.1271 59.1623 21.2955L59.1717 21.3105C59.4127 21.2574 59.6324 21.2615 59.8306 21.3226L59.8309 21.3224ZM57.7283 21.9261L58.1706 21.6504L57.5076 20.5805L57.0653 20.8562L57.7283 21.9261ZM59.1742 23.2702L58.4244 22.0604L57.9821 22.3361L58.7319 23.5459L59.1742 23.2702ZM57.7589 20.4239L58.4218 21.4938L58.4268 21.4907C58.6244 21.3675 58.7519 21.2246 58.8091 21.0619C58.8661 20.8994 58.8379 20.7264 58.7243 20.543C58.6273 20.3864 58.5006 20.2908 58.3447 20.2564C58.1885 20.222 58.0184 20.2622 57.8341 20.377L57.7587 20.424L57.7589 20.4239ZM59.9101 22.5969C59.9549 22.4258 59.9143 22.2385 59.7883 22.0351C59.6706 21.8452 59.5185 21.7367 59.3321 21.7095C59.1458 21.6825 58.942 21.7378 58.7209 21.8756L58.6757 21.9038L59.4254 23.1136L59.5259 23.051C59.7371 22.9194 59.865 22.7681 59.9098 22.5968L59.9101 22.5969Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M83.0931 43.4133C83.7992 42.426 84.8229 41.5883 85.0743 40.4006C85.3732 38.9969 84.4878 37.5832 84.6156 36.155C84.7772 34.3564 86.4334 33.1117 87.2843 31.5217C88.1195 29.9651 87.6674 29.4778 87.6586 27.7104C87.6488 25.048 89.6502 21.5817 92.5938 22.3325C95.5813 23.0955 95.2408 25.8593 94.8303 27.9488C94.5365 29.4362 94.7887 29.6354 95.2208 31.0869C95.7449 32.8415 97.2289 34.4858 96.7345 36.2487C96.429 37.3392 95.4173 38.1155 95.092 39.2012C94.7242 40.431 95.3116 41.7123 95.576 42.9675C96.1356 45.6118 95.6429 47.859 93.8238 50.1579C93.6017 50.3939 93.3685 50.6101 93.123 50.811C90.4441 50.8121 87.4567 50.8421 84.2082 50.8971C84.1613 50.8546 84.1156 50.8106 84.0692 50.7652C82.2021 48.9205 81.5047 45.6301 83.0938 43.4132L83.0931 43.4133Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.5243 24.8146C91.5629 24.6924 91.6946 24.6255 91.818 24.6655C91.9401 24.7056 92.0068 24.8376 91.9668 24.9599C91.7677 25.5734 91.6075 26.2132 91.4753 26.8783C91.5386 26.8149 91.6048 26.7528 91.6732 26.6914C91.9751 26.4209 92.3241 26.1806 92.7138 25.9708C92.8269 25.9085 92.9672 25.9527 93.0293 26.0653C93.0894 26.1796 93.046 26.3194 92.9335 26.3817C92.5741 26.5747 92.2573 26.7946 91.9822 27.0397C91.7225 27.2733 91.4985 27.5296 91.3113 27.8115C91.0871 29.2476 90.9715 30.7867 90.8789 32.4072C91.1262 31.973 91.3953 31.5825 91.6839 31.2345C92.1847 30.6287 92.7491 30.1484 93.3772 29.796C93.4895 29.7315 93.631 29.772 93.6939 29.8847C93.7559 29.9959 93.7177 30.1384 93.6053 30.2006C93.0293 30.5251 92.5086 30.9697 92.0417 31.5326C91.5789 32.0916 91.1706 32.7695 90.818 33.5626L90.795 34.0019C90.7454 34.9677 90.6936 35.9589 90.6261 36.9712C90.6266 36.9806 90.6264 36.99 90.6233 37.0003C90.5524 38.0577 90.4604 39.1393 90.3317 40.2403C90.7943 39.4365 91.2997 38.7232 91.8487 38.096C92.6515 37.1769 93.5464 36.4471 94.5341 35.9074C94.6465 35.8452 94.7875 35.8879 94.8496 35.9998C94.9103 36.1133 94.8691 36.2545 94.7566 36.316C93.8182 36.8315 92.9642 37.5265 92.1982 38.4024C91.4336 39.2775 90.7542 40.3329 90.1583 41.5673C89.947 43.0295 89.6584 44.521 89.251 46.036L89.2491 46.0412C89.0494 46.7844 88.8211 47.5334 88.5589 48.2856C89.2315 47.6448 89.95 47.0835 90.7188 46.6021C91.7894 45.9299 92.9475 45.4155 94.196 45.0579C94.3192 45.0219 94.4479 45.0935 94.4831 45.2177C94.5199 45.3418 94.4477 45.4708 94.3238 45.5069C93.1159 45.853 91.9944 46.3503 90.9652 46.9957C89.9464 47.6348 89.0133 48.4222 88.1642 49.356C87.9688 49.8578 87.7583 50.3619 87.5304 50.866L87.0155 50.8716C87.7803 49.2114 88.3558 47.5639 88.7945 45.9415C88.8228 44.8001 88.6306 43.6715 88.2164 42.5551C87.7965 41.4238 87.1492 40.2991 86.2721 39.1877C86.1932 39.0868 86.2102 38.9389 86.3095 38.8606C86.4102 38.7801 86.5563 38.7972 86.6367 38.8981C87.5419 40.05 88.2135 41.2133 88.6503 42.3916C88.8985 43.061 89.0718 43.735 89.1685 44.4108C89.7364 41.8526 89.9957 39.366 90.1578 36.9982C89.835 36.014 89.4876 35.186 89.1149 34.5128C88.7453 33.8424 88.3541 33.3302 87.9416 32.98C87.8448 32.8959 87.833 32.7495 87.9168 32.651C88 32.554 88.1468 32.5407 88.2429 32.6248C88.7008 33.0147 89.1266 33.5686 89.5225 34.2869C89.7694 34.734 90.0052 35.2462 90.2322 35.8255C90.2681 35.2001 90.2994 34.5836 90.3312 33.9765L90.3563 33.506C90.3558 33.4966 90.3562 33.49 90.3571 33.4806C90.5234 30.2637 90.7089 27.3363 91.5265 24.8145L91.5243 24.8146Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.0818 44.2783C91.8668 43.8005 92.8273 43.5207 93.3525 42.768C93.974 41.8785 93.7771 40.6307 94.2875 39.6745C94.9318 38.4688 96.4526 38.0936 97.5182 37.2392C98.5593 36.4006 98.3863 35.9293 98.9034 34.6947C99.6841 32.836 102.104 31.0167 103.933 32.413C105.788 33.8325 104.733 35.6568 103.83 36.9896C103.185 37.9381 103.301 38.1523 103.175 39.2915C103.021 40.6697 103.568 42.2565 102.701 43.3367C102.167 44.0059 101.233 44.2459 100.685 44.9056C100.066 45.6523 100.097 46.7196 99.9103 47.6736C99.6588 48.9576 99.1551 50.001 98.3169 50.864C95.9885 50.8239 93.2897 50.8173 90.2759 50.8385C89.9929 50.4916 89.7584 50.1047 89.5876 49.6891C88.8329 47.8501 89.3207 45.3503 91.0826 44.2783L91.0818 44.2783Z" fill="url(#paint2_linear_3499_20375)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M102.36 33.7733C102.423 33.6991 102.534 33.692 102.606 33.7563C102.68 33.8191 102.688 33.9303 102.625 34.0052C102.305 34.3722 102.003 34.7716 101.715 35.1956C101.778 35.1706 101.843 35.147 101.909 35.1248C102.198 35.0266 102.512 34.9612 102.846 34.9302C102.942 34.9209 103.027 34.9933 103.037 35.0913C103.045 35.1872 102.974 35.2727 102.878 35.282C102.569 35.3109 102.284 35.3691 102.019 35.4587C101.768 35.5426 101.538 35.6552 101.324 35.7965C100.742 36.7296 100.207 37.7674 99.6645 38.8679C99.9651 38.641 100.267 38.448 100.571 38.2904C101.1 38.0176 101.635 37.851 102.177 37.7913C102.273 37.7813 102.36 37.8521 102.37 37.9472C102.382 38.0443 102.311 38.1313 102.216 38.1434C101.719 38.1965 101.224 38.3517 100.734 38.605C100.245 38.8583 99.7614 39.2077 99.2813 39.6553L99.1348 39.9547C98.8158 40.6128 98.4861 41.2874 98.1403 41.9729C98.1363 41.9796 98.1338 41.9856 98.1305 41.9922C97.7682 42.7083 97.384 43.4335 96.9697 44.1624C97.5284 43.7403 98.0924 43.3934 98.6587 43.1202C99.4887 42.7182 100.329 42.4758 101.176 42.3916C101.274 42.3822 101.359 42.4546 101.369 42.5511C101.377 42.6471 101.306 42.734 101.211 42.7432C100.406 42.8231 99.6057 43.0555 98.8108 43.4375C98.0203 43.82 97.2361 44.3543 96.4545 45.0384C95.876 45.9923 95.2347 46.9465 94.5013 47.8794L94.4986 47.8839L94.4972 47.884C94.1398 48.342 93.7597 48.7953 93.3533 49.2421C94.012 48.9938 94.6786 48.8175 95.3567 48.7088C96.2994 48.5595 97.2591 48.5456 98.2337 48.6671C98.3311 48.6787 98.3992 48.7665 98.3883 48.8633C98.3766 48.9595 98.2876 49.0286 98.1902 49.0169C97.2454 48.899 96.3189 48.9127 95.4107 49.0579C94.5126 49.2012 93.6289 49.472 92.7626 49.8707C92.4445 50.1942 92.1147 50.5139 91.7689 50.83L91.2371 50.8321C92.3781 49.829 93.3558 48.7676 94.2106 47.6778C94.5678 46.8925 94.7688 46.0478 94.8095 45.1477C94.853 44.2337 94.7315 43.2579 94.4502 42.223C94.4251 42.1287 94.4801 42.0317 94.5742 42.0066C94.6683 41.9808 94.7651 42.0359 94.7902 42.1302C95.0827 43.2 95.2052 44.2105 95.1608 45.163C95.1356 45.7023 95.0567 46.222 94.9248 46.7227C96.0779 45.1085 96.9941 43.4536 97.8068 41.8518C97.8716 41.0721 97.8744 40.3911 97.8143 39.8105C97.7544 39.2335 97.6349 38.7624 97.4506 38.3953C97.4079 38.3084 97.4436 38.2016 97.531 38.1587C97.6169 38.1152 97.7228 38.1503 97.7663 38.2371C97.9685 38.6438 98.1025 39.1577 98.1652 39.7758C98.2059 40.1612 98.2176 40.5878 98.2055 41.0578C98.415 40.6334 98.6197 40.2137 98.8204 39.8L98.9774 39.4783C98.9793 39.4732 98.9833 39.4672 98.9852 39.462C100.052 37.2706 101.046 35.2851 102.362 33.7725L102.36 33.7733Z" fill="white"/> <path d="M14.8188 26.8811C14.4203 27.8318 13.9771 28.8077 13.4899 29.7905C13.0356 30.7041 12.5614 31.5837 12.0797 32.4198L7.50977 31.9482C8.37001 31.7845 9.63307 30.3817 10.6044 28.4343C11.6155 26.4057 11.9581 24.4602 11.4901 23.7217L14.8188 26.8811Z" fill="url(#paint3_linear_3499_20375)"/> <path d="M39.0415 37.853C39.0415 37.853 37.7257 43.3779 34.7527 46.1822C33.7497 45.6722 32.7659 45.2006 31.8194 44.7712C31.3607 44.5615 30.9132 44.3588 30.4707 44.1693C33.4437 41.3644 34.991 35.4756 34.991 35.4756C36.2454 36.262 37.5898 37.054 39.0421 37.853H39.0415Z" fill="#792AFF" fill-opacity="0.75"/> <path d="M35.2326 35.5383C35.2604 35.4325 35.2162 35.321 35.1235 35.2629C28.22 30.9381 24.2342 26.8562 21.9768 23.8653C20.8479 22.3696 20.1506 21.1459 19.7365 20.2998C19.5294 19.8767 19.3931 19.548 19.3091 19.3267C19.2671 19.2161 19.2381 19.1323 19.2199 19.0771C19.2108 19.0494 19.2044 19.0289 19.2004 19.0158L19.1961 19.0016L19.1952 18.9986L19.1952 18.9985L19.1951 18.9983C19.1627 18.8838 19.0541 18.808 18.9354 18.8171C18.8167 18.8263 18.7209 18.9179 18.7064 19.0361L18.6985 19.1009C18.6985 19.1011 18.6985 19.1014 18.6984 19.1016C18.2881 22.3246 17.0289 26.347 14.9934 30.4607C14.9839 30.4749 14.9753 30.4909 14.9683 30.5086C14.9681 30.509 14.9679 30.5095 14.9677 30.51C14.9618 30.5228 14.9564 30.5347 14.951 30.546C13.014 34.4516 10.7147 37.7381 8.50835 40.0049C8.43121 40.0841 8.41531 40.2047 8.46927 40.3012C8.52323 40.3978 8.63422 40.4474 8.74215 40.4232C10.461 40.0382 16.0077 39.2831 25.3912 42.4691L25.3919 42.4693C25.9353 42.6523 26.489 42.8489 27.0585 43.0655L27.0604 43.0662C28.1213 43.46 29.2251 43.9016 30.3711 44.3979C30.4628 44.4376 30.5693 44.4189 30.642 44.3503C32.1685 42.9104 33.3149 40.6964 34.0766 38.8658C34.459 37.9466 34.7476 37.116 34.9407 36.5152C35.0372 36.2147 35.11 35.9713 35.1587 35.8027C35.1831 35.7183 35.2014 35.6527 35.2137 35.6079L35.2277 35.5566L35.2313 35.5432L35.2322 35.5397L35.2325 35.5387L35.2326 35.5384C35.2326 35.5383 35.2326 35.5383 34.9908 35.4748L35.2326 35.5383ZM15.0555 30.396L15.055 30.3963L15.0555 30.396Z" fill="url(#paint4_linear_3499_20375)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/> <mask id="mask0_3499_20375" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="34" y="37" width="10" height="12"> <path style="mix-blend-mode:multiply" d="M43.3557 45.7092C41.8792 48.392 38.239 48.035 38.239 48.035C37.0429 47.3658 35.8835 46.7506 34.7539 46.1815C37.7269 43.3765 39.0427 37.8523 39.0427 37.8523C40.15 38.4555 41.3106 39.0649 42.5377 39.67C43.4203 40.3311 44.8385 43.0277 43.3557 45.7092Z" fill="url(#paint5_linear_3499_20375)"/></mask> <g mask="url(#mask0_3499_20375)"> <path d="M43.3557 45.7092C41.8792 48.392 38.239 48.035 38.239 48.035C37.0429 47.3658 35.8835 46.7506 34.7539 46.1815C37.7269 43.3765 39.0427 37.8523 39.0427 37.8523C40.15 38.4555 41.3106 39.0649 42.5377 39.67C43.4203 40.3311 44.8385 43.0277 43.3557 45.7092Z" fill="url(#paint6_linear_3499_20375)"/></g> <path d="M15.2138 30.5575L15.1797 30.6101L15.1911 30.6177L15.2252 30.5652L15.2138 30.5575Z" fill="url(#paint7_linear_3499_20375)"/> <path d="M11.2614 23.4582L11.2188 23.5039L11.4681 23.7431L11.5108 23.6974L11.2614 23.4582Z" fill="url(#paint8_linear_3499_20375)"/> <path d="M31.8206 44.7706L24.3776 55.1951C24.0002 55.719 23.2926 55.8713 22.7396 55.5389L18.6178 53.0979C17.9009 52.6723 17.7861 51.6643 18.3993 51.0894L23.6576 46.1249L27.1489 42.832C28.2139 43.2274 29.3224 43.6707 30.4719 44.1687C30.9151 44.3582 31.3626 44.5609 31.8206 44.7706Z" fill="#792AFF"/> <path d="M27.1465 42.8318L23.6553 46.1247L22.9757 45.587C22.6771 45.354 22.6579 44.9095 22.931 44.6489L23.2183 44.3718L24.9587 42.7216L25.4707 42.2324C26.0169 42.4163 26.5743 42.614 27.1465 42.8318Z" fill="url(#paint9_linear_3499_20375)"/> <path d="M24.9599 42.7219L23.2195 44.3727L22.9297 44.1713L24.675 42.5273L24.9599 42.7219Z" fill="#792AFF"/> <path d="M17.2603 12.2682C14.1551 10.6766 8.17928 16.3318 3.92211 24.8983C-0.340634 33.4642 -1.27288 41.692 1.83791 43.2849C3.51868 44.1487 6.04666 42.8838 8.6876 40.1796C10.9158 37.8904 13.2297 34.5805 15.1761 30.6555C15.1841 30.6385 15.1922 30.6208 15.2003 30.6032C15.2009 30.5969 15.2071 30.5982 15.2077 30.5919C17.2584 26.4522 18.5314 22.3949 18.9466 19.1329L18.9547 19.0668C19.3848 15.6291 18.8504 13.0779 17.2603 12.2682ZM13.4897 29.7904C13.0354 30.7039 12.5612 31.5835 12.0796 32.4196L7.50958 31.9481C8.36982 31.7844 9.63288 30.3816 10.6042 28.4342C11.6153 26.4056 11.9579 24.4601 11.4899 23.7215L14.8186 26.8809C14.4201 27.8316 13.9769 28.8076 13.4897 29.7904Z" fill="url(#paint10_linear_3499_20375)"/> <path d="M14.8187 26.8819L11.4901 23.7225C11.9581 24.461 11.6155 26.4065 10.6044 28.4352C9.63307 30.3826 8.37 31.7853 7.50976 31.949L12.0797 32.4206C8.54441 38.5486 4.43249 42.4277 2.34704 41.3605C-0.0232879 40.1479 1.11874 33.0005 4.89675 25.3998C8.68158 17.7934 13.6693 12.6111 16.039 13.83C18.1015 14.8884 17.5019 20.446 14.8187 26.8819Z" fill="#1A1818" fill-opacity="0.35"/> <path d="M10.6033 28.4345C9.63198 30.3819 8.36892 31.7846 7.50867 31.9483C7.3355 31.9823 7.18654 31.9641 7.05496 31.8986C6.24561 31.4856 6.52988 29.2693 7.68494 26.9454C8.84062 24.6222 10.4295 23.0695 11.2389 23.4826C11.3419 23.5323 11.4282 23.616 11.4884 23.7218C11.9564 24.4604 11.6138 26.4059 10.6027 28.4345H10.6033Z" fill="url(#paint11_linear_3499_20375)"/> <path d="M9.98637 28.1202C10.8726 26.3381 11.1737 24.68 10.6589 24.4165C10.1442 24.1531 9.00845 25.3842 8.12222 27.1662C7.236 28.9482 6.93488 30.6064 7.44965 30.8698C7.96442 31.1332 9.10015 29.9022 9.98637 28.1202Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M92.2309 18.1755C87.7051 18.665 82.6545 17.7219 77.973 15.0012C78.3022 13.2641 78.7861 11.6056 79.4088 10.0478C83.2876 12.6711 87.5415 13.6698 91.3018 13.4084C91.4372 14.9829 91.7431 16.5811 92.2309 18.1755Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.8591 17.9094C87.5481 18.303 82.7769 17.3854 78.3258 14.851C78.6167 13.3358 79.0255 11.8793 79.543 10.4997C83.3184 12.9569 87.4213 13.9218 91.0868 13.7131C91.2104 15.1005 91.4651 16.5062 91.8591 17.9094Z" fill="url(#paint12_linear_3499_20375)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M89.2754 14.7603C88.8651 14.7434 88.4824 14.5464 88.2268 14.2557C86.2092 14.0542 84.1244 13.5142 82.0766 12.5947C81.7491 12.7696 81.3045 12.7742 80.8607 12.5459C80.4151 12.3177 79.9728 12.0715 79.534 11.8067C79.3177 12.5047 79.1286 13.2198 78.9679 13.9489C79.4429 14.2171 79.9215 14.4667 80.4044 14.697C80.87 14.9207 81.2622 15.2867 81.5218 15.6974C83.8303 16.6345 86.1758 17.1606 88.4498 17.3187C88.702 17.0823 89.0753 16.945 89.5068 16.9543C90.0791 16.9674 90.6453 16.9558 91.2056 16.9211C91.0484 16.1981 90.9261 15.4765 90.84 14.7582C90.3248 14.781 89.8024 14.7811 89.2749 14.7599L89.2754 14.7603Z" fill="#F9F9F9"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M86.8591 15.6046C86.8827 16.601 86.1541 17.3283 85.1344 17.1062C84.1165 16.8858 83.3216 15.7791 83.4451 14.7679C83.5668 13.7567 84.4388 13.2297 85.3103 13.46C86.1837 13.6943 86.8334 14.6016 86.8591 15.6046Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M82.2745 14.4896C82.2195 14.882 81.8171 15.0647 81.3651 14.8717C80.9141 14.6795 80.6062 14.1824 80.6879 13.7906C80.7696 13.3987 81.1882 13.261 81.6125 13.4526C82.0374 13.6455 82.3291 14.0976 82.2745 14.4896Z" fill="#792AFF" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M89.4379 15.9834C89.4818 16.3646 89.173 16.6693 88.7286 16.6429C88.2848 16.6177 87.8942 16.2643 87.8768 15.8795C87.8567 15.4923 88.1939 15.2233 88.6113 15.2538C89.0292 15.2848 89.391 15.602 89.437 15.9835L89.4379 15.9834Z" fill="#792AFF" fill-opacity="0.5"/> <path d="M85.8593 15.4443C85.913 15.5331 85.9299 15.6343 85.9103 15.7482C85.8784 15.9334 85.7948 16.0679 85.6595 16.1517C85.5242 16.2356 85.3538 16.2596 85.1483 16.224L85.0755 16.2114L85.0267 16.495L84.8812 16.4698L84.93 16.1862L84.3831 16.0914L84.6812 14.3611C84.6875 14.3245 84.6873 14.2966 84.6806 14.2776C84.6739 14.2585 84.6633 14.2388 84.6488 14.2184C84.6388 14.2068 84.6323 14.1957 84.629 14.1853C84.6258 14.1747 84.6273 14.1631 84.6335 14.1503C84.6476 14.115 84.6724 14.084 84.708 14.0574C84.7435 14.0308 84.7788 14.0205 84.8137 14.0265C84.8505 14.0329 84.8805 14.056 84.9036 14.0957L85.2789 14.1607L85.3282 13.8742L85.4737 13.8994L85.4244 14.1859L85.4883 14.197C85.6764 14.2296 85.8168 14.3046 85.9095 14.422C86.0021 14.5393 86.0346 14.6781 86.007 14.8382C85.9867 14.9558 85.9437 15.0432 85.8779 15.1004C85.8122 15.1576 85.7259 15.1948 85.6191 15.2119L85.6176 15.2206C85.725 15.281 85.8055 15.3556 85.8592 15.4443L85.8593 15.4443ZM84.8761 14.97L85.1321 15.0144L85.2387 14.3951L84.9827 14.3508L84.8761 14.97ZM84.9705 15.9518L85.0911 15.2516L84.8351 15.2072L84.7145 15.9075L84.9705 15.9518ZM85.3842 14.4203L85.2775 15.0396L85.2804 15.0401C85.3948 15.0599 85.4895 15.049 85.5646 15.0074C85.6396 14.9658 85.6863 14.8919 85.7046 14.7858C85.7202 14.6952 85.7046 14.6174 85.6579 14.5527C85.6111 14.488 85.5344 14.4464 85.4277 14.4279L85.3841 14.4203L85.3842 14.4203ZM85.4674 15.9456C85.5407 15.8968 85.5875 15.8135 85.6078 15.6958C85.6267 15.5859 85.6059 15.4948 85.5454 15.4227C85.4849 15.3507 85.3907 15.3035 85.2627 15.2813L85.2365 15.2768L85.1159 15.977L85.1741 15.9871C85.2963 16.0083 85.394 15.9945 85.4674 15.9455L85.4674 15.9456Z" fill="white"/> <path d="M28.371 12.7364C29.9121 11.1907 29.9121 8.68475 28.371 7.13911C26.83 5.59347 24.3314 5.59347 22.7903 7.13911C21.2493 8.68475 21.2493 11.1907 22.7903 12.7364C24.3314 14.282 26.83 14.282 28.371 12.7364Z" fill="#792AFF" fill-opacity="0.5"/> <path d="M44.5722 19.9514C47.9798 19.9514 50.7421 17.1808 50.7421 13.7632C50.7421 10.3455 47.9798 7.57495 44.5722 7.57495C41.1647 7.57495 38.4023 10.3455 38.4023 13.7632C38.4023 17.1808 41.1647 19.9514 44.5722 19.9514Z" fill="#792AFF" fill-opacity="0.5"/> <path d="M27.473 10.9564C27.4729 10.9564 27.4729 10.9564 27.4728 10.9563C27.425 10.8761 26.538 10.5909 26.2911 10.4227C26.286 10.4193 26.2856 10.412 26.2902 10.4081C26.6705 10.0838 26.9233 9.53921 26.9233 9.05836C26.9233 8.31509 26.3204 7.71069 25.5793 7.71069C24.8383 7.71069 24.2357 8.31537 24.2357 9.05836C24.2357 9.53956 24.4888 10.084 24.8689 10.4082C24.8735 10.4121 24.873 10.4192 24.8682 10.4227C24.6211 10.591 23.7329 10.8764 23.6858 10.9564C23.4601 11.34 23.7839 11.7687 24.2289 11.7687H27.1989C27.5085 11.7687 27.7339 11.4723 27.5996 11.1934C27.5609 11.113 27.5188 11.0341 27.4732 10.9565C27.4732 10.9564 27.4731 10.9564 27.473 10.9564Z" fill="white"/> <path d="M62.6568 7.0507C63.951 5.75268 63.951 3.64819 62.6568 2.35018C61.3627 1.05217 59.2644 1.05217 57.9702 2.35018C56.6761 3.64819 56.6761 5.75269 57.9702 7.0507C59.2644 8.34871 61.3627 8.34871 62.6568 7.0507Z" fill="#792AFF" fill-opacity="0.5"/> <path d="M61.9054 5.5562C61.8656 5.48888 61.1203 5.24933 60.913 5.10792C60.9088 5.10497 60.9084 5.09904 60.9123 5.0957C61.2318 4.82337 61.444 4.3662 61.444 3.96219C61.444 3.33799 60.9379 2.83032 60.3155 2.83032C59.6932 2.83032 59.187 3.33799 59.187 3.96219C59.187 4.3662 59.3993 4.82336 59.7187 5.0957C59.7226 5.09904 59.7222 5.10496 59.718 5.10792C59.5104 5.24933 58.7646 5.48888 58.7251 5.5562C58.5357 5.87829 58.8076 6.23837 59.1812 6.23837H61.4494C61.8232 6.23837 62.0947 5.87844 61.9054 5.5562Z" fill="white"/> <path d="M47.5088 15.3307C47.4358 15.2066 46.0594 14.7642 45.6765 14.5032C45.6687 14.498 45.6681 14.4867 45.6752 14.4807C46.2648 13.9777 46.6569 13.1337 46.6569 12.3877C46.6569 11.2354 45.7221 10.2979 44.573 10.2979C43.4239 10.2979 42.4894 11.2351 42.4894 12.3877C42.4894 13.1337 42.8815 13.9777 43.4712 14.4807C43.4783 14.4867 43.4776 14.4979 43.4699 14.5032C43.0867 14.7642 41.7097 15.2066 41.6367 15.3307C41.2871 15.9255 41.7889 16.5902 42.4788 16.5902H46.667C47.3569 16.5902 47.8587 15.9253 47.5088 15.3307Z" fill="white"/> <path d="M29.8594 10.9832L38.3049 13.6397" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M50.1949 11.1569L58.0452 6.75592" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <g clip-path="url(#clip1_3499_20375)"> <path d="M76.3066 43.1653C75.5785 42.0147 74.4353 41.7035 74.4353 41.7035L71.6956 40.6658H69.0806L66.3409 41.7035C66.3409 41.7035 65.1977 42.0147 64.4696 43.1653H76.3066Z" fill="#FFEBDF"/> <path d="M66.8135 48.6131H62.5807C62.5807 47.8749 62.6275 46.8212 62.6275 46.8212C62.8768 42.3982 66.0301 41.7035 66.0301 41.7035C68.4893 41.0956 67.1492 44.9817 67.1492 44.9817C67.213 45.9181 67.077 47.1787 66.8121 48.6116L66.8135 48.6131Z" fill="url(#paint13_linear_3499_20375)"/> <path d="M68.5925 38.0417H72.1836V41.6181H68.5925V38.0417Z" fill="#FFEBDF"/> <path d="M68.5925 39.7047V38.0417H72.1836V38.8653C71.1594 39.7366 69.9255 39.8466 68.5925 39.7047Z" fill="#EAB894"/> <path d="M64.7686 48.6132C64.6992 46.6853 64.7205 46.1556 64.7375 45.497C64.7743 44.0323 65.0817 42.7934 66.0266 41.705C66.028 41.705 66.0308 41.705 66.0308 41.705L68.5906 40.8525C68.5906 40.8525 68.0523 41.815 70.4052 41.815C70.4052 41.815 71.955 41.7528 72.1816 40.8525L74.7811 41.705C74.7811 41.705 77.0037 42.2637 77.3848 44.6533L76.0787 47.1079L76.1254 48.6146H64.77L64.7686 48.6132Z" fill="url(#paint14_linear_3499_20375)"/> <path d="M66.1151 32.7142C66.1151 31.8646 66.1505 31.0165 66.1307 30.1669C66.1179 29.6241 66.0697 29.7645 66.6845 29.7052C67.5373 29.6227 67.8221 31.2408 67.8816 31.9413C67.9637 32.9067 67.7597 33.9459 67.6025 34.8867C67.5458 35.2268 67.5614 35.907 67.2129 36.0677C66.9154 36.2037 66.6038 35.868 66.448 35.619C66.0882 35.0473 66.1519 34.1717 66.1278 33.5233C66.1179 33.2541 66.1151 32.9849 66.1151 32.7157V32.7142Z" fill="#1A1818"/> <path d="M66.4808 37.544C66.8435 38.9942 67.4823 39.3951 67.4823 39.3951L68.3394 39.5659C68.8324 39.5022 69.6837 39.2967 69.6837 39.2967C71.6415 38.6165 72.2095 38.4326 72.5127 37.6728C72.5254 37.6409 74.0567 32.212 74.0567 32.212C74.3075 29.4172 71.4445 28.8368 71.0125 28.763C70.5847 28.6617 67.8592 27.8888 67.0545 30.5707C67.0545 30.5707 65.8646 33.6781 66.4808 37.544Z" fill="#FFEBDF"/> <path d="M74.3532 29.647C75.3051 30.1159 75.3377 30.9828 75.2924 31.9699C75.2584 32.7356 75.026 33.3869 74.7583 34.1294C74.4542 34.9727 74.1671 35.637 73.897 36.1223C73.897 36.1223 73.2666 37.1239 72.4549 37.8099C72.6901 36.8576 72.7567 36.147 72.9706 35.4146C73.2284 34.5361 73.1533 33.3608 72.7708 32.4591C72.7708 32.4591 72.5541 31.8585 71.6645 31.6877L74.3546 29.6455" fill="#1A1818"/> <path d="M72.788 35.2267C72.788 35.2267 73.2725 34.1499 74.0332 34.7881C74.0332 34.7881 74.3802 35.0646 73.9524 35.7854C73.9524 35.7854 73.3334 36.8216 72.5911 36.2471L72.788 35.2267Z" fill="#FFEBDF"/> <path d="M72.8659 32.7141C72.8659 32.7141 72.863 34.6434 72.516 35.6667L72.8843 35.7419C72.8843 35.7419 73.0783 34.4191 73.7441 34.4032L72.8659 32.7156V32.7141Z" fill="#1A1818"/> <path d="M65.6416 28.5833C62.5818 31.7674 71.842 31.7356 71.842 31.7356C71.842 31.7356 72.8138 32.1944 74.3508 29.6471C74.3508 29.6471 74.7658 27.6555 70.3872 27.0274C70.3872 27.0274 67.4931 26.6641 65.6416 28.5833Z" fill="#1A1818"/> <path d="M73.7544 48.6132H77.7109C77.7109 48.6132 77.6968 47.6536 77.6954 47.5856C77.6656 46.6419 77.6075 45.691 77.4404 44.7618C77.2024 43.4332 76.5706 42.2536 75.1795 41.9164C74.5434 41.7615 73.9768 42.1552 73.6326 42.6907C73.6326 42.6907 73.0036 43.4838 73.7544 48.6146V48.6132Z" fill="url(#paint15_linear_3499_20375)"/></g> <g clip-path="url(#clip2_3499_20375)"> <path d="M46.6348 31.203C46.6348 31.203 46.9861 30.8888 47.6547 31.4577L47.9664 30.9857C47.9664 30.9857 47.2156 30.4057 46.6348 31.203Z" fill="#1A1818"/> <path d="M47.8608 29.8228C47.8608 29.8228 47.4458 30.3266 48.0436 30.9011L47.6398 31.2997C47.6398 31.2997 46.7148 30.1923 47.8608 29.8241V29.8228Z" fill="#1A1818"/> <path d="M46.1562 43.4035C46.8844 42.3031 48.0276 42.0054 48.0276 42.0054L50.7672 41.0129H53.3823L56.122 42.0054C56.122 42.0054 57.2651 42.3031 57.9933 43.4035H46.1562Z" fill="#FFEBDF"/> <path d="M55.6494 48.6125H59.8822C59.8822 47.9065 59.8354 46.8988 59.8354 46.8988C59.5861 42.6685 56.4328 42.0041 56.4328 42.0041C53.9736 41.4227 55.3137 45.1394 55.3137 45.1394C55.2499 46.035 55.3859 47.2407 55.6508 48.6111L55.6494 48.6125Z" fill="url(#paint16_linear_3499_20375)"/> <path d="M53.8704 38.5022H50.2793V41.9227H53.8704V38.5022Z" fill="#FFEBDF"/> <path d="M53.8704 40.0927V38.5022H50.2793V39.2898C51.3035 40.1231 52.5373 40.2283 53.8704 40.0927Z" fill="#EAB894"/> <path d="M57.6943 48.6125C57.7637 46.7687 57.7424 46.262 57.7254 45.6322C57.6886 44.2314 57.3812 43.0465 56.4363 42.0055C56.4349 42.0055 56.4321 42.0055 56.4321 42.0055L53.8723 41.1902C53.8723 41.1902 54.4106 42.1107 52.0577 42.1107C52.0577 42.1107 50.5079 42.0512 50.2813 41.1902L47.6818 42.0055C47.6818 42.0055 45.4592 42.5398 45.0781 44.8252L46.3842 47.1729L46.3375 48.6139H57.6929L57.6943 48.6125Z" fill="url(#paint17_linear_3499_20375)"/><path d="M56.6633 34.2262C56.7653 33.4399 56.5939 32.6855 56.6024 31.9103C56.6081 31.3899 56.5047 30.605 55.8417 30.5385C54.9238 30.4472 54.6404 31.9948 54.5866 32.6661C54.5115 33.5908 54.7467 34.5902 54.9294 35.4955C54.9946 35.8222 54.9889 36.4756 55.3643 36.6334C55.6859 36.7676 56.016 36.4506 56.1803 36.2139C56.5769 35.6436 56.5783 34.8851 56.6619 34.2262H56.6633Z" fill="#1A1818"/><path d="M55.9821 38.0259C55.6194 39.4129 54.9805 39.7963 54.9805 39.7963L54.1235 39.9596C53.6305 39.8987 52.7792 39.7022 52.7792 39.7022C50.8214 39.0516 50.2534 38.8758 49.9502 38.1491C49.9375 38.1186 48.4062 32.9263 48.4062 32.9263C48.1554 30.2533 51.0183 29.6983 51.4504 29.6277C51.8782 29.5308 54.6037 28.7916 55.4084 31.3566C55.4084 31.3566 56.5983 34.3286 55.9821 38.0259Z" fill="#FFEBDF"/><path d="M56.8807 28.7707C57.0351 29.5998 56.8906 30.7487 56.2333 31.2789C55.5193 31.8534 54.9059 31.6969 54.0461 31.8838C53.1522 32.0776 52.2371 32.1551 51.339 32.324C50.71 32.443 49.9833 32.5109 49.6943 33.1628C49.3118 34.0252 49.2368 35.1492 49.4946 35.9895C49.7085 36.6885 49.7751 37.3696 50.0102 38.2804C49.1985 37.6243 48.5681 36.6664 48.5681 36.6664C48.298 36.2022 48.0109 35.5668 47.7068 34.7603C47.4391 34.0501 47.2082 33.4272 47.1728 32.695C47.1289 31.7509 47.5963 31.3398 48.112 30.4733C48.5965 29.6593 49.2764 29.0946 50.1632 28.8371C50.1632 28.8371 54.6566 29.0835 54.5731 28.0813C54.5731 28.0813 55.6525 28.3512 55.4471 29.0517C55.4471 29.0517 56.5761 28.8496 56.3367 28.0813C56.3367 28.0813 56.7928 28.2917 56.8835 28.772L56.8807 28.7707Z" fill="#1A1818"/><path d="M49.6749 35.8096C49.6749 35.8096 49.1904 34.7797 48.4297 35.3902C48.4297 35.3902 48.0827 35.6546 48.5105 36.3439C48.5105 36.3439 49.1295 37.335 49.8718 36.7855L49.6749 35.8096Z" fill="#FFEBDF"/><path d="M49.597 33.4067C49.597 33.4067 49.5999 35.2519 49.9469 36.2306L49.5786 36.3026C49.5786 36.3026 49.3845 35.0374 48.7188 35.0221L49.597 33.4081V33.4067Z" fill="#1A1818"/><path d="M48.7085 48.6125H44.752C44.752 48.6125 44.7661 47.6947 44.7675 47.6297C44.7973 46.7271 44.8554 45.8177 45.0225 44.929C45.2605 43.6583 45.8923 42.5301 47.2834 42.2076C47.9194 42.0595 48.4861 42.436 48.8303 42.9482C48.8303 42.9482 49.4593 43.7067 48.7085 48.6139V48.6125Z" fill="url(#paint18_linear_3499_20375)"/></g><g clip-path="url(#clip3_3499_20375)"><path d="M83.9875 28.3726C83.9875 28.3726 80.5937 29.3625 80.9139 34.5761V45.8138H84.7748L83.9875 28.3726Z" fill="#1A1818"/><path d="M83.6016 31.7417V47.8853H90.9842V34.2864C90.9842 34.2864 91.4689 29.07 87.9412 28.4289C87.9412 28.4289 83.6016 27.6663 83.6016 31.7417Z" fill="#1A1818"/><path d="M88.5488 42.0786H83.416V44.5442H88.5488V42.0786Z" fill="#FFEBDF"/><path d="M94.2102 48.6125H90.7394L90.1194 46.1808C90.1194 46.1808 89.6144 42.4895 92.2237 42.4373C92.2237 42.4373 93.5772 43.358 93.9468 45.5256C93.9468 45.5256 94.2393 47.2569 94.2117 48.6125H94.2102Z" fill="#FFEBDF"/><path d="M82.1525 48.6126L82.6211 46.7782C82.6211 46.7782 83.4506 42.6816 80.8413 42.6294C80.8413 42.6294 78.9523 44.1404 79.0891 48.6126H82.1525Z" fill="#FFEBDF"/><path d="M87.9421 37.0994H84.7754V41.5984H87.9421V37.0994Z" fill="#FFEBDF"/><path d="M87.9421 38.9364V37.1006H84.7754V39.7455C85.8203 39.7144 87.0602 39.5309 87.9421 38.9364Z" fill="#EAB894"/><path d="M80.5762 43.0346C81.2864 41.967 82.404 41.679 82.404 41.679L85.0803 40.7173H87.6343L90.9073 41.679C90.9073 41.679 92.0249 41.9684 92.7351 43.0346H80.5762Z" fill="#FFEBDF"/><path d="M81.2318 46.6905V48.6124H91.512L91.3912 46.3798C91.2631 42.0785 91.7594 42.0771 91.7594 42.0771C91.2966 41.7777 90.9095 41.6774 90.9095 41.6774L88.3322 40.9657C88.3322 40.9657 87.7733 43.2971 85.6472 43.2971C85.6472 43.2971 83.9998 42.4357 84.4465 40.9431L82.4048 41.6774C82.4048 41.6774 81.5956 41.9175 80.8418 42.6278C80.8418 42.6278 81.2056 42.8439 81.2318 46.6891V46.6905Z" fill="url(#paint19_linear_3499_20375)"/><path d="M88.2873 37.4581C88.1796 37.701 88.0283 37.9255 87.8173 38.1232C87.2919 38.6146 86.6763 39.0128 86.0011 39.2811C85.173 39.6116 83.9229 39.6525 83.2637 38.9917C82.7296 38.455 82.4371 37.6431 82.2057 36.9455C81.756 35.5898 81.7618 34.1099 81.8753 32.7034C81.9015 32.3829 81.935 32.0609 81.9903 31.7432C82.5069 28.6746 85.2137 28.6802 85.2137 28.6802C87.257 28.5941 89.1226 29.5303 89.437 31.6203C89.5578 32.421 89.3992 33.2796 89.3569 34.0802C89.3264 34.6677 89.2522 35.2551 89.0586 35.8158C88.8607 36.3877 88.5347 36.9045 88.2888 37.4567L88.2873 37.4581Z" fill="#FFEBDF"/><path d="M88.5522 35.1183C88.5522 35.1183 88.8229 34.0267 89.6874 34.4772C89.6874 34.4772 90.0774 34.6692 89.7965 35.419C89.7965 35.419 89.3861 36.4979 88.5508 36.1039V35.1183H88.5522Z" fill="#FFEBDF"/><path d="M81.6717 32.0949C81.5262 27.8811 85.6461 28.0816 85.6461 28.0816C89.6962 28.2426 89.8985 30.0233 89.8985 30.0233C90.3889 32.2827 89.6875 34.4772 89.6875 34.4772C88.8259 34.295 88.6804 35.412 88.6804 35.412C88.7706 33.6214 88.6193 33.5127 88.6193 33.5127C87.3052 33.5 86.3578 33.363 86.3578 33.363L86.2996 32.1147L85.9823 33.298C85.9823 33.298 84.1195 33.2571 82.1855 32.8122C82.1855 32.8122 81.6703 32.8476 81.6703 34.3684V32.0949H81.6717Z" fill="#1A1818"/></g><g clip-path="url(#clip4_3499_20375)"><path d="M34.3206 40.4408C34.99 40.2011 35.4119 39.6484 36.2079 39.7173C37.6223 39.8408 38.6249 41.4084 39.9869 40.3446C41.6342 39.0556 41.202 31.3052 40.4875 29.5496C39.8822 28.0609 38.7646 29.5166 37.1727 29.0156C35.3217 28.4342 33.7254 29.8754 32.7679 31.3712C31.7726 32.9288 31.259 34.9127 31.4059 36.7415C31.5413 38.4297 31.8512 41.3251 34.322 40.4394L34.3206 40.4408Z" fill="#1A1818"/><path d="M28.0011 48.6332H31.4716L32.0915 46.1613C32.0915 46.1613 32.5965 42.4088 29.9874 42.3557C29.9874 42.3557 28.6341 43.2917 28.2645 45.4952C28.2645 45.4952 27.972 47.2551 27.9997 48.6332H28.0011Z" fill="url(#paint20_linear_3499_20375)"/><path d="M40.0573 48.6332L39.5888 46.7684C39.5888 46.7684 39.0839 43.016 41.6929 42.9629C41.6929 42.9629 43.2572 44.0869 43.1204 48.6332H40.0573Z" fill="url(#paint21_linear_3499_20375)"/><path d="M34.2691 36.9294H37.4355V41.503H34.2691V36.9294Z" fill="#FFEBDF"/><path d="M34.2691 38.7971V36.9309H37.4355V39.6196C36.3908 39.588 35.151 39.4014 34.2691 38.7971Z" fill="#EAB894"/><path d="M41.6328 42.9629C40.9227 41.8776 39.8051 41.5848 39.8051 41.5848L37.1291 40.6072H34.5753L31.3027 41.5848C31.3027 41.5848 30.1852 41.879 29.475 42.9629H41.6328Z" fill="#FFEBDF"/><path d="M40.9778 48.6333H30.4323L30.4993 46.6795L28.9481 44.3655C29.293 42.1117 31.3011 41.5849 31.3011 41.5849L33.8781 40.8614C33.8781 40.8614 33.7239 41.6897 35.8513 41.6897C35.8513 41.6897 37.5597 41.6897 37.7634 40.8398L39.805 41.5863C39.805 41.5863 41.3008 42.0371 42.1172 43.5286L40.9764 46.681V48.6347L40.9778 48.6333Z" fill="url(#paint22_linear_3499_20375)"/><path d="M33.9243 37.2939C34.0319 37.5408 34.1833 37.7691 34.3943 37.97C34.9196 38.4696 35.5351 38.8744 36.2103 39.1472C37.0383 39.4831 38.2882 39.5247 38.9474 38.8529C39.4815 38.3074 39.7739 37.482 40.0053 36.7728C40.455 35.3947 40.4491 33.8903 40.3356 32.4605C40.3094 32.1347 40.276 31.8074 40.2207 31.4844C39.7056 28.365 36.999 28.3708 36.999 28.3708C34.9559 28.2832 33.0905 29.2349 32.7761 31.3595C32.6554 32.1734 32.814 33.0462 32.8562 33.8602C32.8867 34.4573 32.9609 35.0545 33.1545 35.6244C33.3524 36.2058 33.6783 36.7312 33.9243 37.2925V37.2939Z" fill="#FFEBDF"/><path d="M40.5517 34.2994C41.3156 31.9667 40.6987 29.2234 39.7863 28.365C37.2005 25.9318 32.4247 26.8577 30.9666 30.6618C30.5155 31.836 30.728 36.2187 30.9317 37.5652C31.2053 39.3696 31.8775 40.6056 33.2658 40.6056C33.2294 39.1184 33.3443 37.469 33.2105 35.6502C33.078 33.8457 33.3211 33.8012 34.8431 32.9615C35.5998 32.5437 36.0407 32.1073 36.5107 31.4298C37.0928 30.59 36.6548 30.2972 37.8073 30.3345C37.8073 30.3345 40.4848 30.244 40.5502 34.2979L40.5517 34.2994Z" fill="#1A1818"/><path d="M33.6587 34.9153C33.6587 34.9153 33.388 33.8057 32.5237 34.2636C32.5237 34.2636 32.1337 34.4589 32.4146 35.2211C32.4146 35.2211 32.8249 36.3178 33.6602 35.9173V34.9153H33.6587Z" fill="#FFEBDF"/></g><path d="M24.4895 36.093C24.246 36.0075 23.8443 35.8665 23.2477 36.0344C22.9163 35.8298 22.5961 35.7501 22.2842 35.75C21.9478 35.7499 21.6349 35.8428 21.3524 35.9411C21.2809 35.966 21.2115 35.991 21.1438 36.0153C20.6499 36.1927 20.2497 36.3365 19.7947 36.189C19.7 36.1583 19.596 36.1867 19.5301 36.2614C19.4642 36.3361 19.449 36.4429 19.4913 36.533C19.7585 37.1023 20.1293 37.5022 20.5206 37.8389C20.7084 38.0005 20.9032 38.1494 21.0914 38.2932L21.11 38.3074C21.3059 38.4571 21.4946 38.602 21.6742 38.7587C21.6956 38.7774 21.72 38.7923 21.7464 38.8027C22.6849 39.1753 23.5948 39.1419 24.4709 38.8036C24.4851 38.7981 24.4987 38.7914 24.5116 38.7834C24.8832 38.5553 25.2297 38.2296 25.5468 37.9316C25.6223 37.8606 25.6961 37.7912 25.7682 37.7251C26.1639 37.3621 26.5048 37.0992 26.8485 37.032C26.9405 37.0141 27.0147 36.9462 27.0408 36.8562C27.0668 36.7662 27.0404 36.6692 26.9722 36.6049C26.736 36.3818 26.5127 36.2399 26.2918 36.1582C26.0695 36.0759 25.8638 36.0598 25.672 36.0693C25.5396 36.0759 25.4025 36.0961 25.2784 36.1144C25.2329 36.1211 25.1891 36.1275 25.1479 36.133C24.9847 36.1545 24.8309 36.1658 24.6672 36.1451L24.6671 36.1451C24.6215 36.1393 24.5627 36.1186 24.4895 36.093ZM24.698 38.8521C24.6335 38.8116 24.554 38.8027 24.4821 38.828C23.5827 39.1447 22.6787 39.1913 21.745 38.8306C21.6714 38.8022 21.5888 38.8101 21.522 38.8521C19.4116 40.1769 18.0878 41.7814 17.5009 43.4384C16.9348 45.0366 17.0605 46.6677 17.8067 48.1069L16.8227 49.0963C16.7515 49.1679 16.7304 49.2754 16.7692 49.3686C16.8079 49.4619 16.899 49.5226 17 49.5226C18.0897 49.5226 19.0376 49.7004 20.0088 49.8832L20.0167 49.8847C20.9819 50.0664 21.9706 50.2525 23.1101 50.2525C24.2496 50.2525 25.2383 50.0664 26.2034 49.8847L26.2112 49.8832C27.1823 49.7004 28.1301 49.5226 29.2198 49.5226C29.3208 49.5226 29.4119 49.4619 29.4507 49.3686C29.4894 49.2754 29.4683 49.1679 29.3971 49.0963L28.4131 48.1069C29.1593 46.6677 29.285 45.0367 28.719 43.4385C28.1321 41.7815 26.8083 40.1769 24.698 38.8521Z" fill="url(#paint23_linear_3499_20375)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M19.7891 36.4819C20.1084 36.8995 21.3726 36.8909 21.7807 36.8864C21.9876 36.8842 22.2112 36.9024 22.4017 36.989C22.5225 37.0443 22.6 37.1157 22.7058 37.188C22.954 37.3572 23.266 37.2833 23.5365 37.212C23.9156 37.1121 24.2826 37.0225 24.6698 36.9557C25.3433 36.8392 26.0271 36.7851 26.7087 36.7742C26.7167 36.772 26.7248 36.7697 26.7323 36.7679C26.4011 36.4672 26.0809 36.3174 25.6198 36.3555C25.2759 36.3842 24.9854 36.4702 24.631 36.4252C24.4454 36.4017 24.2621 36.3083 24.0762 36.2665C23.7817 36.1991 23.5029 36.2433 23.2193 36.3386L23.2041 36.3438L23.1907 36.3345C22.8258 36.082 22.4547 35.9861 22.0157 36.0543C21.2474 36.1738 20.5957 36.6963 19.7893 36.4815L19.7891 36.4819Z" fill="url(#paint24_linear_3499_20375)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M21.8391 38.5703C21.7216 38.7675 21.6572 38.9336 21.6465 39.0689C21.6495 39.0675 21.652 39.0655 21.6552 39.0638C22.0491 39.2161 22.4381 39.2998 22.8227 39.3251C23.4129 39.364 23.9929 39.2649 24.5654 39.0638C24.5423 38.907 24.4812 38.7423 24.3813 38.5705C23.8711 38.7673 23.3522 38.8539 22.8227 38.8079C22.4992 38.78 22.1717 38.7023 21.8391 38.5705V38.5703Z" fill="#F9F9F9"/><path d="M24.6736 44.9743C24.834 45.1606 24.9141 45.3911 24.9141 45.6659C24.9141 46.113 24.7735 46.4622 24.4923 46.7137C24.2111 46.9651 23.8243 47.0908 23.3317 47.0908H23.1575V47.7752H22.8089V47.0908H21.4984V42.9138C21.4984 42.8254 21.4868 42.7602 21.4636 42.7182C21.4403 42.6764 21.4079 42.6344 21.3659 42.5926C21.3381 42.5694 21.3183 42.546 21.3066 42.5228C21.2949 42.4994 21.2938 42.4717 21.3031 42.439C21.3216 42.3506 21.3669 42.268 21.4391 42.1911C21.5111 42.1142 21.589 42.0757 21.6726 42.0757C21.7608 42.0757 21.8399 42.1177 21.9095 42.2014H22.8087V41.5098H23.1573V42.2014H23.3106C23.7612 42.2014 24.118 42.3201 24.3806 42.5577C24.643 42.7951 24.7743 43.1072 24.7743 43.4936C24.7743 43.7776 24.7092 43.9999 24.5791 44.1607C24.449 44.3214 24.2631 44.4436 24.0215 44.5273V44.5483C24.2955 44.6462 24.5128 44.7883 24.6732 44.9743H24.6736ZM22.1959 44.2619H22.8093V42.7671H22.1959V44.2619ZM22.8091 46.525V44.8346H22.1957V46.525H22.8091ZM23.1577 42.7671V44.2619H23.1646C23.4387 44.2619 23.6548 44.1979 23.8128 44.0699C23.9706 43.9419 24.0497 43.7499 24.0497 43.4936C24.0497 43.2748 23.9823 43.0989 23.8476 42.9663C23.7128 42.8335 23.5176 42.7671 23.2621 42.7671H23.1575H23.1577ZM23.9627 46.3086C24.1136 46.1643 24.1891 45.9501 24.1891 45.6659C24.1891 45.4006 24.1043 45.1957 23.9346 45.0512C23.765 44.907 23.5269 44.8346 23.2202 44.8346H23.1575V46.525H23.2969C23.5898 46.525 23.8114 46.4529 23.9625 46.3084L23.9627 46.3086Z" fill="white"/></g><defs><linearGradient id="paint0_linear_3499_20375" x1="58.3158" y1="14.2986" x2="58.3158" y2="56.5217" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint1_linear_3499_20375" x1="57.9469" y1="11.7432" x2="57.9469" y2="30.5005" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><linearGradient id="paint2_linear_3499_20375" x1="96.6456" y1="32.2173" x2="97.6077" y2="50.9005" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint3_linear_3499_20375" x1="11.1643" y1="23.7217" x2="11.1643" y2="32.4198" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint4_linear_3499_20375" x1="9.93213" y1="29.7927" x2="35.3052" y2="32.8372" gradientUnits="userSpaceOnUse"><stop stop-color="white"/><stop offset="0.37" stop-color="#F2F2FF"/><stop offset="1" stop-color="#D2D2FF"/></linearGradient><linearGradient id="paint5_linear_3499_20375" x1="32.5734" y1="40.0301" x2="41.269" y2="44.0896" gradientUnits="userSpaceOnUse"><stop stop-color="white"/><stop offset="0.17" stop-color="#C5C5C5"/><stop offset="0.4" stop-color="#808080"/><stop offset="0.6" stop-color="#494949"/><stop offset="0.78" stop-color="#212121"/><stop offset="0.92" stop-color="#080808"/><stop offset="1"/></linearGradient><linearGradient id="paint6_linear_3499_20375" x1="32.5734" y1="40.0301" x2="41.269" y2="44.0896" gradientUnits="userSpaceOnUse"><stop stop-color="#FFD571"/><stop offset="0.28" stop-color="#FFAC54"/><stop offset="0.59" stop-color="#FF8538"/><stop offset="0.85" stop-color="#FF6C27"/><stop offset="1" stop-color="#FF6421"/></linearGradient><linearGradient id="paint7_linear_3499_20375" x1="15.1821" y1="30.5846" x2="15.2216" y2="30.5894" gradientUnits="userSpaceOnUse"><stop stop-color="#808080"/><stop offset="0.64" stop-color="#51514C"/><stop offset="1" stop-color="#3C3C35"/></linearGradient><linearGradient id="paint8_linear_3499_20375" x1="11.2101" y1="23.5806" x2="11.5208" y2="23.6178" gradientUnits="userSpaceOnUse"><stop stop-color="#808080"/><stop offset="0.64" stop-color="#51514C"/><stop offset="1" stop-color="#3C3C35"/></linearGradient><linearGradient id="paint9_linear_3499_20375" x1="24.9424" y1="42.2324" x2="24.9424" y2="46.1247" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint10_linear_3499_20375" x1="9.54878" y1="12" x2="9.54878" y2="43.5539" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint11_linear_3499_20375" x1="9.14417" y1="23.416" x2="9.14417" y2="31.9651" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint12_linear_3499_20375" x1="88.3242" y1="11.2479" x2="82.1018" y2="18.2206" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><linearGradient id="paint13_linear_3499_20375" x1="65.0472" y1="41.6389" x2="65.0472" y2="48.6131" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint14_linear_3499_20375" x1="71.0526" y1="40.8525" x2="71.0526" y2="48.6146" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint15_linear_3499_20375" x1="75.5484" y1="41.8823" x2="75.5484" y2="48.6146" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint16_linear_3499_20375" x1="57.4157" y1="41.9424" x2="57.4157" y2="48.6125" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint17_linear_3499_20375" x1="51.4102" y1="41.1902" x2="51.4102" y2="48.6139" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint18_linear_3499_20375" x1="46.9145" y1="42.175" x2="46.9145" y2="48.6139" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint19_linear_3499_20375" x1="86.3006" y1="40.9431" x2="86.3006" y2="48.6124" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint20_linear_3499_20375" x1="30.0643" y1="42.3557" x2="30.0643" y2="48.6332" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint21_linear_3499_20375" x1="41.3392" y1="42.9629" x2="41.3392" y2="48.6332" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint22_linear_3499_20375" x1="35.5326" y1="40.8398" x2="35.5326" y2="48.6347" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint23_linear_3499_20375" x1="23.1099" y1="36" x2="23.1099" y2="50.0025" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint24_linear_3499_20375" x1="23.2607" y1="36.033" x2="23.2607" y2="37.2911" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><clipPath id="clip0_3499_20375"><rect width="106" height="48" fill="white"/></clipPath><clipPath id="clip1_3499_20375"><rect width="15.1292" height="21.6131" fill="white" transform="matrix(-1 0 0 1 77.7109 27)"/></clipPath><clipPath id="clip2_3499_20375"><rect width="15.1292" height="20.5325" fill="white" transform="translate(44.752 28.0801)"/></clipPath><clipPath id="clip3_3499_20375"><rect width="15.1292" height="20.5325" fill="white" transform="translate(79.082 28.0801)"/></clipPath><clipPath id="clip4_3499_20375"><rect width="15.1292" height="21.6131" fill="white" transform="matrix(-1 0 0 1 43.1289 27.02)"/></clipPath></defs></svg>'),
                                    )
                                  : appConfigService.countryConfigCollection.toString() == "rafco"
                                      ? Container(
                                          height: 48.h,
                                          width: 106.w,
                                          child: SvgPicture.string(
                                              '<svg width="106" height="48" viewBox="0 0 106 48" fill="none" xmlns="http://www.w3.org/2000/svg"> <g opacity="0.8" clip-path="url(#clip0_3898_64225)"> <path opacity="0.15" d="M58.316 67.8205C76.1637 67.8205 90.6321 53.3099 90.6321 35.4103C90.6321 17.5106 76.1637 3 58.316 3C40.4684 3 26 17.5106 26 35.4103C26 53.3099 40.4684 67.8205 58.316 67.8205Z" fill="#22409A"/> <path opacity="0.5" d="M58.3158 56.5217C69.9415 56.5217 79.366 47.0698 79.366 35.4102C79.366 23.7506 69.9415 14.2986 58.3158 14.2986C46.6901 14.2986 37.2656 23.7506 37.2656 35.4102C37.2656 47.0698 46.6901 56.5217 58.3158 56.5217Z" fill="url(#paint0_linear_3898_64225)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M73 16.8974C66.8729 23.6784 58.0564 29.0157 47.431 31.1975C45.6162 28.1562 44.1371 25.0259 43 21.8609C52.2939 20.6059 59.9912 16.4166 65.2751 11C67.5698 13.177 70.1511 15.1616 73 16.8974Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M72.091 16.9951C66.1582 23.3457 57.7939 28.3476 47.7612 30.5005C46.1838 27.8427 44.8607 25.1152 43.8027 22.3576C52.7212 20.992 60.1474 16.9542 65.3593 11.7432C67.3877 13.6557 69.6368 15.4203 72.091 16.9951Z" fill="url(#paint1_linear_3898_64225)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M64.0395 15.7308C63.4026 16.2537 62.5677 16.47 61.7988 16.3763C58.5098 18.7708 54.6701 20.7483 50.3818 22.1078C50.1239 22.8074 49.4642 23.4085 48.4962 23.6597C47.5257 23.9134 46.5361 24.1357 45.5273 24.3257C46.1307 25.66 46.7976 26.9835 47.5257 28.2902C48.5933 28.057 49.6417 27.7913 50.6709 27.4908C51.6653 27.2035 52.7389 27.2275 53.6733 27.4956C58.3754 25.8139 62.5869 23.4674 66.2023 20.6653C66.2658 19.9741 66.6425 19.2697 67.301 18.7071C68.1755 17.9618 69.008 17.1877 69.8008 16.3871C68.6049 15.5144 67.463 14.5972 66.3798 13.6367C65.6385 14.3592 64.8564 15.0576 64.0383 15.7308H64.0395Z" fill="#F9F9F9"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M61.5438 20.2243C62.9028 21.6848 62.7781 23.7476 60.956 24.7778C59.1387 25.808 56.4782 25.2129 55.3195 23.5336C54.1583 21.8567 54.7641 19.9021 56.375 19.0823C57.9944 18.2661 60.1727 18.7565 61.5438 20.2243Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M53.1938 24.6832C53.6329 25.3443 53.273 26.1557 52.3398 26.4707C51.409 26.7856 50.2874 26.4526 49.8892 25.7566C49.4909 25.0606 49.9348 24.2949 50.8248 24.0148C51.7172 23.7359 52.7548 24.0232 53.1938 24.6832Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M65.9024 17.347C66.4746 17.8591 66.417 18.7282 65.7165 19.2824C65.0183 19.8378 63.964 19.8306 63.4266 19.2776C62.882 18.7246 63.0295 17.8712 63.6953 17.3591C64.3622 16.847 65.3254 16.8386 65.9012 17.3482L65.9024 17.347Z" fill="#EA1B23" fill-opacity="0.5"/> <path d="M59.8309 21.3224C60.0291 21.3836 60.1891 21.5126 60.311 21.7093C60.5093 22.0293 60.5628 22.3424 60.4716 22.6488C60.3803 22.9551 60.1571 23.2189 59.802 23.4403L59.6763 23.5186L59.9799 24.0085L59.7285 24.1652L59.425 23.6753L58.48 24.2643L56.6274 21.2747C56.5882 21.2114 56.5509 21.17 56.5156 21.1503C56.4802 21.1309 56.4382 21.1154 56.3894 21.1043C56.359 21.1002 56.3344 21.0924 56.3157 21.081C56.2969 21.0695 56.2838 21.0502 56.276 21.0226C56.2502 20.951 56.2462 20.8715 56.2641 20.7841C56.2819 20.6967 56.321 20.6341 56.3814 20.5965C56.4449 20.5569 56.5206 20.5514 56.6079 20.58L57.2563 20.1759L56.9495 19.6809L57.2009 19.5242L57.5076 20.0192L57.6181 19.9503C57.9431 19.7478 58.253 19.6724 58.5477 19.7244C58.8422 19.7764 59.0753 19.9408 59.2467 20.2174C59.3727 20.4206 59.4244 20.609 59.4018 20.7825C59.3793 20.956 59.2995 21.1271 59.1623 21.2955L59.1717 21.3105C59.4127 21.2574 59.6324 21.2615 59.8306 21.3226L59.8309 21.3224ZM57.7283 21.9261L58.1706 21.6504L57.5076 20.5805L57.0653 20.8562L57.7283 21.9261ZM59.1742 23.2702L58.4244 22.0604L57.9821 22.3361L58.7319 23.5459L59.1742 23.2702ZM57.7589 20.4239L58.4218 21.4938L58.4268 21.4907C58.6244 21.3675 58.7519 21.2246 58.8091 21.0619C58.8661 20.8994 58.8379 20.7264 58.7243 20.543C58.6273 20.3864 58.5006 20.2908 58.3447 20.2564C58.1885 20.222 58.0184 20.2622 57.8341 20.377L57.7587 20.424L57.7589 20.4239ZM59.9101 22.5969C59.9549 22.4258 59.9143 22.2385 59.7883 22.0351C59.6706 21.8452 59.5185 21.7367 59.3321 21.7095C59.1458 21.6825 58.942 21.7378 58.7209 21.8756L58.6757 21.9038L59.4254 23.1136L59.5259 23.051C59.7371 22.9194 59.865 22.7681 59.9098 22.5968L59.9101 22.5969Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M83.0931 43.4133C83.7992 42.426 84.8229 41.5883 85.0743 40.4006C85.3732 38.9969 84.4878 37.5832 84.6156 36.155C84.7772 34.3564 86.4334 33.1117 87.2843 31.5217C88.1195 29.9651 87.6674 29.4778 87.6586 27.7104C87.6488 25.048 89.6502 21.5817 92.5938 22.3325C95.5813 23.0955 95.2408 25.8593 94.8303 27.9488C94.5365 29.4362 94.7887 29.6354 95.2208 31.0869C95.7449 32.8415 97.2289 34.4858 96.7345 36.2487C96.429 37.3392 95.4173 38.1155 95.092 39.2012C94.7242 40.431 95.3116 41.7123 95.576 42.9675C96.1356 45.6118 95.6429 47.859 93.8238 50.1579C93.6017 50.3939 93.3685 50.6101 93.123 50.811C90.4441 50.8121 87.4567 50.8421 84.2082 50.8971C84.1613 50.8546 84.1156 50.8106 84.0692 50.7652C82.2021 48.9205 81.5047 45.6301 83.0938 43.4132L83.0931 43.4133Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.5243 24.8146C91.5629 24.6924 91.6946 24.6255 91.818 24.6655C91.9401 24.7056 92.0068 24.8376 91.9668 24.9599C91.7677 25.5734 91.6075 26.2132 91.4753 26.8783C91.5386 26.8149 91.6048 26.7528 91.6732 26.6914C91.9751 26.4209 92.3241 26.1806 92.7138 25.9708C92.8269 25.9085 92.9672 25.9527 93.0293 26.0653C93.0894 26.1796 93.046 26.3194 92.9335 26.3817C92.5741 26.5747 92.2573 26.7946 91.9822 27.0397C91.7225 27.2733 91.4985 27.5296 91.3113 27.8115C91.0871 29.2476 90.9715 30.7867 90.8789 32.4072C91.1262 31.973 91.3953 31.5825 91.6839 31.2345C92.1847 30.6287 92.7491 30.1484 93.3772 29.796C93.4895 29.7315 93.631 29.772 93.6939 29.8847C93.7559 29.9959 93.7177 30.1384 93.6053 30.2006C93.0293 30.5251 92.5086 30.9697 92.0417 31.5326C91.5789 32.0916 91.1706 32.7695 90.818 33.5626L90.795 34.0019C90.7454 34.9677 90.6936 35.9589 90.6261 36.9712C90.6266 36.9806 90.6264 36.99 90.6233 37.0003C90.5524 38.0577 90.4604 39.1393 90.3317 40.2403C90.7943 39.4365 91.2997 38.7232 91.8487 38.096C92.6515 37.1769 93.5464 36.4471 94.5341 35.9074C94.6465 35.8452 94.7875 35.8879 94.8496 35.9998C94.9103 36.1133 94.8691 36.2545 94.7566 36.316C93.8182 36.8315 92.9642 37.5265 92.1982 38.4024C91.4336 39.2775 90.7542 40.3329 90.1583 41.5673C89.947 43.0295 89.6584 44.521 89.251 46.036L89.2491 46.0412C89.0494 46.7844 88.8211 47.5334 88.5589 48.2856C89.2315 47.6448 89.95 47.0835 90.7188 46.6021C91.7894 45.9299 92.9475 45.4155 94.196 45.0579C94.3192 45.0219 94.4479 45.0935 94.4831 45.2177C94.5199 45.3418 94.4477 45.4708 94.3238 45.5069C93.1159 45.853 91.9944 46.3503 90.9652 46.9957C89.9464 47.6348 89.0133 48.4222 88.1642 49.356C87.9688 49.8578 87.7583 50.3619 87.5304 50.866L87.0155 50.8716C87.7803 49.2114 88.3558 47.5639 88.7945 45.9415C88.8228 44.8001 88.6306 43.6715 88.2164 42.5551C87.7965 41.4238 87.1492 40.2991 86.2721 39.1877C86.1932 39.0868 86.2102 38.9389 86.3095 38.8606C86.4102 38.7801 86.5563 38.7972 86.6367 38.8981C87.5419 40.05 88.2135 41.2133 88.6503 42.3916C88.8985 43.061 89.0718 43.735 89.1685 44.4108C89.7364 41.8526 89.9957 39.366 90.1578 36.9982C89.835 36.014 89.4876 35.186 89.1149 34.5128C88.7453 33.8424 88.3541 33.3302 87.9416 32.98C87.8448 32.8959 87.833 32.7495 87.9168 32.651C88 32.554 88.1468 32.5407 88.2429 32.6248C88.7008 33.0147 89.1266 33.5686 89.5225 34.2869C89.7694 34.734 90.0052 35.2462 90.2322 35.8255C90.2681 35.2001 90.2994 34.5836 90.3312 33.9765L90.3563 33.506C90.3558 33.4966 90.3562 33.49 90.3571 33.4806C90.5234 30.2637 90.7089 27.3363 91.5265 24.8145L91.5243 24.8146Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.0818 44.2783C91.8668 43.8005 92.8273 43.5207 93.3525 42.768C93.974 41.8785 93.7771 40.6307 94.2875 39.6745C94.9318 38.4688 96.4526 38.0936 97.5182 37.2392C98.5593 36.4006 98.3863 35.9293 98.9034 34.6947C99.6841 32.836 102.104 31.0167 103.933 32.413C105.788 33.8325 104.733 35.6568 103.83 36.9896C103.185 37.9381 103.301 38.1523 103.175 39.2915C103.021 40.6697 103.568 42.2565 102.701 43.3367C102.167 44.0059 101.233 44.2459 100.685 44.9056C100.066 45.6523 100.097 46.7196 99.9103 47.6736C99.6588 48.9576 99.1551 50.001 98.3169 50.864C95.9885 50.8239 93.2897 50.8173 90.2759 50.8385C89.9929 50.4916 89.7584 50.1047 89.5876 49.6891C88.8329 47.8501 89.3207 45.3503 91.0826 44.2783L91.0818 44.2783Z" fill="url(#paint2_linear_3898_64225)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M102.36 33.7733C102.423 33.6991 102.534 33.692 102.606 33.7563C102.68 33.8191 102.688 33.9303 102.625 34.0052C102.305 34.3722 102.003 34.7716 101.715 35.1956C101.778 35.1706 101.843 35.147 101.909 35.1248C102.198 35.0266 102.512 34.9612 102.846 34.9302C102.942 34.9209 103.027 34.9933 103.037 35.0913C103.045 35.1872 102.974 35.2727 102.878 35.282C102.569 35.3109 102.284 35.3691 102.019 35.4587C101.768 35.5426 101.538 35.6552 101.324 35.7965C100.742 36.7296 100.207 37.7674 99.6645 38.8679C99.9651 38.641 100.267 38.448 100.571 38.2904C101.1 38.0176 101.635 37.851 102.177 37.7913C102.273 37.7813 102.36 37.8521 102.37 37.9472C102.382 38.0443 102.311 38.1313 102.216 38.1434C101.719 38.1965 101.224 38.3517 100.734 38.605C100.245 38.8583 99.7614 39.2077 99.2813 39.6553L99.1348 39.9547C98.8158 40.6128 98.4861 41.2874 98.1403 41.9729C98.1363 41.9796 98.1338 41.9856 98.1305 41.9922C97.7682 42.7083 97.384 43.4335 96.9697 44.1624C97.5284 43.7403 98.0924 43.3934 98.6587 43.1202C99.4887 42.7182 100.329 42.4758 101.176 42.3916C101.274 42.3822 101.359 42.4546 101.369 42.5511C101.377 42.6471 101.306 42.734 101.211 42.7432C100.406 42.8231 99.6057 43.0555 98.8108 43.4375C98.0203 43.82 97.2361 44.3543 96.4545 45.0384C95.876 45.9923 95.2347 46.9465 94.5013 47.8794L94.4986 47.8839L94.4972 47.884C94.1398 48.342 93.7597 48.7953 93.3533 49.2421C94.012 48.9938 94.6786 48.8175 95.3567 48.7088C96.2994 48.5595 97.2591 48.5456 98.2337 48.6671C98.3311 48.6787 98.3992 48.7665 98.3883 48.8633C98.3766 48.9595 98.2876 49.0286 98.1902 49.0169C97.2454 48.899 96.3189 48.9127 95.4107 49.0579C94.5126 49.2012 93.6289 49.472 92.7626 49.8707C92.4445 50.1942 92.1147 50.5139 91.7689 50.83L91.2371 50.8321C92.3781 49.829 93.3558 48.7676 94.2106 47.6778C94.5678 46.8925 94.7688 46.0478 94.8095 45.1477C94.853 44.2337 94.7315 43.2579 94.4502 42.223C94.4251 42.1287 94.4801 42.0317 94.5742 42.0066C94.6683 41.9808 94.7651 42.0359 94.7902 42.1302C95.0827 43.2 95.2052 44.2105 95.1608 45.163C95.1356 45.7023 95.0567 46.222 94.9248 46.7227C96.0779 45.1085 96.9941 43.4536 97.8068 41.8518C97.8716 41.0721 97.8744 40.3911 97.8143 39.8105C97.7544 39.2335 97.6349 38.7624 97.4506 38.3953C97.4079 38.3084 97.4436 38.2016 97.531 38.1587C97.6169 38.1152 97.7228 38.1503 97.7663 38.2371C97.9685 38.6438 98.1025 39.1577 98.1652 39.7758C98.2059 40.1612 98.2176 40.5878 98.2055 41.0578C98.415 40.6334 98.6197 40.2137 98.8204 39.8L98.9774 39.4783C98.9793 39.4732 98.9833 39.4672 98.9852 39.462C100.052 37.2706 101.046 35.2851 102.362 33.7725L102.36 33.7733Z" fill="white"/> <path d="M14.8188 26.8811C14.4203 27.8318 13.9771 28.8077 13.4899 29.7905C13.0356 30.7041 12.5614 31.5837 12.0797 32.4198L7.50977 31.9482C8.37001 31.7845 9.63307 30.3817 10.6044 28.4343C11.6155 26.4057 11.9581 24.4602 11.4901 23.7217L14.8188 26.8811Z" fill="url(#paint3_linear_3898_64225)"/> <path d="M39.0415 37.853C39.0415 37.853 37.7257 43.3779 34.7527 46.1822C33.7497 45.6722 32.7659 45.2006 31.8194 44.7712C31.3607 44.5615 30.9132 44.3588 30.4707 44.1693C33.4437 41.3644 34.991 35.4756 34.991 35.4756C36.2454 36.262 37.5898 37.054 39.0421 37.853H39.0415Z" fill="#EA1B23" fill-opacity="0.75"/> <path d="M35.2326 35.5383C35.2604 35.4325 35.2162 35.321 35.1235 35.2629C28.22 30.9381 24.2342 26.8562 21.9768 23.8653C20.8479 22.3696 20.1506 21.1459 19.7365 20.2998C19.5294 19.8767 19.3931 19.548 19.3091 19.3267C19.2671 19.2161 19.2381 19.1323 19.2199 19.0771C19.2108 19.0494 19.2044 19.0289 19.2004 19.0158L19.1961 19.0016L19.1952 18.9986L19.1952 18.9985L19.1951 18.9983C19.1627 18.8838 19.0541 18.808 18.9354 18.8171C18.8167 18.8263 18.7209 18.9179 18.7064 19.0361L18.6985 19.1009C18.6985 19.1011 18.6985 19.1014 18.6984 19.1016C18.2881 22.3246 17.0289 26.347 14.9934 30.4607C14.9839 30.4749 14.9753 30.4909 14.9683 30.5086C14.9681 30.509 14.9679 30.5095 14.9677 30.51C14.9618 30.5228 14.9564 30.5347 14.951 30.546C13.014 34.4516 10.7147 37.7381 8.50835 40.0049C8.43121 40.0841 8.41531 40.2047 8.46927 40.3012C8.52323 40.3978 8.63422 40.4474 8.74215 40.4232C10.461 40.0382 16.0077 39.2831 25.3912 42.4691L25.3919 42.4693C25.9353 42.6523 26.489 42.8489 27.0585 43.0655L27.0604 43.0662C28.1213 43.46 29.2251 43.9016 30.3711 44.3979C30.4628 44.4376 30.5693 44.4189 30.642 44.3503C32.1685 42.9104 33.3149 40.6964 34.0766 38.8658C34.459 37.9466 34.7476 37.116 34.9407 36.5152C35.0372 36.2147 35.11 35.9713 35.1587 35.8027C35.1831 35.7183 35.2014 35.6527 35.2137 35.6079L35.2277 35.5566L35.2313 35.5432L35.2322 35.5397L35.2325 35.5387L35.2326 35.5384C35.2326 35.5383 35.2326 35.5383 34.9908 35.4748L35.2326 35.5383ZM15.0555 30.396L15.055 30.3963L15.0555 30.396Z" fill="url(#paint4_linear_3898_64225)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/> <mask id="mask0_3898_64225" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="34" y="37" width="10" height="12"> <path style="mix-blend-mode:multiply" d="M43.3557 45.7092C41.8792 48.392 38.239 48.035 38.239 48.035C37.0429 47.3658 35.8835 46.7506 34.7539 46.1815C37.7269 43.3765 39.0427 37.8523 39.0427 37.8523C40.15 38.4555 41.3106 39.0649 42.5377 39.67C43.4203 40.3311 44.8385 43.0277 43.3557 45.7092Z" fill="url(#paint5_linear_3898_64225)"/></mask> <g mask="url(#mask0_3898_64225)"> <path d="M43.3557 45.7092C41.8792 48.392 38.239 48.035 38.239 48.035C37.0429 47.3658 35.8835 46.7506 34.7539 46.1815C37.7269 43.3765 39.0427 37.8523 39.0427 37.8523C40.15 38.4555 41.3106 39.0649 42.5377 39.67C43.4203 40.3311 44.8385 43.0277 43.3557 45.7092Z" fill="url(#paint6_linear_3898_64225)"/></g> <path d="M15.2138 30.5575L15.1797 30.6101L15.1911 30.6177L15.2252 30.5652L15.2138 30.5575Z" fill="url(#paint7_linear_3898_64225)"/> <path d="M11.2614 23.4582L11.2188 23.5039L11.4681 23.7431L11.5108 23.6974L11.2614 23.4582Z" fill="url(#paint8_linear_3898_64225)"/> <path d="M31.8206 44.7706L24.3776 55.1951C24.0002 55.719 23.2926 55.8713 22.7396 55.5389L18.6178 53.0979C17.9009 52.6723 17.7861 51.6643 18.3993 51.0894L23.6576 46.1249L27.1489 42.832C28.2139 43.2274 29.3224 43.6707 30.4719 44.1687C30.9151 44.3582 31.3626 44.5609 31.8206 44.7706Z" fill="#EA1B23"/> <path d="M27.1465 42.8318L23.6553 46.1247L22.9757 45.587C22.6771 45.354 22.6579 44.9095 22.931 44.6489L23.2183 44.3718L24.9587 42.7216L25.4707 42.2324C26.0169 42.4163 26.5743 42.614 27.1465 42.8318Z" fill="url(#paint9_linear_3898_64225)"/> <path d="M24.9599 42.7219L23.2195 44.3727L22.9297 44.1713L24.675 42.5273L24.9599 42.7219Z" fill="#EA1B23"/> <path d="M17.2603 12.2682C14.1551 10.6766 8.17928 16.3318 3.92211 24.8983C-0.340634 33.4642 -1.27288 41.692 1.83791 43.2849C3.51868 44.1487 6.04666 42.8838 8.6876 40.1796C10.9158 37.8904 13.2297 34.5805 15.1761 30.6555C15.1841 30.6385 15.1922 30.6208 15.2003 30.6032C15.2009 30.5969 15.2071 30.5982 15.2077 30.5919C17.2584 26.4522 18.5314 22.3949 18.9466 19.1329L18.9547 19.0668C19.3848 15.6291 18.8504 13.0779 17.2603 12.2682ZM13.4897 29.7904C13.0354 30.7039 12.5612 31.5835 12.0796 32.4196L7.50958 31.9481C8.36982 31.7844 9.63288 30.3816 10.6042 28.4342C11.6153 26.4056 11.9579 24.4601 11.4899 23.7215L14.8186 26.8809C14.4201 27.8316 13.9769 28.8076 13.4897 29.7904Z" fill="url(#paint10_linear_3898_64225)"/> <path d="M14.8187 26.8819L11.4901 23.7225C11.9581 24.461 11.6155 26.4065 10.6044 28.4352C9.63307 30.3826 8.37 31.7853 7.50976 31.949L12.0797 32.4206C8.54441 38.5486 4.43249 42.4277 2.34704 41.3605C-0.0232879 40.1479 1.11874 33.0005 4.89675 25.3998C8.68158 17.7934 13.6693 12.6111 16.039 13.83C18.1015 14.8884 17.5019 20.446 14.8187 26.8819Z" fill="#1A1818" fill-opacity="0.35"/> <path d="M10.6033 28.4345C9.63198 30.3819 8.36892 31.7846 7.50867 31.9483C7.3355 31.9823 7.18654 31.9641 7.05496 31.8986C6.24561 31.4856 6.52988 29.2693 7.68494 26.9454C8.84062 24.6222 10.4295 23.0695 11.2389 23.4826C11.3419 23.5323 11.4282 23.616 11.4884 23.7218C11.9564 24.4604 11.6138 26.4059 10.6027 28.4345H10.6033Z" fill="url(#paint11_linear_3898_64225)"/> <path d="M9.98637 28.1202C10.8726 26.3381 11.1737 24.68 10.6589 24.4165C10.1442 24.1531 9.00845 25.3842 8.12222 27.1662C7.236 28.9482 6.93488 30.6064 7.44965 30.8698C7.96442 31.1332 9.10015 29.9022 9.98637 28.1202Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M92.2309 18.1755C87.7051 18.665 82.6545 17.7219 77.973 15.0012C78.3022 13.2641 78.7861 11.6056 79.4088 10.0478C83.2876 12.6711 87.5415 13.6698 91.3018 13.4084C91.4372 14.9829 91.7431 16.5811 92.2309 18.1755Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.8591 17.9094C87.5481 18.303 82.7769 17.3854 78.3258 14.851C78.6167 13.3358 79.0255 11.8793 79.543 10.4997C83.3184 12.9569 87.4213 13.9218 91.0868 13.7131C91.2104 15.1005 91.4651 16.5062 91.8591 17.9094Z" fill="url(#paint12_linear_3898_64225)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M89.2754 14.7603C88.8651 14.7434 88.4824 14.5464 88.2268 14.2557C86.2092 14.0542 84.1244 13.5142 82.0766 12.5947C81.7491 12.7696 81.3045 12.7742 80.8607 12.5459C80.4151 12.3177 79.9728 12.0715 79.534 11.8067C79.3177 12.5047 79.1286 13.2198 78.9679 13.9489C79.4429 14.2171 79.9215 14.4667 80.4044 14.697C80.87 14.9207 81.2622 15.2867 81.5218 15.6974C83.8303 16.6345 86.1758 17.1606 88.4498 17.3187C88.702 17.0823 89.0753 16.945 89.5068 16.9543C90.0791 16.9674 90.6453 16.9558 91.2056 16.9211C91.0484 16.1981 90.9261 15.4765 90.84 14.7582C90.3248 14.781 89.8024 14.7811 89.2749 14.7599L89.2754 14.7603Z" fill="#F9F9F9"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M86.8591 15.6046C86.8827 16.601 86.1541 17.3283 85.1344 17.1062C84.1165 16.8858 83.3216 15.7791 83.4451 14.7679C83.5668 13.7567 84.4388 13.2297 85.3103 13.46C86.1837 13.6943 86.8334 14.6016 86.8591 15.6046Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M82.2745 14.4896C82.2195 14.882 81.8171 15.0647 81.3651 14.8717C80.9141 14.6795 80.6062 14.1824 80.6879 13.7906C80.7696 13.3987 81.1882 13.261 81.6125 13.4526C82.0374 13.6455 82.3291 14.0976 82.2745 14.4896Z" fill="#EA1B23" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M89.4379 15.9834C89.4818 16.3646 89.173 16.6693 88.7286 16.6429C88.2848 16.6177 87.8942 16.2643 87.8768 15.8795C87.8567 15.4923 88.1939 15.2233 88.6113 15.2538C89.0292 15.2848 89.391 15.602 89.437 15.9835L89.4379 15.9834Z" fill="#EA1B23" fill-opacity="0.5"/> <path d="M85.8593 15.4443C85.913 15.5331 85.9299 15.6343 85.9103 15.7482C85.8784 15.9334 85.7948 16.0679 85.6595 16.1517C85.5242 16.2356 85.3538 16.2596 85.1483 16.224L85.0755 16.2114L85.0267 16.495L84.8812 16.4698L84.93 16.1862L84.3831 16.0914L84.6812 14.3611C84.6875 14.3245 84.6873 14.2966 84.6806 14.2776C84.6739 14.2585 84.6633 14.2388 84.6488 14.2184C84.6388 14.2068 84.6323 14.1957 84.629 14.1853C84.6258 14.1747 84.6273 14.1631 84.6335 14.1503C84.6476 14.115 84.6724 14.084 84.708 14.0574C84.7435 14.0308 84.7788 14.0205 84.8137 14.0265C84.8505 14.0329 84.8805 14.056 84.9036 14.0957L85.2789 14.1607L85.3282 13.8742L85.4737 13.8994L85.4244 14.1859L85.4883 14.197C85.6764 14.2296 85.8168 14.3046 85.9095 14.422C86.0021 14.5393 86.0346 14.6781 86.007 14.8382C85.9867 14.9558 85.9437 15.0432 85.8779 15.1004C85.8122 15.1576 85.7259 15.1948 85.6191 15.2119L85.6176 15.2206C85.725 15.281 85.8055 15.3556 85.8592 15.4443L85.8593 15.4443ZM84.8761 14.97L85.1321 15.0144L85.2387 14.3951L84.9827 14.3508L84.8761 14.97ZM84.9705 15.9518L85.0911 15.2516L84.8351 15.2072L84.7145 15.9075L84.9705 15.9518ZM85.3842 14.4203L85.2775 15.0396L85.2804 15.0401C85.3948 15.0599 85.4895 15.049 85.5646 15.0074C85.6396 14.9658 85.6863 14.8919 85.7046 14.7858C85.7202 14.6952 85.7046 14.6174 85.6579 14.5527C85.6111 14.488 85.5344 14.4464 85.4277 14.4279L85.3841 14.4203L85.3842 14.4203ZM85.4674 15.9456C85.5407 15.8968 85.5875 15.8135 85.6078 15.6958C85.6267 15.5859 85.6059 15.4948 85.5454 15.4227C85.4849 15.3507 85.3907 15.3035 85.2627 15.2813L85.2365 15.2768L85.1159 15.977L85.1741 15.9871C85.2963 16.0083 85.394 15.9945 85.4674 15.9455L85.4674 15.9456Z" fill="white"/> <path d="M28.371 12.7364C29.9121 11.1907 29.9121 8.68475 28.371 7.13911C26.83 5.59347 24.3314 5.59347 22.7903 7.13911C21.2493 8.68475 21.2493 11.1907 22.7903 12.7364C24.3314 14.282 26.83 14.282 28.371 12.7364Z" fill="#EA1B23" fill-opacity="0.5"/> <path d="M44.5722 19.9514C47.9798 19.9514 50.7421 17.1808 50.7421 13.7632C50.7421 10.3455 47.9798 7.57495 44.5722 7.57495C41.1647 7.57495 38.4023 10.3455 38.4023 13.7632C38.4023 17.1808 41.1647 19.9514 44.5722 19.9514Z" fill="#EA1B23" fill-opacity="0.5"/> <path d="M27.473 10.9564C27.4729 10.9564 27.4729 10.9564 27.4728 10.9563C27.425 10.8761 26.538 10.5909 26.2911 10.4227C26.286 10.4193 26.2856 10.412 26.2902 10.4081C26.6705 10.0838 26.9233 9.53921 26.9233 9.05836C26.9233 8.31509 26.3204 7.71069 25.5793 7.71069C24.8383 7.71069 24.2357 8.31537 24.2357 9.05836C24.2357 9.53956 24.4888 10.084 24.8689 10.4082C24.8735 10.4121 24.873 10.4192 24.8682 10.4227C24.6211 10.591 23.7329 10.8764 23.6858 10.9564C23.4601 11.34 23.7839 11.7687 24.2289 11.7687H27.1989C27.5085 11.7687 27.7339 11.4723 27.5996 11.1934C27.5609 11.113 27.5188 11.0341 27.4732 10.9565C27.4732 10.9564 27.4731 10.9564 27.473 10.9564Z" fill="white"/> <path d="M62.6568 7.0507C63.951 5.75268 63.951 3.64819 62.6568 2.35018C61.3627 1.05217 59.2644 1.05217 57.9702 2.35018C56.6761 3.64819 56.6761 5.75269 57.9702 7.0507C59.2644 8.34871 61.3627 8.34871 62.6568 7.0507Z" fill="#EA1B23" fill-opacity="0.5"/> <path d="M61.9054 5.5562C61.8656 5.48888 61.1203 5.24933 60.913 5.10792C60.9088 5.10497 60.9084 5.09904 60.9123 5.0957C61.2318 4.82337 61.444 4.3662 61.444 3.96219C61.444 3.33799 60.9379 2.83032 60.3155 2.83032C59.6932 2.83032 59.187 3.33799 59.187 3.96219C59.187 4.3662 59.3993 4.82336 59.7187 5.0957C59.7226 5.09904 59.7222 5.10496 59.718 5.10792C59.5104 5.24933 58.7646 5.48888 58.7251 5.5562C58.5357 5.87829 58.8076 6.23837 59.1812 6.23837H61.4494C61.8232 6.23837 62.0947 5.87844 61.9054 5.5562Z" fill="white"/> <path d="M47.5088 15.3307C47.4358 15.2066 46.0594 14.7642 45.6765 14.5032C45.6687 14.498 45.6681 14.4867 45.6752 14.4807C46.2648 13.9777 46.6569 13.1337 46.6569 12.3877C46.6569 11.2354 45.7221 10.2979 44.573 10.2979C43.4239 10.2979 42.4894 11.2351 42.4894 12.3877C42.4894 13.1337 42.8815 13.9777 43.4712 14.4807C43.4783 14.4867 43.4776 14.4979 43.4699 14.5032C43.0867 14.7642 41.7097 15.2066 41.6367 15.3307C41.2871 15.9255 41.7889 16.5902 42.4788 16.5902H46.667C47.3569 16.5902 47.8587 15.9253 47.5088 15.3307Z" fill="white"/> <path d="M29.8594 10.9832L38.3049 13.6397" stroke="#EA1B23" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M50.1949 11.1569L58.0452 6.75592" stroke="#EA1B23" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <g clip-path="url(#clip1_3898_64225)"> <path d="M76.3066 43.1653C75.5785 42.0147 74.4353 41.7035 74.4353 41.7035L71.6956 40.6658H69.0806L66.3409 41.7035C66.3409 41.7035 65.1977 42.0147 64.4696 43.1653H76.3066Z" fill="#FFEBDF"/> <path d="M66.8135 48.6131H62.5807C62.5807 47.8749 62.6275 46.8212 62.6275 46.8212C62.8768 42.3982 66.0301 41.7035 66.0301 41.7035C68.4893 41.0956 67.1492 44.9817 67.1492 44.9817C67.213 45.9181 67.077 47.1787 66.8121 48.6116L66.8135 48.6131Z" fill="url(#paint13_linear_3898_64225)"/> <path d="M68.5925 38.0417H72.1836V41.6181H68.5925V38.0417Z" fill="#FFEBDF"/> <path d="M68.5925 39.7047V38.0417H72.1836V38.8653C71.1594 39.7366 69.9255 39.8466 68.5925 39.7047Z" fill="#EAB894"/> <path d="M64.7686 48.6132C64.6992 46.6853 64.7205 46.1556 64.7375 45.497C64.7743 44.0323 65.0817 42.7934 66.0266 41.705C66.028 41.705 66.0308 41.705 66.0308 41.705L68.5906 40.8525C68.5906 40.8525 68.0523 41.815 70.4052 41.815C70.4052 41.815 71.955 41.7528 72.1816 40.8525L74.7811 41.705C74.7811 41.705 77.0037 42.2637 77.3848 44.6533L76.0787 47.1079L76.1254 48.6146H64.77L64.7686 48.6132Z" fill="url(#paint14_linear_3898_64225)"/> <path d="M66.1151 32.7142C66.1151 31.8646 66.1505 31.0165 66.1307 30.1669C66.1179 29.6241 66.0697 29.7645 66.6845 29.7052C67.5373 29.6227 67.8221 31.2408 67.8816 31.9413C67.9637 32.9067 67.7597 33.9459 67.6025 34.8867C67.5458 35.2268 67.5614 35.907 67.2129 36.0677C66.9154 36.2037 66.6038 35.868 66.448 35.619C66.0882 35.0473 66.1519 34.1717 66.1278 33.5233C66.1179 33.2541 66.1151 32.9849 66.1151 32.7157V32.7142Z" fill="#1A1818"/> <path d="M66.4808 37.544C66.8435 38.9942 67.4823 39.3951 67.4823 39.3951L68.3394 39.5659C68.8324 39.5022 69.6837 39.2967 69.6837 39.2967C71.6415 38.6165 72.2095 38.4326 72.5127 37.6728C72.5254 37.6409 74.0567 32.212 74.0567 32.212C74.3075 29.4172 71.4445 28.8368 71.0125 28.763C70.5847 28.6617 67.8592 27.8888 67.0545 30.5707C67.0545 30.5707 65.8646 33.6781 66.4808 37.544Z" fill="#FFEBDF"/> <path d="M74.3532 29.647C75.3051 30.1159 75.3377 30.9828 75.2924 31.9699C75.2584 32.7356 75.026 33.3869 74.7583 34.1294C74.4542 34.9727 74.1671 35.637 73.897 36.1223C73.897 36.1223 73.2666 37.1239 72.4549 37.8099C72.6901 36.8576 72.7567 36.147 72.9706 35.4146C73.2284 34.5361 73.1533 33.3608 72.7708 32.4591C72.7708 32.4591 72.5541 31.8585 71.6645 31.6877L74.3546 29.6455" fill="#1A1818"/> <path d="M72.788 35.2267C72.788 35.2267 73.2725 34.1499 74.0332 34.7881C74.0332 34.7881 74.3802 35.0646 73.9524 35.7854C73.9524 35.7854 73.3334 36.8216 72.5911 36.2471L72.788 35.2267Z" fill="#FFEBDF"/> <path d="M72.8659 32.7141C72.8659 32.7141 72.863 34.6434 72.516 35.6667L72.8843 35.7419C72.8843 35.7419 73.0783 34.4191 73.7441 34.4032L72.8659 32.7156V32.7141Z" fill="#1A1818"/> <path d="M65.6416 28.5833C62.5818 31.7674 71.842 31.7356 71.842 31.7356C71.842 31.7356 72.8138 32.1944 74.3508 29.6471C74.3508 29.6471 74.7658 27.6555 70.3872 27.0274C70.3872 27.0274 67.4931 26.6641 65.6416 28.5833Z" fill="#1A1818"/> <path d="M73.7544 48.6132H77.7109C77.7109 48.6132 77.6968 47.6536 77.6954 47.5856C77.6656 46.6419 77.6075 45.691 77.4404 44.7618C77.2024 43.4332 76.5706 42.2536 75.1795 41.9164C74.5434 41.7615 73.9768 42.1552 73.6326 42.6907C73.6326 42.6907 73.0036 43.4838 73.7544 48.6146V48.6132Z" fill="url(#paint15_linear_3898_64225)"/></g> <g clip-path="url(#clip2_3898_64225)"> <path d="M46.6348 31.203C46.6348 31.203 46.9861 30.8888 47.6547 31.4577L47.9664 30.9857C47.9664 30.9857 47.2156 30.4057 46.6348 31.203Z" fill="#1A1818"/> <path d="M47.8608 29.8228C47.8608 29.8228 47.4458 30.3266 48.0436 30.9011L47.6398 31.2997C47.6398 31.2997 46.7148 30.1923 47.8608 29.8241V29.8228Z" fill="#1A1818"/> <path d="M46.1562 43.4035C46.8844 42.3031 48.0276 42.0054 48.0276 42.0054L50.7672 41.0129H53.3823L56.122 42.0054C56.122 42.0054 57.2651 42.3031 57.9933 43.4035H46.1562Z" fill="#FFEBDF"/> <path d="M55.6494 48.6125H59.8822C59.8822 47.9065 59.8354 46.8988 59.8354 46.8988C59.5861 42.6685 56.4328 42.0041 56.4328 42.0041C53.9736 41.4227 55.3137 45.1394 55.3137 45.1394C55.2499 46.035 55.3859 47.2407 55.6508 48.6111L55.6494 48.6125Z" fill="url(#paint16_linear_3898_64225)"/> <path d="M53.8704 38.5022H50.2793V41.9227H53.8704V38.5022Z" fill="#FFEBDF"/> <path d="M53.8704 40.0927V38.5022H50.2793V39.2898C51.3035 40.1231 52.5373 40.2283 53.8704 40.0927Z" fill="#EAB894"/> <path d="M57.6943 48.6125C57.7637 46.7687 57.7424 46.262 57.7254 45.6322C57.6886 44.2314 57.3812 43.0465 56.4363 42.0055C56.4349 42.0055 56.4321 42.0055 56.4321 42.0055L53.8723 41.1902C53.8723 41.1902 54.4106 42.1107 52.0577 42.1107C52.0577 42.1107 50.5079 42.0512 50.2813 41.1902L47.6818 42.0055C47.6818 42.0055 45.4592 42.5398 45.0781 44.8252L46.3842 47.1729L46.3375 48.6139H57.6929L57.6943 48.6125Z" fill="url(#paint17_linear_3898_64225)"/> <path d="M56.6633 34.2262C56.7653 33.4399 56.5939 32.6855 56.6024 31.9103C56.6081 31.3899 56.5047 30.605 55.8417 30.5385C54.9238 30.4472 54.6404 31.9948 54.5866 32.6661C54.5115 33.5908 54.7467 34.5902 54.9294 35.4955C54.9946 35.8222 54.9889 36.4756 55.3643 36.6334C55.6859 36.7676 56.016 36.4506 56.1803 36.2139C56.5769 35.6436 56.5783 34.8851 56.6619 34.2262H56.6633Z" fill="#1A1818"/> <path d="M55.9821 38.0259C55.6194 39.4129 54.9805 39.7963 54.9805 39.7963L54.1235 39.9596C53.6305 39.8987 52.7792 39.7022 52.7792 39.7022C50.8214 39.0516 50.2534 38.8758 49.9502 38.1491C49.9375 38.1186 48.4062 32.9263 48.4062 32.9263C48.1554 30.2533 51.0183 29.6983 51.4504 29.6277C51.8782 29.5308 54.6037 28.7916 55.4084 31.3566C55.4084 31.3566 56.5983 34.3286 55.9821 38.0259Z" fill="#FFEBDF"/> <path d="M56.8807 28.7707C57.0351 29.5998 56.8906 30.7487 56.2333 31.2789C55.5193 31.8534 54.9059 31.6969 54.0461 31.8838C53.1522 32.0776 52.2371 32.1551 51.339 32.324C50.71 32.443 49.9833 32.5109 49.6943 33.1628C49.3118 34.0252 49.2368 35.1492 49.4946 35.9895C49.7085 36.6885 49.7751 37.3696 50.0102 38.2804C49.1985 37.6243 48.5681 36.6664 48.5681 36.6664C48.298 36.2022 48.0109 35.5668 47.7068 34.7603C47.4391 34.0501 47.2082 33.4272 47.1728 32.695C47.1289 31.7509 47.5963 31.3398 48.112 30.4733C48.5965 29.6593 49.2764 29.0946 50.1632 28.8371C50.1632 28.8371 54.6566 29.0835 54.5731 28.0813C54.5731 28.0813 55.6525 28.3512 55.4471 29.0517C55.4471 29.0517 56.5761 28.8496 56.3367 28.0813C56.3367 28.0813 56.7928 28.2917 56.8835 28.772L56.8807 28.7707Z" fill="#1A1818"/> <path d="M49.6749 35.8096C49.6749 35.8096 49.1904 34.7797 48.4297 35.3902C48.4297 35.3902 48.0827 35.6546 48.5105 36.3439C48.5105 36.3439 49.1295 37.335 49.8718 36.7855L49.6749 35.8096Z" fill="#FFEBDF"/> <path d="M49.597 33.4067C49.597 33.4067 49.5999 35.2519 49.9469 36.2306L49.5786 36.3026C49.5786 36.3026 49.3845 35.0374 48.7188 35.0221L49.597 33.4081V33.4067Z" fill="#1A1818"/> <path d="M48.7085 48.6125H44.752C44.752 48.6125 44.7661 47.6947 44.7675 47.6297C44.7973 46.7271 44.8554 45.8177 45.0225 44.929C45.2605 43.6583 45.8923 42.5301 47.2834 42.2076C47.9194 42.0595 48.4861 42.436 48.8303 42.9482C48.8303 42.9482 49.4593 43.7067 48.7085 48.6139V48.6125Z" fill="url(#paint18_linear_3898_64225)"/></g> <g clip-path="url(#clip3_3898_64225)"> <path d="M83.9875 28.3726C83.9875 28.3726 80.5937 29.3625 80.9139 34.5761V45.8138H84.7748L83.9875 28.3726Z" fill="#1A1818"/> <path d="M83.6016 31.7417V47.8853H90.9842V34.2864C90.9842 34.2864 91.4689 29.07 87.9412 28.4289C87.9412 28.4289 83.6016 27.6663 83.6016 31.7417Z" fill="#1A1818"/> <path d="M88.5488 42.0786H83.416V44.5442H88.5488V42.0786Z" fill="#FFEBDF"/> <path d="M94.2102 48.6125H90.7394L90.1194 46.1808C90.1194 46.1808 89.6144 42.4895 92.2237 42.4373C92.2237 42.4373 93.5772 43.358 93.9468 45.5256C93.9468 45.5256 94.2393 47.2569 94.2117 48.6125H94.2102Z" fill="#FFEBDF"/> <path d="M82.1525 48.6126L82.6211 46.7782C82.6211 46.7782 83.4506 42.6816 80.8413 42.6294C80.8413 42.6294 78.9523 44.1404 79.0891 48.6126H82.1525Z" fill="#FFEBDF"/> <path d="M87.9421 37.0994H84.7754V41.5984H87.9421V37.0994Z" fill="#FFEBDF"/> <path d="M87.9421 38.9364V37.1006H84.7754V39.7455C85.8203 39.7144 87.0602 39.5309 87.9421 38.9364Z" fill="#EAB894"/> <path d="M80.5762 43.0346C81.2864 41.967 82.404 41.679 82.404 41.679L85.0803 40.7173H87.6343L90.9073 41.679C90.9073 41.679 92.0249 41.9684 92.7351 43.0346H80.5762Z" fill="#FFEBDF"/> <path d="M81.2318 46.6905V48.6124H91.512L91.3912 46.3798C91.2631 42.0785 91.7594 42.0771 91.7594 42.0771C91.2966 41.7777 90.9095 41.6774 90.9095 41.6774L88.3322 40.9657C88.3322 40.9657 87.7733 43.2971 85.6472 43.2971C85.6472 43.2971 83.9998 42.4357 84.4465 40.9431L82.4048 41.6774C82.4048 41.6774 81.5956 41.9175 80.8418 42.6278C80.8418 42.6278 81.2056 42.8439 81.2318 46.6891V46.6905Z" fill="url(#paint19_linear_3898_64225)"/> <path d="M88.2873 37.4581C88.1796 37.701 88.0283 37.9255 87.8173 38.1232C87.2919 38.6146 86.6763 39.0128 86.0011 39.2811C85.173 39.6116 83.9229 39.6525 83.2637 38.9917C82.7296 38.455 82.4371 37.6431 82.2057 36.9455C81.756 35.5898 81.7618 34.1099 81.8753 32.7034C81.9015 32.3829 81.935 32.0609 81.9903 31.7432C82.5069 28.6746 85.2137 28.6802 85.2137 28.6802C87.257 28.5941 89.1226 29.5303 89.437 31.6203C89.5578 32.421 89.3992 33.2796 89.3569 34.0802C89.3264 34.6677 89.2522 35.2551 89.0586 35.8158C88.8607 36.3877 88.5347 36.9045 88.2888 37.4567L88.2873 37.4581Z" fill="#FFEBDF"/> <path d="M88.5522 35.1183C88.5522 35.1183 88.8229 34.0267 89.6874 34.4772C89.6874 34.4772 90.0774 34.6692 89.7965 35.419C89.7965 35.419 89.3861 36.4979 88.5508 36.1039V35.1183H88.5522Z" fill="#FFEBDF"/> <path d="M81.6717 32.0949C81.5262 27.8811 85.6461 28.0816 85.6461 28.0816C89.6962 28.2426 89.8985 30.0233 89.8985 30.0233C90.3889 32.2827 89.6875 34.4772 89.6875 34.4772C88.8259 34.295 88.6804 35.412 88.6804 35.412C88.7706 33.6214 88.6193 33.5127 88.6193 33.5127C87.3052 33.5 86.3578 33.363 86.3578 33.363L86.2996 32.1147L85.9823 33.298C85.9823 33.298 84.1195 33.2571 82.1855 32.8122C82.1855 32.8122 81.6703 32.8476 81.6703 34.3684V32.0949H81.6717Z" fill="#1A1818"/></g> <g clip-path="url(#clip4_3898_64225)"> <path d="M34.3206 40.4408C34.99 40.2011 35.4119 39.6484 36.2079 39.7173C37.6223 39.8408 38.6249 41.4084 39.9869 40.3446C41.6342 39.0556 41.202 31.3052 40.4875 29.5496C39.8822 28.0609 38.7646 29.5166 37.1727 29.0156C35.3217 28.4342 33.7254 29.8754 32.7679 31.3712C31.7726 32.9288 31.259 34.9127 31.4059 36.7415C31.5413 38.4297 31.8512 41.3251 34.322 40.4394L34.3206 40.4408Z" fill="#1A1818"/> <path d="M28.0011 48.6332H31.4716L32.0915 46.1613C32.0915 46.1613 32.5965 42.4088 29.9874 42.3557C29.9874 42.3557 28.6341 43.2917 28.2645 45.4952C28.2645 45.4952 27.972 47.2551 27.9997 48.6332H28.0011Z" fill="url(#paint20_linear_3898_64225)"/> <path d="M40.0573 48.6332L39.5888 46.7684C39.5888 46.7684 39.0839 43.016 41.6929 42.9629C41.6929 42.9629 43.2572 44.0869 43.1204 48.6332H40.0573Z" fill="url(#paint21_linear_3898_64225)"/> <path d="M34.2691 36.9294H37.4355V41.503H34.2691V36.9294Z" fill="#FFEBDF"/> <path d="M34.2691 38.7971V36.9309H37.4355V39.6196C36.3908 39.588 35.151 39.4014 34.2691 38.7971Z" fill="#EAB894"/> <path d="M41.6328 42.9629C40.9227 41.8776 39.8051 41.5848 39.8051 41.5848L37.1291 40.6072H34.5753L31.3027 41.5848C31.3027 41.5848 30.1852 41.879 29.475 42.9629H41.6328Z" fill="#FFEBDF"/> <path d="M40.9778 48.6333H30.4323L30.4993 46.6795L28.9481 44.3655C29.293 42.1117 31.3011 41.5849 31.3011 41.5849L33.8781 40.8614C33.8781 40.8614 33.7239 41.6897 35.8513 41.6897C35.8513 41.6897 37.5597 41.6897 37.7634 40.8398L39.805 41.5863C39.805 41.5863 41.3008 42.0371 42.1172 43.5286L40.9764 46.681V48.6347L40.9778 48.6333Z" fill="url(#paint22_linear_3898_64225)"/> <path d="M33.9243 37.2939C34.0319 37.5408 34.1833 37.7691 34.3943 37.97C34.9196 38.4696 35.5351 38.8744 36.2103 39.1472C37.0383 39.4831 38.2882 39.5247 38.9474 38.8529C39.4815 38.3074 39.7739 37.482 40.0053 36.7728C40.455 35.3947 40.4491 33.8903 40.3356 32.4605C40.3094 32.1347 40.276 31.8074 40.2207 31.4844C39.7056 28.365 36.999 28.3708 36.999 28.3708C34.9559 28.2832 33.0905 29.2349 32.7761 31.3595C32.6554 32.1734 32.814 33.0462 32.8562 33.8602C32.8867 34.4573 32.9609 35.0545 33.1545 35.6244C33.3524 36.2058 33.6783 36.7312 33.9243 37.2925V37.2939Z" fill="#FFEBDF"/> <path d="M40.5517 34.2994C41.3156 31.9667 40.6987 29.2234 39.7863 28.365C37.2005 25.9318 32.4247 26.8577 30.9666 30.6618C30.5155 31.836 30.728 36.2187 30.9317 37.5652C31.2053 39.3696 31.8775 40.6056 33.2658 40.6056C33.2294 39.1184 33.3443 37.469 33.2105 35.6502C33.078 33.8457 33.3211 33.8012 34.8431 32.9615C35.5998 32.5437 36.0407 32.1073 36.5107 31.4298C37.0928 30.59 36.6548 30.2972 37.8073 30.3345C37.8073 30.3345 40.4848 30.244 40.5502 34.2979L40.5517 34.2994Z" fill="#1A1818"/> <path d="M33.6587 34.9153C33.6587 34.9153 33.388 33.8057 32.5237 34.2636C32.5237 34.2636 32.1337 34.4589 32.4146 35.2211C32.4146 35.2211 32.8249 36.3178 33.6602 35.9173V34.9153H33.6587Z" fill="#FFEBDF"/></g> <path d="M24.4895 36.093C24.246 36.0075 23.8443 35.8665 23.2477 36.0344C22.9163 35.8298 22.5961 35.7501 22.2842 35.75C21.9478 35.7499 21.6349 35.8428 21.3524 35.9411C21.2809 35.966 21.2115 35.991 21.1438 36.0153C20.6499 36.1927 20.2497 36.3365 19.7947 36.189C19.7 36.1583 19.596 36.1867 19.5301 36.2614C19.4642 36.3361 19.449 36.4429 19.4913 36.533C19.7585 37.1023 20.1293 37.5022 20.5206 37.8389C20.7084 38.0005 20.9032 38.1494 21.0914 38.2932L21.11 38.3074C21.3059 38.4571 21.4946 38.602 21.6742 38.7587C21.6956 38.7774 21.72 38.7923 21.7464 38.8027C22.6849 39.1753 23.5948 39.1419 24.4709 38.8036C24.4851 38.7981 24.4987 38.7914 24.5116 38.7834C24.8832 38.5553 25.2297 38.2296 25.5468 37.9316C25.6223 37.8606 25.6961 37.7912 25.7682 37.7251C26.1639 37.3621 26.5048 37.0992 26.8485 37.032C26.9405 37.0141 27.0147 36.9462 27.0408 36.8562C27.0668 36.7662 27.0404 36.6692 26.9722 36.6049C26.736 36.3818 26.5127 36.2399 26.2918 36.1582C26.0695 36.0759 25.8638 36.0598 25.672 36.0693C25.5396 36.0759 25.4025 36.0961 25.2784 36.1144C25.2329 36.1211 25.1891 36.1275 25.1479 36.133C24.9847 36.1545 24.8309 36.1658 24.6672 36.1451L24.6671 36.1451C24.6215 36.1393 24.5627 36.1186 24.4895 36.093ZM24.698 38.8521C24.6335 38.8116 24.554 38.8027 24.4821 38.828C23.5827 39.1447 22.6787 39.1913 21.745 38.8306C21.6714 38.8022 21.5888 38.8101 21.522 38.8521C19.4116 40.1769 18.0878 41.7814 17.5009 43.4384C16.9348 45.0366 17.0605 46.6677 17.8067 48.1069L16.8227 49.0963C16.7515 49.1679 16.7304 49.2754 16.7692 49.3686C16.8079 49.4619 16.899 49.5226 17 49.5226C18.0897 49.5226 19.0376 49.7004 20.0088 49.8832L20.0167 49.8847C20.9819 50.0664 21.9706 50.2525 23.1101 50.2525C24.2496 50.2525 25.2383 50.0664 26.2034 49.8847L26.2112 49.8832C27.1823 49.7004 28.1301 49.5226 29.2198 49.5226C29.3208 49.5226 29.4119 49.4619 29.4507 49.3686C29.4894 49.2754 29.4683 49.1679 29.3971 49.0963L28.4131 48.1069C29.1593 46.6677 29.285 45.0367 28.719 43.4385C28.1321 41.7815 26.8083 40.1769 24.698 38.8521Z" fill="url(#paint23_linear_3898_64225)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M19.7891 36.4819C20.1084 36.8995 21.3726 36.8909 21.7807 36.8864C21.9876 36.8842 22.2112 36.9024 22.4017 36.989C22.5225 37.0443 22.6 37.1157 22.7058 37.188C22.954 37.3572 23.266 37.2833 23.5365 37.212C23.9156 37.1121 24.2826 37.0225 24.6698 36.9557C25.3433 36.8392 26.0271 36.7851 26.7087 36.7742C26.7167 36.772 26.7248 36.7697 26.7323 36.7679C26.4011 36.4672 26.0809 36.3174 25.6198 36.3555C25.2759 36.3842 24.9854 36.4702 24.631 36.4252C24.4454 36.4017 24.2621 36.3083 24.0762 36.2665C23.7817 36.1991 23.5029 36.2433 23.2193 36.3386L23.2041 36.3438L23.1907 36.3345C22.8258 36.082 22.4547 35.9861 22.0157 36.0543C21.2474 36.1738 20.5957 36.6963 19.7893 36.4815L19.7891 36.4819Z" fill="url(#paint24_linear_3898_64225)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8391 38.5703C21.7216 38.7675 21.6572 38.9336 21.6465 39.0689C21.6495 39.0675 21.652 39.0655 21.6552 39.0638C22.0491 39.2161 22.4381 39.2998 22.8227 39.3251C23.4129 39.364 23.9929 39.2649 24.5654 39.0638C24.5423 38.907 24.4812 38.7423 24.3813 38.5705C23.8711 38.7673 23.3522 38.8539 22.8227 38.8079C22.4992 38.78 22.1717 38.7023 21.8391 38.5705V38.5703Z" fill="#F9F9F9"/> <path d="M24.6736 44.9743C24.834 45.1606 24.9141 45.3911 24.9141 45.6659C24.9141 46.113 24.7735 46.4622 24.4923 46.7137C24.2111 46.9651 23.8243 47.0908 23.3317 47.0908H23.1575V47.7752H22.8089V47.0908H21.4984V42.9138C21.4984 42.8254 21.4868 42.7602 21.4636 42.7182C21.4403 42.6764 21.4079 42.6344 21.3659 42.5926C21.3381 42.5694 21.3183 42.546 21.3066 42.5228C21.2949 42.4994 21.2938 42.4717 21.3031 42.439C21.3216 42.3506 21.3669 42.268 21.4391 42.1911C21.5111 42.1142 21.589 42.0757 21.6726 42.0757C21.7608 42.0757 21.8399 42.1177 21.9095 42.2014H22.8087V41.5098H23.1573V42.2014H23.3106C23.7612 42.2014 24.118 42.3201 24.3806 42.5577C24.643 42.7951 24.7743 43.1072 24.7743 43.4936C24.7743 43.7776 24.7092 43.9999 24.5791 44.1607C24.449 44.3214 24.2631 44.4436 24.0215 44.5273V44.5483C24.2955 44.6462 24.5128 44.7883 24.6732 44.9743H24.6736ZM22.1959 44.2619H22.8093V42.7671H22.1959V44.2619ZM22.8091 46.525V44.8346H22.1957V46.525H22.8091ZM23.1577 42.7671V44.2619H23.1646C23.4387 44.2619 23.6548 44.1979 23.8128 44.0699C23.9706 43.9419 24.0497 43.7499 24.0497 43.4936C24.0497 43.2748 23.9823 43.0989 23.8476 42.9663C23.7128 42.8335 23.5176 42.7671 23.2621 42.7671H23.1575H23.1577ZM23.9627 46.3086C24.1136 46.1643 24.1891 45.9501 24.1891 45.6659C24.1891 45.4006 24.1043 45.1957 23.9346 45.0512C23.765 44.907 23.5269 44.8346 23.2202 44.8346H23.1575V46.525H23.2969C23.5898 46.525 23.8114 46.4529 23.9625 46.3084L23.9627 46.3086Z" fill="white"/></g><defs> <linearGradient id="paint0_linear_3898_64225" x1="58.3158" y1="14.2986" x2="58.3158" y2="56.5217" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint1_linear_3898_64225" x1="57.9469" y1="11.7432" x2="57.9469" y2="30.5005" gradientUnits="userSpaceOnUse"> <stop stop-color="#F0BFC0"/> <stop offset="1" stop-color="#EA9C9F"/></linearGradient> <linearGradient id="paint2_linear_3898_64225" x1="96.6456" y1="32.2173" x2="97.6077" y2="50.9005" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint3_linear_3898_64225" x1="11.1643" y1="23.7217" x2="11.1643" y2="32.4198" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint4_linear_3898_64225" x1="9.93213" y1="29.7927" x2="35.3052" y2="32.8372" gradientUnits="userSpaceOnUse"> <stop stop-color="white"/> <stop offset="0.37" stop-color="#F2F2FF"/> <stop offset="1" stop-color="#D2D2FF"/></linearGradient> <linearGradient id="paint5_linear_3898_64225" x1="32.5734" y1="40.0301" x2="41.269" y2="44.0896" gradientUnits="userSpaceOnUse"> <stop stop-color="white"/> <stop offset="0.17" stop-color="#C5C5C5"/> <stop offset="0.4" stop-color="#808080"/> <stop offset="0.6" stop-color="#494949"/> <stop offset="0.78" stop-color="#212121"/> <stop offset="0.92" stop-color="#080808"/> <stop offset="1"/></linearGradient> <linearGradient id="paint6_linear_3898_64225" x1="39.3774" y1="37.8523" x2="39.3774" y2="48.0508" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint7_linear_3898_64225" x1="15.1821" y1="30.5846" x2="15.2216" y2="30.5894" gradientUnits="userSpaceOnUse"> <stop stop-color="#808080"/> <stop offset="0.64" stop-color="#51514C"/> <stop offset="1" stop-color="#3C3C35"/></linearGradient> <linearGradient id="paint8_linear_3898_64225" x1="11.2101" y1="23.5806" x2="11.5208" y2="23.6178" gradientUnits="userSpaceOnUse"> <stop stop-color="#808080"/> <stop offset="0.64" stop-color="#51514C"/> <stop offset="1" stop-color="#3C3C35"/></linearGradient> <linearGradient id="paint9_linear_3898_64225" x1="24.9424" y1="42.2324" x2="24.9424" y2="46.1247" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint10_linear_3898_64225" x1="9.54878" y1="12" x2="9.54878" y2="43.5539" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint11_linear_3898_64225" x1="9.14417" y1="23.416" x2="9.14417" y2="31.9651" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint12_linear_3898_64225" x1="88.3242" y1="11.2479" x2="82.1018" y2="18.2206" gradientUnits="userSpaceOnUse"> <stop stop-color="#F0BFC0"/> <stop offset="1" stop-color="#EA9C9F"/></linearGradient> <linearGradient id="paint13_linear_3898_64225" x1="65.0472" y1="41.6389" x2="65.0472" y2="48.6131" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint14_linear_3898_64225" x1="71.0526" y1="40.8525" x2="71.0526" y2="48.6146" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint15_linear_3898_64225" x1="75.5484" y1="41.8823" x2="75.5484" y2="48.6146" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint16_linear_3898_64225" x1="57.4157" y1="41.9424" x2="57.4157" y2="48.6125" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint17_linear_3898_64225" x1="51.4102" y1="41.1902" x2="51.4102" y2="48.6139" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint18_linear_3898_64225" x1="46.9145" y1="42.175" x2="46.9145" y2="48.6139" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint19_linear_3898_64225" x1="86.3006" y1="40.9431" x2="86.3006" y2="48.6124" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint20_linear_3898_64225" x1="30.0643" y1="42.3557" x2="30.0643" y2="48.6332" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint21_linear_3898_64225" x1="41.3392" y1="42.9629" x2="41.3392" y2="48.6332" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint22_linear_3898_64225" x1="35.5326" y1="40.8398" x2="35.5326" y2="48.6347" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint23_linear_3898_64225" x1="23.1099" y1="36" x2="23.1099" y2="50.0025" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint24_linear_3898_64225" x1="23.2607" y1="36.033" x2="23.2607" y2="37.2911" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <clipPath id="clip0_3898_64225"> <rect width="106" height="48" fill="white"/></clipPath> <clipPath id="clip1_3898_64225"> <rect width="15.1292" height="21.6131" fill="white" transform="matrix(-1 0 0 1 77.7109 27)"/></clipPath> <clipPath id="clip2_3898_64225"> <rect width="15.1292" height="20.5325" fill="white" transform="translate(44.752 28.0801)"/></clipPath> <clipPath id="clip3_3898_64225"> <rect width="15.1292" height="20.5325" fill="white" transform="translate(79.082 28.0801)"/></clipPath> <clipPath id="clip4_3898_64225"> <rect width="15.1292" height="21.6131" fill="white" transform="matrix(-1 0 0 1 43.1289 27.02)"/></clipPath></defs></svg>'))
                                      : Container(
                                          height: 48.h,
                                          width: 106.w,
                                          child: SvgPicture.string(
                                              '<svg width="106" height="48" viewBox="0 0 106 48" fill="none" xmlns="http://www.w3.org/2000/svg"> <g opacity="0.8" clip-path="url(#clip0_3696_28852)"> <path opacity="0.15" d="M58.316 67.8205C76.1637 67.8205 90.6321 53.3099 90.6321 35.4103C90.6321 17.5106 76.1637 3 58.316 3C40.4684 3 26 17.5106 26 35.4103C26 53.3099 40.4684 67.8205 58.316 67.8205Z" fill="#6A7165"/> <path opacity="0.5" d="M58.3158 56.5217C69.9415 56.5217 79.366 47.0698 79.366 35.4102C79.366 23.7506 69.9415 14.2986 58.3158 14.2986C46.6901 14.2986 37.2656 23.7506 37.2656 35.4102C37.2656 47.0698 46.6901 56.5217 58.3158 56.5217Z" fill="url(#paint0_linear_3696_28852)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M73 16.8974C66.8729 23.6784 58.0564 29.0157 47.431 31.1975C45.6162 28.1562 44.1371 25.0259 43 21.8609C52.2939 20.6059 59.9912 16.4166 65.2751 11C67.5698 13.177 70.1511 15.1616 73 16.8974Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M72.091 16.9951C66.1582 23.3457 57.7939 28.3476 47.7612 30.5005C46.1838 27.8427 44.8607 25.1152 43.8027 22.3576C52.7212 20.992 60.1474 16.9542 65.3593 11.7432C67.3877 13.6557 69.6368 15.4203 72.091 16.9951Z" fill="url(#paint1_linear_3696_28852)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M64.0395 15.7308C63.4026 16.2537 62.5677 16.47 61.7988 16.3763C58.5098 18.7708 54.6701 20.7483 50.3818 22.1078C50.1239 22.8074 49.4642 23.4085 48.4962 23.6597C47.5257 23.9134 46.5361 24.1357 45.5273 24.3257C46.1307 25.66 46.7976 26.9835 47.5257 28.2902C48.5933 28.057 49.6417 27.7913 50.6709 27.4908C51.6653 27.2035 52.7389 27.2275 53.6733 27.4956C58.3754 25.8139 62.5869 23.4674 66.2023 20.6653C66.2658 19.9741 66.6425 19.2697 67.301 18.7071C68.1755 17.9618 69.008 17.1877 69.8008 16.3871C68.6049 15.5144 67.463 14.5972 66.3798 13.6367C65.6385 14.3592 64.8564 15.0576 64.0383 15.7308H64.0395Z" fill="#F9F9F9"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M61.5438 20.2243C62.9028 21.6848 62.7781 23.7476 60.956 24.7778C59.1387 25.808 56.4782 25.2129 55.3195 23.5336C54.1583 21.8567 54.7641 19.9021 56.375 19.0823C57.9944 18.2661 60.1727 18.7565 61.5438 20.2243Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M53.1938 24.6832C53.6329 25.3443 53.273 26.1557 52.3398 26.4707C51.409 26.7856 50.2874 26.4526 49.8892 25.7566C49.4909 25.0606 49.9348 24.2949 50.8248 24.0148C51.7172 23.7359 52.7548 24.0232 53.1938 24.6832Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M65.9024 17.347C66.4746 17.8591 66.417 18.7282 65.7165 19.2824C65.0183 19.8378 63.964 19.8306 63.4266 19.2776C62.882 18.7246 63.0295 17.8712 63.6953 17.3591C64.3622 16.847 65.3254 16.8386 65.9012 17.3482L65.9024 17.347Z" fill="#FFC20E" fill-opacity="0.5"/> <path d="M59.8309 21.3224C60.0291 21.3836 60.1891 21.5126 60.311 21.7093C60.5093 22.0293 60.5628 22.3424 60.4716 22.6488C60.3803 22.9551 60.1571 23.2189 59.802 23.4403L59.6763 23.5186L59.9799 24.0085L59.7285 24.1652L59.425 23.6753L58.48 24.2643L56.6274 21.2747C56.5882 21.2114 56.5509 21.17 56.5156 21.1503C56.4802 21.1309 56.4382 21.1154 56.3894 21.1043C56.359 21.1002 56.3344 21.0924 56.3157 21.081C56.2969 21.0695 56.2838 21.0502 56.276 21.0226C56.2502 20.951 56.2462 20.8715 56.2641 20.7841C56.2819 20.6967 56.321 20.6341 56.3814 20.5965C56.4449 20.5569 56.5206 20.5514 56.6079 20.58L57.2563 20.1759L56.9495 19.6809L57.2009 19.5242L57.5076 20.0192L57.6181 19.9503C57.9431 19.7478 58.253 19.6724 58.5477 19.7244C58.8422 19.7764 59.0753 19.9408 59.2467 20.2174C59.3727 20.4206 59.4244 20.609 59.4018 20.7825C59.3793 20.956 59.2995 21.1271 59.1623 21.2955L59.1717 21.3105C59.4127 21.2574 59.6324 21.2615 59.8306 21.3226L59.8309 21.3224ZM57.7283 21.9261L58.1706 21.6504L57.5076 20.5805L57.0653 20.8562L57.7283 21.9261ZM59.1742 23.2702L58.4244 22.0604L57.9821 22.3361L58.7319 23.5459L59.1742 23.2702ZM57.7589 20.4239L58.4218 21.4938L58.4268 21.4907C58.6244 21.3675 58.7519 21.2246 58.8091 21.0619C58.8661 20.8994 58.8379 20.7264 58.7243 20.543C58.6273 20.3864 58.5006 20.2908 58.3447 20.2564C58.1885 20.222 58.0184 20.2622 57.8341 20.377L57.7587 20.424L57.7589 20.4239ZM59.9101 22.5969C59.9549 22.4258 59.9143 22.2385 59.7883 22.0351C59.6706 21.8452 59.5185 21.7367 59.3321 21.7095C59.1458 21.6825 58.942 21.7378 58.7209 21.8756L58.6757 21.9038L59.4254 23.1136L59.5259 23.051C59.7371 22.9194 59.865 22.7681 59.9098 22.5968L59.9101 22.5969Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M83.0931 43.4133C83.7992 42.426 84.8229 41.5883 85.0743 40.4006C85.3732 38.9969 84.4878 37.5832 84.6156 36.155C84.7772 34.3564 86.4334 33.1117 87.2843 31.5217C88.1195 29.9651 87.6674 29.4778 87.6586 27.7104C87.6488 25.048 89.6502 21.5817 92.5938 22.3325C95.5813 23.0955 95.2408 25.8593 94.8303 27.9488C94.5365 29.4362 94.7887 29.6354 95.2208 31.0869C95.7449 32.8415 97.2289 34.4858 96.7345 36.2487C96.429 37.3392 95.4173 38.1155 95.092 39.2012C94.7242 40.431 95.3116 41.7123 95.576 42.9675C96.1356 45.6118 95.6429 47.859 93.8238 50.1579C93.6017 50.3939 93.3685 50.6101 93.123 50.811C90.4441 50.8121 87.4567 50.8421 84.2082 50.8971C84.1613 50.8546 84.1156 50.8106 84.0692 50.7652C82.2021 48.9205 81.5047 45.6301 83.0938 43.4132L83.0931 43.4133Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.5243 24.8146C91.5629 24.6924 91.6946 24.6255 91.818 24.6655C91.9401 24.7056 92.0068 24.8376 91.9668 24.9599C91.7677 25.5734 91.6075 26.2132 91.4753 26.8783C91.5386 26.8149 91.6048 26.7528 91.6732 26.6914C91.9751 26.4209 92.3241 26.1806 92.7138 25.9708C92.8269 25.9085 92.9672 25.9527 93.0293 26.0653C93.0894 26.1796 93.046 26.3194 92.9335 26.3817C92.5741 26.5747 92.2573 26.7946 91.9822 27.0397C91.7225 27.2733 91.4985 27.5296 91.3113 27.8115C91.0871 29.2476 90.9715 30.7867 90.8789 32.4072C91.1262 31.973 91.3953 31.5825 91.6839 31.2345C92.1847 30.6287 92.7491 30.1484 93.3772 29.796C93.4895 29.7315 93.631 29.772 93.6939 29.8847C93.7559 29.9959 93.7177 30.1384 93.6053 30.2006C93.0293 30.5251 92.5086 30.9697 92.0417 31.5326C91.5789 32.0916 91.1706 32.7695 90.818 33.5626L90.795 34.0019C90.7454 34.9677 90.6936 35.9589 90.6261 36.9712C90.6266 36.9806 90.6264 36.99 90.6233 37.0003C90.5524 38.0577 90.4604 39.1393 90.3317 40.2403C90.7943 39.4365 91.2997 38.7232 91.8487 38.096C92.6515 37.1769 93.5464 36.4471 94.5341 35.9074C94.6465 35.8452 94.7875 35.8879 94.8496 35.9998C94.9103 36.1133 94.8691 36.2545 94.7566 36.316C93.8182 36.8315 92.9642 37.5265 92.1982 38.4024C91.4336 39.2775 90.7542 40.3329 90.1583 41.5673C89.947 43.0295 89.6584 44.521 89.251 46.036L89.2491 46.0412C89.0494 46.7844 88.8211 47.5334 88.5589 48.2856C89.2315 47.6448 89.95 47.0835 90.7188 46.6021C91.7894 45.9299 92.9475 45.4155 94.196 45.0579C94.3192 45.0219 94.4479 45.0935 94.4831 45.2177C94.5199 45.3418 94.4477 45.4708 94.3238 45.5069C93.1159 45.853 91.9944 46.3503 90.9652 46.9957C89.9464 47.6348 89.0133 48.4222 88.1642 49.356C87.9688 49.8578 87.7583 50.3619 87.5304 50.866L87.0155 50.8716C87.7803 49.2114 88.3558 47.5639 88.7945 45.9415C88.8228 44.8001 88.6306 43.6715 88.2164 42.5551C87.7965 41.4238 87.1492 40.2991 86.2721 39.1877C86.1932 39.0868 86.2102 38.9389 86.3095 38.8606C86.4102 38.7801 86.5563 38.7972 86.6367 38.8981C87.5419 40.05 88.2135 41.2133 88.6503 42.3916C88.8985 43.061 89.0718 43.735 89.1685 44.4108C89.7364 41.8526 89.9957 39.366 90.1578 36.9982C89.835 36.014 89.4876 35.186 89.1149 34.5128C88.7453 33.8424 88.3541 33.3302 87.9416 32.98C87.8448 32.8959 87.833 32.7495 87.9168 32.651C88 32.554 88.1468 32.5407 88.2429 32.6248C88.7008 33.0147 89.1266 33.5686 89.5225 34.2869C89.7694 34.734 90.0052 35.2462 90.2322 35.8255C90.2681 35.2001 90.2994 34.5836 90.3312 33.9765L90.3563 33.506C90.3558 33.4966 90.3562 33.49 90.3571 33.4806C90.5234 30.2637 90.7089 27.3363 91.5265 24.8145L91.5243 24.8146Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.0818 44.2783C91.8668 43.8005 92.8273 43.5207 93.3525 42.768C93.974 41.8785 93.7771 40.6307 94.2875 39.6745C94.9318 38.4688 96.4526 38.0936 97.5182 37.2392C98.5593 36.4006 98.3863 35.9293 98.9034 34.6947C99.6841 32.836 102.104 31.0167 103.933 32.413C105.788 33.8325 104.733 35.6568 103.83 36.9896C103.185 37.9381 103.301 38.1523 103.175 39.2915C103.021 40.6697 103.568 42.2565 102.701 43.3367C102.167 44.0059 101.233 44.2459 100.685 44.9056C100.066 45.6523 100.097 46.7196 99.9103 47.6736C99.6588 48.9576 99.1551 50.001 98.3169 50.864C95.9885 50.8239 93.2897 50.8173 90.2759 50.8385C89.9929 50.4916 89.7584 50.1047 89.5876 49.6891C88.8329 47.8501 89.3207 45.3503 91.0826 44.2783L91.0818 44.2783Z" fill="url(#paint2_linear_3696_28852)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M102.36 33.7733C102.423 33.6991 102.534 33.692 102.606 33.7563C102.68 33.8191 102.688 33.9303 102.625 34.0052C102.305 34.3722 102.003 34.7716 101.715 35.1956C101.778 35.1706 101.843 35.147 101.909 35.1248C102.198 35.0266 102.512 34.9612 102.846 34.9302C102.942 34.9209 103.027 34.9933 103.037 35.0913C103.045 35.1872 102.974 35.2727 102.878 35.282C102.569 35.3109 102.284 35.3691 102.019 35.4587C101.768 35.5426 101.538 35.6552 101.324 35.7965C100.742 36.7296 100.207 37.7674 99.6645 38.8679C99.9651 38.641 100.267 38.448 100.571 38.2904C101.1 38.0176 101.635 37.851 102.177 37.7913C102.273 37.7813 102.36 37.8521 102.37 37.9472C102.382 38.0443 102.311 38.1313 102.216 38.1434C101.719 38.1965 101.224 38.3517 100.734 38.605C100.245 38.8583 99.7614 39.2077 99.2813 39.6553L99.1348 39.9547C98.8158 40.6128 98.4861 41.2874 98.1403 41.9729C98.1363 41.9796 98.1338 41.9856 98.1305 41.9922C97.7682 42.7083 97.384 43.4335 96.9697 44.1624C97.5284 43.7403 98.0924 43.3934 98.6587 43.1202C99.4887 42.7182 100.329 42.4758 101.176 42.3916C101.274 42.3822 101.359 42.4546 101.369 42.5511C101.377 42.6471 101.306 42.734 101.211 42.7432C100.406 42.8231 99.6057 43.0555 98.8108 43.4375C98.0203 43.82 97.2361 44.3543 96.4545 45.0384C95.876 45.9923 95.2347 46.9465 94.5013 47.8794L94.4986 47.8839L94.4972 47.884C94.1398 48.342 93.7597 48.7953 93.3533 49.2421C94.012 48.9938 94.6786 48.8175 95.3567 48.7088C96.2994 48.5595 97.2591 48.5456 98.2337 48.6671C98.3311 48.6787 98.3992 48.7665 98.3883 48.8633C98.3766 48.9595 98.2876 49.0286 98.1902 49.0169C97.2454 48.899 96.3189 48.9127 95.4107 49.0579C94.5126 49.2012 93.6289 49.472 92.7626 49.8707C92.4445 50.1942 92.1147 50.5139 91.7689 50.83L91.2371 50.8321C92.3781 49.829 93.3558 48.7676 94.2106 47.6778C94.5678 46.8925 94.7688 46.0478 94.8095 45.1477C94.853 44.2337 94.7315 43.2579 94.4502 42.223C94.4251 42.1287 94.4801 42.0317 94.5742 42.0066C94.6683 41.9808 94.7651 42.0359 94.7902 42.1302C95.0827 43.2 95.2052 44.2105 95.1608 45.163C95.1356 45.7023 95.0567 46.222 94.9248 46.7227C96.0779 45.1085 96.9941 43.4536 97.8068 41.8518C97.8716 41.0721 97.8744 40.3911 97.8143 39.8105C97.7544 39.2335 97.6349 38.7624 97.4506 38.3953C97.4079 38.3084 97.4436 38.2016 97.531 38.1587C97.6169 38.1152 97.7228 38.1503 97.7663 38.2371C97.9685 38.6438 98.1025 39.1577 98.1652 39.7758C98.2059 40.1612 98.2176 40.5878 98.2055 41.0578C98.415 40.6334 98.6197 40.2137 98.8204 39.8L98.9774 39.4783C98.9793 39.4732 98.9833 39.4672 98.9852 39.462C100.052 37.2706 101.046 35.2851 102.362 33.7725L102.36 33.7733Z" fill="white"/> <path d="M14.8188 26.8811C14.4203 27.8318 13.9771 28.8077 13.4899 29.7905C13.0356 30.7041 12.5614 31.5837 12.0797 32.4198L7.50977 31.9482C8.37001 31.7845 9.63307 30.3817 10.6044 28.4343C11.6155 26.4057 11.9581 24.4602 11.4901 23.7217L14.8188 26.8811Z" fill="url(#paint3_linear_3696_28852)"/> <path d="M39.0415 37.853C39.0415 37.853 37.7257 43.3779 34.7527 46.1822C33.7497 45.6722 32.7659 45.2006 31.8194 44.7712C31.3607 44.5615 30.9132 44.3588 30.4707 44.1693C33.4437 41.3644 34.991 35.4756 34.991 35.4756C36.2454 36.262 37.5898 37.054 39.0421 37.853H39.0415Z" fill="#6A7165" fill-opacity="0.75"/> <path d="M35.2326 35.5383C35.2604 35.4325 35.2162 35.321 35.1235 35.2629C28.22 30.9381 24.2342 26.8562 21.9768 23.8653C20.8479 22.3696 20.1506 21.1459 19.7365 20.2998C19.5294 19.8767 19.3931 19.548 19.3091 19.3267C19.2671 19.2161 19.2381 19.1323 19.2199 19.0771C19.2108 19.0494 19.2044 19.0289 19.2004 19.0158L19.1961 19.0016L19.1952 18.9986L19.1952 18.9985L19.1951 18.9983C19.1627 18.8838 19.0541 18.808 18.9354 18.8171C18.8167 18.8263 18.7209 18.9179 18.7064 19.0361L18.6985 19.1009C18.6985 19.1011 18.6985 19.1014 18.6984 19.1016C18.2881 22.3246 17.0289 26.347 14.9934 30.4607C14.9839 30.4749 14.9753 30.4909 14.9683 30.5086C14.9681 30.509 14.9679 30.5095 14.9677 30.51C14.9618 30.5228 14.9564 30.5347 14.951 30.546C13.014 34.4516 10.7147 37.7381 8.50835 40.0049C8.43121 40.0841 8.41531 40.2047 8.46927 40.3012C8.52323 40.3978 8.63422 40.4474 8.74215 40.4232C10.461 40.0382 16.0077 39.2831 25.3912 42.4691L25.3919 42.4693C25.9353 42.6523 26.489 42.8489 27.0585 43.0655L27.0604 43.0662C28.1213 43.46 29.2251 43.9016 30.3711 44.3979C30.4628 44.4376 30.5693 44.4189 30.642 44.3503C32.1685 42.9104 33.3149 40.6964 34.0766 38.8658C34.459 37.9466 34.7476 37.116 34.9407 36.5152C35.0372 36.2147 35.11 35.9713 35.1587 35.8027C35.1831 35.7183 35.2014 35.6527 35.2137 35.6079L35.2277 35.5566L35.2313 35.5432L35.2322 35.5397L35.2325 35.5387L35.2326 35.5384C35.2326 35.5383 35.2326 35.5383 34.9908 35.4748L35.2326 35.5383ZM15.0555 30.396L15.055 30.3963L15.0555 30.396Z" fill="url(#paint4_linear_3696_28852)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/> <mask id="mask0_3696_28852" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="34" y="37" width="10" height="12"> <path style="mix-blend-mode:multiply" d="M43.3557 45.7092C41.8792 48.392 38.239 48.035 38.239 48.035C37.0429 47.3658 35.8835 46.7506 34.7539 46.1815C37.7269 43.3765 39.0427 37.8523 39.0427 37.8523C40.15 38.4555 41.3106 39.0649 42.5377 39.67C43.4203 40.3311 44.8385 43.0277 43.3557 45.7092Z" fill="url(#paint5_linear_3696_28852)"/></mask> <g mask="url(#mask0_3696_28852)"> <path d="M43.3557 45.7092C41.8792 48.392 38.239 48.035 38.239 48.035C37.0429 47.3658 35.8835 46.7506 34.7539 46.1815C37.7269 43.3765 39.0427 37.8523 39.0427 37.8523C40.15 38.4555 41.3106 39.0649 42.5377 39.67C43.4203 40.3311 44.8385 43.0277 43.3557 45.7092Z" fill="url(#paint6_linear_3696_28852)"/></g> <path d="M15.2138 30.5575L15.1797 30.6101L15.1911 30.6177L15.2252 30.5652L15.2138 30.5575Z" fill="url(#paint7_linear_3696_28852)"/> <path d="M11.2614 23.4582L11.2188 23.5039L11.4681 23.7431L11.5108 23.6974L11.2614 23.4582Z" fill="url(#paint8_linear_3696_28852)"/> <path d="M31.8206 44.7706L24.3776 55.1951C24.0002 55.719 23.2926 55.8713 22.7396 55.5389L18.6178 53.0979C17.9009 52.6723 17.7861 51.6643 18.3993 51.0894L23.6576 46.1249L27.1489 42.832C28.2139 43.2274 29.3224 43.6707 30.4719 44.1687C30.9151 44.3582 31.3626 44.5609 31.8206 44.7706Z" fill="#6A7165"/> <path d="M27.1465 42.8318L23.6553 46.1247L22.9757 45.587C22.6771 45.354 22.6579 44.9095 22.931 44.6489L23.2183 44.3718L24.9587 42.7216L25.4707 42.2324C26.0169 42.4163 26.5743 42.614 27.1465 42.8318Z" fill="url(#paint9_linear_3696_28852)"/> <path d="M24.9599 42.7219L23.2195 44.3727L22.9297 44.1713L24.675 42.5273L24.9599 42.7219Z" fill="#6A7165"/> <path d="M17.2603 12.2682C14.1551 10.6766 8.17928 16.3318 3.92211 24.8983C-0.340634 33.4642 -1.27288 41.692 1.83791 43.2849C3.51868 44.1487 6.04666 42.8838 8.6876 40.1796C10.9158 37.8904 13.2297 34.5805 15.1761 30.6555C15.1841 30.6385 15.1922 30.6208 15.2003 30.6032C15.2009 30.5969 15.2071 30.5982 15.2077 30.5919C17.2584 26.4522 18.5314 22.3949 18.9466 19.1329L18.9547 19.0668C19.3848 15.6291 18.8504 13.0779 17.2603 12.2682ZM13.4897 29.7904C13.0354 30.7039 12.5612 31.5835 12.0796 32.4196L7.50958 31.9481C8.36982 31.7844 9.63288 30.3816 10.6042 28.4342C11.6153 26.4056 11.9579 24.4601 11.4899 23.7215L14.8186 26.8809C14.4201 27.8316 13.9769 28.8076 13.4897 29.7904Z" fill="url(#paint10_linear_3696_28852)"/> <path d="M14.8187 26.8819L11.4901 23.7225C11.9581 24.461 11.6155 26.4065 10.6044 28.4352C9.63307 30.3826 8.37 31.7853 7.50976 31.949L12.0797 32.4206C8.54441 38.5486 4.43249 42.4277 2.34704 41.3605C-0.0232879 40.1479 1.11874 33.0005 4.89675 25.3998C8.68158 17.7934 13.6693 12.6111 16.039 13.83C18.1015 14.8884 17.5019 20.446 14.8187 26.8819Z" fill="#1A1818" fill-opacity="0.35"/> <path d="M10.6033 28.4345C9.63198 30.3819 8.36892 31.7846 7.50867 31.9483C7.3355 31.9823 7.18654 31.9641 7.05496 31.8986C6.24561 31.4856 6.52988 29.2693 7.68494 26.9454C8.84062 24.6222 10.4295 23.0695 11.2389 23.4826C11.3419 23.5323 11.4282 23.616 11.4884 23.7218C11.9564 24.4604 11.6138 26.4059 10.6027 28.4345H10.6033Z" fill="url(#paint11_linear_3696_28852)"/> <path d="M9.98637 28.1202C10.8726 26.3381 11.1737 24.68 10.6589 24.4165C10.1442 24.1531 9.00845 25.3842 8.12222 27.1662C7.236 28.9482 6.93488 30.6064 7.44965 30.8698C7.96442 31.1332 9.10015 29.9022 9.98637 28.1202Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M92.2309 18.1755C87.7051 18.665 82.6545 17.7219 77.973 15.0012C78.3022 13.2641 78.7861 11.6056 79.4088 10.0478C83.2876 12.6711 87.5415 13.6698 91.3018 13.4084C91.4372 14.9829 91.7431 16.5811 92.2309 18.1755Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M91.8591 17.9094C87.5481 18.303 82.7769 17.3854 78.3258 14.851C78.6167 13.3358 79.0255 11.8793 79.543 10.4997C83.3184 12.9569 87.4213 13.9218 91.0868 13.7131C91.2104 15.1005 91.4651 16.5062 91.8591 17.9094Z" fill="url(#paint12_linear_3696_28852)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M89.2754 14.7603C88.8651 14.7434 88.4824 14.5464 88.2268 14.2557C86.2092 14.0542 84.1244 13.5142 82.0766 12.5947C81.7491 12.7696 81.3045 12.7742 80.8607 12.5459C80.4151 12.3177 79.9728 12.0715 79.534 11.8067C79.3177 12.5047 79.1286 13.2198 78.9679 13.9489C79.4429 14.2171 79.9215 14.4667 80.4044 14.697C80.87 14.9207 81.2622 15.2867 81.5218 15.6974C83.8303 16.6345 86.1758 17.1606 88.4498 17.3187C88.702 17.0823 89.0753 16.945 89.5068 16.9543C90.0791 16.9674 90.6453 16.9558 91.2056 16.9211C91.0484 16.1981 90.9261 15.4765 90.84 14.7582C90.3248 14.781 89.8024 14.7811 89.2749 14.7599L89.2754 14.7603Z" fill="#F9F9F9"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M86.8591 15.6046C86.8827 16.601 86.1541 17.3283 85.1344 17.1062C84.1165 16.8858 83.3216 15.7791 83.4451 14.7679C83.5668 13.7567 84.4388 13.2297 85.3103 13.46C86.1837 13.6943 86.8334 14.6016 86.8591 15.6046Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M82.2745 14.4896C82.2195 14.882 81.8171 15.0647 81.3651 14.8717C80.9141 14.6795 80.6062 14.1824 80.6879 13.7906C80.7696 13.3987 81.1882 13.261 81.6125 13.4526C82.0374 13.6455 82.3291 14.0976 82.2745 14.4896Z" fill="#FFC20E" fill-opacity="0.5"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M89.4379 15.9834C89.4818 16.3646 89.173 16.6693 88.7286 16.6429C88.2848 16.6177 87.8942 16.2643 87.8768 15.8795C87.8567 15.4923 88.1939 15.2233 88.6113 15.2538C89.0292 15.2848 89.391 15.602 89.437 15.9835L89.4379 15.9834Z" fill="#FFC20E" fill-opacity="0.5"/> <path d="M85.8593 15.4443C85.913 15.5331 85.9299 15.6343 85.9103 15.7482C85.8784 15.9334 85.7948 16.0679 85.6595 16.1517C85.5242 16.2356 85.3538 16.2596 85.1483 16.224L85.0755 16.2114L85.0267 16.495L84.8812 16.4698L84.93 16.1862L84.3831 16.0914L84.6812 14.3611C84.6875 14.3245 84.6873 14.2966 84.6806 14.2776C84.6739 14.2585 84.6633 14.2388 84.6488 14.2184C84.6388 14.2068 84.6323 14.1957 84.629 14.1853C84.6258 14.1747 84.6273 14.1631 84.6335 14.1503C84.6476 14.115 84.6724 14.084 84.708 14.0574C84.7435 14.0308 84.7788 14.0205 84.8137 14.0265C84.8505 14.0329 84.8805 14.056 84.9036 14.0957L85.2789 14.1607L85.3282 13.8742L85.4737 13.8994L85.4244 14.1859L85.4883 14.197C85.6764 14.2296 85.8168 14.3046 85.9095 14.422C86.0021 14.5393 86.0346 14.6781 86.007 14.8382C85.9867 14.9558 85.9437 15.0432 85.8779 15.1004C85.8122 15.1576 85.7259 15.1948 85.6191 15.2119L85.6176 15.2206C85.725 15.281 85.8055 15.3556 85.8592 15.4443L85.8593 15.4443ZM84.8761 14.97L85.1321 15.0144L85.2387 14.3951L84.9827 14.3508L84.8761 14.97ZM84.9705 15.9518L85.0911 15.2516L84.8351 15.2072L84.7145 15.9075L84.9705 15.9518ZM85.3842 14.4203L85.2775 15.0396L85.2804 15.0401C85.3948 15.0599 85.4895 15.049 85.5646 15.0074C85.6396 14.9658 85.6863 14.8919 85.7046 14.7858C85.7202 14.6952 85.7046 14.6174 85.6579 14.5527C85.6111 14.488 85.5344 14.4464 85.4277 14.4279L85.3841 14.4203L85.3842 14.4203ZM85.4674 15.9456C85.5407 15.8968 85.5875 15.8135 85.6078 15.6958C85.6267 15.5859 85.6059 15.4948 85.5454 15.4227C85.4849 15.3507 85.3907 15.3035 85.2627 15.2813L85.2365 15.2768L85.1159 15.977L85.1741 15.9871C85.2963 16.0083 85.394 15.9945 85.4674 15.9455L85.4674 15.9456Z" fill="white"/> <path d="M28.371 12.7364C29.9121 11.1907 29.9121 8.68475 28.371 7.13911C26.83 5.59347 24.3314 5.59347 22.7903 7.13911C21.2493 8.68475 21.2493 11.1907 22.7903 12.7364C24.3314 14.282 26.83 14.282 28.371 12.7364Z" fill="#FFC20E" fill-opacity="0.5"/> <path d="M44.5722 19.9514C47.9798 19.9514 50.7421 17.1808 50.7421 13.7632C50.7421 10.3455 47.9798 7.57495 44.5722 7.57495C41.1647 7.57495 38.4023 10.3455 38.4023 13.7632C38.4023 17.1808 41.1647 19.9514 44.5722 19.9514Z" fill="#FFC20E" fill-opacity="0.5"/> <path d="M27.473 10.9564C27.4729 10.9564 27.4729 10.9564 27.4728 10.9563C27.425 10.8761 26.538 10.5909 26.2911 10.4227C26.286 10.4193 26.2856 10.412 26.2902 10.4081C26.6705 10.0838 26.9233 9.53921 26.9233 9.05836C26.9233 8.31509 26.3204 7.71069 25.5793 7.71069C24.8383 7.71069 24.2357 8.31537 24.2357 9.05836C24.2357 9.53956 24.4888 10.084 24.8689 10.4082C24.8735 10.4121 24.873 10.4192 24.8682 10.4227C24.6211 10.591 23.7329 10.8764 23.6858 10.9564C23.4601 11.34 23.7839 11.7687 24.2289 11.7687H27.1989C27.5085 11.7687 27.7339 11.4723 27.5996 11.1934C27.5609 11.113 27.5188 11.0341 27.4732 10.9565C27.4732 10.9564 27.4731 10.9564 27.473 10.9564Z" fill="white"/> <path d="M62.6568 7.0507C63.951 5.75268 63.951 3.64819 62.6568 2.35018C61.3627 1.05217 59.2644 1.05217 57.9702 2.35018C56.6761 3.64819 56.6761 5.75269 57.9702 7.0507C59.2644 8.34871 61.3627 8.34871 62.6568 7.0507Z" fill="#FFC20E" fill-opacity="0.5"/> <path d="M61.9054 5.5562C61.8656 5.48888 61.1203 5.24933 60.913 5.10792C60.9088 5.10497 60.9084 5.09904 60.9123 5.0957C61.2318 4.82337 61.444 4.3662 61.444 3.96219C61.444 3.33799 60.9379 2.83032 60.3155 2.83032C59.6932 2.83032 59.187 3.33799 59.187 3.96219C59.187 4.3662 59.3993 4.82336 59.7187 5.0957C59.7226 5.09904 59.7222 5.10496 59.718 5.10792C59.5104 5.24933 58.7646 5.48888 58.7251 5.5562C58.5357 5.87829 58.8076 6.23837 59.1812 6.23837H61.4494C61.8232 6.23837 62.0947 5.87844 61.9054 5.5562Z" fill="white"/> <path d="M47.5088 15.3307C47.4358 15.2066 46.0594 14.7642 45.6765 14.5032C45.6687 14.498 45.6681 14.4867 45.6752 14.4807C46.2648 13.9777 46.6569 13.1337 46.6569 12.3877C46.6569 11.2354 45.7221 10.2979 44.573 10.2979C43.4239 10.2979 42.4894 11.2351 42.4894 12.3877C42.4894 13.1337 42.8815 13.9777 43.4712 14.4807C43.4783 14.4867 43.4776 14.4979 43.4699 14.5032C43.0867 14.7642 41.7097 15.2066 41.6367 15.3307C41.2871 15.9255 41.7889 16.5902 42.4788 16.5902H46.667C47.3569 16.5902 47.8587 15.9253 47.5088 15.3307Z" fill="white"/> <path d="M29.8594 10.9832L38.3049 13.6397" stroke="#FFC20E" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M50.1949 11.1569L58.0452 6.75592" stroke="#FFC20E" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <g clip-path="url(#clip1_3696_28852)"> <path d="M76.3066 43.1653C75.5785 42.0147 74.4353 41.7035 74.4353 41.7035L71.6956 40.6658H69.0806L66.3409 41.7035C66.3409 41.7035 65.1977 42.0147 64.4696 43.1653H76.3066Z" fill="#FFEBDF"/> <path d="M66.8135 48.6131H62.5807C62.5807 47.8749 62.6275 46.8212 62.6275 46.8212C62.8768 42.3982 66.0301 41.7035 66.0301 41.7035C68.4893 41.0956 67.1492 44.9817 67.1492 44.9817C67.213 45.9181 67.077 47.1787 66.8121 48.6116L66.8135 48.6131Z" fill="url(#paint13_linear_3696_28852)"/> <path d="M68.5925 38.0417H72.1836V41.6181H68.5925V38.0417Z" fill="#FFEBDF"/> <path d="M68.5925 39.7047V38.0417H72.1836V38.8653C71.1594 39.7366 69.9255 39.8466 68.5925 39.7047Z" fill="#EAB894"/> <path d="M64.7686 48.6132C64.6992 46.6853 64.7205 46.1556 64.7375 45.497C64.7743 44.0323 65.0817 42.7934 66.0266 41.705C66.028 41.705 66.0308 41.705 66.0308 41.705L68.5906 40.8525C68.5906 40.8525 68.0523 41.815 70.4052 41.815C70.4052 41.815 71.955 41.7528 72.1816 40.8525L74.7811 41.705C74.7811 41.705 77.0037 42.2637 77.3848 44.6533L76.0787 47.1079L76.1254 48.6146H64.77L64.7686 48.6132Z" fill="url(#paint14_linear_3696_28852)"/> <path d="M66.1151 32.7142C66.1151 31.8646 66.1505 31.0165 66.1307 30.1669C66.1179 29.6241 66.0697 29.7645 66.6845 29.7052C67.5373 29.6227 67.8221 31.2408 67.8816 31.9413C67.9637 32.9067 67.7597 33.9459 67.6025 34.8867C67.5458 35.2268 67.5614 35.907 67.2129 36.0677C66.9154 36.2037 66.6038 35.868 66.448 35.619C66.0882 35.0473 66.1519 34.1717 66.1278 33.5233C66.1179 33.2541 66.1151 32.9849 66.1151 32.7157V32.7142Z" fill="#1A1818"/> <path d="M66.4808 37.544C66.8435 38.9942 67.4823 39.3951 67.4823 39.3951L68.3394 39.5659C68.8324 39.5022 69.6837 39.2967 69.6837 39.2967C71.6415 38.6165 72.2095 38.4326 72.5127 37.6728C72.5254 37.6409 74.0567 32.212 74.0567 32.212C74.3075 29.4172 71.4445 28.8368 71.0125 28.763C70.5847 28.6617 67.8592 27.8888 67.0545 30.5707C67.0545 30.5707 65.8646 33.6781 66.4808 37.544Z" fill="#FFEBDF"/> <path d="M74.3532 29.647C75.3051 30.1159 75.3377 30.9828 75.2924 31.9699C75.2584 32.7356 75.026 33.3869 74.7583 34.1294C74.4542 34.9727 74.1671 35.637 73.897 36.1223C73.897 36.1223 73.2666 37.1239 72.4549 37.8099C72.6901 36.8576 72.7567 36.147 72.9706 35.4146C73.2284 34.5361 73.1533 33.3608 72.7708 32.4591C72.7708 32.4591 72.5541 31.8585 71.6645 31.6877L74.3546 29.6455" fill="#1A1818"/> <path d="M72.788 35.2267C72.788 35.2267 73.2725 34.1499 74.0332 34.7881C74.0332 34.7881 74.3802 35.0646 73.9524 35.7854C73.9524 35.7854 73.3334 36.8216 72.5911 36.2471L72.788 35.2267Z" fill="#FFEBDF"/> <path d="M72.8659 32.7141C72.8659 32.7141 72.863 34.6434 72.516 35.6667L72.8843 35.7419C72.8843 35.7419 73.0783 34.4191 73.7441 34.4032L72.8659 32.7156V32.7141Z" fill="#1A1818"/> <path d="M65.6416 28.5833C62.5818 31.7674 71.842 31.7356 71.842 31.7356C71.842 31.7356 72.8138 32.1944 74.3508 29.6471C74.3508 29.6471 74.7658 27.6555 70.3872 27.0274C70.3872 27.0274 67.4931 26.6641 65.6416 28.5833Z" fill="#1A1818"/> <path d="M73.7544 48.6132H77.7109C77.7109 48.6132 77.6968 47.6536 77.6954 47.5856C77.6656 46.6419 77.6075 45.691 77.4404 44.7618C77.2024 43.4332 76.5706 42.2536 75.1795 41.9164C74.5434 41.7615 73.9768 42.1552 73.6326 42.6907C73.6326 42.6907 73.0036 43.4838 73.7544 48.6146V48.6132Z" fill="url(#paint15_linear_3696_28852)"/></g> <g clip-path="url(#clip2_3696_28852)"> <path d="M46.6348 31.203C46.6348 31.203 46.9861 30.8888 47.6547 31.4577L47.9664 30.9857C47.9664 30.9857 47.2156 30.4057 46.6348 31.203Z" fill="#1A1818"/> <path d="M47.8608 29.8228C47.8608 29.8228 47.4458 30.3266 48.0436 30.9011L47.6398 31.2997C47.6398 31.2997 46.7148 30.1923 47.8608 29.8241V29.8228Z" fill="#1A1818"/> <path d="M46.1562 43.4035C46.8844 42.3031 48.0276 42.0054 48.0276 42.0054L50.7672 41.0129H53.3823L56.122 42.0054C56.122 42.0054 57.2651 42.3031 57.9933 43.4035H46.1562Z" fill="#FFEBDF"/> <path d="M55.6494 48.6125H59.8822C59.8822 47.9065 59.8354 46.8988 59.8354 46.8988C59.5861 42.6685 56.4328 42.0041 56.4328 42.0041C53.9736 41.4227 55.3137 45.1394 55.3137 45.1394C55.2499 46.035 55.3859 47.2407 55.6508 48.6111L55.6494 48.6125Z" fill="url(#paint16_linear_3696_28852)"/> <path d="M53.8704 38.5022H50.2793V41.9227H53.8704V38.5022Z" fill="#FFEBDF"/> <path d="M53.8704 40.0927V38.5022H50.2793V39.2898C51.3035 40.1231 52.5373 40.2283 53.8704 40.0927Z" fill="#EAB894"/> <path d="M57.6943 48.6125C57.7637 46.7687 57.7424 46.262 57.7254 45.6322C57.6886 44.2314 57.3812 43.0465 56.4363 42.0055C56.4349 42.0055 56.4321 42.0055 56.4321 42.0055L53.8723 41.1902C53.8723 41.1902 54.4106 42.1107 52.0577 42.1107C52.0577 42.1107 50.5079 42.0512 50.2813 41.1902L47.6818 42.0055C47.6818 42.0055 45.4592 42.5398 45.0781 44.8252L46.3842 47.1729L46.3375 48.6139H57.6929L57.6943 48.6125Z" fill="url(#paint17_linear_3696_28852)"/> <path d="M56.6633 34.2262C56.7653 33.4399 56.5939 32.6855 56.6024 31.9103C56.6081 31.3899 56.5047 30.605 55.8417 30.5385C54.9238 30.4472 54.6404 31.9948 54.5866 32.6661C54.5115 33.5908 54.7467 34.5902 54.9294 35.4955C54.9946 35.8222 54.9889 36.4756 55.3643 36.6334C55.6859 36.7676 56.016 36.4506 56.1803 36.2139C56.5769 35.6436 56.5783 34.8851 56.6619 34.2262H56.6633Z" fill="#1A1818"/> <path d="M55.9821 38.0259C55.6194 39.4129 54.9805 39.7963 54.9805 39.7963L54.1235 39.9596C53.6305 39.8987 52.7792 39.7022 52.7792 39.7022C50.8214 39.0516 50.2534 38.8758 49.9502 38.1491C49.9375 38.1186 48.4062 32.9263 48.4062 32.9263C48.1554 30.2533 51.0183 29.6983 51.4504 29.6277C51.8782 29.5308 54.6037 28.7916 55.4084 31.3566C55.4084 31.3566 56.5983 34.3286 55.9821 38.0259Z" fill="#FFEBDF"/> <path d="M56.8807 28.7707C57.0351 29.5998 56.8906 30.7487 56.2333 31.2789C55.5193 31.8534 54.9059 31.6969 54.0461 31.8838C53.1522 32.0776 52.2371 32.1551 51.339 32.324C50.71 32.443 49.9833 32.5109 49.6943 33.1628C49.3118 34.0252 49.2368 35.1492 49.4946 35.9895C49.7085 36.6885 49.7751 37.3696 50.0102 38.2804C49.1985 37.6243 48.5681 36.6664 48.5681 36.6664C48.298 36.2022 48.0109 35.5668 47.7068 34.7603C47.4391 34.0501 47.2082 33.4272 47.1728 32.695C47.1289 31.7509 47.5963 31.3398 48.112 30.4733C48.5965 29.6593 49.2764 29.0946 50.1632 28.8371C50.1632 28.8371 54.6566 29.0835 54.5731 28.0813C54.5731 28.0813 55.6525 28.3512 55.4471 29.0517C55.4471 29.0517 56.5761 28.8496 56.3367 28.0813C56.3367 28.0813 56.7928 28.2917 56.8835 28.772L56.8807 28.7707Z" fill="#1A1818"/> <path d="M49.6749 35.8096C49.6749 35.8096 49.1904 34.7797 48.4297 35.3902C48.4297 35.3902 48.0827 35.6546 48.5105 36.3439C48.5105 36.3439 49.1295 37.335 49.8718 36.7855L49.6749 35.8096Z" fill="#FFEBDF"/> <path d="M49.597 33.4067C49.597 33.4067 49.5999 35.2519 49.9469 36.2306L49.5786 36.3026C49.5786 36.3026 49.3845 35.0374 48.7188 35.0221L49.597 33.4081V33.4067Z" fill="#1A1818"/> <path d="M48.7085 48.6125H44.752C44.752 48.6125 44.7661 47.6947 44.7675 47.6297C44.7973 46.7271 44.8554 45.8177 45.0225 44.929C45.2605 43.6583 45.8923 42.5301 47.2834 42.2076C47.9194 42.0595 48.4861 42.436 48.8303 42.9482C48.8303 42.9482 49.4593 43.7067 48.7085 48.6139V48.6125Z" fill="url(#paint18_linear_3696_28852)"/></g> <g clip-path="url(#clip3_3696_28852)"> <path d="M83.9875 28.3726C83.9875 28.3726 80.5937 29.3625 80.9139 34.5761V45.8138H84.7748L83.9875 28.3726Z" fill="#1A1818"/> <path d="M83.6016 31.7417V47.8853H90.9842V34.2864C90.9842 34.2864 91.4689 29.07 87.9412 28.4289C87.9412 28.4289 83.6016 27.6663 83.6016 31.7417Z" fill="#1A1818"/> <path d="M88.5488 42.0786H83.416V44.5442H88.5488V42.0786Z" fill="#FFEBDF"/> <path d="M94.2102 48.6125H90.7394L90.1194 46.1808C90.1194 46.1808 89.6144 42.4895 92.2237 42.4373C92.2237 42.4373 93.5772 43.358 93.9468 45.5256C93.9468 45.5256 94.2393 47.2569 94.2117 48.6125H94.2102Z" fill="#FFEBDF"/> <path d="M82.1525 48.6126L82.6211 46.7782C82.6211 46.7782 83.4506 42.6816 80.8413 42.6294C80.8413 42.6294 78.9523 44.1404 79.0891 48.6126H82.1525Z" fill="#FFEBDF"/> <path d="M87.9421 37.0994H84.7754V41.5984H87.9421V37.0994Z" fill="#FFEBDF"/> <path d="M87.9421 38.9364V37.1006H84.7754V39.7455C85.8203 39.7144 87.0602 39.5309 87.9421 38.9364Z" fill="#EAB894"/> <path d="M80.5762 43.0346C81.2864 41.967 82.404 41.679 82.404 41.679L85.0803 40.7173H87.6343L90.9073 41.679C90.9073 41.679 92.0249 41.9684 92.7351 43.0346H80.5762Z" fill="#FFEBDF"/> <path d="M81.2318 46.6905V48.6124H91.512L91.3912 46.3798C91.2631 42.0785 91.7594 42.0771 91.7594 42.0771C91.2966 41.7777 90.9095 41.6774 90.9095 41.6774L88.3322 40.9657C88.3322 40.9657 87.7733 43.2971 85.6472 43.2971C85.6472 43.2971 83.9998 42.4357 84.4465 40.9431L82.4048 41.6774C82.4048 41.6774 81.5956 41.9175 80.8418 42.6278C80.8418 42.6278 81.2056 42.8439 81.2318 46.6891V46.6905Z" fill="url(#paint19_linear_3696_28852)"/> <path d="M88.2873 37.4581C88.1796 37.701 88.0283 37.9255 87.8173 38.1232C87.2919 38.6146 86.6763 39.0128 86.0011 39.2811C85.173 39.6116 83.9229 39.6525 83.2637 38.9917C82.7296 38.455 82.4371 37.6431 82.2057 36.9455C81.756 35.5898 81.7618 34.1099 81.8753 32.7034C81.9015 32.3829 81.935 32.0609 81.9903 31.7432C82.5069 28.6746 85.2137 28.6802 85.2137 28.6802C87.257 28.5941 89.1226 29.5303 89.437 31.6203C89.5578 32.421 89.3992 33.2796 89.3569 34.0802C89.3264 34.6677 89.2522 35.2551 89.0586 35.8158C88.8607 36.3877 88.5347 36.9045 88.2888 37.4567L88.2873 37.4581Z" fill="#FFEBDF"/> <path d="M88.5522 35.1183C88.5522 35.1183 88.8229 34.0267 89.6874 34.4772C89.6874 34.4772 90.0774 34.6692 89.7965 35.419C89.7965 35.419 89.3861 36.4979 88.5508 36.1039V35.1183H88.5522Z" fill="#FFEBDF"/> <path d="M81.6717 32.0949C81.5262 27.8811 85.6461 28.0816 85.6461 28.0816C89.6962 28.2426 89.8985 30.0233 89.8985 30.0233C90.3889 32.2827 89.6875 34.4772 89.6875 34.4772C88.8259 34.295 88.6804 35.412 88.6804 35.412C88.7706 33.6214 88.6193 33.5127 88.6193 33.5127C87.3052 33.5 86.3578 33.363 86.3578 33.363L86.2996 32.1147L85.9823 33.298C85.9823 33.298 84.1195 33.2571 82.1855 32.8122C82.1855 32.8122 81.6703 32.8476 81.6703 34.3684V32.0949H81.6717Z" fill="#1A1818"/></g> <g clip-path="url(#clip4_3696_28852)"> <path d="M34.3206 40.4408C34.99 40.2011 35.4119 39.6484 36.2079 39.7173C37.6223 39.8408 38.6249 41.4084 39.9869 40.3446C41.6342 39.0556 41.202 31.3052 40.4875 29.5496C39.8822 28.0609 38.7646 29.5166 37.1727 29.0156C35.3217 28.4342 33.7254 29.8754 32.7679 31.3712C31.7726 32.9288 31.259 34.9127 31.4059 36.7415C31.5413 38.4297 31.8512 41.3251 34.322 40.4394L34.3206 40.4408Z" fill="#1A1818"/> <path d="M28.0011 48.6332H31.4716L32.0915 46.1613C32.0915 46.1613 32.5965 42.4088 29.9874 42.3557C29.9874 42.3557 28.6341 43.2917 28.2645 45.4952C28.2645 45.4952 27.972 47.2551 27.9997 48.6332H28.0011Z" fill="url(#paint20_linear_3696_28852)"/> <path d="M40.0573 48.6332L39.5888 46.7684C39.5888 46.7684 39.0839 43.016 41.6929 42.9629C41.6929 42.9629 43.2572 44.0869 43.1204 48.6332H40.0573Z" fill="url(#paint21_linear_3696_28852)"/> <path d="M34.2691 36.9294H37.4355V41.503H34.2691V36.9294Z" fill="#FFEBDF"/> <path d="M34.2691 38.7971V36.9309H37.4355V39.6196C36.3908 39.588 35.151 39.4014 34.2691 38.7971Z" fill="#EAB894"/> <path d="M41.6328 42.9629C40.9227 41.8776 39.8051 41.5848 39.8051 41.5848L37.1291 40.6072H34.5753L31.3027 41.5848C31.3027 41.5848 30.1852 41.879 29.475 42.9629H41.6328Z" fill="#FFEBDF"/> <path d="M40.9778 48.6333H30.4323L30.4993 46.6795L28.9481 44.3655C29.293 42.1117 31.3011 41.5849 31.3011 41.5849L33.8781 40.8614C33.8781 40.8614 33.7239 41.6897 35.8513 41.6897C35.8513 41.6897 37.5597 41.6897 37.7634 40.8398L39.805 41.5863C39.805 41.5863 41.3008 42.0371 42.1172 43.5286L40.9764 46.681V48.6347L40.9778 48.6333Z" fill="url(#paint22_linear_3696_28852)"/> <path d="M33.9243 37.2939C34.0319 37.5408 34.1833 37.7691 34.3943 37.97C34.9196 38.4696 35.5351 38.8744 36.2103 39.1472C37.0383 39.4831 38.2882 39.5247 38.9474 38.8529C39.4815 38.3074 39.7739 37.482 40.0053 36.7728C40.455 35.3947 40.4491 33.8903 40.3356 32.4605C40.3094 32.1347 40.276 31.8074 40.2207 31.4844C39.7056 28.365 36.999 28.3708 36.999 28.3708C34.9559 28.2832 33.0905 29.2349 32.7761 31.3595C32.6554 32.1734 32.814 33.0462 32.8562 33.8602C32.8867 34.4573 32.9609 35.0545 33.1545 35.6244C33.3524 36.2058 33.6783 36.7312 33.9243 37.2925V37.2939Z" fill="#FFEBDF"/> <path d="M40.5517 34.2994C41.3156 31.9667 40.6987 29.2234 39.7863 28.365C37.2005 25.9318 32.4247 26.8577 30.9666 30.6618C30.5155 31.836 30.728 36.2187 30.9317 37.5652C31.2053 39.3696 31.8775 40.6056 33.2658 40.6056C33.2294 39.1184 33.3443 37.469 33.2105 35.6502C33.078 33.8457 33.3211 33.8012 34.8431 32.9615C35.5998 32.5437 36.0407 32.1073 36.5107 31.4298C37.0928 30.59 36.6548 30.2972 37.8073 30.3345C37.8073 30.3345 40.4848 30.244 40.5502 34.2979L40.5517 34.2994Z" fill="#1A1818"/> <path d="M33.6587 34.9153C33.6587 34.9153 33.388 33.8057 32.5237 34.2636C32.5237 34.2636 32.1337 34.4589 32.4146 35.2211C32.4146 35.2211 32.8249 36.3178 33.6602 35.9173V34.9153H33.6587Z" fill="#FFEBDF"/></g> <path d="M24.4895 36.093C24.246 36.0075 23.8443 35.8665 23.2477 36.0344C22.9163 35.8298 22.5961 35.7501 22.2842 35.75C21.9478 35.7499 21.6349 35.8428 21.3524 35.9411C21.2809 35.966 21.2115 35.991 21.1438 36.0153C20.6499 36.1927 20.2497 36.3365 19.7947 36.189C19.7 36.1583 19.596 36.1867 19.5301 36.2614C19.4642 36.3361 19.449 36.4429 19.4913 36.533C19.7585 37.1023 20.1293 37.5022 20.5206 37.8389C20.7084 38.0005 20.9032 38.1494 21.0914 38.2932L21.11 38.3074C21.3059 38.4571 21.4946 38.602 21.6742 38.7587C21.6956 38.7774 21.72 38.7923 21.7464 38.8027C22.6849 39.1753 23.5948 39.1419 24.4709 38.8036C24.4851 38.7981 24.4987 38.7914 24.5116 38.7834C24.8832 38.5553 25.2297 38.2296 25.5468 37.9316C25.6223 37.8606 25.6961 37.7912 25.7682 37.7251C26.1639 37.3621 26.5048 37.0992 26.8485 37.032C26.9405 37.0141 27.0147 36.9462 27.0408 36.8562C27.0668 36.7662 27.0404 36.6692 26.9722 36.6049C26.736 36.3818 26.5127 36.2399 26.2918 36.1582C26.0695 36.0759 25.8638 36.0598 25.672 36.0693C25.5396 36.0759 25.4025 36.0961 25.2784 36.1144C25.2329 36.1211 25.1891 36.1275 25.1479 36.133C24.9847 36.1545 24.8309 36.1658 24.6672 36.1451L24.6671 36.1451C24.6215 36.1393 24.5627 36.1186 24.4895 36.093ZM24.698 38.8521C24.6335 38.8116 24.554 38.8027 24.4821 38.828C23.5827 39.1447 22.6787 39.1913 21.745 38.8306C21.6714 38.8022 21.5888 38.8101 21.522 38.8521C19.4116 40.1769 18.0878 41.7814 17.5009 43.4384C16.9348 45.0366 17.0605 46.6677 17.8067 48.1069L16.8227 49.0963C16.7515 49.1679 16.7304 49.2754 16.7692 49.3686C16.8079 49.4619 16.899 49.5226 17 49.5226C18.0897 49.5226 19.0376 49.7004 20.0088 49.8832L20.0167 49.8847C20.9819 50.0664 21.9706 50.2525 23.1101 50.2525C24.2496 50.2525 25.2383 50.0664 26.2034 49.8847L26.2112 49.8832C27.1823 49.7004 28.1301 49.5226 29.2198 49.5226C29.3208 49.5226 29.4119 49.4619 29.4507 49.3686C29.4894 49.2754 29.4683 49.1679 29.3971 49.0963L28.4131 48.1069C29.1593 46.6677 29.285 45.0367 28.719 43.4385C28.1321 41.7815 26.8083 40.1769 24.698 38.8521Z" fill="url(#paint23_linear_3696_28852)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M19.7891 36.4819C20.1084 36.8995 21.3726 36.8909 21.7807 36.8864C21.9876 36.8842 22.2112 36.9024 22.4017 36.989C22.5225 37.0443 22.6 37.1157 22.7058 37.188C22.954 37.3572 23.266 37.2833 23.5365 37.212C23.9156 37.1121 24.2826 37.0225 24.6698 36.9557C25.3433 36.8392 26.0271 36.7851 26.7087 36.7742C26.7167 36.772 26.7248 36.7697 26.7323 36.7679C26.4011 36.4672 26.0809 36.3174 25.6198 36.3555C25.2759 36.3842 24.9854 36.4702 24.631 36.4252C24.4454 36.4017 24.2621 36.3083 24.0762 36.2665C23.7817 36.1991 23.5029 36.2433 23.2193 36.3386L23.2041 36.3438L23.1907 36.3345C22.8258 36.082 22.4547 35.9861 22.0157 36.0543C21.2474 36.1738 20.5957 36.6963 19.7893 36.4815L19.7891 36.4819Z" fill="url(#paint24_linear_3696_28852)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8391 38.5703C21.7216 38.7675 21.6572 38.9336 21.6465 39.0689C21.6495 39.0675 21.652 39.0655 21.6552 39.0638C22.0491 39.2161 22.4381 39.2998 22.8227 39.3251C23.4129 39.364 23.9929 39.2649 24.5654 39.0638C24.5423 38.907 24.4812 38.7423 24.3813 38.5705C23.8711 38.7673 23.3522 38.8539 22.8227 38.8079C22.4992 38.78 22.1717 38.7023 21.8391 38.5705V38.5703Z" fill="#F9F9F9"/> <path d="M24.6736 44.9743C24.834 45.1606 24.9141 45.3911 24.9141 45.6659C24.9141 46.113 24.7735 46.4622 24.4923 46.7137C24.2111 46.9651 23.8243 47.0908 23.3317 47.0908H23.1575V47.7752H22.8089V47.0908H21.4984V42.9138C21.4984 42.8254 21.4868 42.7602 21.4636 42.7182C21.4403 42.6764 21.4079 42.6344 21.3659 42.5926C21.3381 42.5694 21.3183 42.546 21.3066 42.5228C21.2949 42.4994 21.2938 42.4717 21.3031 42.439C21.3216 42.3506 21.3669 42.268 21.4391 42.1911C21.5111 42.1142 21.589 42.0757 21.6726 42.0757C21.7608 42.0757 21.8399 42.1177 21.9095 42.2014H22.8087V41.5098H23.1573V42.2014H23.3106C23.7612 42.2014 24.118 42.3201 24.3806 42.5577C24.643 42.7951 24.7743 43.1072 24.7743 43.4936C24.7743 43.7776 24.7092 43.9999 24.5791 44.1607C24.449 44.3214 24.2631 44.4436 24.0215 44.5273V44.5483C24.2955 44.6462 24.5128 44.7883 24.6732 44.9743H24.6736ZM22.1959 44.2619H22.8093V42.7671H22.1959V44.2619ZM22.8091 46.525V44.8346H22.1957V46.525H22.8091ZM23.1577 42.7671V44.2619H23.1646C23.4387 44.2619 23.6548 44.1979 23.8128 44.0699C23.9706 43.9419 24.0497 43.7499 24.0497 43.4936C24.0497 43.2748 23.9823 43.0989 23.8476 42.9663C23.7128 42.8335 23.5176 42.7671 23.2621 42.7671H23.1575H23.1577ZM23.9627 46.3086C24.1136 46.1643 24.1891 45.9501 24.1891 45.6659C24.1891 45.4006 24.1043 45.1957 23.9346 45.0512C23.765 44.907 23.5269 44.8346 23.2202 44.8346H23.1575V46.525H23.2969C23.5898 46.525 23.8114 46.4529 23.9625 46.3084L23.9627 46.3086Z" fill="white"/></g><defs> <linearGradient id="paint0_linear_3696_28852" x1="58.3158" y1="14.2986" x2="58.3158" y2="56.5217" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint1_linear_3696_28852" x1="57.9469" y1="11.7432" x2="57.9469" y2="30.5005" gradientUnits="userSpaceOnUse"> <stop stop-color="#F0E2BF"/> <stop offset="1" stop-color="#EAD99C"/></linearGradient> <linearGradient id="paint2_linear_3696_28852" x1="96.6456" y1="32.2173" x2="97.6077" y2="50.9005" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint3_linear_3696_28852" x1="11.1643" y1="23.7217" x2="11.1643" y2="32.4198" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint4_linear_3696_28852" x1="9.93213" y1="29.7927" x2="35.3052" y2="32.8372" gradientUnits="userSpaceOnUse"> <stop stop-color="white"/> <stop offset="0.37" stop-color="#F2F2FF"/> <stop offset="1" stop-color="#D2D2FF"/></linearGradient> <linearGradient id="paint5_linear_3696_28852" x1="32.5734" y1="40.0301" x2="41.269" y2="44.0896" gradientUnits="userSpaceOnUse"> <stop stop-color="white"/> <stop offset="0.17" stop-color="#C5C5C5"/> <stop offset="0.4" stop-color="#808080"/> <stop offset="0.6" stop-color="#494949"/> <stop offset="0.78" stop-color="#212121"/> <stop offset="0.92" stop-color="#080808"/> <stop offset="1"/></linearGradient> <linearGradient id="paint6_linear_3696_28852" x1="39.3774" y1="37.8523" x2="39.3774" y2="48.0508" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint7_linear_3696_28852" x1="15.1821" y1="30.5846" x2="15.2216" y2="30.5894" gradientUnits="userSpaceOnUse"> <stop stop-color="#808080"/> <stop offset="0.64" stop-color="#51514C"/> <stop offset="1" stop-color="#3C3C35"/></linearGradient> <linearGradient id="paint8_linear_3696_28852" x1="11.2101" y1="23.5806" x2="11.5208" y2="23.6178" gradientUnits="userSpaceOnUse"> <stop stop-color="#808080"/> <stop offset="0.64" stop-color="#51514C"/> <stop offset="1" stop-color="#3C3C35"/></linearGradient> <linearGradient id="paint9_linear_3696_28852" x1="24.9424" y1="42.2324" x2="24.9424" y2="46.1247" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint10_linear_3696_28852" x1="9.54878" y1="12" x2="9.54878" y2="43.5539" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint11_linear_3696_28852" x1="9.14417" y1="23.416" x2="9.14417" y2="31.9651" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint12_linear_3696_28852" x1="88.3242" y1="11.2479" x2="82.1018" y2="18.2206" gradientUnits="userSpaceOnUse"> <stop stop-color="#F0E2BF"/> <stop offset="1" stop-color="#EAD99C"/></linearGradient> <linearGradient id="paint13_linear_3696_28852" x1="65.0472" y1="41.6389" x2="65.0472" y2="48.6131" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint14_linear_3696_28852" x1="71.0526" y1="40.8525" x2="71.0526" y2="48.6146" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint15_linear_3696_28852" x1="75.5484" y1="41.8823" x2="75.5484" y2="48.6146" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint16_linear_3696_28852" x1="57.4157" y1="41.9424" x2="57.4157" y2="48.6125" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint17_linear_3696_28852" x1="51.4102" y1="41.1902" x2="51.4102" y2="48.6139" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint18_linear_3696_28852" x1="46.9145" y1="42.175" x2="46.9145" y2="48.6139" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint19_linear_3696_28852" x1="86.3006" y1="40.9431" x2="86.3006" y2="48.6124" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint20_linear_3696_28852" x1="30.0643" y1="42.3557" x2="30.0643" y2="48.6332" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint21_linear_3696_28852" x1="41.3392" y1="42.9629" x2="41.3392" y2="48.6332" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint22_linear_3696_28852" x1="35.5326" y1="40.8398" x2="35.5326" y2="48.6347" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint23_linear_3696_28852" x1="23.1099" y1="36" x2="23.1099" y2="50.0025" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint24_linear_3696_28852" x1="23.2607" y1="36.033" x2="23.2607" y2="37.2911" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <clipPath id="clip0_3696_28852"> <rect width="106" height="48" fill="white"/></clipPath> <clipPath id="clip1_3696_28852"> <rect width="15.1292" height="21.6131" fill="white" transform="matrix(-1 0 0 1 77.7109 27)"/></clipPath> <clipPath id="clip2_3696_28852"> <rect width="15.1292" height="20.5325" fill="white" transform="translate(44.752 28.0801)"/></clipPath> <clipPath id="clip3_3696_28852"> <rect width="15.1292" height="20.5325" fill="white" transform="translate(79.082 28.0801)"/></clipPath> <clipPath id="clip4_3696_28852"> <rect width="15.1292" height="21.6131" fill="white" transform="matrix(-1 0 0 1 43.1289 27.02)"/></clipPath></defs></svg>')),
                            )
                          ]),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 106.h),
                    child: Container(
                      height: Get.height,
                      width: 375.w,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              spreadRadius: 0,
                              blurRadius: 40,
                              offset: const Offset(0, 4), // changes position of shadow
                            ),
                          ]),
                      child: Padding(
                        padding: EdgeInsets.only(
                          top: 20.h,
                          left: 24.w,
                          right: 24.w,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(bottom: 16.h),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    appConfigService.countryConfigCollection == "aam"
                                        ? Container(
                                            child: SvgPicture.string(
                                                '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3430_45422)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#995DFE" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3430_45422"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'))
                                        : appConfigService.countryConfigCollection == "rafco"
                                            ? Container(
                                                child: SvgPicture.string(
                                                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_66556)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#22409A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#22409A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#22409A" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3898_66556"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'))
                                            : Container(
                                                child: SvgPicture.string(
                                                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_31713)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#6A7165" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#6A7165" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#6A7165" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_31713"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Expanded(
                                      child: Text(
                                        mrAccumulate.tr,
                                        style: TextStyle(
                                          //overflow: TextOverflow.visible,
                                          color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                          fontSize: configTheme().textTheme.bodySmall?.fontSize,
                                          fontFamily: configTheme().textTheme.bodySmall?.fontFamily,
                                          fontWeight: configTheme().textTheme.bodySmall?.fontWeight,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Text(
                                mrWhat.tr,
                                style: TextStyle(
                                  color: configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme().textTheme.headlineSmall?.fontSize,
                                  // fontFamily: configTheme().textTheme.headlineSmall?.fontFamily,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(
                                height: 16.h,
                              ),
                              Text(
                                mrRecommend.tr,
                                style: TextStyle(
                                  color: configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme().textTheme.bodySmall?.fontSize,
                                  fontFamily: configTheme().textTheme.bodySmall?.fontFamily,
                                  fontWeight: configTheme().textTheme.bodySmall?.fontWeight,
                                ),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              InkWell(
                                onTap: () {
                                  // if(appConfigService.countryConfigCollection=="rafco"||appConfigService.countryConfigCollection=="rplc"){
                                  //   selected = mrLoanService.tr;
                                  //   setState(() {});
                                  //   print("hdfhur");
                                  // }else{
                                  buildShowActive(context);
                                  // }
                                },
                                child: Container(
                                  height: 54.h,
                                  width: Get.width,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF9F9F9),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: const Color(0xFF1A1818).withOpacity(0.08),
                                      width: 1,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.only(left: 16.w, right: 16.w),
                                    child: Row(
                                      children: [
                                        Row(
                                          children: [
                                            selected == ""
                                                ? Container()
                                                : Container(
                                                    height: 24.h,
                                                    width: 24.w,
                                                    decoration: BoxDecoration(
                                                      // shape: BoxShape.circle,
                                                      color: configTheme().colorScheme.onSecondary.withOpacity(0.05),
                                                      borderRadius: BorderRadius.circular(18),
                                                    ),
                                                    child: appConfigService.countryConfigCollection == "aam"
                                                        ? Center(
                                                            child: SvgPicture.string(
                                                                '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="6" y="9" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/><path d="M9 15L15 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 12L13 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 18L13 18" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19 14V16C19 18.8284 19 20.2426 18.1213 21.1213C17.2426 22 15.8284 22 13 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V10C6 7.17157 6 5.75736 6.87868 4.87868C7.75736 4 9.17157 4 12 4H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M19 4L19 10" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M22 7H16" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'),
                                                          )
                                                        : appConfigService.countryConfigCollection == "rafco"
                                                            ? Center(
                                                                child: SvgPicture.string(
                                                                    '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="9" width="10" height="13" rx="2" fill="#EA1B23" fill-opacity="0.5"/><path d="M9 15L15 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 12L13 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 18L13 18" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19 14V16C19 18.8284 19 20.2426 18.1213 21.1213C17.2426 22 15.8284 22 13 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V10C6 7.17157 6 5.75736 6.87868 4.87868C7.75736 4 9.17157 4 12 4H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M19 4L19 10" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M22 7H16" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'))
                                                            : Center(
                                                                child: SvgPicture.string(
                                                                    '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="9" width="10" height="13" rx="2" fill="#FFC20E" fill-opacity="0.75"/><path d="M9 15L15 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 12L13 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 18L13 18" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19 14V16C19 18.8284 19 20.2426 18.1213 21.1213C17.2426 22 15.8284 22 13 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V10C6 7.17157 6 5.75736 6.87868 4.87868C7.75736 4 9.17157 4 12 4H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M19 4L19 10" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M22 7H16" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'))),
                                            SizedBox(width: 8.w),
                                            selected == ""
                                                ? Text(
                                                    mrSelectProduct.tr,
                                                    style: TextStyle(
                                                      color:
                                                          configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                                      fontSize: configTheme().textTheme.bodySmall?.fontSize,
                                                      fontFamily: configTheme().textTheme.bodySmall?.fontFamily,
                                                      fontWeight: configTheme().textTheme.bodySmall?.fontWeight,
                                                    ),
                                                  )
                                                : Text(
                                                    selected,
                                                    style: TextStyle(
                                                      color: configTheme().textTheme.bodyMedium?.color,
                                                      fontSize: configTheme().textTheme.bodySmall?.fontSize,
                                                      fontFamily: configTheme().textTheme.bodySmall?.fontFamily,
                                                      fontWeight: configTheme().textTheme.bodySmall?.fontWeight,
                                                    ),
                                                  ),
                                          ],
                                        ),
                                        const Spacer(),
                                        Container(
                                            height: 24.h,
                                            width: 24.w,
                                            child: Center(
                                                child: SvgPicture.string(
                                                    '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>')))
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              selected == ""
                                  ? Container()
                                  : Container(
                                      height: 430.h,
                                      color: Colors.white,
                                      // color: Colors.teal,
                                      child: Column(
                                        children: [
                                          Container(
                                            height: 360.h,
                                            child: ListView.builder(
                                              // padding: EdgeInsets.only(right: 50.w),
                                              itemExtent: Get.width - 24.w,
                                              scrollDirection: Axis.horizontal,
                                              itemCount: 3,
                                              physics: NeverScrollableScrollPhysics(),
                                              itemBuilder: (context, index) {
                                                // index = currentIndex;
                                                return currentIndex == 0

                                                    /// หน้าใส่ชื่อ นามสกุล
                                                    ? Container(
                                                        // color: Colors.teal,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(right: 24.w),
                                                          child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            mainAxisAlignment: MainAxisAlignment.start,
                                                            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                            children: [
                                                              SizedBox(
                                                                height: 16.h,
                                                              ),
                                                              Text(
                                                                mrFirstName.tr,
                                                                style: TextStyle(
                                                                    fontSize:
                                                                        configTheme().textTheme.bodySmall!.fontSize,
                                                                    fontWeight:
                                                                        configTheme().textTheme.bodySmall!.fontWeight,
                                                                    color: AppColors.textBlackColor,
                                                                    fontFamily:
                                                                        configTheme().textTheme.bodySmall!.fontFamily),
                                                              ),
                                                              SizedBox(
                                                                height: 8.h,
                                                              ),
                                                              mrCtl.isActive == false
                                                                  ? const SizedBox()
                                                                  :Container(
                                                                height: 54.h,
                                                                width: Get.width,
                                                                decoration: BoxDecoration(
                                                                  borderRadius: BorderRadius.circular(16),
                                                                  border: Border.all(
                                                                      color: mrCtl
                                                                              .firstNameController.value.text.isEmpty
                                                                          ? AppColors.textBlackColor.withOpacity(0.08)
                                                                          : configTheme().colorScheme.onSecondary,
                                                                      width: 1),
                                                                ),
                                                                child: TextFormField(
                                                                  controller: mrController.firstNameController.value,
                                                                  cursorColor: configTheme().colorScheme.onSecondary,
                                                                  cursorHeight: 20.h,
                                                                  showCursor: true,
                                                                  cursorRadius: Radius.circular(2),
                                                                  style: TextStyle(
                                                                    color: AppColors.textBlackColor,
                                                                    fontSize:
                                                                    configTheme().textTheme.bodySmall!.fontSize,
                                                                    fontWeight:
                                                                    configTheme().textTheme.bodySmall!.fontWeight,
                                                                  ),
                                                                  textAlign: TextAlign.start,
                                                                  decoration: InputDecoration(
                                                                    border: InputBorder.none,
                                                                    contentPadding:
                                                                        EdgeInsets.only(left: 16.w, top: 3.h),
                                                                    hintText: mrInputFirstName.tr,
                                                                    hintStyle: TextStyle(
                                                                      color: AppColors.textBlackColor.withOpacity(0.5),
                                                                      fontSize:
                                                                          configTheme().textTheme.bodySmall?.fontSize,
                                                                      fontFamily:
                                                                          configTheme().textTheme.bodySmall?.fontFamily,
                                                                    ),
                                                                  ),
                                                                  onTap: () {
                                                                    mrCtl.firstNameController.value.text;
                                                                    setState(() {});
                                                                    print("textName");
                                                                    print(mrCtl.firstNameController.value.toString());
                                                                  },
                                                                  // onChanged: (value) {
                                                                  //   if(value.isNotEmpty){
                                                                  //     mrCtl.firstNameController.value.text = value;
                                                                  //   }else{
                                                                  //     value = "";
                                                                  //   }
                                                                  //   setState(() {});
                                                                  // },
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 16.h,
                                                              ),
                                                              Text(mrLastName.tr),
                                                              SizedBox(
                                                                height: 8.h,
                                                              ),
                                                              mrCtl.isActive == false
                                                                  ? const SizedBox()
                                                                  :Container(
                                                                height: 54.h,
                                                                width: Get.width,
                                                                decoration: BoxDecoration(
                                                                  borderRadius: BorderRadius.circular(16),
                                                                  border: Border.all(
                                                                      color: mrCtl.lastNameController.value.text.isEmpty
                                                                          ? AppColors.textBlackColor.withOpacity(0.08)
                                                                          : configTheme().colorScheme.onSecondary,
                                                                      width: 1),
                                                                ),
                                                                child: TextFormField(
                                                                  controller: mrController.lastNameController.value,
                                                                  cursorColor: configTheme().colorScheme.onSecondary,
                                                                  cursorHeight: 20.h,
                                                                  style: TextStyle(
                                                                    color: AppColors.textBlackColor,
                                                                    fontSize:
                                                                        configTheme().textTheme.bodySmall?.fontSize,
                                                                    fontFamily:
                                                                        configTheme().textTheme.bodySmall?.fontFamily,
                                                                  ),
                                                                  decoration: InputDecoration(
                                                                    border: InputBorder.none,
                                                                    contentPadding:
                                                                        EdgeInsets.only(left: 16.w, top: 3.h),
                                                                    hintText: mrInputLastName.tr,
                                                                    hintStyle: TextStyle(
                                                                      color: AppColors.textBlackColor.withOpacity(0.5),
                                                                      fontSize:
                                                                          configTheme().textTheme.bodySmall?.fontSize,
                                                                      fontFamily:
                                                                          configTheme().textTheme.bodySmall?.fontFamily,
                                                                    ),
                                                                  ),
                                                                  onTap: () {
                                                                    mrCtl.lastNameController.value.text;
                                                                    setState(() {});
                                                                    print("lastName");
                                                                    print(mrCtl.lastNameController.value.toString());
                                                                  },
                                                                  // onChanged: (value) {
                                                                  //   mrController.lastNameController.text = value;
                                                                  // },
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: 16.h,
                                                              ),
                                                              mrCtl.isActive == false
                                                                  ? const SizedBox()
                                                                  : Text(mrPhone.tr),
                                                              SizedBox(
                                                                height: 8.h,
                                                              ),
                                                              mrCtl.isActive == false
                                                                  ? const SizedBox()
                                                                  :  Container(
                                                                  height: 54.h,
                                                                  width: Get.width,
                                                                  decoration: BoxDecoration(
                                                                    borderRadius: BorderRadius.circular(16),
                                                                    border: Border.all(
                                                                        color: mrCtl.phoneController.value.text.isEmpty
                                                                            ? AppColors.textBlackColor.withOpacity(0.08)
                                                                            : configTheme().colorScheme.onSecondary,
                                                                        width: 1),
                                                                  ),
                                                                  child: TextFormField(
                                                                    controller: mrController.phoneController.value,
                                                                    cursorColor: configTheme().colorScheme.onSecondary,
                                                                    cursorHeight: 20.h,
                                                                    style: TextStyle(
                                                                      color: AppColors.textBlackColor,
                                                                      fontSize:
                                                                          configTheme().textTheme.bodySmall?.fontSize,
                                                                      fontFamily:
                                                                          configTheme().textTheme.bodySmall?.fontFamily,
                                                                    ),
                                                                    decoration: InputDecoration(
                                                                      border: InputBorder.none,
                                                                      contentPadding:
                                                                          EdgeInsets.only(left: 16.w, top: 3.h),
                                                                      hintText: '080-1234567',
                                                                      hintStyle: TextStyle(
                                                                        color:
                                                                            AppColors.textBlackColor.withOpacity(0.5),
                                                                        fontSize:
                                                                            configTheme().textTheme.bodySmall?.fontSize,
                                                                        fontFamily: configTheme()
                                                                            .textTheme
                                                                            .bodySmall
                                                                            ?.fontFamily,
                                                                      ),
                                                                    ),
                                                                    keyboardType: TextInputType.phone,
                                                                    inputFormatters: [
                                                                      FilteringTextInputFormatter.allow(
                                                                          RegExp(r'[0-9]')),
                                                                      appConfigService.countryConfigCollection == "aam"
                                                                          ? LengthLimitingTextInputFormatter(10)
                                                                          : appConfigService.countryConfigCollection ==
                                                                                  "rafco"
                                                                              ? LengthLimitingTextInputFormatter(13)
                                                                              : LengthLimitingTextInputFormatter(11),
                                                                    ],
                                                                    onTap: () {
                                                                      mrCtl.phoneController.value.text;
                                                                      setState(() {});
                                                                      print("phone");
                                                                      print(mrCtl.phoneController.value.toString());
                                                                    },
                                                                    onChanged: (value) {
                                                                      // print("value: $value");
                                                                      // mrController.phoneController.text = value;
                                                                      // print("mrController.phoneController.text: ${mrController.phoneController.text}");
                                                                      // mrController.phoneController.text = value;
                                                                    },
                                                                  )),

                                                              // Spacer(),
                                                            ],
                                                          ),
                                                        ),
                                                      )
                                                    : currentIndex == 1

                                                        /// หน้าเลือกจังหวัด อำเภอ
                                                        ? Container(
                                                            // height: 200.h,
                                                            // width: 200.w,
                                                            // color: Colors.green,
                                                            child: Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                              children: [
                                                                SizedBox(
                                                                  height: 16.h,
                                                                ),
                                                                //TODO Province
                                                                Text(
                                                                  signUpChooseProvince.tr,
                                                                  style: TextStyle(
                                                                    fontSize:
                                                                        configTheme().textTheme.bodySmall?.fontSize,
                                                                    fontFamily:
                                                                        configTheme().textTheme.bodySmall?.fontFamily,
                                                                    color: AppColors.textBlackColor,
                                                                    fontWeight:
                                                                        configTheme().textTheme.bodySmall?.fontWeight,
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                  height: 8.h,
                                                                ),
                                                                Container(
                                                                  width: 326.w,
                                                                  height: 54.h,
                                                                  // margin: EdgeInsets.only(top: 30.h, left: 24.w, right: 24.w),
                                                                  decoration: ShapeDecoration(
                                                                    shape: RoundedRectangleBorder(
                                                                      side: BorderSide(
                                                                        width: 0.50.w,
                                                                        color:
                                                                            mrCtl.provinceController!.value.text.isEmpty
                                                                                ? Colors.black.withOpacity(0.2)
                                                                                : configTheme().colorScheme.onSecondary,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(12.r),
                                                                    ),
                                                                  ),
                                                                  child: InkWell(
                                                                    onTap: () {
                                                                      _buildUpdateProvince(
                                                                          context,
                                                                          accountAddressProvince.tr,
                                                                          mrCtl.provinceController);
                                                                      setState(() {});
                                                                    },
                                                                    child: Padding(
                                                                      padding: EdgeInsets.only(left: 14.w, right: 14.w),
                                                                      child: Row(
                                                                        // mainAxisSize: MainAxisSize.min,
                                                                        crossAxisAlignment: CrossAxisAlignment.center,
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.spaceBetween,
                                                                        children: [
                                                                          Container(
                                                                            child: Text(
                                                                              mrCtl.selectedProvince!.value.isEmpty
                                                                                  ? menuGetLoanReqTime.tr +
                                                                                      signUpChooseProvince.tr
                                                                                  : mrCtl.selectedProvince!.value,
                                                                              // textAlign: TextAlign.center,
                                                                              style: TextStyle(
                                                                                color: mrCtl
                                                                                        .selectedProvince!.value.isEmpty
                                                                                    ? Color(0xFF1A1818)
                                                                                        .withOpacity(0.35)
                                                                                    : Color(0xFF1A1818),
                                                                                fontSize: 14.sp,
                                                                                fontFamily: 'NotoSansThai',
                                                                                fontWeight: FontWeight.w400,
                                                                                height: 0.14.h,
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          Container(
                                                                            // margin: EdgeInsets.only(right: 20.62.w),
                                                                            child: SvgPicture.string(
                                                                              '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                                                                              allowDrawingOutsideViewBox: true,
                                                                              width: 11.w,
                                                                              height: 6.h,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                  height: 16.h,
                                                                ),
                                                                //TODO District
                                                                Text(
                                                                  signUpChooseDistrict.tr,
                                                                  style: TextStyle(
                                                                    fontSize:
                                                                        configTheme().textTheme.bodySmall?.fontSize,
                                                                    fontFamily:
                                                                        configTheme().textTheme.bodySmall?.fontFamily,
                                                                    color: AppColors.textBlackColor,
                                                                    fontWeight:
                                                                        configTheme().textTheme.bodySmall?.fontWeight,
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                  height: 8.h,
                                                                ),
                                                                Container(
                                                                  width: 326.w,
                                                                  height: 54.h,
                                                                  // margin: EdgeInsets.only( top: 18),
                                                                  decoration: ShapeDecoration(
                                                                    shape: RoundedRectangleBorder(
                                                                      side: BorderSide(
                                                                        width: 0.50.w,
                                                                        // strokeAlign: BorderSide.strokeAlignInside,
                                                                        color:
                                                                            mrCtl.districtController!.value.text.isEmpty
                                                                                ? Colors.black.withOpacity(0.2)
                                                                                : configTheme().colorScheme.onSecondary,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(12.r),
                                                                    ),
                                                                  ),
                                                                  child: InkWell(
                                                                    onTap: () {
                                                                      _buildUpdateProvince(
                                                                          context,
                                                                          accountAddressDistrict.tr,
                                                                          mrCtl.districtController);
                                                                      setState(() {});
                                                                    },
                                                                    child: Padding(
                                                                      padding: EdgeInsets.only(left: 14.w, right: 14.w),
                                                                      child: Row(
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.spaceBetween,
                                                                        crossAxisAlignment: CrossAxisAlignment.center,
                                                                        children: [
                                                                          Container(
                                                                            child: Text(
                                                                              mrCtl.selectedDistrict!.value.isEmpty
                                                                                  ? menuGetLoanReqTime.tr +
                                                                                      signUpChooseDistrict.tr
                                                                                  : mrCtl.selectedDistrict!.value,
                                                                              textAlign: TextAlign.center,
                                                                              style: TextStyle(
                                                                                color: mrCtl
                                                                                        .selectedDistrict!.value.isEmpty
                                                                                    ? Color(0xFF1A1818)
                                                                                        .withOpacity(0.35)
                                                                                    : Color(0xFF1A1818),
                                                                                fontSize: 14.sp,
                                                                                fontFamily: 'NotoSansThai',
                                                                                fontWeight: FontWeight.w400,
                                                                                height: 0.14.h,
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          Container(
                                                                            child: SvgPicture.string(
                                                                              '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                                                                              allowDrawingOutsideViewBox: true,
                                                                              width: 11.w,
                                                                              height: 6.h,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                // Spacer(),
                                                                // buildRowIndex(currentIndex),
                                                              ],
                                                            ),
                                                          )

                                                        /// หน้าเลือกผลิตภัณฑ์
                                                        : Container(
                                                            // height: 200.h,
                                                            // width: 200.w,
                                                            // color: Colors.green,
                                                            child: Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                              children: [
                                                                SizedBox(
                                                                  height: 16.h,
                                                                ),
                                                                Text(mrGuarantee.tr),
                                                                SizedBox(
                                                                  height: 8.h,
                                                                ),
                                                                // ToDo Guarantee
                                                                InkWell(
                                                                  onTap: () {
                                                                    buildBottomSheetGuarantee(context);
                                                                    setState(() {});
                                                                    // showModalBottomSheet(
                                                                    //   backgroundColor: Colors.white,
                                                                    //     context: context,
                                                                    //     builder: (_)=>
                                                                    //         buildBottomSheetGuarantee(context));
                                                                  },
                                                                  child: Container(
                                                                      height: 54.h,
                                                                      width: 327.w,
                                                                      // padding: EdgeInsets.only(right: 24.w),
                                                                      // width: Get.width,
                                                                      decoration: BoxDecoration(
                                                                        borderRadius: BorderRadius.circular(16),
                                                                        border: Border.all(
                                                                            color: loanCtl
                                                                                    .selectedGuarantee!.value.isEmpty
                                                                                ? AppColors.textBlackColor
                                                                                    .withOpacity(0.08)
                                                                                : configTheme().colorScheme.onSecondary,
                                                                            width: 1),
                                                                      ),
                                                                      child: loanCtl.selectedGuarantee!.value.isNotEmpty
                                                                          ? _listSelectLoanMenu(
                                                                              context,
                                                                              loanCtl.guaranteeList[
                                                                                  loanCtl.selectedGuaranteeIndex.value],
                                                                              SvgPicture.string(
                                                                                appConfigService
                                                                                            .countryConfigCollection ==
                                                                                        "aam"
                                                                                    ? loanCtl.guaranteeIconListSelected[
                                                                                        loanCtl.selectedGuaranteeIndex
                                                                                            .value]
                                                                                    : appConfigService
                                                                                                .countryConfigCollection ==
                                                                                            "rplc"
                                                                                        ? loanCtl
                                                                                                .guaranteeIconListSelectedRPLC[
                                                                                            loanCtl
                                                                                                .selectedGuaranteeIndex
                                                                                                .value]
                                                                                        : loanCtl
                                                                                                .guaranteeIconListSelectedRafco[
                                                                                            loanCtl
                                                                                                .selectedGuaranteeIndex
                                                                                                .value], // allowDrawingOutsideViewBox: true,
                                                                              ),
                                                                              true)
                                                                          : Padding(
                                                                              padding: EdgeInsets.only(
                                                                                  left: 16.w, right: 16.w),
                                                                              child: Row(
                                                                                mainAxisAlignment:
                                                                                    MainAxisAlignment.spaceBetween,
                                                                                children: [
                                                                                  Text(mrInputGuarantee.tr,
                                                                                      style: TextStyle(
                                                                                        color: Color(0xFF1A1818)
                                                                                            .withOpacity(0.35),
                                                                                        fontSize: configTheme()
                                                                                            .textTheme
                                                                                            .bodySmall!
                                                                                            .fontSize,
                                                                                        fontFamily: configTheme()
                                                                                            .textTheme
                                                                                            .bodySmall!
                                                                                            .fontFamily,
                                                                                        fontWeight: configTheme()
                                                                                            .textTheme
                                                                                            .bodySmall!
                                                                                            .fontWeight,
                                                                                        // height: 0.14.h,
                                                                                      )),
                                                                                  Container(
                                                                                      height: 24.h,
                                                                                      width: 24.w,
                                                                                      child: Center(
                                                                                          child: SvgPicture.string(
                                                                                              '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>')))
                                                                                ],
                                                                              ),
                                                                            )),
                                                                ),
                                                                SizedBox(
                                                                  height: 16.h,
                                                                ),
                                                                Text(mrCollateral.tr),
                                                                SizedBox(
                                                                  height: 8.h,
                                                                ),
                                                                //ToDo Upload Image
                                                                Container(
                                                                  width: Get.width,
                                                                  height: 54.h,
                                                                  child: ListView.builder(
                                                                    scrollDirection: Axis.horizontal,
                                                                    itemCount: mrController.imageList.length + 1,
                                                                    itemBuilder: (context, index) => InkWell(
                                                                      onTap: () {
                                                                        showModalBottomSheet(
                                                                            backgroundColor: Colors.white,
                                                                            context: context,
                                                                            builder: (_) => buildContainerUploadImg());
                                                                        setState(() {});
                                                                      },
                                                                      child: Padding(
                                                                        padding: EdgeInsets.only(right: 8.w),
                                                                        child: Container(
                                                                          height: 54.h,
                                                                          width: 150.w,
                                                                          decoration: BoxDecoration(
                                                                            borderRadius: BorderRadius.circular(16),
                                                                            border: Border.all(
                                                                                color: mrController.imageList.length ==
                                                                                        index
                                                                                    ? AppColors.textBlackColor
                                                                                        .withOpacity(0.08)
                                                                                    : configTheme().colorScheme.primary,
                                                                                width: 1),
                                                                          ),
                                                                          child: Padding(
                                                                            padding: EdgeInsets.only(
                                                                                left: 16.w, right: 16.w),
                                                                            child: mrController.imageList.length ==
                                                                                    index
                                                                                ? Row(
                                                                                    mainAxisAlignment:
                                                                                        MainAxisAlignment.spaceBetween,
                                                                                    children: [
                                                                                      Center(
                                                                                        child: Text(
                                                                                          accountEditUploadImg.tr,
                                                                                          style: TextStyle(
                                                                                            color: Color(0xFF1A1818)
                                                                                                .withOpacity(0.35),
                                                                                            fontSize: configTheme()
                                                                                                .textTheme
                                                                                                .bodySmall!
                                                                                                .fontSize,
                                                                                            fontFamily: configTheme()
                                                                                                .textTheme
                                                                                                .bodySmall!
                                                                                                .fontFamily,
                                                                                            fontWeight: configTheme()
                                                                                                .textTheme
                                                                                                .bodySmall!
                                                                                                .fontWeight,
                                                                                          ),
                                                                                        ),
                                                                                      ),
                                                                                      Container(
                                                                                        child: SvgPicture.string(
                                                                                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.19714 20.9343L15.2557 12.4086L19.5186 16.6714M6.19714 20.9343H16.8543C18.62 20.9343 20.0514 19.5029 20.0514 17.7371V12.4086M6.19714 20.9343C4.43141 20.9343 3 19.5029 3 17.7371V7.08001C3 5.31427 4.43141 3.88287 6.19714 3.88287H13.1243M18.9857 9.02859L18.9857 6.01429M18.9857 6.01429L18.9857 3M18.9857 6.01429L15.9714 6.01429M18.9857 6.01429L22 6.01429M9.39428 8.67858C9.39428 9.56144 8.67858 10.2771 7.79571 10.2771C6.91285 10.2771 6.19714 9.56144 6.19714 8.67858C6.19714 7.79571 6.91285 7.08001 7.79571 7.08001C8.67858 7.08001 9.39428 7.79571 9.39428 8.67858Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
                                                                                      )
                                                                                    ],
                                                                                  )
                                                                                : mrController.imageList.length < 6
                                                                                    ? Row(
                                                                                        mainAxisAlignment:
                                                                                            MainAxisAlignment
                                                                                                .spaceBetween,
                                                                                        children: [
                                                                                          Center(
                                                                                            child: Text(
                                                                                              'IMG0${index + 1}',
                                                                                              style: TextStyle(
                                                                                                color:
                                                                                                    Color(0xFF1A1818),
                                                                                                fontSize: configTheme()
                                                                                                    .textTheme
                                                                                                    .bodyMedium!
                                                                                                    .fontSize,
                                                                                                fontFamily:
                                                                                                    configTheme()
                                                                                                        .textTheme
                                                                                                        .bodyMedium!
                                                                                                        .fontFamily,
                                                                                                fontWeight:
                                                                                                    configTheme()
                                                                                                        .textTheme
                                                                                                        .bodyMedium!
                                                                                                        .fontWeight,
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          InkWell(
                                                                                            onTap: () {
                                                                                              mrController.imageList
                                                                                                  .removeAt(index);
                                                                                              print(index.toString());
                                                                                              setState(() {});
                                                                                            },
                                                                                            child: Container(
                                                                                                child: SvgPicture.string(
                                                                                                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="12" fill="#1A1818" fill-opacity="0.2"/><path d="M12 12L7 7M12 12L17 17M12 12L17 7M12 12L7 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                                                                                          )
                                                                                        ],
                                                                                      )
                                                                                    : Row(
                                                                                        mainAxisAlignment:
                                                                                            MainAxisAlignment
                                                                                                .spaceBetween,
                                                                                        children: [
                                                                                          Center(
                                                                                            child: Text(
                                                                                              accountEditUploadImg.tr,
                                                                                              style: TextStyle(
                                                                                                color: Color(0xFF1A1818)
                                                                                                    .withOpacity(0.35),
                                                                                                fontSize: configTheme()
                                                                                                    .textTheme
                                                                                                    .bodySmall!
                                                                                                    .fontSize,
                                                                                                fontFamily:
                                                                                                    configTheme()
                                                                                                        .textTheme
                                                                                                        .bodySmall!
                                                                                                        .fontFamily,
                                                                                                fontWeight:
                                                                                                    configTheme()
                                                                                                        .textTheme
                                                                                                        .bodySmall!
                                                                                                        .fontWeight,
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          Container(
                                                                                            child: SvgPicture.string(
                                                                                                '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.19714 20.9343L15.2557 12.4086L19.5186 16.6714M6.19714 20.9343H16.8543C18.62 20.9343 20.0514 19.5029 20.0514 17.7371V12.4086M6.19714 20.9343C4.43141 20.9343 3 19.5029 3 17.7371V7.08001C3 5.31427 4.43141 3.88287 6.19714 3.88287H13.1243M18.9857 9.02859L18.9857 6.01429M18.9857 6.01429L18.9857 3M18.9857 6.01429L15.9714 6.01429M18.9857 6.01429L22 6.01429M9.39428 8.67858C9.39428 9.56144 8.67858 10.2771 7.79571 10.2771C6.91285 10.2771 6.19714 9.56144 6.19714 8.67858C6.19714 7.79571 6.91285 7.08001 7.79571 7.08001C8.67858 7.08001 9.39428 7.79571 9.39428 8.67858Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
                                                                                          )
                                                                                        ],
                                                                                      ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                                // Spacer(),
                                                                // buildRowIndex(currentIndex),
                                                              ],
                                                            ),
                                                          );
                                              },
                                            ),
                                          ),
                                          // Spacer(),
                                          buildRowIndex(currentIndex),
                                          Spacer(),
                                          currentIndex == 0
                                              ? Align(
                                                  alignment: Alignment.bottomRight,
                                                  child: InkWell(
                                                    onTap: () {
                                                      if (mrCtl.firstNameController.value.text.isNotEmpty &&
                                                          mrCtl.lastNameController.value.text.isNotEmpty &&
                                                          mrCtl.phoneController.value.text.isNotEmpty) {
                                                        changePage();
                                                      } else {
                                                        print('error');
                                                      }
                                                    },
                                                    child: Container(
                                                      height: 52.h,
                                                      width: 100.w,
                                                      decoration: BoxDecoration(
                                                        color: mrCtl.firstNameController.value.text.isEmpty &&
                                                                mrCtl.lastNameController.value.text.isEmpty &&
                                                                mrCtl.phoneController.value.text.isEmpty
                                                            ? AppColors.buttonColorRPLC.withOpacity(0.15)
                                                            : configTheme().colorScheme.onPrimary,
                                                        borderRadius: BorderRadius.circular(16),
                                                      ),
                                                      child: Center(
                                                          child: Text(
                                                        signUpNext.tr,
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: configTheme().textTheme.titleMedium!.fontSize,
                                                          fontFamily: configTheme().textTheme.titleMedium!.fontFamily,
                                                          fontWeight: configTheme().textTheme.titleMedium!.fontWeight,
                                                        ),
                                                      )),
                                                    ),
                                                  ),
                                                )
                                              : currentIndex == 1
                                                  ? Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        InkWell(
                                                          onTap: () {
                                                            backPage();
                                                          },
                                                          child: Container(
                                                            height: 38.h,
                                                            width: 38.w,
                                                            child: Center(
                                                                child: SvgPicture.string(
                                                                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3898_65076)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3898_65076"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                                                          ),
                                                        ),
                                                        SizedBox(width: 16.w),
                                                        InkWell(
                                                          onTap: () {
                                                            if (mrCtl.selectedProvince!.value.isNotEmpty &&
                                                                mrCtl.selectedDistrict!.value.isNotEmpty) {
                                                              changePage();
                                                            } else {
                                                              print('error');
                                                            }
                                                          },
                                                          child: Container(
                                                            height: 52.h,
                                                            width: 100.w,
                                                            decoration: BoxDecoration(
                                                              color: mrCtl.selectedProvince!.value.isEmpty &&
                                                                      mrCtl.selectedDistrict!.value.isEmpty
                                                                  ? AppColors.buttonColorRPLC.withOpacity(0.15)
                                                                  : configTheme().colorScheme.onPrimary,
                                                              borderRadius: BorderRadius.circular(16),
                                                            ),
                                                            child: Center(
                                                                child: Text(signUpNext.tr,
                                                                    style: TextStyle(
                                                                      color: Colors.white,
                                                                      fontSize:
                                                                          configTheme().textTheme.titleMedium!.fontSize,
                                                                      fontFamily: configTheme()
                                                                          .textTheme
                                                                          .titleMedium!
                                                                          .fontFamily,
                                                                      fontWeight: configTheme()
                                                                          .textTheme
                                                                          .titleMedium!
                                                                          .fontWeight,
                                                                    ))),
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  : Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        InkWell(
                                                          onTap: () {
                                                            backPage();
                                                          },
                                                          child: Container(
                                                            height: 38.h,
                                                            width: 38.w,
                                                            child: Center(
                                                                child: SvgPicture.string(
                                                                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3898_65076)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3898_65076"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                                                          ),
                                                        ),
                                                        SizedBox(width: 16.w),
                                                        InkWell(
                                                          onTap: () {
                                                            //print("click confirm");
                                                            if (mrCtl.firstNameController.value.text.isNotEmpty &&
                                                                mrCtl.lastNameController.value.text.isNotEmpty &&
                                                                mrCtl.phoneController.value.text.isNotEmpty &&
                                                                loanCtl.selectedGuarantee.value.isNotEmpty &&
                                                                mrCtl.selectedProvince!.value.isNotEmpty &&
                                                                mrCtl.selectedDistrict!.value.isNotEmpty) {
                                                             // print("ตรงนี้");

                                                              //print (  mrCtl.referFriend(context));

                                                              mrCtl.referFriend(context);
                                                              // showDialog(context: context, builder: (_)=>Container());
                                                            } else {
                                                              print("ถัดไป");
                                                            }
                                                          },
                                                          child: Container(
                                                            height: 52.h,
                                                            width: 100.w,
                                                            decoration: BoxDecoration(
                                                              color: configTheme().colorScheme.onPrimary,
                                                              borderRadius: BorderRadius.circular(16),
                                                            ),
                                                            child: Center(
                                                                child: Text(
                                                              menuGetLoanConfirm.tr,
                                                              style: TextStyle(
                                                                color: Colors.white,
                                                                fontSize: configTheme().textTheme.titleMedium!.fontSize,
                                                                fontWeight:
                                                                    configTheme().textTheme.titleMedium!.fontWeight,
                                                                fontFamily:
                                                                    configTheme().textTheme.titleMedium!.fontFamily,
                                                              ),
                                                            )),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                        ],
                                      ),
                                    ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ));
  }

  buildRowIndex(index) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Opacity(
          opacity: index == 0 ? 1 : 0.40,
          child: Container(
            width: index == 0 ? 15 : 4,
            height: 4.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),
        SizedBox(width: 6.w),
        Opacity(
          opacity: index == 1 ? 1 : 0.40,
          child: Container(
            width: index == 1 ? 15 : 4,
            height: 4.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),
        SizedBox(width: 6.w),
        Opacity(
          opacity: index == 2 ? 1 : 0.40,
          child: Container(
            width: index == 2 ? 15 : 4,
            height: 4.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),
      ],
    );
  }

  buildContainerUploadImg() {
    final mrController = Get.put(MRController());
    return Container(
      height: 274.h,
      child: Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 15.h, bottom: 38.h),
              child: Container(
                child: SvgPicture.string(
                    '<svg width="44" height="5" viewBox="0 0 44 5" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="44" height="5" rx="2.5" fill="#1A1818" fill-opacity="0.2"/></svg>'),
              ),
            ),

            /// ถ่ายรูป
            InkWell(
              onTap: () {
                mrController.openCamera(context);
              },
              child: Container(
                height: 54.h,
                width: Get.width,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: configTheme().colorScheme.primary.withOpacity(0.05)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                        child: SvgPicture.string(
                            color: configTheme().colorScheme.primary,
                            '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3.5 9.8541C3.5 8.83011 4.33011 8 5.3541 8V8C6.05638 8 6.69839 7.60322 7.01246 6.97508L7.83333 5.33333C7.94329 5.11342 7.99827 5.00346 8.06062 4.90782C8.3859 4.40882 8.91668 4.08078 9.50848 4.01299C9.6219 4 9.74484 4 9.99071 4H15.0093C15.2552 4 15.3781 4 15.4915 4.01299C16.0833 4.08078 16.6141 4.40882 16.9394 4.90782C17.0017 5.00346 17.0567 5.11342 17.1667 5.33333L17.9875 6.97508C18.3016 7.60322 18.9436 8 19.6459 8V8C20.6699 8 21.5 8.83011 21.5 9.8541V14.8571C21.5 16.8619 21.5 17.8643 21.0402 18.5961C20.8004 18.9777 20.4777 19.3004 20.0961 19.5402C19.3643 20 18.3619 20 16.3571 20H8.64286C6.6381 20 5.63571 20 4.9039 19.5402C4.52229 19.3004 4.19961 18.9777 3.95983 18.5961C3.5 17.8643 3.5 16.8619 3.5 14.8571V9.8541Z" stroke="#22409A" stroke-width="1.5"/><circle cx="12.5" cy="13" r="3.25" stroke="#22409A" stroke-width="1.5"/></svg>')),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(mrCamera.tr,
                        style: TextStyle(
                            color: configTheme().colorScheme.primary,
                            fontSize: configTheme().textTheme.titleMedium!.fontSize,
                            fontWeight: configTheme().textTheme.titleMedium!.fontWeight))
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 10.h,
            ),

            ///เลือกจากคลัง
            InkWell(
              onTap: () {
                mrController.pickImage();
                setState(() {});
                // mrController.imageList
              },
              child: Container(
                height: 54.h,
                width: Get.width,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: configTheme().colorScheme.primary.withOpacity(0.05)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                        child: SvgPicture.string(
                            color: configTheme().colorScheme.primary,
                            '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.69714 20.9343L15.7557 12.4086L20.0186 16.6714M6.69714 20.9343H17.3543C19.12 20.9343 20.5514 19.5029 20.5514 17.7371V12.4086M6.69714 20.9343C4.93141 20.9343 3.5 19.5029 3.5 17.7371V7.08001C3.5 5.31427 4.93141 3.88287 6.69714 3.88287H13.6243M19.4857 9.02859L19.4857 6.01429M19.4857 6.01429L19.4857 3M19.4857 6.01429L16.4714 6.01429M19.4857 6.01429L22.5 6.01429M9.89428 8.67858C9.89428 9.56144 9.17858 10.2771 8.29571 10.2771C7.41285 10.2771 6.69714 9.56144 6.69714 8.67858C6.69714 7.79571 7.41285 7.08001 8.29571 7.08001C9.17858 7.08001 9.89428 7.79571 9.89428 8.67858Z" stroke="#22409A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(mrUpload.tr,
                        style: TextStyle(
                            color: configTheme().colorScheme.primary,
                            fontSize: configTheme().textTheme.titleMedium!.fontSize,
                            fontWeight: configTheme().textTheme.titleMedium!.fontWeight))
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text("ยกเลิก",
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: configTheme().textTheme.titleMedium!.fontSize,
                      fontWeight: configTheme().textTheme.titleMedium!.fontWeight,
                      // fontFamily: configTheme().textTheme.titleMedium!.fontFamily
                    )))
          ],
        ),
      ),
    );
  }

  buildShowActive(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Container(
              height: 260.h,
              width: Get.width,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  top: 15.h,
                ),
                child: Column(
                  children: [
                    Container(
                      child: SvgPicture.string(
                          '<svg width="44" height="5" viewBox="0 0 44 5" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="44" height="5" rx="2.5" fill="#1A1818" fill-opacity="0.2"/></svg>'),
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 16.w, right: 16.w),
                      child: Container(
                        height: 160.h,
                        child: ListView.builder(
                            itemCount: 1,
                            // itemCount:  appConfigService.countryConfigCollection=='rafco'||appConfigService.countryConfigCollection=="rplc"?1:active.length,
                            itemBuilder: (BuildContext, index) {
                              return InkWell(
                                onTap: () {
                                  setState(() {
                                    selected = active[index]['head'].toString();
                                  });
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  height: 80.h,
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                          height: 46.h,
                                          width: 46.w,
                                          decoration: BoxDecoration(
                                            // shape: BoxShape.circle,
                                            color: configTheme().colorScheme.onSecondary.withOpacity(0.05),
                                            borderRadius: BorderRadius.circular(18),
                                          ),
                                          child: appConfigService.countryConfigCollection == "aam"
                                              ? Center(
                                                  child: SvgPicture.string(
                                                      '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="6" y="9" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/><path d="M9 15L15 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 12L13 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 18L13 18" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19 14V16C19 18.8284 19 20.2426 18.1213 21.1213C17.2426 22 15.8284 22 13 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V10C6 7.17157 6 5.75736 6.87868 4.87868C7.75736 4 9.17157 4 12 4H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M19 4L19 10" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M22 7H16" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'),
                                                )
                                              : appConfigService.countryConfigCollection == "rafco"
                                                  ? Center(
                                                      child: SvgPicture.string(
                                                          '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="9" width="10" height="13" rx="2" fill="#EA1B23" fill-opacity="0.5"/><path d="M9 15L15 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 12L13 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 18L13 18" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19 14V16C19 18.8284 19 20.2426 18.1213 21.1213C17.2426 22 15.8284 22 13 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V10C6 7.17157 6 5.75736 6.87868 4.87868C7.75736 4 9.17157 4 12 4H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M19 4L19 10" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M22 7H16" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'))
                                                  : Center(
                                                      child: SvgPicture.string(
                                                          '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="9" width="10" height="13" rx="2" fill="#FFC20E" fill-opacity="0.75"/><path d="M9 15L15 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 12L13 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M9 18L13 18" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19 14V16C19 18.8284 19 20.2426 18.1213 21.1213C17.2426 22 15.8284 22 13 22H12C9.17157 22 7.75736 22 6.87868 21.1213C6 20.2426 6 18.8284 6 16V10C6 7.17157 6 5.75736 6.87868 4.87868C7.75736 4 9.17157 4 12 4H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M19 4L19 10" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M22 7H16" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'))),
                                      SizedBox(
                                        width: 20.w,
                                      ),

                                      // :
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            active[index]['head'],
                                            style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
                                          ),
                                          Text(
                                            active[index]['subhead'],
                                            style: TextStyle(
                                                fontSize: 12.sp, color: const Color(0xFF1A1818).withOpacity(0.5)),
                                          )
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              );
                            }),
                      ),
                    )
                  ],
                ),
              ));
        });
  }

  buildBottomSheetGuarantee(context) {
    // final loanCtl = Get.put(LoanController());
    AppConfigService appConfigService = Get.find<AppConfigService>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: configTheme().colorScheme.background.withOpacity(1.0),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(18.0),
          ),
        ),
        builder: (_) {
          return Container(
            child: appConfigService.countryConfigCollection.toString() == "aam"
                ? Container(
                    width: Get.width,
                    height: 440.h,
                    padding: const EdgeInsets.only(bottom: 20),
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.background,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        Container(
                          height: 34.h,
                          alignment: Alignment.center,
                          child: SvgPicture.string(AppSvgImage.close_bar),
                        ),
                        SizedBox(
                          height: 26.h,
                        ),
                        listLoanMenu(
                            context,
                            menuGetLoanTypeCar.tr,
                            menuGetLoanTypeCarDes.tr,
                            // loanCtl.guaranteeList[0],
                            // loanCtl.guaranteeListDesc[0],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[0],
                            ), () {
                          loanCtl.selectGuarantee('car', 0);
                          loanCtl.checkBu('aam');
                          setState(() {});
                          Navigator.pop(context);
                        }),
                        listLoanMenu(
                            context,
                            menuGetLoanTypeTruck.tr,
                            menuGetLoanTypeTruckDes.tr,
                            // loanCtl.guaranteeList[1],
                            // loanCtl.guaranteeListDesc[1],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[1],
                            ), () {
                          loanCtl.selectGuarantee('truck', 1);
                          loanCtl.checkBu('aam');
                          setState(() {});
                          Navigator.pop(context);
                        }),
                        listLoanMenu(
                            context,
                            menuGetLoanTypeMoto.tr,
                            menuGetLoanTypeMotoDes.tr,
                            // loanCtl.guaranteeList[2],
                            // loanCtl.guaranteeListDesc[2],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[2],
                            ), () {
                          loanCtl.selectGuarantee('motocycle', 2);
                          loanCtl.checkBu('aam');
                          setState(() {});
                          Navigator.pop(context);
                        }),
                        listLoanMenu(
                            context,
                            menuGetLoanTypeLand.tr,
                            menuGetLoanTypeLandDes.tr,
                            // loanCtl.guaranteeList[3],
                            // loanCtl.guaranteeListDesc[3],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[3],
                            ), () {
                          loanCtl.selectGuarantee('land', 3);
                          loanCtl.checkBu('aam');
                          setState(() {});
                          Navigator.pop(context);
                        }),
                      ],
                    ),
                  )
                : appConfigService.countryConfigCollection.toString() == "rafco"
                    ? Container(
                        width: Get.width,
                        height: 440.h,
                        padding: const EdgeInsets.only(bottom: 20),
                        decoration: ShapeDecoration(
                          color: configTheme().colorScheme.background,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            Container(
                              height: 34.h,
                              alignment: Alignment.center,
                              child: SvgPicture.string(AppSvgImage.close_bar),
                            ),
                            SizedBox(
                              height: 26.h,
                            ),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeMoto.tr,
                                menuGetLoanTypeMotoDes.tr,
                                // loanCtl.guaranteeList[1],
                                // loanCtl.guaranteeListDesc[1],
                                SvgPicture.string(loanCtl.guaranteeIconListRafco[1].toString()), () {
                              loanCtl.selectGuarantee('motocycle', 1);
                              loanCtl.checkBu('rafco');
                              setState(() {});
                              Navigator.pop(context);
                            }),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeThree.tr,
                                menuGetLoanTypeThreeDes.tr,
                                // loanCtl.guaranteeList[2],
                                // loanCtl.guaranteeListDesc[2],
                                SvgPicture.string(loanCtl.guaranteeIconListRafco[2].toString()), () {
                              loanCtl.selectGuarantee('threevehicle', 2);
                              loanCtl.checkBu('rafco');
                              setState(() {});
                              Navigator.pop(context);
                            }),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeCar.tr,
                                menuGetLoanTypeCarDes.tr,
                                // loanCtl.guaranteeList[0],
                                // loanCtl.guaranteeListDesc[0],
                                SvgPicture.string(loanCtl.guaranteeIconListRafco[0].toString()),
                                // Image.asset(loanCtl.guaranteeIconListRafco[0], color: configTheme().colorScheme.onSecondary),
                                () {
                              loanCtl.selectGuarantee('car', 0);
                              loanCtl.checkBu('rafco');
                              setState(() {});
                              Navigator.pop(context);
                            }),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeLand.tr,
                                menuGetLoanTypeLandDes.tr,
                                // loanCtl.guaranteeList[3],
                                // loanCtl.guaranteeListDesc[3],
                                SvgPicture.string(loanCtl.guaranteeIconListRafco[3].toString()), () {
                              loanCtl.selectGuarantee('land', 3);
                              loanCtl.checkBu('rafco');
                              setState(() {});
                              Navigator.pop(context);
                            }),
                          ],
                        ),
                      )
                    : Container(
                        width: Get.width,
                        height: 350.h,
                        padding: const EdgeInsets.only(bottom: 20),
                        decoration: ShapeDecoration(
                          color: configTheme().colorScheme.background,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                        ),
                        child: Column(
                          // mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Align(
                              alignment: Alignment.topCenter,
                              child: Container(
                                height: 34.h,
                                alignment: Alignment.center,
                                child: SvgPicture.string(AppSvgImage.close_bar),
                              ),
                            ),
                            SizedBox(
                              height: 26.h,
                            ),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeMoto.tr,
                                menuGetLoanTypeMotoDes.tr,
                                // loanCtl.guaranteeList[0],
                                // loanCtl.guaranteeListDesc[0],
                                SvgPicture.string(loanCtl.guaranteeIconListRPLC[0].toString()), () {
                              loanCtl.selectGuarantee('motocycle', 0);
                              loanCtl.checkBu('rplc');
                              setState(() {});
                              Navigator.pop(context);
                            }),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeCar.tr,
                                menuGetLoanTypeCarDes.tr,
                                // loanCtl.guaranteeList[1],
                                // loanCtl.guaranteeListDesc[1],
                                SvgPicture.string(loanCtl.guaranteeIconListRPLC[1].toString()), () {
                              loanCtl.selectGuarantee('car', 1);
                              loanCtl.checkBu('rplc');
                              Navigator.pop(context);
                            }),
                            listLoanMenu(
                                context,
                                menuGetLoanTypeLand.tr,
                                menuGetLoanTypeLandDes.tr,
                                // loanCtl.guaranteeList[2],
                                // loanCtl.guaranteeListDesc[2],
                                SvgPicture.string(loanCtl.guaranteeIconListRPLC[2].toString()),
                                // Image.asset(loanCtl.guaranteeIconListRPLC[2], color: configTheme().colorScheme.onSecondary),
                                () {
                              loanCtl.selectGuarantee('land', 2);
                              loanCtl.checkBu('rplc');
                              setState(() {});
                              Navigator.pop(context);
                            }),
                            // _listLoanMenu(
                            //     context,
                            //     loanCtl.guaranteeList[3],
                            //     loanCtl.guaranteeListDesc[3],
                            //     Image.asset(loanCtl.guaranteeIconList[3],
                            //         color: configTheme().colorScheme.onSecondary), () {
                            //   loanCtl.selectGuarantee('land', 3);
                            //   Navigator.pop(context);
                            // }),
                          ],
                        ),
                      ),
          );
        });
  }

  listLoanMenu(context, String title, String sub_title, Widget icon, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 376.w,
        height: 80.h,
        child: Row(
          children: [
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 46.w,
              child: icon,
            ),
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 270.w,
              // height: 44.h,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 270.w,
                    child: Text(
                      title,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily: configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                        fontWeight: configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Container(
                    width: 269.w,
                    child: Container(
                      height: 20.h,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 180.w,
                            child: Text(
                              sub_title,
                              style: TextStyle(
                                color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                                fontFamily: configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  _listSelectLoanMenu(context, String title, Widget icon, bool isShowIcon) {
    return Container(
      // width: 376.w,
      height: 80.h,
      child: Row(
        children: [
          isShowIcon
              ? Container(
                  margin: EdgeInsets.only(left: 20.w),
                  width: 34.w,
                  child: icon,
                )
              : Container(),
          SizedBox(
            width: 10.w,
          ),
          Container(
            height: 44.h,
            alignment: Alignment.centerLeft,
            child: Text(
              title,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
                fontFamily: configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                fontWeight: configTheme().primaryTextTheme.bodyLarge?.fontWeight,
              ),
            ),
          )
        ],
      ),
    );
  }
  //
  // Future<dynamic> _buildBottomSheetPutParam(type) {
  //   return showModalBottomSheet(
  //       context: context,
  //       isScrollControlled: true,
  //       backgroundColor: Colors.white.withOpacity(1.0),
  //       shape: const RoundedRectangleBorder(
  //         borderRadius: BorderRadius.vertical(
  //           top: Radius.circular(18.0),
  //         ),
  //       ),
  //       builder: (context) {
  //         return Container(
  //           child: scrollInput(type),
  //         );
  //       });
  // }
  //
  // scrollInput(type) {
  //   return Container(
  //     width: 375.w,
  //     height: 624.h,
  //     clipBehavior: Clip.antiAlias,
  //     decoration: ShapeDecoration(
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(12.r),
  //           topRight: Radius.circular(12.r),
  //         ),
  //       ),
  //       shadows: [
  //         const BoxShadow(
  //           color: Color(0x19000000),
  //           blurRadius: 20,
  //           offset: Offset(0, 4),
  //           spreadRadius: 0,
  //         )
  //       ],
  //       gradient: LinearGradient(
  //         begin: Alignment.topCenter,
  //         end: Alignment.bottomCenter,
  //         colors: [
  //           Colors.white.withOpacity(1),
  //           Colors.white.withOpacity(0.9),
  //         ],
  //       ),
  //     ),
  //     child: Column(
  //         // mainAxisSize: MainAxisSize.min,
  //         mainAxisAlignment: MainAxisAlignment.start,
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Container(
  //             width: 375.w,
  //             height: 48.h,
  //             // color: Colors.teal,
  //             // margin: EdgeInsets.only(top: 28.h),
  //             child: Center(
  //               child: Text(
  //                 type == accountAddressProvince.tr
  //                     ? accountAddress.tr
  //                     : type == accountAddressDistrict.tr?
  //                 signUpChooseDistrict.tr
  //                     : accountAddressSubDistrict.tr ,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   color: const Color(0xFF1A1818),
  //                   fontSize: 16.sp,
  //                   fontFamily: 'NotoSansThai',
  //                   fontWeight: FontWeight.w600,
  //                   height: 0.19.h,
  //                 ),
  //               ),
  //             ),
  //           ),
  //           Container(
  //             width: 375.w,
  //             decoration: ShapeDecoration(
  //               shape: RoundedRectangleBorder(
  //                 side: BorderSide(
  //                   width: 0.50.w,
  //                   strokeAlign: BorderSide.strokeAlignCenter,
  //                   color: const Color(0x191A1818),
  //                 ),
  //               ),
  //             ),
  //           ),
  //           Container(
  //             height: 524.h,
  //             child: ListView.builder(
  //                 itemCount: type == accountAddressProvince.tr
  //                     ? regisAddressController.citiesData.length
  //                     : type == accountAddressDistrict.tr
  //                     ? regisAddressController.districtsData.length
  //                     : 0,
  //                 itemBuilder: (BuildContext context, int index) {
  //                   return Padding(
  //                     padding: EdgeInsets.only(left: 24.w, right: 24.w),
  //                     child: InkWell(
  //                       onTap: () {
  //                         mrCtl.setDropdown(context, type, index);
  //                         if(type == accountAddressProvince.tr){
  //                           // registerController.idCity = regisAddressController.citiesData[index].cityId!;
  //                           mrCtl.selectedProvince!.value = regisAddressController.citiesData[index].cityNameLocal.toString();
  //                           mrCtl.selectedDistrict!.value = '';
  //                           // registerController.selectedSubDistrict!.value = '';
  //                         } else if(type == accountAddressDistrict.tr){
  //                           mrCtl.selectedDistrict!.value = regisAddressController.districtsData[index].districtNameLocal.toString();
  //                           // editProfileController.selectedSubDistrict!.value = '';
  //                           // } else if(type == accountAddressSubDistrict.tr){
  //                           //   editProfileController.selectedSubDistrict!.value = addressCtl.subDistrictsData[index].subDistrictName.toString();
  //                         }
  //                         setState(() {});
  //                       },
  //                       child: Container(
  //                         width: 327.w,
  //                         height: 52.h,
  //                         decoration: BoxDecoration(
  //                           border: Border(
  //                             bottom: BorderSide(
  //                               color: const Color(0x191A1818),
  //                               width: 0.5.w,
  //                             ),
  //                           ),
  //                         ),
  //                         child: Align(
  //                           alignment: Alignment.centerLeft,
  //                           child: Text(
  //                             type == accountAddressProvince.tr
  //                                 ? regisAddressController
  //                                 .citiesData[index].cityNameLocal
  //                                 .toString()
  //                                 : type == accountAddressDistrict.tr
  //                                 ? regisAddressController.districtsData[index]
  //                                 .districtNameLocal
  //                                 .toString()
  //                                 : '',
  //                           ),
  //                         ),
  //                       ),
  //                     ),
  //                   );
  //                 }),
  //           )
  //         ]),
  //   );
  // }

  _buildUpdateProvince(context, String type, controller) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            height: 624.h,
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 375.w,
                  height: 48.h,
                  // color: Colors.teal,
                  // margin: EdgeInsets.only(top: 28.h),
                  child: Center(
                    child: Text(
                      type == accountAddressProvince.tr
                          ? accountAddress.tr
                          : type == accountAddressDistrict.tr
                              ? signUpChooseDistrict.tr
                              : accountAddressSubDistrict.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize: 16.sp,
                        fontFamily: 'NotoSansThai',
                        fontWeight: FontWeight.w600,
                        // height: 0.19.h,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50.w,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: const Color(0x191A1818),
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 524.h,
                  child: ListView.builder(
                      itemCount: type == accountAddressProvince.tr
                          ? regisAddressController.citiesData.length
                          : type == accountAddressDistrict.tr
                              ? regisAddressController.districtsData.length
                              : type == accountAddressSubDistrict.tr
                                  ? regisAddressController.subDistrictsData.length
                                  : 0,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(left: 24.w, right: 24.w),
                          child: InkWell(
                            onTap: () {
                              mrCtl.setDropdown(context, type, index);
                              if (type == accountAddressProvince.tr) {
                                mrCtl.selectedProvince!.value =
                                    regisAddressController.citiesData[index].cityNameLocal.toString();
                                mrCtl.selectedDistrict!.value = '';
                                // mrCtl
                                //     .selectedSubDistrict!.value = '';
                              } else if (type == accountAddressDistrict.tr) {
                                mrCtl.selectedDistrict!.value =
                                    regisAddressController.districtsData[index].districtNameLocal.toString();
                                // mrCtl
                                //     .selectedSubDistrict!.value = '';
                              }
                              // else if (type == accountAddressSubDistrict.tr) {
                              //   editProfileController
                              //       .selectedSubDistrict!.value =
                              //       addressCtl.subDistrictsData[index]
                              //           .subDistrictNameLocal
                              //           .toString();
                              // }
                              setState(() {});
                            },
                            child: Container(
                                width: 327.w,
                                height: 52.h,
                                decoration: BoxDecoration(
                                  // color: Colors.red,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: const Color(0x191A1818),
                                      width: 0.5.w,
                                    ),
                                  ),
                                ),
                                child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      type == accountAddressProvince.tr
                                          ? regisAddressController.citiesData[index].cityNameLocal.toString()
                                          : type == accountAddressDistrict.tr
                                              ? regisAddressController.districtsData[index].districtNameLocal.toString()
                                              : type == accountAddressSubDistrict.tr
                                                  ? regisAddressController.subDistrictsData[index].subDistrictNameLocal
                                                      .toString()
                                                  : '',
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 16.sp,
                                        fontFamily: 'NotoSansThai',
                                        fontWeight: FontWeight.w400,
                                        // height: 0.19.h,
                                      ),
                                    ))),
                          ),
                        );
                      }),
                )
              ],
            ),
          );
        });
  }
}
