import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/mr/mr_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class MrRegisterSuccess extends StatefulWidget {
  const MrRegisterSuccess({super.key});

  @override
  State<MrRegisterSuccess> createState() => _MrRegisterSuccessState();
}

class _MrRegisterSuccessState extends State<MrRegisterSuccess> {
  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    // Get.put(AppConfigService(context: context));
    return Scaffold(
      body: Container(
        height: Get.height,
        width: Get.width,
        decoration: BoxDecoration(
            gradient:
            appConfigService.countryConfigCollection == "aam"?
            LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,

                colors: [
                  Color(0xFFA169FF),
                  Color(0xFF792AFF),
                ]
            )
                :appConfigService.countryConfigCollection == "rafco"?
            LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF7699FF),
                  Color(0xFF22409A),
                ]
            )
                : LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFFFC30E),
                  Color(0xFFC08F00),
                ]
            )
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding:  EdgeInsets.only(top: 81.h,bottom: 24.h),
              child: SvgPicture.string('<svg width="74" height="40" viewBox="0 0 74 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M40 36.5C42.4292 36.5 44.7833 35.6577 46.6611 34.1166C48.5389 32.5755 49.8243 30.431 50.2982 28.0484C50.7722 25.6659 50.4053 23.1927 49.2602 21.0503C48.115 18.9079 46.2625 17.2289 44.0182 16.2993C41.7739 15.3696 39.2766 15.247 36.952 15.9521C34.6274 16.6573 32.6192 18.1467 31.2696 20.1665C29.92 22.1863 29.3125 24.6116 29.5506 27.0292C29.7887 29.4467 30.8577 31.7069 32.5754 33.4246" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M44.666 23.6666L40.2347 28.9842C39.5792 29.7709 39.2514 30.1642 38.8108 30.1842C38.3702 30.2042 38.0082 29.8421 37.2841 29.118L35.3327 27.1666" stroke="white" stroke-width="1.2" stroke-linecap="round"/><g opacity="0.3"><path d="M66.7897 3.1749C66.9412 3.31915 67.1423 3.3996 67.3514 3.3996C67.5606 3.3996 67.7617 3.31915 67.9132 3.1749C67.993 3.10467 68.0569 3.01824 68.1006 2.92138C68.1444 2.82453 68.167 2.71945 68.167 2.61316C68.167 2.50688 68.1444 2.4018 68.1006 2.30495C68.0569 2.20809 67.993 2.12166 67.9132 2.05143L66.1156 0.253861C66.0454 0.174084 65.959 0.110184 65.8621 0.0664115C65.7652 0.0226394 65.6602 0 65.5539 0C65.4476 0 65.3425 0.0226394 65.2457 0.0664115C65.1488 0.110184 65.0624 0.174084 64.9921 0.253861C64.9124 0.324098 64.8485 0.410522 64.8047 0.507381C64.7609 0.604239 64.7383 0.70931 64.7383 0.8156C64.7383 0.921889 64.7609 1.02696 64.8047 1.12382C64.8485 1.22068 64.9124 1.3071 64.9921 1.37734L66.7897 3.1749Z" fill="white"/><path d="M72.52 8.90513C72.6715 9.04937 72.8726 9.12982 73.0818 9.12982C73.2909 9.12982 73.4921 9.04937 73.6435 8.90513C73.7233 8.83489 73.7872 8.74847 73.831 8.65161C73.8747 8.55475 73.8974 8.44968 73.8974 8.34339C73.8974 8.2371 73.8747 8.13203 73.831 8.03517C73.7872 7.93831 73.7233 7.85189 73.6435 7.78165L71.846 5.98408C71.7757 5.90431 71.6893 5.84041 71.5924 5.79664C71.4956 5.75286 71.3905 5.73022 71.2842 5.73022C71.1779 5.73022 71.0729 5.75286 70.976 5.79664C70.8791 5.84041 70.7927 5.90431 70.7225 5.98408C70.4978 6.32113 70.3854 6.88287 70.7225 7.10756L72.52 8.90513Z" fill="white"/><path d="M65.6652 9.13035C65.8735 9.12612 66.0732 9.04625 66.2269 8.90566L68.0245 7.10809C68.1671 6.95781 68.2453 6.75781 68.2426 6.55069C68.2398 6.34357 68.1563 6.14571 68.0099 5.99924C67.8634 5.85277 67.6656 5.76928 67.4584 5.76655C67.2513 5.76382 67.0513 5.84206 66.901 5.98461L65.1035 7.78218C65.0237 7.85242 64.9598 7.93884 64.916 8.0357C64.8722 8.13256 64.8496 8.23763 64.8496 8.34392C64.8496 8.45021 64.8722 8.55528 64.916 8.65214C64.9598 8.74899 65.0237 8.83542 65.1035 8.90566C65.2572 9.04625 65.4569 9.12612 65.6652 9.13035Z" fill="white"/><path d="M71.2843 3.3996C71.4927 3.39537 71.6923 3.3155 71.8461 3.1749L73.6436 1.37734C73.7234 1.3071 73.7873 1.22068 73.8311 1.12382C73.8749 1.02696 73.8975 0.921889 73.8975 0.8156C73.8975 0.70931 73.8749 0.604239 73.8311 0.507381C73.7873 0.410522 73.7234 0.324098 73.6436 0.253861C73.5734 0.174084 73.487 0.110184 73.3901 0.0664115C73.2933 0.0226394 73.1882 0 73.0819 0C72.9756 0 72.8705 0.0226394 72.7737 0.0664115C72.6768 0.110184 72.5904 0.174084 72.5202 0.253861L70.7226 2.05143C70.6428 2.12166 70.5789 2.20809 70.5352 2.30495C70.4914 2.4018 70.4688 2.50687 70.4688 2.61316C70.4688 2.71945 70.4914 2.82453 70.5352 2.92138C70.5789 3.01824 70.6428 3.10467 70.7226 3.1749C70.7892 3.25731 70.8758 3.32125 70.9741 3.36059C71.0725 3.39994 71.1793 3.41337 71.2843 3.3996Z" fill="white"/></g><path opacity="0.3" d="M68.5933 32.2719C69.6908 32.2553 70.7387 31.8119 71.5148 31.0358C72.2909 30.2596 72.7343 29.2117 72.751 28.1142C72.751 27.0115 72.3129 25.954 71.5332 25.1743C70.7535 24.3946 69.6959 23.9565 68.5933 23.9565C67.4906 23.9565 66.433 24.3946 65.6533 25.1743C64.8736 25.954 64.4356 27.0115 64.4356 28.1142C64.4343 28.6606 64.5409 29.2018 64.7494 29.7069C64.9579 30.2119 65.2641 30.6707 65.6504 31.0571C66.0367 31.4434 66.4956 31.7496 67.0006 31.9581C67.5057 32.1666 68.0469 32.2732 68.5933 32.2719ZM68.5933 25.6418C69.0823 25.6418 69.5603 25.7868 69.9669 26.0584C70.3735 26.3301 70.6904 26.7163 70.8775 27.1681C71.0647 27.6198 71.1136 28.117 71.0182 28.5966C70.9228 29.0762 70.6873 29.5168 70.3416 29.8625C69.9958 30.2083 69.5552 30.4438 69.0756 30.5392C68.596 30.6346 68.0989 30.5856 67.6471 30.3985C67.1953 30.2114 66.8091 29.8945 66.5375 29.4879C66.2658 29.0813 66.1208 28.6032 66.1208 28.1142C66.1264 27.4602 66.3887 26.8346 66.8511 26.3721C67.3136 25.9097 67.9393 25.6474 68.5933 25.6418Z" fill="white"/><g opacity="0.3"><path d="M10.9504 10.7009H26.3453C26.4527 10.7078 26.5603 10.6927 26.6616 10.6564C26.7628 10.6201 26.8556 10.5635 26.9341 10.4899C27.0126 10.4163 27.0751 10.3275 27.1179 10.2288C27.1607 10.1301 27.1827 10.0236 27.1827 9.91607C27.1827 9.80851 27.1607 9.70208 27.1179 9.60338C27.0751 9.50468 27.0126 9.41581 26.9341 9.34225C26.8556 9.26869 26.7628 9.21202 26.6616 9.17573C26.5603 9.13945 26.4527 9.12432 26.3453 9.13129H10.9504C10.8464 9.1288 10.743 9.14744 10.6465 9.18608C10.55 9.22472 10.4623 9.28256 10.3887 9.35609C10.3152 9.42962 10.2574 9.51731 10.2187 9.61385C10.1801 9.71039 10.1615 9.81377 10.1639 9.91773C10.0516 10.3704 10.501 10.7009 10.9504 10.7009Z" fill="white"/><path d="M0.837385 10.701H5.78152C5.88886 10.7079 5.99649 10.6928 6.09775 10.6565C6.19901 10.6203 6.29176 10.5636 6.37024 10.49C6.44873 10.4165 6.51129 10.3276 6.55406 10.2289C6.59683 10.1302 6.6189 10.0238 6.6189 9.9162C6.6189 9.80863 6.59683 9.7022 6.55406 9.6035C6.51129 9.5048 6.44873 9.41593 6.37024 9.34237C6.29176 9.26881 6.19901 9.21214 6.09775 9.17586C5.99649 9.13957 5.88886 9.12444 5.78152 9.13141H0.837385C0.730043 9.12444 0.622413 9.13957 0.52115 9.17586C0.419887 9.21214 0.327144 9.26881 0.248657 9.34237C0.170169 9.41593 0.107607 9.5048 0.0648369 9.6035C0.0220672 9.7022 0 9.80863 0 9.9162C0 10.0238 0.0220672 10.1302 0.0648369 10.2289C0.107607 10.3276 0.170169 10.4165 0.248657 10.49C0.327144 10.5636 0.419887 10.6203 0.52115 10.6565C0.622413 10.6928 0.730043 10.7079 0.837385 10.701Z" fill="white"/><path d="M12.4117 15.1985C12.4142 15.0945 12.3955 14.9912 12.3569 14.8946C12.3183 14.7981 12.2604 14.7104 12.1869 14.6369C12.1134 14.5633 12.0257 14.5055 11.9291 14.4668C11.8326 14.4282 11.7292 14.4096 11.6253 14.4121H0.837385C0.730043 14.4051 0.622413 14.4202 0.52115 14.4565C0.419887 14.4928 0.327144 14.5495 0.248657 14.623C0.170169 14.6966 0.107607 14.7854 0.0648369 14.8841C0.0220672 14.9828 0 15.0893 0 15.1968C0 15.3044 0.0220672 15.4108 0.0648369 15.5095C0.107607 15.6082 0.170169 15.6971 0.248657 15.7707C0.327144 15.8442 0.419887 15.9009 0.52115 15.9372C0.622413 15.9735 0.730043 15.9886 0.837385 15.9816H11.6253C11.7289 15.9841 11.832 15.9656 11.9283 15.9271C12.0247 15.8887 12.1122 15.8311 12.1857 15.758C12.2592 15.6848 12.3171 15.5975 12.3559 15.5013C12.3948 15.4052 12.4137 15.3022 12.4117 15.1985Z" fill="white"/><path d="M16.2319 14.4121C16.1246 14.4051 16.0169 14.4202 15.9157 14.4565C15.8144 14.4928 15.7217 14.5495 15.6432 14.623C15.5647 14.6966 15.5021 14.7854 15.4594 14.8841C15.4166 14.9828 15.3945 15.0893 15.3945 15.1968C15.3945 15.3044 15.4166 15.4108 15.4594 15.5095C15.5021 15.6082 15.5647 15.6971 15.6432 15.7707C15.7217 15.8442 15.8144 15.9009 15.9157 15.9372C16.0169 15.9735 16.1246 15.9886 16.2319 15.9816H19.7155C19.8229 15.9886 19.9305 15.9735 20.0318 15.9372C20.133 15.9009 20.2258 15.8442 20.3043 15.7707C20.3827 15.6971 20.4453 15.6082 20.4881 15.5095C20.5308 15.4108 20.5529 15.3044 20.5529 15.1968C20.5529 15.0893 20.5308 14.9828 20.4881 14.8841C20.4453 14.7854 20.3827 14.6966 20.3043 14.623C20.2258 14.5495 20.133 14.4928 20.0318 14.4565C19.9305 14.4202 19.8229 14.4051 19.7155 14.4121H16.2319Z" fill="white"/></g></svg>'),
            ),
            SizedBox(
              width: 327.w,
                child: Center(child:
                Text(mrSuccessRegister.tr,textAlign: TextAlign.center,
                  style: TextStyle(
                  color: configTheme().textTheme.titleMedium?.color,
                  fontSize:
                  configTheme().primaryTextTheme.displayMedium?.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.displayMedium?.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.displayMedium?.fontWeight,
                ),))),
            SizedBox(height: 10.h,),
            SizedBox(
              width: 327.w,
              child: Center(
                  child: Text(
                      mrSuccessRegisterDes.tr,
                  textAlign: TextAlign.center,
                      style: TextStyle(
                        color: configTheme().textTheme.titleMedium?.color,
                        fontSize:
                        configTheme().primaryTextTheme.bodySmall?.fontSize,
                        fontFamily:
                        configTheme().primaryTextTheme.bodySmall?.fontFamily,
                        fontWeight:
                        configTheme().primaryTextTheme.bodySmall?.fontWeight,
                      )
                  )),
            ),
            Spacer(),
            InkWell(
              onTap: (){
                Get.off(()=>HomeNavigator());
              },
              child: Container(
                width:  double.infinity,
                height: 52.h,
                margin: EdgeInsets.only(left: 24.w,right: 24.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: Color(0xFF1A1818).withOpacity(0.5)
                  // Check all fields
                        // appConfigService.countryConfigCollection == "aam"
                        // ? Color(0xFF1A1818).withOpacity(0.5)
                        // : appConfigService.countryConfigCollection == "rafco"
                        // ? AppColors.primaryRafco
                        // : AppColors.RPLCMyloanCard2

                ),
                child: Center(child: Text(accountOk.tr,style: TextStyle(
                  color: configTheme().textTheme.titleMedium?.color,
                  fontSize:
                  configTheme().primaryTextTheme.titleMedium?.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.titleMedium?.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.titleMedium?.fontWeight,
                ))),
              ),
            ),
            SizedBox(height: 48.h,),
          ],
        ),
      )
    );
  }
}
