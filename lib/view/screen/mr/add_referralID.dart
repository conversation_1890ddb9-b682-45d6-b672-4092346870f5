import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/mr/mr_page.dart';
import 'package:AAMG/view/screen/mr/scanid_mr.dart';
import 'package:AAMG/view/screen/mr/success_addreferalcode.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class AddReferralID extends StatefulWidget {
  const AddReferralID({super.key});

  @override
  State<AddReferralID> createState() => _AddReferralIDState();
}

class _AddReferralIDState extends State<AddReferralID> {
  bool isNoOpen = false;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MRController>(
        init: MRController(),
        builder: (mrController) => WillPopScope(
              onWillPop: () async {
                mrController.referCode.text = '';
                return true;
              },
              child: Scaffold(
                body: Container(
                    height: Get.height,
                    width: Get.width,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          height: 106.h,
                          child: Padding(
                            padding: EdgeInsets.only(top: 44.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                    mrController.referCode.text = '';
                                    // Get.back();
                                  },
                                  child: Container(
                                    height: 50.h,
                                    width: 50.w,
                                    child: Center(
                                        child: SvgPicture.string(
                                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12 12L7 7M12 12L17 17M12 12L17 7M12 12L7 17" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                                  ),
                                ),
                                Text(
                                  accountEditProfileIDMr.tr,
                                  style: TextStyle(
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontSize,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontWeight,
                                    color: Color(0xFF1A1818),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    // showDialog(
                                    //   context: context,
                                    //   // barrierDismissible: false, // ป้องกันการปิดโดยการแตะนอก Dialog
                                    //   builder: (BuildContext context) {
                                    //     return AlertDialog(
                                    //       title: Text('แจ้ง'),
                                    //       content: Text(
                                    //           'ขออภัย อยู่ในช่วงพัฒนาระบบ'),
                                    //       actions: [
                                    //         TextButton(
                                    //           onPressed: () {
                                    //             Navigator.push(
                                    //                 context,
                                    //                 MaterialPageRoute(
                                    //                     builder: (context) =>
                                    //                         MRPage()));
                                    //             Get.to(() => HomeNavigator());
                                    //           },
                                    //           child: Text('ตกลง'),
                                    //         ),
                                    //       ],
                                    //     );
                                    //   },
                                    // );
                                    Get.to(() => ScanIdMr());
                                  },
                                  child: Container(
                                    height: 50.h,
                                    width: 50.w,
                                    child: Center(
                                      child: SvgPicture.string(
                                          '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19278 2.00098C3.90748 2.00721 3.62624 2.07003 3.36541 2.18578C3.10457 2.30152 2.86928 2.46792 2.67324 2.67529C2.46657 2.87161 2.30098 3.10701 2.18604 3.36785C2.07111 3.6287 2.00916 3.9098 2.00375 4.19479V7.11388C1.99373 7.3075 2.06097 7.49719 2.19075 7.64121C2.32054 7.78524 2.50223 7.87182 2.69584 7.88195C2.79181 7.88691 2.8878 7.87292 2.97835 7.84076C3.0689 7.80859 3.15221 7.75889 3.22354 7.69451C3.29487 7.63012 3.35278 7.5523 3.39403 7.46551C3.43527 7.37872 3.45901 7.28466 3.46387 7.18869C3.46387 7.16376 3.46387 7.13881 3.46387 7.11388V4.19479C3.48917 4.0102 3.57424 3.83898 3.70609 3.70734C3.83794 3.57571 4.00931 3.49089 4.19394 3.46589H7.1131C7.30672 3.47591 7.49633 3.40862 7.64036 3.27884C7.78439 3.14906 7.87101 2.9674 7.88113 2.77379C7.88609 2.67783 7.87207 2.58183 7.8399 2.49129C7.80774 2.40074 7.75807 2.31743 7.69369 2.2461C7.6293 2.17476 7.55145 2.11681 7.46466 2.07557C7.37786 2.03433 7.28384 2.01059 7.18788 2.00572H7.1131L4.19278 2.00098ZM15.8763 2.00098C15.6827 1.99096 15.493 2.05824 15.349 2.18802C15.205 2.3178 15.1184 2.49947 15.1083 2.69307C15.0983 2.88669 15.1655 3.07638 15.2953 3.22041C15.4251 3.36443 15.6068 3.45101 15.8004 3.46114H18.7954C18.9808 3.48622 19.153 3.57145 19.2853 3.70376C19.4176 3.83607 19.5028 4.00816 19.5279 4.19359V7.11269C19.5178 7.3063 19.5852 7.49599 19.715 7.64002C19.8447 7.78405 20.0264 7.87063 20.22 7.88075C20.3159 7.88571 20.4119 7.87173 20.5025 7.83956C20.593 7.8074 20.6764 7.7577 20.7477 7.69331C20.8191 7.62892 20.877 7.55111 20.9182 7.46431C20.9595 7.37752 20.9832 7.28347 20.9881 7.1875C20.9881 7.16257 20.9881 7.13762 20.9881 7.11269V4.19359C20.9816 3.90833 20.9188 3.62718 20.803 3.36637C20.6873 3.10556 20.521 2.87026 20.3138 2.67409C20.118 2.46752 19.8832 2.30179 19.623 2.18646C19.3628 2.07114 19.0823 2.0085 18.7978 2.00217L15.8763 2.00098ZM2.72077 15.1447C2.5288 15.1494 2.34639 15.2295 2.21307 15.3677C2.07975 15.5059 2.00621 15.6911 2.00846 15.8831V18.807C2.01372 19.0919 2.07552 19.3729 2.19025 19.6337C2.30498 19.8946 2.47041 20.13 2.67686 20.3265C2.87303 20.5337 3.10829 20.6999 3.3691 20.8157C3.62991 20.9314 3.91106 20.9943 4.19633 21.0007H7.11542C7.30903 21.0108 7.49873 20.9435 7.64275 20.8137C7.78678 20.6839 7.8734 20.5023 7.88352 20.3087C7.88864 20.2127 7.87476 20.1166 7.84266 20.026C7.81055 19.9354 7.76086 19.852 7.69644 19.7806C7.63203 19.7092 7.55413 19.6513 7.46727 19.6101C7.3804 19.5689 7.28622 19.5453 7.19019 19.5406H4.19633C4.0109 19.5155 3.83884 19.4303 3.70653 19.298C3.57421 19.1656 3.48895 18.9936 3.46387 18.8081V15.889C3.46497 15.793 3.44717 15.6978 3.41141 15.6087C3.37566 15.5197 3.32264 15.4385 3.25549 15.3699C3.18834 15.3013 3.10838 15.2467 3.02008 15.2091C2.93178 15.1714 2.83689 15.1516 2.74091 15.1507H2.72548L2.72077 15.1447ZM20.2497 15.1447C20.0577 15.1494 19.8753 15.2295 19.742 15.3677C19.6087 15.5059 19.5351 15.6911 19.5374 15.8831V18.807C19.5123 18.9924 19.4271 19.1645 19.2948 19.2968C19.1625 19.4291 18.9904 19.5143 18.805 19.5394H15.8858C15.6922 19.5294 15.5026 19.5967 15.3586 19.7265C15.2145 19.8562 15.1279 20.0379 15.1178 20.2315C15.1078 20.4251 15.175 20.6148 15.3048 20.7588C15.4346 20.9028 15.6163 20.9894 15.8099 20.9995H18.805C19.3781 20.9716 19.9203 20.7314 20.326 20.3257C20.7318 19.9199 20.972 19.3777 21 18.8046V15.8855C21.001 15.7895 20.9832 15.6943 20.9474 15.6052C20.9117 15.5161 20.8587 15.4349 20.7916 15.3664C20.7244 15.2978 20.6444 15.2431 20.5561 15.2055C20.4678 15.1678 20.373 15.148 20.277 15.1471H20.2556L20.2497 15.1447ZM2.65664 10.7655C2.46374 10.7855 2.28671 10.8813 2.16431 11.0317C2.04191 11.1821 1.98416 11.375 2.00375 11.5679C2.02254 11.7481 2.10743 11.915 2.24205 12.0362C2.37667 12.1575 2.55148 12.2245 2.73265 12.2244H20.2603C20.4539 12.2143 20.6356 12.1277 20.7654 11.9837C20.8952 11.8396 20.9624 11.65 20.9524 11.4564C20.943 11.2759 20.8671 11.1052 20.7393 10.9774C20.6115 10.8496 20.4408 10.7737 20.2603 10.7643H2.65549L2.65664 10.7655ZM5.64464 4.91297C5.49924 4.91675 5.35827 4.96389 5.23983 5.04832C5.22295 5.06011 5.20668 5.07277 5.19114 5.08628H5.18042C5.1449 5.11551 5.11234 5.14811 5.08311 5.18363L5.06652 5.205L5.0392 5.24301C5.02615 5.262 5.01304 5.28217 5.00116 5.30235C4.92783 5.44101 4.90123 5.59964 4.92523 5.75465V8.56216C4.92617 8.75569 5.00378 8.94096 5.14108 9.07736C5.27837 9.21376 5.46415 9.29019 5.65769 9.28987H10.0369C10.2296 9.28893 10.4142 9.21196 10.5505 9.07569C10.6867 8.93942 10.7637 8.75487 10.7646 8.56216V5.64307C10.765 5.44954 10.6885 5.26375 10.5521 5.12646C10.4157 4.98917 10.2304 4.91156 10.0369 4.91062H5.75738C5.72171 4.90421 5.68559 4.90063 5.64935 4.89993L5.64464 4.91297ZM6.38297 6.38502H9.30213V7.84514H6.38297V6.38502ZM15.8799 4.92485C15.784 4.92971 15.69 4.95344 15.6032 4.9947C15.5165 5.03596 15.4388 5.09391 15.3745 5.16526C15.3102 5.23661 15.2606 5.31995 15.2286 5.41049C15.1965 5.50103 15.1827 5.59701 15.1878 5.69292C15.1972 5.87341 15.2732 6.04401 15.401 6.17181C15.5288 6.29962 15.6994 6.37558 15.8799 6.38502H17.3401C17.5337 6.39504 17.7233 6.32775 17.8673 6.19797C18.0114 6.06819 18.098 5.88653 18.1081 5.69292C18.1131 5.59696 18.099 5.50096 18.0669 5.41041C18.0347 5.31987 17.985 5.23652 17.9207 5.16519C17.8563 5.09386 17.7785 5.03594 17.6917 4.9947C17.6049 4.95346 17.5108 4.92972 17.4148 4.92485H15.8799ZM12.9394 4.91772C12.7465 4.9227 12.5634 5.00374 12.43 5.14316C12.2965 5.28259 12.2237 5.46911 12.2272 5.66205V8.58115C12.2171 8.77476 12.2844 8.96445 12.4142 9.10848C12.544 9.25251 12.7257 9.33908 12.9193 9.34921C13.0152 9.35417 13.1112 9.34019 13.2018 9.30802C13.2923 9.27586 13.3756 9.22615 13.447 9.16177C13.5183 9.09738 13.5762 9.01956 13.6174 8.93277C13.6587 8.84598 13.6824 8.75192 13.6873 8.65596C13.6873 8.63103 13.6873 8.60607 13.6873 8.58115V5.66205C13.6911 5.46902 13.6183 5.28233 13.4849 5.14284C13.3514 5.00334 13.1681 4.92239 12.9751 4.91772H12.9394ZM15.8728 7.84989C15.7769 7.85475 15.6829 7.87851 15.5961 7.91977C15.5094 7.96103 15.4316 8.01898 15.3673 8.09033C15.303 8.16168 15.2535 8.24502 15.2215 8.33556C15.1894 8.4261 15.1756 8.52205 15.1807 8.61795C15.1901 8.79845 15.266 8.96905 15.3938 9.09685C15.5216 9.22465 15.6923 9.30061 15.8728 9.31005H17.3329C17.5265 9.32007 17.7162 9.25279 17.8602 9.123C18.0043 8.99322 18.0909 8.81156 18.101 8.61795C18.106 8.52199 18.0919 8.426 18.0598 8.33545C18.0276 8.2449 17.9779 8.16159 17.9136 8.09026C17.8492 8.01893 17.7713 7.96097 17.6845 7.91973C17.5977 7.87849 17.5037 7.85475 17.4077 7.84989H15.8728ZM5.65051 13.6952C5.45698 13.6949 5.27127 13.7714 5.13398 13.9078C4.99668 14.0442 4.91907 14.2294 4.91813 14.423V17.342C4.90811 17.5357 4.97536 17.7254 5.10514 17.8694C5.23492 18.0134 5.41655 18.1 5.61015 18.1101C5.70612 18.1151 5.80211 18.1011 5.89266 18.0689C5.98321 18.0368 6.06659 17.9871 6.13792 17.9227C6.20925 17.8583 6.26717 17.7805 6.30841 17.6937C6.34965 17.6069 6.37339 17.5128 6.37826 17.4169C6.37826 17.3919 6.37826 17.367 6.37826 17.342V15.1566C6.57187 15.1666 6.76156 15.0993 6.90559 14.9696C7.04962 14.8398 7.13616 14.6581 7.14629 14.4645C7.15125 14.3685 7.1373 14.2725 7.10513 14.182C7.07297 14.0914 7.02323 14.0081 6.95884 13.9368C6.89446 13.8654 6.81668 13.8075 6.72988 13.7663C6.64309 13.725 6.549 13.7013 6.45303 13.6964H6.37826L5.65051 13.6952ZM8.49961 16.6144C8.30577 16.6147 8.11993 16.6918 7.98286 16.8289C7.84579 16.966 7.76863 17.1518 7.76832 17.3456C7.76863 17.5397 7.84597 17.7257 7.98329 17.8628C8.12062 17.9999 8.30672 18.0769 8.50077 18.0769C8.52568 18.0783 8.55071 18.0783 8.57562 18.0769H10.0357C10.2294 18.0869 10.4191 18.0196 10.5631 17.8898C10.7071 17.7601 10.7936 17.5784 10.8038 17.3848C10.8087 17.2888 10.7948 17.1928 10.7626 17.1023C10.7305 17.0117 10.6807 16.9284 10.6163 16.8571C10.5519 16.7857 10.4742 16.7278 10.3874 16.6866C10.3006 16.6453 10.2065 16.6216 10.1105 16.6167H8.49961V16.6144ZM12.9524 13.6952C12.7597 13.6962 12.5752 13.7732 12.439 13.9094C12.3027 14.0457 12.2257 14.2302 12.2248 14.423V17.342C12.2245 17.5356 12.3009 17.7214 12.4373 17.8587C12.5737 17.9959 12.7589 18.0736 12.9524 18.0745H17.3317C17.5254 18.0845 17.715 18.0172 17.8591 17.8875C18.0031 17.7577 18.0896 17.576 18.0998 17.3824C18.1049 17.2864 18.091 17.1904 18.0589 17.0998C18.0268 17.0091 17.9771 16.9257 17.9127 16.8544C17.8483 16.783 17.7704 16.7251 17.6836 16.6839C17.5967 16.6427 17.5025 16.6191 17.4065 16.6144H13.6873V15.1542H17.3377C17.5313 15.1642 17.721 15.0969 17.865 14.9672C18.009 14.8374 18.0956 14.6557 18.1057 14.4621C18.1107 14.3662 18.0967 14.2702 18.0646 14.1796C18.0324 14.0891 17.9827 14.0058 17.9183 13.9344C17.8539 13.8631 17.7761 13.8051 17.6893 13.7639C17.6025 13.7227 17.5084 13.6989 17.4125 13.694H12.9537L12.9524 13.6952ZM9.23207 13.6952C9.13604 13.6946 9.04084 13.7129 8.95188 13.7491C8.86292 13.7853 8.78192 13.8386 8.71357 13.9061C8.64523 13.9735 8.59085 14.0538 8.55352 14.1423C8.5162 14.2307 8.49668 14.3257 8.49606 14.4218C8.49528 14.5178 8.51343 14.613 8.54946 14.7021C8.58549 14.7911 8.63869 14.8721 8.70604 14.9406C8.77339 15.009 8.85353 15.0636 8.94196 15.101C9.03038 15.1385 9.12538 15.1582 9.22142 15.159C9.2483 15.1604 9.27524 15.1604 9.30213 15.159H10.0346C10.2282 15.169 10.4178 15.1017 10.5618 14.9719C10.7059 14.8421 10.7925 14.6605 10.8026 14.4669C10.8076 14.3709 10.7936 14.2749 10.7614 14.1844C10.7292 14.0938 10.6796 14.0105 10.6152 13.9392C10.5508 13.8678 10.4729 13.8099 10.3861 13.7686C10.2993 13.7274 10.2053 13.7037 10.1094 13.6988H9.23091L9.23207 13.6952Z" fill="#1A1818"/></svg>'),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 36.h,
                        ),
                        appConfigService.countryConfigCollection == "aam"
                            ? Container(
                                child: SvgPicture.string(
                                    '<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M10 2.5C10.69 2.5 11.25 3.06 11.25 3.75C11.25 4.44 10.69 5 10 5H6.25C5.91875 5 5.6 5.13125 5.36625 5.36625C5.13125 5.6 5 5.91875 5 6.25V9.375C5 10.065 4.44 10.625 3.75 10.625C3.06 10.625 2.5 10.065 2.5 9.375V6.25C2.5 5.255 2.895 4.30125 3.59875 3.59875C4.30125 2.895 5.255 2.5 6.25 2.5H10Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M30 5C29.31 5 28.75 4.44 28.75 3.75C28.75 3.06 29.31 2.5 30 2.5H33.75C34.745 2.5 35.6988 2.895 36.4013 3.59875C37.105 4.30125 37.5 5.255 37.5 6.25V9.375C37.5 10.065 36.94 10.625 36.25 10.625C35.56 10.625 35 10.065 35 9.375V6.25C35 5.91875 34.8688 5.6 34.6338 5.36625C34.4 5.13125 34.0812 5 33.75 5H30Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M26.25 8.75C25.56 8.75 25 8.19 25 7.5C25 6.81 25.56 6.25 26.25 6.25H30C30.995 6.25 31.9488 6.645 32.6513 7.34875C33.355 8.05125 33.75 9.005 33.75 10V17.5C33.75 18.19 33.19 18.75 32.5 18.75C31.81 18.75 31.25 18.19 31.25 17.5V10C31.25 9.66875 31.1188 9.35 30.8838 9.11625C30.65 8.88125 30.3312 8.75 30 8.75H26.25Z" fill="#792AFF"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 21.25C18.19 21.25 18.75 21.81 18.75 22.5C18.75 23.19 18.19 23.75 17.5 23.75H10C9.31 23.75 8.75 24.31 8.75 25V32.5C8.75 33.19 8.19 33.75 7.5 33.75C6.81 33.75 6.25 33.19 6.25 32.5V25C6.25 22.9288 7.92875 21.25 10 21.25H17.5Z" fill="#792AFF"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28.75 11.25C29.44 11.25 30 11.81 30 12.5C30 13.19 29.44 13.75 28.75 13.75H25C24.6688 13.75 24.35 13.8812 24.1162 14.1162C23.8812 14.35 23.75 14.6688 23.75 15V17.5C23.75 18.19 23.19 18.75 22.5 18.75C21.81 18.75 21.25 18.19 21.25 17.5V15C21.25 14.005 21.645 13.0512 22.3487 12.3487C23.0512 11.645 24.005 11.25 25 11.25H28.75Z" fill="#792AFF"/><path fill-rule="evenodd" clip-rule="evenodd" d="M11.25 33.75C10.56 33.75 10 33.19 10 32.5C10 31.81 10.56 31.25 11.25 31.25H17.5C18.19 31.25 18.75 31.81 18.75 32.5C18.75 33.19 18.19 33.75 17.5 33.75H11.25Z" fill="#792AFF"/><path fill-rule="evenodd" clip-rule="evenodd" d="M10 35C10.69 35 11.25 35.56 11.25 36.25C11.25 36.94 10.69 37.5 10 37.5H6.25C5.255 37.5 4.30125 37.105 3.59875 36.4013C2.895 35.6988 2.5 34.745 2.5 33.75V30.625C2.5 29.935 3.06 29.375 3.75 29.375C4.44 29.375 5 29.935 5 30.625V33.75C5 34.0812 5.13125 34.4 5.36625 34.6338C5.6 34.8688 5.91875 35 6.25 35H10Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M30 37.5C29.31 37.5 28.75 36.94 28.75 36.25C28.75 35.56 29.31 35 30 35H33.75C34.0812 35 34.4 34.8688 34.6338 34.6338C34.8688 34.4 35 34.0812 35 33.75V30.625C35 29.935 35.56 29.375 36.25 29.375C36.94 29.375 37.5 29.935 37.5 30.625V33.75C37.5 34.745 37.105 35.6988 36.4013 36.4013C35.6988 37.105 34.745 37.5 33.75 37.5H30Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M18.698 9.96684V14.9656C18.698 17.0367 17.0201 18.7147 14.949 18.7147H9.95023C7.87909 18.7147 6.20117 17.0367 6.20117 14.9656V9.96684C6.20117 7.89569 7.87909 6.21777 9.95023 6.21777H14.949C17.0201 6.21777 18.698 7.89569 18.698 9.96684ZM16.1987 9.96684C16.1987 9.27701 15.6388 8.71715 14.949 8.71715H9.95023C9.26041 8.71715 8.70055 9.27701 8.70055 9.96684V14.9656C8.70055 15.6554 9.26041 16.2153 9.95023 16.2153H14.949C15.6388 16.2153 16.1987 15.6554 16.1987 14.9656V9.96684Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M33.698 24.9668V29.9656C33.698 32.0367 32.0201 33.7147 29.949 33.7147H24.9502C22.8791 33.7147 21.2012 32.0367 21.2012 29.9656V24.9668C21.2012 22.8957 22.8791 21.2178 24.9502 21.2178H29.949C32.0201 21.2178 33.698 22.8957 33.698 24.9668ZM31.1987 24.9668C31.1987 24.277 30.6388 23.7171 29.949 23.7171H24.9502C24.2604 23.7171 23.7005 24.277 23.7005 24.9668V29.9656C23.7005 30.6554 24.2604 31.2153 24.9502 31.2153H29.949C30.6388 31.2153 31.1987 30.6554 31.1987 29.9656V24.9668Z" fill="#1A1818"/><path d="M14.375 30C15.7557 30 16.875 28.8807 16.875 27.5C16.875 26.1193 15.7557 25 14.375 25C12.9943 25 11.875 26.1193 11.875 27.5C11.875 28.8807 12.9943 30 14.375 30Z" fill="#792AFF"/></svg>'),
                              )
                            : appConfigService.countryConfigCollection ==
                                    "rafco"
                                ? Container(
                                    child: SvgPicture.string(
                                        '<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0.5C8.69 0.5 9.25 1.06 9.25 1.75C9.25 2.44 8.69 3 8 3H4.25C3.91875 3 3.6 3.13125 3.36625 3.36625C3.13125 3.6 3 3.91875 3 4.25V7.375C3 8.065 2.44 8.625 1.75 8.625C1.06 8.625 0.5 8.065 0.5 7.375V4.25C0.5 3.255 0.894997 2.30125 1.59875 1.59875C2.30125 0.894997 3.255 0.5 4.25 0.5H8Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28 3C27.31 3 26.75 2.44 26.75 1.75C26.75 1.06 27.31 0.5 28 0.5H31.75C32.745 0.5 33.6988 0.894997 34.4013 1.59875C35.105 2.30125 35.5 3.255 35.5 4.25V7.375C35.5 8.065 34.94 8.625 34.25 8.625C33.56 8.625 33 8.065 33 7.375V4.25C33 3.91875 32.8688 3.6 32.6338 3.36625C32.4 3.13125 32.0812 3 31.75 3H28Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M24.25 6.75C23.56 6.75 23 6.19 23 5.5C23 4.81 23.56 4.25 24.25 4.25H28C28.995 4.25 29.9488 4.645 30.6513 5.34875C31.355 6.05125 31.75 7.005 31.75 8V15.5C31.75 16.19 31.19 16.75 30.5 16.75C29.81 16.75 29.25 16.19 29.25 15.5V8C29.25 7.66875 29.1188 7.35 28.8838 7.11625C28.65 6.88125 28.3312 6.75 28 6.75H24.25Z" fill="#22409A"/><path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 19.25C16.19 19.25 16.75 19.81 16.75 20.5C16.75 21.19 16.19 21.75 15.5 21.75H8C7.31 21.75 6.75 22.31 6.75 23V30.5C6.75 31.19 6.19 31.75 5.5 31.75C4.81 31.75 4.25 31.19 4.25 30.5V23C4.25 20.9288 5.92875 19.25 8 19.25H15.5Z" fill="#22409A"/><path fill-rule="evenodd" clip-rule="evenodd" d="M26.75 9.25C27.44 9.25 28 9.81 28 10.5C28 11.19 27.44 11.75 26.75 11.75H23C22.6688 11.75 22.35 11.8812 22.1162 12.1162C21.8812 12.35 21.75 12.6688 21.75 13V15.5C21.75 16.19 21.19 16.75 20.5 16.75C19.81 16.75 19.25 16.19 19.25 15.5V13C19.25 12.005 19.645 11.0512 20.3487 10.3487C21.0512 9.645 22.005 9.25 23 9.25H26.75Z" fill="#22409A"/><path fill-rule="evenodd" clip-rule="evenodd" d="M9.25 31.75C8.56 31.75 8 31.19 8 30.5C8 29.81 8.56 29.25 9.25 29.25H15.5C16.19 29.25 16.75 29.81 16.75 30.5C16.75 31.19 16.19 31.75 15.5 31.75H9.25Z" fill="#22409A"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 33C8.69 33 9.25 33.56 9.25 34.25C9.25 34.94 8.69 35.5 8 35.5H4.25C3.255 35.5 2.30125 35.105 1.59875 34.4013C0.894997 33.6988 0.5 32.745 0.5 31.75V28.625C0.5 27.935 1.06 27.375 1.75 27.375C2.44 27.375 3 27.935 3 28.625V31.75C3 32.0812 3.13125 32.4 3.36625 32.6338C3.6 32.8688 3.91875 33 4.25 33H8Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28 35.5C27.31 35.5 26.75 34.94 26.75 34.25C26.75 33.56 27.31 33 28 33H31.75C32.0812 33 32.4 32.8688 32.6338 32.6338C32.8688 32.4 33 32.0812 33 31.75V28.625C33 27.935 33.56 27.375 34.25 27.375C34.94 27.375 35.5 27.935 35.5 28.625V31.75C35.5 32.745 35.105 33.6988 34.4013 34.4013C33.6988 35.105 32.745 35.5 31.75 35.5H28Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M16.698 7.96684V12.9656C16.698 15.0367 15.0201 16.7147 12.949 16.7147H7.95023C5.87909 16.7147 4.20117 15.0367 4.20117 12.9656V7.96684C4.20117 5.89569 5.87909 4.21777 7.95023 4.21777H12.949C15.0201 4.21777 16.698 5.89569 16.698 7.96684ZM14.1987 7.96684C14.1987 7.27701 13.6388 6.71715 12.949 6.71715H7.95023C7.26041 6.71715 6.70055 7.27701 6.70055 7.96684V12.9656C6.70055 13.6554 7.26041 14.2153 7.95023 14.2153H12.949C13.6388 14.2153 14.1987 13.6554 14.1987 12.9656V7.96684Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M31.698 22.9668V27.9656C31.698 30.0367 30.0201 31.7147 27.949 31.7147H22.9502C20.8791 31.7147 19.2012 30.0367 19.2012 27.9656V22.9668C19.2012 20.8957 20.8791 19.2178 22.9502 19.2178H27.949C30.0201 19.2178 31.698 20.8957 31.698 22.9668ZM29.1987 22.9668C29.1987 22.277 28.6388 21.7171 27.949 21.7171H22.9502C22.2604 21.7171 21.7005 22.277 21.7005 22.9668V27.9656C21.7005 28.6554 22.2604 29.2153 22.9502 29.2153H27.949C28.6388 29.2153 29.1987 28.6554 29.1987 27.9656V22.9668Z" fill="#1A1818"/><path d="M12.375 28C13.7557 28 14.875 26.8807 14.875 25.5C14.875 24.1193 13.7557 23 12.375 23C10.9943 23 9.875 24.1193 9.875 25.5C9.875 26.8807 10.9943 28 12.375 28Z" fill="#22409A"/></svg>'))
                                : Container(
                                    child: SvgPicture.string(
                                        '<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M10 2.5C10.69 2.5 11.25 3.06 11.25 3.75C11.25 4.44 10.69 5 10 5H6.25C5.91875 5 5.6 5.13125 5.36625 5.36625C5.13125 5.6 5 5.91875 5 6.25V9.375C5 10.065 4.44 10.625 3.75 10.625C3.06 10.625 2.5 10.065 2.5 9.375V6.25C2.5 5.255 2.895 4.30125 3.59875 3.59875C4.30125 2.895 5.255 2.5 6.25 2.5H10Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M30 5C29.31 5 28.75 4.44 28.75 3.75C28.75 3.06 29.31 2.5 30 2.5H33.75C34.745 2.5 35.6988 2.895 36.4013 3.59875C37.105 4.30125 37.5 5.255 37.5 6.25V9.375C37.5 10.065 36.94 10.625 36.25 10.625C35.56 10.625 35 10.065 35 9.375V6.25C35 5.91875 34.8688 5.6 34.6338 5.36625C34.4 5.13125 34.0812 5 33.75 5H30Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M26.25 8.75C25.56 8.75 25 8.19 25 7.5C25 6.81 25.56 6.25 26.25 6.25H30C30.995 6.25 31.9488 6.645 32.6513 7.34875C33.355 8.05125 33.75 9.005 33.75 10V17.5C33.75 18.19 33.19 18.75 32.5 18.75C31.81 18.75 31.25 18.19 31.25 17.5V10C31.25 9.66875 31.1188 9.35 30.8838 9.11625C30.65 8.88125 30.3312 8.75 30 8.75H26.25Z" fill="#FFC20E"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 21.25C18.19 21.25 18.75 21.81 18.75 22.5C18.75 23.19 18.19 23.75 17.5 23.75H10C9.31 23.75 8.75 24.31 8.75 25V32.5C8.75 33.19 8.19 33.75 7.5 33.75C6.81 33.75 6.25 33.19 6.25 32.5V25C6.25 22.9288 7.92875 21.25 10 21.25H17.5Z" fill="#FFC20E"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28.75 11.25C29.44 11.25 30 11.81 30 12.5C30 13.19 29.44 13.75 28.75 13.75H25C24.6688 13.75 24.35 13.8812 24.1162 14.1162C23.8812 14.35 23.75 14.6688 23.75 15V17.5C23.75 18.19 23.19 18.75 22.5 18.75C21.81 18.75 21.25 18.19 21.25 17.5V15C21.25 14.005 21.645 13.0512 22.3487 12.3487C23.0512 11.645 24.005 11.25 25 11.25H28.75Z" fill="#FFC20E"/><path fill-rule="evenodd" clip-rule="evenodd" d="M11.25 33.75C10.56 33.75 10 33.19 10 32.5C10 31.81 10.56 31.25 11.25 31.25H17.5C18.19 31.25 18.75 31.81 18.75 32.5C18.75 33.19 18.19 33.75 17.5 33.75H11.25Z" fill="#FFC20E"/><path fill-rule="evenodd" clip-rule="evenodd" d="M10 35C10.69 35 11.25 35.56 11.25 36.25C11.25 36.94 10.69 37.5 10 37.5H6.25C5.255 37.5 4.30125 37.105 3.59875 36.4013C2.895 35.6988 2.5 34.745 2.5 33.75V30.625C2.5 29.935 3.06 29.375 3.75 29.375C4.44 29.375 5 29.935 5 30.625V33.75C5 34.0812 5.13125 34.4 5.36625 34.6338C5.6 34.8688 5.91875 35 6.25 35H10Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M30 37.5C29.31 37.5 28.75 36.94 28.75 36.25C28.75 35.56 29.31 35 30 35H33.75C34.0812 35 34.4 34.8688 34.6338 34.6338C34.8688 34.4 35 34.0812 35 33.75V30.625C35 29.935 35.56 29.375 36.25 29.375C36.94 29.375 37.5 29.935 37.5 30.625V33.75C37.5 34.745 37.105 35.6988 36.4013 36.4013C35.6988 37.105 34.745 37.5 33.75 37.5H30Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M18.698 9.96684V14.9656C18.698 17.0367 17.0201 18.7147 14.949 18.7147H9.95023C7.87909 18.7147 6.20117 17.0367 6.20117 14.9656V9.96684C6.20117 7.89569 7.87909 6.21777 9.95023 6.21777H14.949C17.0201 6.21777 18.698 7.89569 18.698 9.96684ZM16.1987 9.96684C16.1987 9.27701 15.6388 8.71715 14.949 8.71715H9.95023C9.26041 8.71715 8.70055 9.27701 8.70055 9.96684V14.9656C8.70055 15.6554 9.26041 16.2153 9.95023 16.2153H14.949C15.6388 16.2153 16.1987 15.6554 16.1987 14.9656V9.96684Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M33.698 24.9668V29.9656C33.698 32.0367 32.0201 33.7147 29.949 33.7147H24.9502C22.8791 33.7147 21.2012 32.0367 21.2012 29.9656V24.9668C21.2012 22.8957 22.8791 21.2178 24.9502 21.2178H29.949C32.0201 21.2178 33.698 22.8957 33.698 24.9668ZM31.1987 24.9668C31.1987 24.277 30.6388 23.7171 29.949 23.7171H24.9502C24.2604 23.7171 23.7005 24.277 23.7005 24.9668V29.9656C23.7005 30.6554 24.2604 31.2153 24.9502 31.2153H29.949C30.6388 31.2153 31.1987 30.6554 31.1987 29.9656V24.9668Z" fill="#1A1818"/><path d="M14.375 30C15.7557 30 16.875 28.8807 16.875 27.5C16.875 26.1193 15.7557 25 14.375 25C12.9943 25 11.875 26.1193 11.875 27.5C11.875 28.8807 12.9943 30 14.375 30Z" fill="#FFC20E"/></svg>')),
                        SizedBox(
                          height: 34.h,
                        ),
                        Text(
                          mrReferCode.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium!.color,
                            fontSize:
                                configTheme().textTheme.bodySmall!.fontSize,
                            fontWeight:
                                configTheme().textTheme.bodySmall!.fontWeight,
                          ),
                        ),
                        SizedBox(
                          height: 34.h,
                        ),
                        Container(
                          height: 54.h,
                          width: 327.w,
                          decoration: BoxDecoration(
                            // color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                                color: mrController.referCode.text == ""
                                    ? Colors.black.withOpacity(0.2)
                                    : configTheme().colorScheme.primary,
                                width: 1),
                          ),
                          child: TextFormField(
                            // onTap: () {
                            //   showDialog(
                            //       context: context,
                            //       // barrierDismissible: false, // Prevent closing by tapping outside (optional)
                            //       builder: (_) => AlertDialog(
                            //             title: Text('แจ้ง'),
                            //             content:
                            //                 Text('ขออภัย อยู่ในช่วงพัฒนาระบบ'),
                            //             actions: [
                            //               TextButton(
                            //                 onPressed: () {
                            //                   Navigator.push(
                            //                       context,
                            //                       MaterialPageRoute(
                            //                           builder: (context) =>
                            //                               MRPage()));
                            //                   Get.to(() => HomeNavigator());
                            //                 },
                            //                 child: Text('ตกลง'),
                            //               ),
                            //             ],
                            //           ));
                            // },
                            // enabled: false,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium!.color,
                              fontSize:
                                  configTheme().textTheme.bodySmall!.fontSize,
                              fontWeight:
                                  configTheme().textTheme.bodySmall!.fontWeight,
                            ),
                            cursorColor: configTheme().colorScheme.primary,
                            cursorHeight: 24.h,
                            textAlign: TextAlign.center,
                            controller: mrController.referCode,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              // textAlign: TextAlign.center,
                              hintText: mrReferCodeInput.tr,
                              hintStyle: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium!
                                    .color
                                    ?.withOpacity(0.5),
                                fontSize:
                                    configTheme().textTheme.bodySmall!.fontSize,
                                fontWeight: configTheme()
                                    .textTheme
                                    .bodySmall!
                                    .fontWeight,
                              ),

                              // contentPadding: EdgeInsets.only(left: 20.w),
                            ),
                            onChanged: (value) {
                              mrController.referCode.text = value;
                              setState(() {});
                            },
                          ),
                        ),
                        Spacer(),
                        InkWell(
                          onTap: () {
                            if (mrController.referCode.text != "") {
                              print("dsdsdsdsds");
                              print(mrController.referCode.text);
                              mrController.addReferalCode();
                              // showDialog(
                              //     useSafeArea: false,
                              //     context: context,
                              //     builder: (_) => SuccessAddReferralCode());
                              // mrController.referCode.text = "";
                            } else {
                              print("error");
                            }
                          },
                          child: Container(
                            height: 54.h,
                            width: 327.w,
                            decoration: BoxDecoration(
                              color: mrController.referCode.text != ""
                                  ? configTheme().textTheme.bodyMedium!.color
                              :configTheme()
                                  .textTheme
                                  .bodyMedium!
                                  .color
                                  ?.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Center(
                              child: Text(
                                backInNewPasswordFinish.tr,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: configTheme()
                                      .textTheme
                                      .bodySmall!
                                      .fontSize,
                                  fontWeight: configTheme()
                                      .textTheme
                                      .bodySmall!
                                      .fontWeight,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 48.h,
                        )
                      ],
                    )),
              ),
            ));
  }
}
