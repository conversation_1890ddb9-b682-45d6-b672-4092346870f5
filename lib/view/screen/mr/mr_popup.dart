import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/utils/AppSvgImage.dart';

class MRPopup {
  static buildMRPrivileges(context) {
    final String country = appConfigService.countryConfigCollection.toString();

    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 364.h,
            decoration: const ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 16.h,
                ),
                Container(
                    width: 86.w,
                    height: 70.h,
                    child: Image.asset(
                      country == 'rplc'
                          ? AppImageAssets.referFriend_RPLC
                          : country == 'rafco'
                              ? AppImageAssets.referFriend_RAFCO
                              : AppImageAssets.referFriend_AAM,
                    )),
                SizedBox(
                  height: 16.h,
                ),
                SizedBox(
                  height: 20.h,
                  child: Text(
                    mrSpecial.tr,
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                          configTheme().primaryTextTheme.titleLarge?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.titleLarge?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.titleLarge?.fontWeight,
                      height: 0.09,
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
                SizedBox(
                  height: 72.h,
                  child: Text(
                    mrSpecialDes.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.5),
                      fontSize:
                          configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodySmall?.fontWeight,
                      // height: 0.13,
                    ),
                  ),
                ),
                SizedBox(
                  height: 26.h,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          mrUnderstand.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8999999761581421),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            // height: 0.14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }
}
