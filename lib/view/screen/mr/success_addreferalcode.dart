import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/mr/mr_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SuccessAddReferralCode extends StatefulWidget {
  const SuccessAddReferralCode({super.key});

  @override
  State<SuccessAddReferralCode> createState() => _SuccessAddReferralCodeState();
}

class _SuccessAddReferralCodeState extends State<SuccessAddReferralCode> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WillPopScope(
        onWillPop: () async {
          Get.back();
          return true;
        },
        child: Container(
          height: Get.height,
          width: Get.width,
          decoration: BoxDecoration(
              gradient:
              appConfigService.countryConfigCollection == "aam"?
              LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,

                  colors: [
                    Color(0xFFA169FF),
                    Color(0xFF792AFF),
                  ]
              )
              :appConfigService.countryConfigCollection == "rafco"?
              LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF7699FF),
                    Color(0xFF22409A),
                  ]
              )
                  : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFFFC30E),
                    Color(0xFFC08F00),
                  ]
              )
          ),
          child: Padding(
            padding:  EdgeInsets.only(top:81.h),
            child: Column(
              children: [
                Container(
                  child: SvgPicture.string('<svg width="74" height="40" viewBox="0 0 74 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M40 36.5C42.4292 36.5 44.7833 35.6577 46.6611 34.1166C48.5389 32.5755 49.8243 30.431 50.2982 28.0484C50.7722 25.6659 50.4053 23.1927 49.2602 21.0503C48.115 18.9079 46.2625 17.2289 44.0182 16.2993C41.7739 15.3696 39.2766 15.247 36.952 15.9521C34.6274 16.6573 32.6192 18.1467 31.2696 20.1665C29.92 22.1863 29.3125 24.6116 29.5506 27.0292C29.7887 29.4467 30.8577 31.7069 32.5754 33.4246" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M44.667 23.6667L40.2357 28.9843C39.5801 29.771 39.2523 30.1643 38.8118 30.1843C38.3712 30.2043 38.0092 29.8422 37.285 29.1181L35.3337 27.1667" stroke="white" stroke-width="1.2" stroke-linecap="round"/><g opacity="0.3"><path d="M66.7897 3.1749C66.9412 3.31915 67.1423 3.3996 67.3514 3.3996C67.5606 3.3996 67.7617 3.31915 67.9132 3.1749C67.993 3.10467 68.0569 3.01824 68.1006 2.92138C68.1444 2.82453 68.167 2.71945 68.167 2.61316C68.167 2.50688 68.1444 2.4018 68.1006 2.30495C68.0569 2.20809 67.993 2.12166 67.9132 2.05143L66.1156 0.253861C66.0454 0.174084 65.959 0.110184 65.8621 0.0664115C65.7652 0.0226394 65.6602 0 65.5539 0C65.4476 0 65.3425 0.0226394 65.2457 0.0664115C65.1488 0.110184 65.0624 0.174084 64.9921 0.253861C64.9124 0.324098 64.8485 0.410522 64.8047 0.507381C64.7609 0.604239 64.7383 0.70931 64.7383 0.8156C64.7383 0.921889 64.7609 1.02696 64.8047 1.12382C64.8485 1.22068 64.9124 1.3071 64.9921 1.37734L66.7897 3.1749Z" fill="white"/><path d="M72.52 8.90513C72.6715 9.04937 72.8726 9.12982 73.0818 9.12982C73.2909 9.12982 73.4921 9.04937 73.6435 8.90513C73.7233 8.83489 73.7872 8.74847 73.831 8.65161C73.8747 8.55475 73.8974 8.44968 73.8974 8.34339C73.8974 8.2371 73.8747 8.13203 73.831 8.03517C73.7872 7.93831 73.7233 7.85189 73.6435 7.78165L71.846 5.98408C71.7757 5.90431 71.6893 5.84041 71.5924 5.79664C71.4956 5.75286 71.3905 5.73022 71.2842 5.73022C71.1779 5.73022 71.0729 5.75286 70.976 5.79664C70.8791 5.84041 70.7927 5.90431 70.7225 5.98408C70.4978 6.32113 70.3854 6.88287 70.7225 7.10756L72.52 8.90513Z" fill="white"/><path d="M65.6652 9.13023C65.8735 9.126 66.0732 9.04613 66.2269 8.90553L68.0245 7.10797C68.1671 6.95769 68.2453 6.75769 68.2426 6.55057C68.2398 6.34345 68.1563 6.14559 68.0099 5.99912C67.8634 5.85265 67.6656 5.76916 67.4584 5.76643C67.2513 5.76369 67.0513 5.84194 66.901 5.98449L65.1035 7.78206C65.0237 7.85229 64.9598 7.93872 64.916 8.03558C64.8722 8.13243 64.8496 8.23751 64.8496 8.3438C64.8496 8.45008 64.8722 8.55516 64.916 8.65201C64.9598 8.74887 65.0237 8.8353 65.1035 8.90553C65.2572 9.04613 65.4569 9.126 65.6652 9.13023Z" fill="white"/><path d="M71.2834 3.3996C71.4917 3.39537 71.6913 3.3155 71.8451 3.1749L73.6427 1.37734C73.7224 1.3071 73.7864 1.22068 73.8301 1.12382C73.8739 1.02696 73.8965 0.921889 73.8965 0.8156C73.8965 0.70931 73.8739 0.604239 73.8301 0.507381C73.7864 0.410522 73.7224 0.324098 73.6427 0.253861C73.5724 0.174084 73.486 0.110184 73.3892 0.0664115C73.2923 0.0226394 73.1872 0 73.0809 0C72.9746 0 72.8696 0.0226394 72.7727 0.0664115C72.6759 0.110184 72.5894 0.174084 72.5192 0.253861L70.7216 2.05143C70.6419 2.12166 70.578 2.20809 70.5342 2.30495C70.4904 2.4018 70.4678 2.50687 70.4678 2.61316C70.4678 2.71945 70.4904 2.82453 70.5342 2.92138C70.578 3.01824 70.6419 3.10467 70.7216 3.1749C70.7882 3.25731 70.8748 3.32125 70.9732 3.36059C71.0715 3.39994 71.1783 3.41337 71.2834 3.3996Z" fill="white"/></g><path opacity="0.3" d="M68.5923 32.2719C69.6898 32.2553 70.7377 31.8119 71.5138 31.0358C72.29 30.2596 72.7333 29.2117 72.75 28.1142C72.75 27.0115 72.3119 25.954 71.5322 25.1743C70.7525 24.3946 69.695 23.9565 68.5923 23.9565C67.4896 23.9565 66.4321 24.3946 65.6523 25.1743C64.8726 25.954 64.4346 27.0115 64.4346 28.1142C64.4333 28.6606 64.5399 29.2018 64.7484 29.7069C64.9569 30.2119 65.2631 30.6707 65.6494 31.0571C66.0358 31.4434 66.4946 31.7496 66.9997 31.9581C67.5047 32.1666 68.0459 32.2732 68.5923 32.2719ZM68.5923 25.6418C69.0813 25.6418 69.5593 25.7868 69.9659 26.0584C70.3725 26.3301 70.6894 26.7163 70.8766 27.1681C71.0637 27.6198 71.1126 28.117 71.0172 28.5966C70.9218 29.0762 70.6864 29.5168 70.3406 29.8625C69.9948 30.2083 69.5542 30.4438 69.0746 30.5392C68.595 30.6346 68.0979 30.5856 67.6461 30.3985C67.1943 30.2114 66.8082 29.8945 66.5365 29.4879C66.2648 29.0813 66.1198 28.6032 66.1198 28.1142C66.1254 27.4602 66.3877 26.8346 66.8502 26.3721C67.3126 25.9097 67.9383 25.6474 68.5923 25.6418Z" fill="white"/><g opacity="0.3"><path d="M10.9514 10.7006H26.3463C26.4537 10.7076 26.5613 10.6925 26.6625 10.6562C26.7638 10.6199 26.8566 10.5632 26.935 10.4897C27.0135 10.4161 27.0761 10.3272 27.1189 10.2285C27.1616 10.1298 27.1837 10.0234 27.1837 9.91583C27.1837 9.80826 27.1616 9.70184 27.1189 9.60314C27.0761 9.50444 27.0135 9.41556 26.935 9.34201C26.8566 9.26845 26.7638 9.21177 26.6625 9.17549C26.5613 9.1392 26.4537 9.12408 26.3463 9.13105H10.9514C10.8474 9.12856 10.744 9.1472 10.6475 9.18584C10.5509 9.22448 10.4632 9.28232 10.3897 9.35585C10.3162 9.42937 10.2584 9.51707 10.2197 9.61361C10.1811 9.71015 10.1624 9.81353 10.1649 9.91748C10.0526 10.3702 10.502 10.7006 10.9514 10.7006Z" fill="white"/><path d="M0.837385 10.7009H5.78152C5.88886 10.7078 5.99649 10.6927 6.09775 10.6564C6.19901 10.6201 6.29176 10.5635 6.37024 10.4899C6.44873 10.4163 6.51129 10.3275 6.55406 10.2288C6.59683 10.1301 6.6189 10.0236 6.6189 9.91607C6.6189 9.80851 6.59683 9.70208 6.55406 9.60338C6.51129 9.50468 6.44873 9.41581 6.37024 9.34225C6.29176 9.26869 6.19901 9.21202 6.09775 9.17573C5.99649 9.13945 5.88886 9.12432 5.78152 9.13129H0.837385C0.730043 9.12432 0.622413 9.13945 0.52115 9.17573C0.419887 9.21202 0.327144 9.26869 0.248657 9.34225C0.170169 9.41581 0.107607 9.50468 0.0648369 9.60338C0.0220672 9.70208 0 9.80851 0 9.91607C0 10.0236 0.0220672 10.1301 0.0648369 10.2288C0.107607 10.3275 0.170169 10.4163 0.248657 10.4899C0.327144 10.5635 0.419887 10.6201 0.52115 10.6564C0.622413 10.6927 0.730043 10.7078 0.837385 10.7009Z" fill="white"/><path d="M12.4117 15.1985C12.4142 15.0945 12.3955 14.9912 12.3569 14.8946C12.3183 14.7981 12.2604 14.7104 12.1869 14.6369C12.1134 14.5633 12.0257 14.5055 11.9291 14.4668C11.8326 14.4282 11.7292 14.4096 11.6253 14.4121H0.837385C0.730043 14.4051 0.622413 14.4202 0.52115 14.4565C0.419887 14.4928 0.327144 14.5495 0.248657 14.623C0.170169 14.6966 0.107607 14.7854 0.0648369 14.8841C0.0220672 14.9828 0 15.0893 0 15.1968C0 15.3044 0.0220672 15.4108 0.0648369 15.5095C0.107607 15.6082 0.170169 15.6971 0.248657 15.7707C0.327144 15.8442 0.419887 15.9009 0.52115 15.9372C0.622413 15.9735 0.730043 15.9886 0.837385 15.9816H11.6253C11.7289 15.9841 11.832 15.9656 11.9283 15.9271C12.0247 15.8887 12.1122 15.8311 12.1857 15.758C12.2592 15.6848 12.3171 15.5975 12.3559 15.5013C12.3948 15.4052 12.4137 15.3022 12.4117 15.1985Z" fill="white"/><path d="M16.2329 14.4121C16.1256 14.4051 16.0179 14.4202 15.9167 14.4565C15.8154 14.4928 15.7227 14.5495 15.6442 14.623C15.5657 14.6966 15.5031 14.7854 15.4603 14.8841C15.4176 14.9828 15.3955 15.0893 15.3955 15.1968C15.3955 15.3044 15.4176 15.4108 15.4603 15.5095C15.5031 15.6082 15.5657 15.6971 15.6442 15.7707C15.7227 15.8442 15.8154 15.9009 15.9167 15.9372C16.0179 15.9735 16.1256 15.9886 16.2329 15.9816H19.7165C19.8238 15.9886 19.9315 15.9735 20.0327 15.9372C20.134 15.9009 20.2267 15.8442 20.3052 15.7707C20.3837 15.6971 20.4463 15.6082 20.489 15.5095C20.5318 15.4108 20.5539 15.3044 20.5539 15.1968C20.5539 15.0893 20.5318 14.9828 20.489 14.8841C20.4463 14.7854 20.3837 14.6966 20.3052 14.623C20.2267 14.5495 20.134 14.4928 20.0327 14.4565C19.9315 14.4202 19.8238 14.4051 19.7165 14.4121H16.2329Z" fill="white"/></g></svg>'),
                ),
                SizedBox(
                  height: 24.h,
                ),
                Text(mrPlaceQrDes.tr,textAlign: TextAlign.center,style: TextStyle(
                  fontSize: configTheme().textTheme.displayMedium!.fontSize,
                  color: Colors.white,
                  fontWeight: configTheme().textTheme.displayMedium!.fontWeight,
                )),
                SizedBox(
                  height: 10.h,
                ),
                Text(mrInforRgister.tr,textAlign: TextAlign.center,style: TextStyle(
                  fontSize: configTheme().textTheme.bodySmall!.fontSize,
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: configTheme().textTheme.bodySmall!.fontWeight,
                )),
                Spacer(),
                InkWell(
                  onTap: (){
                    Get.to(()=> MRPage());
                  },
                  child: Container(
                    height: 54.h,
                    width: 327.w,
                    decoration: BoxDecoration(
                      color:  configTheme().textTheme.bodyMedium!.color?.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Text(
                        accountOk.tr,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: configTheme().textTheme.bodySmall!.fontSize,
                          fontWeight: configTheme().textTheme.bodySmall!.fontWeight,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 48.h,
                )
              ]
            ),
          )
        ),
      ),
    );
  }
}
