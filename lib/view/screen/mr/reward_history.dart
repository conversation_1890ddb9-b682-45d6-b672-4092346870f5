import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../service/AppService.dart';

class RewardHistory extends StatelessWidget {
  final String point;

  RewardHistory({
    Key? key,
    required this.point,
  }) : super(key: key);

  // String point ="";


  String dateLast = DateFormat('dd/MM/yyyy').format(DateTime.now());
  final WebViewPointController webViewPointCtl =
      Get.find<WebViewPointController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            point == "" || point == "0" || point != "0"
                ? buildHead(context)
                : _buildTransection(
                    context), //todo มีรายการได้รับคะแนนจากการแนะนำ
            HeaderWidget(context),
          ],
        ));
  }

  Widget HeaderWidget(context) {
    return Container(
      height: 88.h,
      child: Padding(
        padding: EdgeInsets.only(left: 24.w, top: 44.h, right: 24.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () {
                Navigator.pop(context);
                // Get.back();
              },
              child: Container(
                height: 50.h,
                width: 50.w,
                child: Padding(
                  padding: EdgeInsets.only(top: 11.h),
                  child: Align(
                      alignment: Alignment.topLeft,
                      child: SvgPicture.string(
                        '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3430_45588)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3430_45588"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>',
                      )),
                ),
              ),
            ),
            Text(
              mrReceive.tr,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.titleLarge?.fontSize,
                fontFamily:
                    configTheme().primaryTextTheme.titleLarge?.fontFamily,
                fontWeight:
                    configTheme().primaryTextTheme.titleLarge?.fontWeight,
              ),
            ),
            SizedBox(
              width: 50.w,
              height: 50.h,
            )
          ],
        ),
      ),
    );
  }

  Widget buildHead(BuildContext context) {
    final WebViewPointController webViewPointCtl =
        Get.find<WebViewPointController>();
    final appName = appConfigService
        .countryConfigCollection
        .toString();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 90.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                mrTatal.tr,
                style: TextStyle(
                  color: configTheme()
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.5),
                  fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.bodySmall?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.bodySmall?.fontWeight,
                ),
              ),
              Container(
                height: 36.h,
                // color: Colors.red,
                child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    // mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        child: SvgPicture.string(
                            // '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M25.455 14C25.455 20.326 20.3265 25.4545 14.0004 25.4545C7.6744 25.4545 2.5459 20.326 2.5459 14C2.5459 7.67391 7.6744 2.54541 14.0004 2.54541C17.0384 2.54541 19.952 3.75228 22.1001 5.90032C24.2481 8.04836 25.455 10.962 25.455 14Z" fill="#FF9300" fill-opacity="0.5"/><path d="M25.455 14C25.455 20.326 20.3265 25.4545 14.0004 25.4545C7.6744 25.4545 2.5459 20.326 2.5459 14C2.5459 7.67391 7.6744 2.54541 14.0004 2.54541C17.0384 2.54541 19.952 3.75228 22.1001 5.90032C24.2481 8.04836 25.455 10.962 25.455 14Z" stroke="#1A1818"/><path d="M21.636 14.0001C21.636 18.2178 18.2169 21.6365 13.9996 21.6365C9.782 21.6365 6.36328 18.2174 6.36328 14.0001C6.36328 11.9746 7.1678 10.0323 8.5998 8.60028C10.0318 7.16829 11.9742 6.36377 13.9996 6.36377C18.2173 6.36377 21.636 9.78289 21.636 14.0001Z" fill="#FF9300" fill-opacity="0.2"/><path d="M10.1816 19.091V12.9106C10.1816 10.7007 11.9731 8.90918 14.1831 8.90918C14.9523 8.90918 15.6077 9.05667 16.1491 9.35165C16.6905 9.64332 17.1032 10.0493 17.3872 10.5697C17.6744 11.0867 17.818 11.6833 17.818 12.3595C17.818 13.0356 17.6727 13.6322 17.3822 14.1492C17.0917 14.6663 16.6707 15.069 16.1194 15.3573C15.5713 15.6457 14.9077 15.7899 14.1286 15.7899H11.5782V14.0647H13.7819C14.1946 14.0647 14.5347 13.9935 14.8021 13.8509C15.0728 13.7051 15.2742 13.5046 15.4063 13.2494C15.5416 12.9909 15.6093 12.6942 15.6093 12.3595C15.6093 12.0214 15.5416 11.7264 15.4063 11.4745C15.2742 11.2193 15.0728 11.0221 14.8021 10.8829C14.5314 10.7404 14.188 10.6691 13.772 10.6691C12.9734 10.6691 12.326 11.3165 12.326 12.1152V19.091H10.1816Z" fill="#F9F9F9"/></svg>'
                            webViewPointCtl.point_icon.value),
                      ),
                      SizedBox(
                        width: 4.w,
                      ),
                      Container(
                        padding: EdgeInsets.only(top: 5.h),
                        child: Center(
                            child: Text(
                          "0",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: 24.sp,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontFamily,
                              fontWeight: FontWeight.w600,
                              height: 1.5.h),
                        )),
                      ),
                    ]),
              )
            ],
          ),
        ),
        SizedBox(
          height: 24.h,
        ),
        Divider(
          color: configTheme().dividerColor.withOpacity(0.08),
          indent: 24,
          endIndent: 24,
        ),
        Padding(
          padding: EdgeInsets.only(top: 34.h, bottom: 14.h),
          child: Container(
            child: SvgPicture.string(
                '<svg width="38" height="41" viewBox="0 0 38 41" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M37.4993 18.4996C37.4993 8.28246 29.2168 0 18.9996 0C8.78246 0 0.5 8.28246 0.5 18.4996C0.5 27.9735 7.62163 35.7831 16.8033 36.8696L17.8519 38.6855L19.1184 40.8789L20.3849 38.6855L21.4517 36.8375C30.5095 35.6381 37.5 27.8854 37.5 18.4996H37.4993Z" fill="#792AFF" fill-opacity="0.08"/><path d="M17.5069 19.8426L17.0726 13.3418C16.991 12.0753 16.9502 11.1659 16.9502 10.6143C16.9502 9.86372 17.1469 9.27784 17.5404 8.85664C17.9339 8.43618 18.4521 8.22559 19.094 8.22559C19.8716 8.22559 20.3919 8.49448 20.6549 9.03299C20.9173 9.57151 21.0484 10.3468 21.0484 11.3605C21.0484 11.9573 21.0164 12.5643 20.9537 13.1793L20.37 19.8696C20.3066 20.666 20.1711 21.2767 19.9627 21.7015C19.7543 22.1271 19.4103 22.3391 18.9315 22.3391C18.4528 22.3391 18.1037 22.1337 17.9135 21.7219C17.7234 21.3102 17.5878 20.6835 17.5062 19.8426H17.5069ZM19.0139 28.7722C18.4615 28.7722 17.9799 28.5936 17.5689 28.2358C17.1571 27.8788 16.9517 27.3789 16.9517 26.7362C16.9517 26.1751 17.1484 25.6977 17.5419 25.3042C17.9354 24.9107 18.4171 24.714 18.9869 24.714C19.5568 24.714 20.0428 24.9107 20.4458 25.3042C20.848 25.6977 21.0499 26.1751 21.0499 26.7362C21.0499 27.3694 20.8466 27.8671 20.4392 28.2293C20.0319 28.5914 19.5568 28.7722 19.0146 28.7722H19.0139Z" fill="#792AFF" fill-opacity="0.15"/></svg>',
                allowDrawingOutsideViewBox: true,
              color: appName.contains("rafco") ? AppColors.primaryRafco :appName.contains("rplc") ? AppColors.primaryRPLC_Yellow : AppColors.AAMPurple,

            ),
          ),
        ),
        Center(
            child: Text(
          mrReward.tr,
          textAlign: TextAlign.center,
          style: TextStyle(
              color:
                  configTheme().textTheme.bodyMedium?.color?.withOpacity(0.35),
              fontSize: configTheme().textTheme.bodySmall?.fontSize,
              fontFamily: configTheme().primaryTextTheme.bodySmall?.fontFamily,
              fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight,
              height: 1.5.h),
        ))
      ],
    );
  }

  Widget _buildTransection(context) {
    return Container(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 90.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(mrTatal.tr),
                Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    // mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        child: SvgPicture.string(
                            // '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M25.455 14C25.455 20.326 20.3265 25.4545 14.0004 25.4545C7.6744 25.4545 2.5459 20.326 2.5459 14C2.5459 7.67391 7.6744 2.54541 14.0004 2.54541C17.0384 2.54541 19.952 3.75228 22.1001 5.90032C24.2481 8.04836 25.455 10.962 25.455 14Z" fill="#FF9300" fill-opacity="0.5"/><path d="M25.455 14C25.455 20.326 20.3265 25.4545 14.0004 25.4545C7.6744 25.4545 2.5459 20.326 2.5459 14C2.5459 7.67391 7.6744 2.54541 14.0004 2.54541C17.0384 2.54541 19.952 3.75228 22.1001 5.90032C24.2481 8.04836 25.455 10.962 25.455 14Z" stroke="#1A1818"/><path d="M21.636 14.0001C21.636 18.2178 18.2169 21.6365 13.9996 21.6365C9.782 21.6365 6.36328 18.2174 6.36328 14.0001C6.36328 11.9746 7.1678 10.0323 8.5998 8.60028C10.0318 7.16829 11.9742 6.36377 13.9996 6.36377C18.2173 6.36377 21.636 9.78289 21.636 14.0001Z" fill="#FF9300" fill-opacity="0.2"/><path d="M10.1816 19.091V12.9106C10.1816 10.7007 11.9731 8.90918 14.1831 8.90918C14.9523 8.90918 15.6077 9.05667 16.1491 9.35165C16.6905 9.64332 17.1032 10.0493 17.3872 10.5697C17.6744 11.0867 17.818 11.6833 17.818 12.3595C17.818 13.0356 17.6727 13.6322 17.3822 14.1492C17.0917 14.6663 16.6707 15.069 16.1194 15.3573C15.5713 15.6457 14.9077 15.7899 14.1286 15.7899H11.5782V14.0647H13.7819C14.1946 14.0647 14.5347 13.9935 14.8021 13.8509C15.0728 13.7051 15.2742 13.5046 15.4063 13.2494C15.5416 12.9909 15.6093 12.6942 15.6093 12.3595C15.6093 12.0214 15.5416 11.7264 15.4063 11.4745C15.2742 11.2193 15.0728 11.0221 14.8021 10.8829C14.5314 10.7404 14.188 10.6691 13.772 10.6691C12.9734 10.6691 12.326 11.3165 12.326 12.1152V19.091H10.1816Z" fill="#F9F9F9"/></svg>'
                            webViewPointCtl.point_icon.value),
                      ),
                      SizedBox(
                        width: 4.w,
                      ),
                      Container(
                        padding: EdgeInsets.only(top: 5.h),
                        child: Center(
                            child: Text(
                          point,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: 24.sp,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontFamily,
                              fontWeight: FontWeight.w600,
                              height: 1.5.h),
                        )),
                      ),
                    ]),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 24.h),
            child: Container(
              height: 48.h,
              width: Get.width,
              decoration: BoxDecoration(
                border: Border.all(
                    color: Color(0xFF1A1818).withOpacity(0.08), width: 1),
                // color: Color(0xFF995DFE),
                borderRadius: BorderRadius.all(Radius.circular(32)),
              ),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 24.w, right: 16.w),
                    height: 24.h,
                    width: 24.w,
                    child: Center(
                        child: SvgPicture.string(
                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 20L15.9497 15.9497M15.9497 15.9497C17.2165 14.683 18 12.933 18 11C18 7.13401 14.866 4 11 4C7.13401 4 4 7.13401 4 11C4 14.866 7.13401 18 11 18C12.933 18 14.683 17.2165 15.9497 15.9497Z" stroke="#1A1818" stroke-opacity="0.2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                  ),

                  /// ค้นหา
                  Expanded(
                    child: TextFormField(
                      style: TextStyle(
                        color: Color(0xFF1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall
                            ?.fontWeight,
                      ),
                      decoration: InputDecoration(
                        // prefixIcon: Icon(Icons.search_rounded),
                        // icon: Icon(Icons.search),
                        hintText: search.tr,
                        hintStyle: TextStyle(
                          color: Color(0xFF1A1818).withOpacity(0.5),
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontWeight,
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),

          ///listview
          Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(AppService.dateLocalized(DateTime.now(), "th"),
                        style: TextStyle(
                          color: Color(0xFF1A1818).withOpacity(0.5),
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontWeight,
                        )),
                    Expanded(
                      child: Divider(
                        height: 1,
                        color: Color(0xFF1A1818).withOpacity(0.08),
                        indent: 10.w,
                        endIndent: 10.w,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 14.h,
                ),
                transectionDetail(context, "sdsd", "12:21", "test", "500")
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget transectionDetail(
      context, String status, String time, String name, String point) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              height: 46.h,
              width: 46.w,
              decoration: const BoxDecoration(
                  color: Color(0xFF995DFE), shape: BoxShape.circle),
              child: const Icon(
                Icons.person,
                color: Colors.white,
              ),
            ),
            SizedBox(
              width: 20.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(status,
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: configTheme()
                          .primaryTextTheme
                          .bodySmall
                          ?.fontSize,
                      fontFamily: configTheme()
                          .primaryTextTheme
                          .bodySmall
                          ?.fontFamily,
                      fontWeight: configTheme()
                          .primaryTextTheme
                          .bodySmall
                          ?.fontWeight,
                    )),
                Text(DateFormat('HH:mm').format(DateTime.now()))
              ],
            ),
          ],
        ),
        Column(
          children: [Text("+$point",style: TextStyle(
            color: Color(0xFF1A1818),
            fontSize: configTheme()
                .primaryTextTheme
                .bodySmall
                ?.fontSize,
            fontFamily: configTheme()
                .primaryTextTheme
                .bodySmall
                ?.fontFamily,
            fontWeight: configTheme()
                .primaryTextTheme
                .bodySmall
                ?.fontWeight,
          ),), Text(name)],
        )
      ],
    );
  }
}
