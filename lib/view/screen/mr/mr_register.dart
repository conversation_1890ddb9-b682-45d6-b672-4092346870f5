import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/screen/mr/mr_register_success.dart';
import 'package:AAMG/view/screen/register/register_mr_scan.dart';
import 'package:AAMG/view/screen/register/register_scan.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class MrRegister extends StatefulWidget {
  const MrRegister({super.key});

  @override
  State<MrRegister> createState() => _MrRegisterState();
}

class _MrRegisterState extends State<MrRegister> {
  final MRController mrController = Get.put(MRController(), tag: UniqueKey().toString(), permanent: false);

  final HomeController homeController = Get.find<HomeController>();
  final RegisterController registerController = Get.put(RegisterController());
  final ProfileController profileCtl = Get.find<ProfileController>();
  late TextEditingController nameCtrl;

  @override
  void initState() {
    super.initState();
    nameCtrl = TextEditingController();
  }

  @override
  void dispose() {
    nameCtrl.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    // Get.put(AppConfigService(context: context));
    mrController.enterphoneController.text = profileCtl.profile.value.phone.toString();
    return Scaffold(
        body: SingleChildScrollView(
            child: GetBuilder<MRController>(
                builder: (controller) =>
                    Container(
                      height: Get.height,
                      width: Get.width,
                      color: Colors.white,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 24,
                          right: 24,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              color: Colors.white,
                              height: 88.h,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      Get.back();
                                    },
                                    child: Container(
                                      height: 50.h,
                                      width: 50.w,
                                      child: Center(
                                          child: SvgPicture.string(
                                              '<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 12.5L7 7.5M12 12.5L17 17.5M12 12.5L17 7.5M12 12.5L7 17.5" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                                    ),
                                  ),
                                   Container(
                                     margin: EdgeInsets.only(bottom: 12.5.h),
                                     child: Text(mrSignUp.tr,
                                      style: TextStyle(
                                        color: configTheme().textTheme.bodyMedium?.color,
                                        fontSize:
                                        configTheme().primaryTextTheme.titleLarge?.fontSize,
                                        fontFamily:
                                        configTheme().primaryTextTheme.titleLarge?.fontFamily,
                                        fontWeight:
                                        configTheme().primaryTextTheme.titleLarge?.fontWeight,
                                        height: 0,
                                      ),),
                                   ),
                                  Container(
                                    height: 50.h,
                                    width: 50.w,
                                  )
                                ],
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                 Text(accountEditProfileName.tr,style: TextStyle(
                                   color: configTheme().textTheme.bodyMedium?.color,
                                   fontSize:
                                   configTheme().primaryTextTheme.bodySmall?.fontSize,
                                   fontFamily:
                                   configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                   fontWeight:
                                   configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                   height: 0,
                                 )),
                                Container(
                                    // width: 165.w,
                                    // height: 50.h,
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      // crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                         InkWell(
                                           onTap: () {
                                              Get.to(const RegisterMRScanScreen());
                                           },
                                         
                                        // SizedBox(width: 4.w),
                                        child: Container(
                                          height: 26.h,
                                          width: 26.w,
                                          margin: EdgeInsets.only(top: 5.h,left: 4.w),
                                          child: SvgPicture.string(
                                              '<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="26" height="26" rx="13" fill="#1A1818"/> <mask id="mask0_3430_37729" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="6" y="6" width="14" height="14"><path d="M20 6H6V19.998H20V6Z" fill="white"/></mask><g mask="url(#mask0_3430_37729)"><path d="M19.3515 15.5226C19.1793 15.5239 19.0146 15.5931 18.8931 15.7153C18.7717 15.8374 18.7035 16.0026 18.7032 16.1748V17.9322C18.703 18.134 18.6227 18.3275 18.4799 18.4702C18.3372 18.613 18.1437 18.6932 17.9419 18.6935H16.1737C16.0008 18.6935 15.8348 18.7622 15.7125 18.8845C15.5902 19.0069 15.5215 19.1728 15.5215 19.3458C15.5215 19.5188 15.5902 19.6847 15.7125 19.807C15.8348 19.9293 16.0008 19.998 16.1737 19.998H17.9419C18.489 19.998 19.0137 19.7807 19.4006 19.3938C19.7874 19.007 20.0048 18.4823 20.0048 17.9352V16.1748C20.0045 16.0026 19.9363 15.8374 19.8148 15.7153C19.6934 15.5931 19.5287 15.5239 19.3565 15.5226" fill="white"/><path d="M9.82613 18.6994H8.06778C7.86595 18.6991 7.67246 18.6188 7.52975 18.4761C7.38703 18.3334 7.30674 18.1399 7.30648 17.9381V16.1758C7.30622 16.0029 7.23742 15.8372 7.11515 15.7149C6.99289 15.5926 6.82713 15.5238 6.65422 15.5236C6.56849 15.5233 6.48354 15.54 6.40424 15.5726C6.32494 15.6052 6.25285 15.6531 6.19208 15.7136C6.13132 15.7741 6.08308 15.8459 6.05012 15.9251C6.01716 16.0042 6.00013 16.0891 6 16.1748V17.9371C6.00052 18.4839 6.21809 19.0081 6.60489 19.3945C6.9917 19.781 7.5161 19.998 8.06287 19.998H9.84185C10.0023 19.9796 10.1502 19.9024 10.257 19.7812C10.3637 19.66 10.4217 19.5036 10.4198 19.3421C10.4178 19.1806 10.356 19.0256 10.2464 18.9071C10.1367 18.7885 9.98697 18.7139 9.82613 18.6994Z" fill="white"/><path d="M8.82227 12.9991C8.82253 13.172 8.89133 13.3377 9.0136 13.46C9.13586 13.5823 9.30161 13.6511 9.47452 13.6513H16.5256C16.6986 13.6513 16.8645 13.5826 16.9868 13.4603C17.1091 13.338 17.1779 13.1721 17.1779 12.9991C17.1779 12.8261 17.1091 12.6602 16.9868 12.5378C16.8645 12.4155 16.6986 12.3468 16.5256 12.3468H9.47452C9.30161 12.3471 9.13586 12.4159 9.0136 12.5381C8.89133 12.6604 8.82253 12.8262 8.82227 12.9991Z" fill="white"/><path d="M6.00026 9.92927C6.01427 10.0919 6.08872 10.2433 6.20893 10.3537C6.32914 10.4641 6.48637 10.5254 6.64957 10.5255H6.70556C6.79101 10.5187 6.87422 10.4949 6.95031 10.4554C7.02639 10.4159 7.09381 10.3616 7.14859 10.2957C7.20369 10.2322 7.24568 10.1585 7.27215 10.0788C7.29862 9.99903 7.30904 9.91483 7.30281 9.83104V8.06287C7.3041 7.86189 7.38472 7.66956 7.52711 7.52772C7.6695 7.38588 7.86214 7.30601 8.06312 7.3055H9.8313C10 7.29895 10.1597 7.22732 10.2768 7.10562C10.3938 6.98392 10.4592 6.82162 10.4592 6.65275C10.4592 6.48389 10.3938 6.32158 10.2768 6.19988C10.1597 6.07818 10 6.00655 9.8313 6H8.06312C7.51602 6 6.99132 6.21734 6.60446 6.6042C6.21759 6.99106 6.00026 7.51576 6.00026 8.06287V9.81631C5.99731 9.8539 5.99731 9.89168 6.00026 9.92927Z" fill="white"/><path d="M17.938 6H16.1698C15.9969 6.00026 15.8312 6.06906 15.7089 6.19133C15.5866 6.3136 15.5178 6.47935 15.5176 6.65226C15.5204 6.82364 15.5903 6.98709 15.7122 7.10756C15.8341 7.22803 15.9984 7.29591 16.1698 7.29666H17.9302C18.132 7.29692 18.3255 7.37721 18.4682 7.51993C18.6109 7.66264 18.6912 7.85613 18.6914 8.05796V9.83104C18.6914 9.9167 18.7083 10.0015 18.7411 10.0807C18.7739 10.1598 18.8219 10.2317 18.8825 10.2923C18.9431 10.3528 19.015 10.4009 19.0941 10.4337C19.1732 10.4664 19.2581 10.4833 19.3437 10.4833C19.4294 10.4833 19.5142 10.4664 19.5933 10.4337C19.6725 10.4009 19.7444 10.3528 19.8049 10.2923C19.8655 10.2317 19.9135 10.1598 19.9463 10.0807C19.9791 10.0015 19.996 9.9167 19.996 9.83104V8.06287C19.996 7.51576 19.7786 6.99106 19.3918 6.6042C19.0049 6.21734 18.4802 6 17.9331 6" fill="white"/></g></svg>'),
                                        )),
                                      ],
                                    )),
                              ],
                            ),
                            // SizedBox(height: 8.h),

                           buildTextInputInfoMr(mrController.enterfullnameController,mrInputName.tr,""),
                            SizedBox(height: 14.h),
                            Text(accountEditProfileIDCard.tr,
                              style: TextStyle(
                                color: configTheme().textTheme.bodyMedium?.color,
                                fontSize:
                                configTheme().primaryTextTheme.bodySmall?.fontSize,
                                fontFamily:
                                configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                fontWeight:
                                configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                // height: 0,
                              ),),
                            buildTextInputInfoMr(mrController.idcardvalueoneController,"0-0000-00-000-00-0",accountEditProfileIDCard.tr),
                            SizedBox(height: 14.h),
                            Text(signInPhone.tr,
                              style: TextStyle(
                                color: configTheme().textTheme.bodyMedium?.color,
                                fontSize:
                                configTheme().primaryTextTheme.bodySmall?.fontSize,
                                fontFamily:
                                configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                fontWeight:
                                configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                // height: 0,
                              ),),
                            buildTextInputInfoMr(mrController.enterphoneController,"080-1234567",signInPhone.tr),
                            SizedBox(height: 14.h),
                            Text(mrOccupation.tr,
                              style: TextStyle(
                                color: configTheme().textTheme.bodyMedium?.color,
                                fontSize:
                                configTheme().primaryTextTheme.bodySmall?.fontSize,
                                fontFamily:
                                configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                fontWeight:
                                configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                // height: 0,
                              ),),
                            buildTextInputInfoMr(mrController.enteroccupationController,mrOccupationInput.tr,mrOccupation.tr),
                            SizedBox(height: 38.h),
                            Row(
                              // mainAxisSize: MainAxisSize.max,
                              children: [
                                Container(
                                  width: 2.w,
                                  height: 40.h,
                                  decoration: BoxDecoration(
                                    color: appConfigService
                                        .countryConfigCollection ==
                                        "aam"
                                        ? AppColors.AAMPurple
                                        : appConfigService
                                        .countryConfigCollection ==
                                        "rafco"
                                        ? AppColors.primaryRafco
                                        : AppColors.RPLCMyloanCard2,
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                ),
                                SizedBox(width: 12.w),
                                Text(mrJoinRefer.tr,
                                  overflow: TextOverflow.visible,
                                  style: TextStyle(
                                    color: configTheme().textTheme.bodyMedium?.color,
                                    fontSize:
                                    configTheme().primaryTextTheme.titleLarge?.fontSize,
                                    fontFamily:
                                    configTheme().primaryTextTheme.titleLarge?.fontFamily,
                                    fontWeight:
                                    configTheme().primaryTextTheme.titleLarge?.fontWeight,
                                  ),
                                )
                                // Expanded(
                                //   child: Text(
                                //     "msg4".tr,
                                //     // maxLines: 2,
                                //     overflow: TextOverflow.ellipsis,
                                //     // style: theme.textTheme.titleMedium!.copyWith(
                                //     //   height: 1.50,
                                //     // ),
                                //   ),
                                // )
                              ],
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Text(mrSecurity.tr,
                              style: TextStyle(
                                color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                fontSize:
                                configTheme().primaryTextTheme.bodySmall?.fontSize,
                                fontFamily:
                                configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                fontWeight:
                                configTheme().primaryTextTheme.bodySmall?.fontWeight,
                              ),
                            ),
                            SizedBox(height: 24.h),
                            Row(
                              children: [
                                InkWell(
                                  onTap: () {
                                    controller.acceptPolicy();
                                    // policyCtl.acceptTermPolicy();
                                  },
                                  child: Container(
                                    width: 20.w,
                                    height: 20.h,
                                    decoration: ShapeDecoration(
                                      color: controller
                                          .checkPolicy!
                                          .value
                                          ? appConfigService
                                          .countryConfigCollection ==
                                          "aam"
                                          ? AppColors
                                          .AAMPurple
                                          : appConfigService
                                          .countryConfigCollection ==
                                          "rafco"
                                          ? AppColors
                                          .primaryRafco
                                          : AppColors
                                          .RPLCMyloanCard2
                                          : Colors.transparent,
                                      shape: OvalBorder(
                                        side: BorderSide(
                                            width: 1.w,
                                            color: appConfigService
                                                .countryConfigCollection ==
                                                "aam"
                                                ? AppColors
                                                .AAMPurple
                                                .withOpacity(
                                                0.25)
                                                : appConfigService
                                                .countryConfigCollection ==
                                                "rafco"
                                                ? AppColors
                                                .primaryRafco
                                                .withOpacity(
                                                0.25)
                                                : AppColors
                                                .RPLCMyloanCard2
                                                .withOpacity(
                                                0.25)),
                                      ),
                                    ),
                                    child: SvgPicture.string(
                                        AppSvgImage.check),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                SizedBox(
                                  // width: 158,
                                  height: 20,
                                  child: Center(
                                    child: Text(
                                      policyClickTerms.tr,
                                      style: TextStyle(
                                        color: configTheme().textTheme.bodyMedium?.color,
                                        fontSize:
                                        configTheme().primaryTextTheme.bodySmall?.fontSize,
                                        fontFamily:
                                        configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                        fontWeight:
                                        configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                      ),
                                    ),
                                  ),
                                ),

                              ],
                            ),
                            Spacer(),
                            InkWell(
                              onTap: (){
                                if(controller.checkPolicy!.value != false &&
                                    mrController.enterfullnameController.text != "" &&
                                    mrController.idcardvalueoneController.text != "" &&
                                    mrController.enterphoneController.text != "" &&
                                    mrController.enteroccupationController.text != ""){
                                  mrController.registerMR();
                                  Get.to(()=>MrRegisterSuccess());
                                }else{
                                  print("Please fill all fields");
                                }
                              },
                              child: Container(
                                width:  double.infinity,
                                height: 52.h,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16),
                                  color: (controller.checkPolicy!.value != false &&
                                      mrController.enterfullnameController.text != "" &&
                                      mrController.idcardvalueoneController.text != "" &&
                                      mrController.enterphoneController.text != "" &&
                                      mrController.enteroccupationController.text != "") // Check all fields
                                      ? appConfigService.countryConfigCollection == "aam"
                                      ? Color(0xFF995DFE)
                                      : appConfigService.countryConfigCollection == "rafco"
                                      ? AppColors.primaryRafco
                                      : AppColors.RPLCMyloanCard2
                                      : Color(0xFF1A181826).withOpacity(0.15)
                                ),
                                child: Center(child: Text(mrRegister.tr,
                                  style: TextStyle(
                                    color: configTheme().textTheme.titleMedium?.color,
                                    fontSize:
                                    configTheme().primaryTextTheme.titleMedium?.fontSize,
                                    fontFamily:
                                    configTheme().primaryTextTheme.titleMedium?.fontFamily,
                                    fontWeight:
                                    configTheme().primaryTextTheme.titleMedium?.fontWeight,
                                  ),
                                )),
                              ),
                            ),
                            SizedBox(height: 40.h),
                          ],
                        ),
                      ),
                    )
            )));
  }
  buildTextInputInfoMr(controller,hintText,text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(text),
        SizedBox(height: 8.h),
        Container(
          height: 54.h,
          // color: Colors.teal,
          child: TextFormField(
              controller: controller,
              keyboardType: hintText == mrInputName.tr || hintText == mrOccupationInput.tr?TextInputType.text :TextInputType.number,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize:
                configTheme().primaryTextTheme.bodyMedium?.fontSize,
                fontFamily:
                configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                fontWeight:
                configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                height: 1.h,
              ),
              // textAlign: TextAlign.center,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle:  TextStyle(
                  color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.35),
                  fontSize:
                  configTheme().primaryTextTheme.bodySmall?.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.bodySmall?.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.bodySmall?.fontWeight,
                  height: 1.h,
                ),
                fillColor: const Color(0xffF5F5F5),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide.none,
                ),

              )),
        ),
      ],
    );
  }
}
