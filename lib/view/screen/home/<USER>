import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';

class UpdatePersonalDataPage extends StatelessWidget {
  const UpdatePersonalDataPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileController>(builder: (profileCtl) {
      return Container(
        color: configTheme().colorScheme.onPrimary,
        width: Get.width,
        height: 56.h,
        padding: EdgeInsets.only(right: 24.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 56.h,
              child: Image.asset(
                  appConfigService.countryConfigCollection.toString() == 'aam'
                      ? 'assets/poi/update_data.png'
                      : appConfigService.countryConfigCollection.toString() ==
                              'rafco'
                          ? 'assets/poi/update_data_rafco.png'
                          : 'assets/poi/update_data_rplc.png'),
            ),
            SizedBox(width: 12.w),
            SizedBox(
              width: 120.w,
              height: 36.h,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 120.w,
                    height: 18.h,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'เพิ่มข้อมูลที่อยู่',
                      //TODO เข็ค personal data ตามลำดับ ถ้าไม่มีข้อมูลให้แสดงข้อความตามลำดับ **แสดงผลแค่อันเดียว
                      //'เพิ่มข้อมูลบัตรประชาชน' : 'เพิ่มข้อมูลอีเมล์': 'เพิ่มข้อมูลยืนยัน KYC' : 'เพิ่มข้อมูลบัญชีธนาคาร'
                      style: TextStyle(
                        color: Color(0xFFF9F9F9),
                        fontSize: configTheme().primaryTextTheme.labelSmall?.fontSize,
                        fontFamily: configTheme().primaryTextTheme.labelSmall?.fontFamily,
                        fontWeight: configTheme().primaryTextTheme.labelSmall?.fontWeight,
                        height: 0.12,
                      ),
                    ),
                  ),
                  Container(
                    width: 120.w,
                    height: 18.h,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'รับ 300 Point',
                      style: TextStyle(
                        color: Color(0xFFFFE500),
                        fontSize: configTheme().primaryTextTheme.labelLarge?.fontSize,
                        fontFamily: configTheme().primaryTextTheme.labelLarge?.fontFamily,
                        fontWeight: configTheme().primaryTextTheme.labelLarge?.fontWeight,
                        height: 0.12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 55.w),
            Container(
              width: 66.w,
              height: 28.h,
              decoration: ShapeDecoration(
                color: configTheme().colorScheme.onBackground.withOpacity(0.5),
                shape: RoundedRectangleBorder(
                  side: BorderSide(width: 1.w, color: configTheme().colorScheme.onBackground),
                  borderRadius: BorderRadius.circular(6.r),
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                'คลิก!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: configTheme().colorScheme.background,
                  fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
                  fontFamily: configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                  fontWeight: configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                  // height: 0.10,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
