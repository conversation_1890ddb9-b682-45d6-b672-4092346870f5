import 'package:AAMG/app_config.dart';
import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/notification/notification.controllet.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/steam.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/terms_condition.dart';
import 'package:AAMG/view/screen/mr/mr_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../controller/branch/branch.controller.dart';
import '../../../controller/chatinapp/chatinapp.controller.dart';
import '../../../controller/likepoint/webview.point.controller.dart';
import '../../../controller/session/session.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../../models/pop_upModel.dart';
import '../../componance/widgets/app_pop_up/home_popups.dart';
import '../../componance/widgets/app_pop_up/update_pop_up/patchPopup.dart';
import '../aamg_service/aamg_service.dart';
import '../branch/branch_screen.dart';
import '../loan_screen/loan_details_screen.dart';
import '../loan_screen/loan_main_screen.dart';
import '../minilike/webview_minilike.dart';
import '../mr/mr_register.dart';
import '../mr/qr_referal.dart';
import '../mr/referfriend.dart';
import '../notify/notify_page.dart';
import 'home_page.dart';

class HomeNavigator extends StatefulWidget {
  @override
  State<HomeNavigator> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<HomeNavigator> {
  final NavigationController navigationController =
      Get.put(NavigationController());
  GetStorage storage = GetStorage();

  // final List<Widget Function()> _screens = [
  //   () => HomePage(),
  //   () => MRPage(),
  //   () => NotifyPage(0),
  // ];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () async {
      // _checkForUpdates();
      notiController.shownPopupIdsHome.clear();
      notiController.shownPopupIdsMR.clear();

      checkGuestData();
      // await Future.delayed(Duration(seconds: 1));
      // await storage.read('isShow');
      // await storage.read('isShowTutorial');
      // AAMPolicy.alertTermAndPolicy(context);
    });
  }

// ใช้ key คนละตัวสำหรับหน้า Home / MR
  bool shouldShowPopup(String popupId, String pageKey) {
    if (pageKey == 'homePopups') {
      return !notiController.shownPopupIdsHome.contains(popupId);
    } else {
      return !notiController.shownPopupIdsMR.contains(popupId);
    }
  }

  void markPopupAsShown(String popupId, String pageKey) {
    if (pageKey == 'homePopups') {
      notiController.shownPopupIdsHome.add(popupId);
    } else {
      notiController.shownPopupIdsMR.add(popupId);
    }
  }

  //TODO pop loading patch
  Future<void> _checkForUpdates() async {
    final shorebirdCodePush = ShorebirdCodePush();
    final isUpdateAvailable =
        await shorebirdCodePush.isNewPatchAvailableForDownload();
    if (isUpdateAvailable) {
      // Show the update popup.
      showDialog(
          barrierDismissible: false,
          context: context,
          useSafeArea: false,
          builder: (_) => PatchPopUp());
    }
  }

  Future<void> checkGuestData() async {
    try {
      var isGuest = await storage.read('isGuest');
      var isLogin = await storage.read('token');
      debugPrint('isGuest: $isGuest');
      if (isGuest != true) {
        final ProfileController profileController =
            Get.put(ProfileController());
        await profileController.getProfile();
        await profileController.separateAddress();

        if (_shouldCheckPolicy(profileController)) {
          var chkAcceptPolicy =
              await TermsAndConditionWidget.alertTermAndPolicy(context);
          if (chkAcceptPolicy && !kIsWeb) {
            debugPrint('chkAcceptPolicy: $chkAcceptPolicy');
            // await checkContractPermission().then((value) => print(value)); //todo ปิดสำหรับ apple store
            checkNotificationPermission();
          }
        } else {
          print("dadasdsddsd else ####");
          if (!kIsWeb) {
            // var contract = await checkContractPermission(); //todo ปิดสำหรับ apple store
            // if(contract){
            //   print("already accepted contract");
            // }
            checkNotificationPermission();
          }
        }
      }
    } catch (error) {
      // Handle error appropriately
      print('Error checking guest data: $error');
      // Get.put(SessionController()).logout(context); //TODO: Destroy Session And Logout
    }
  }

  bool _shouldCheckPolicy(ProfileController profileController) {
    return profileController.checkPolicy!.value == false &&
        profileController.updatePolicy!.value.isNotEmpty;
  }

  checkContractPermission() async {
    final box = GetStorage();
    bool hasContractsPermission =
        await Get.put(AppConfigController()).checkContractsPermission();
    // bool hasShownPrompt = box.read('hasShownPrompt') ?? false;
    // print('hasShownPrompt : ${hasShownPrompt}');
    // print("hasContractsPermission:  $hasContractsPermission");
    // if (hasShownPrompt) {
    //   return false; // ไม่ต้องแสดง prompt อีก
    // }
    if (hasContractsPermission) {
      print('Contract Permission granted');
      // Permission granted, proceed with notifications
      return true;
    } else {
      print('Contract Permission denied');
      // Permission denied or not yet requested, handle accordingly
      var chk;
      if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
        chk = await HomePopUp.alertAllowContactAAM(context);
      } else if (AppConfig.of(context).countryConfigCollection.toString() ==
          'rplc') {
        chk = await HomePopUp.alertAllowContactRPLC(context);
      } else {
        chk = await HomePopUp.alertAllowContactRAFCO(context);
      }
      box.write('hasShownPrompt', true);
      return chk;
    }
  }

  checkNotificationPermission() async {
    bool hasNotificationPermission =
        await Get.put(AppConfigController()).checkNotificationPermission();

    if (hasNotificationPermission) {
      print('Notification Permission granted');
      // Permission granted, proceed with notifications
    } else {
      print('Notification Permission denied');
      // Permission denied or not yet requested, handle accordingly
      if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
        HomePopUp.alertAllowNotificationAAM(context);
      } else if (AppConfig.of(context).countryConfigCollection.toString() ==
          'rplc') {
        HomePopUp.alertAllowNotificationRPLC(context);
      } else {
        HomePopUp.alertAllowNotificationRAFCO(context);
      }
    }
  }

  void _showTutorialPopup() {
    Get.dialog(
      AlertDialog(
        title: Text("Tutorial"),
        content: Text("This is a tutorial popup for MR tab."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text("Close"),
          ),
        ],
      ),
    );
  }

  final HomeController homeController = Get.find<HomeController>();
  final MRController mrController = Get.find<MRController>();
  final NotificationController notiController =
      Get.find<NotificationController>();
  final ProfileController profileController = Get.find<ProfileController>();

  final List<Widget?> _screens = [null, null, null]; // ให้เริ่มต้นเป็น null
  Widget _buildScreen(int index) {
    switch (index) {
      case 0:
        return HomePage(); // หน้าหลัก
      case 1:
        return MRPage(); // หน้ารายการ MR
      case 2:
        return NotifyPage(0); // หน้าการแจ้งเตือน
      default:
        return Container();
    }
  }

  @override
  Widget build(BuildContext context) {
    var mr_page = false;
    final box = GetStorage();

    print('navigationController.showPopupHome.value  :' +
        navigationController.showPopupHome.value.toString());
    return Obx(() {
      int currentIndex = navigationController.currentIndex.value;

      // โหลดเฉพาะหน้าที่ถูกกดครั้งแรก
      if (_screens[currentIndex] == null) {
        _screens[currentIndex] = _buildScreen(currentIndex);
      }

      return Stack(
        children: [
          Scaffold(
            body: IndexedStack(
              index: navigationController.currentIndex.value,
              children:
                  _screens.map((screen) => screen ?? Container()).toList(),
              alignment: Alignment.center,
            ),
            bottomNavigationBar: Obx(() {
              return Container(
                height: 80.h,
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                ),
                child: BottomNavigationBar(
                  elevation: 0,
                  currentIndex: navigationController.currentIndex.value,
                  onTap: (index) {
                    navigationController.changeIndex(index);
                    if (!Get.find<HomeController>().isGuest!.value) {
                      if (index == 1) {
                        navigationController.isMRPage.value = true;
                        if (mrController.isLoadMRData.value == false) {
                          mrController.getMRData();
                          mrController.getReferFriend();
                        }

                        // print("sddsdssdsdsds");
                        // print(mrController.MRPagepopupList.first.popupType);
                        final isPopupShown = box.read("isMRShown") ?? false;

                        if (mrController.MRPagepopupList.first.popupType ==
                            'mr_page' && isPopupShown == 'already') {
                              showPopUpMR(context,
                            mrController.MRPagepopupList.first.notifyPic, 0);
                            }
                       
                        // profileController.getProfile();
                      } else if (index == 2) {
                        navigationController.isMRPage.value = false;
                        navigationController.isnotiPage.value = true;
                        notiController.getNotification(context);
                      }
                    }
                  },
                  type: BottomNavigationBarType.fixed,
                  selectedItemColor:
                      configTheme().bottomNavigationBarTheme.selectedItemColor,
                  unselectedItemColor: configTheme()
                      .bottomNavigationBarTheme
                      .unselectedItemColor,
                  items: [
                    _buildNavItem(
                      icon:
                          '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12.5 18L12.5 15" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round"/> <path d="M2.85139 13.2135C2.49837 10.9162 2.32186 9.76763 2.75617 8.74938C3.19047 7.73112 4.15403 7.03443 6.08114 5.64106L7.52099 4.6C9.91829 2.86667 11.1169 2 12.5 2C13.8831 2 15.0817 2.86667 17.479 4.6L18.9189 5.64106C20.846 7.03443 21.8095 7.73112 22.2438 8.74938C22.6781 9.76763 22.5016 10.9162 22.1486 13.2135L21.8476 15.1724C21.3471 18.4289 21.0969 20.0572 19.929 21.0286C18.7611 22 17.0537 22 13.6388 22H11.3612C7.94633 22 6.23891 22 5.071 21.0286C3.90309 20.0572 3.65287 18.4289 3.15243 15.1724L2.85139 13.2135Z" stroke="#FF9300" stroke-width="1.5" stroke-linejoin="round"/></svg>',
                      label: bottomBarHome.tr,
                      isActive: navigationController.currentIndex.value == 0,
                    ),
                    _buildNavItem(
                      icon:
                          '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M9.5 13C11.7091 13 13.5 11.2091 13.5 9C13.5 6.79086 11.7091 5 9.5 5C7.29086 5 5.5 6.79086 5.5 9C5.5 11.2091 7.29086 13 9.5 13ZM9.5 13C5.63401 13 2.5 15.6863 2.5 19M9.5 13C13.366 13 16.5 15.6863 16.5 19M15.5 5C17.7091 5 19.5 6.79086 19.5 9C19.5 10.6787 18.4659 12.1159 17 12.7092V13L17.5 13.2485C20.3915 13.9861 22.5 16.282 22.5 19" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                      label: bottomBarMR.tr,
                      isActive: navigationController.currentIndex.value == 1,
                    ),
                    _buildNavItem(
                      icon:
                          '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.94784 7.96942C7.26219 5.14032 9.65349 3 12.5 3V3C15.3465 3 17.7378 5.14032 18.0522 7.96942L18.304 10.2356C18.3072 10.2645 18.3088 10.279 18.3104 10.2933C18.4394 11.4169 18.8051 12.5005 19.3836 13.4725C19.3909 13.4849 19.3984 13.4973 19.4133 13.5222L19.9914 14.4856C20.5159 15.3599 20.7782 15.797 20.7216 16.1559C20.6839 16.3946 20.561 16.6117 20.3757 16.7668C20.0971 17 19.5873 17 18.5678 17H6.43223C5.41268 17 4.90291 17 4.62434 16.7668C4.43897 16.6117 4.31609 16.3946 4.27841 16.1559C4.22179 15.797 4.48407 15.3599 5.00862 14.4856L5.58665 13.5222C5.60161 13.4973 5.60909 13.4849 5.61644 13.4725C6.19488 12.5005 6.56064 11.4169 6.68959 10.2933C6.69123 10.279 6.69283 10.2645 6.69604 10.2356L6.94784 7.96942Z" stroke="#FF9300" stroke-width="1.5"/> <path d="M9.60222 17.6647C9.77315 18.6215 10.1498 19.467 10.6737 20.0701C11.1976 20.6731 11.8396 21 12.5 21C13.1604 21 13.8024 20.6731 14.3263 20.0701C14.8502 19.467 15.2269 18.6215 15.3978 17.6647" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round"/></svg>',
                      label: bottomBarNotify.tr,
                      isActive: navigationController.currentIndex.value == 2,
                    ),
                  ],
                ),
              );
            }),
          ),
          Obx(() {
            final isLoading = mrController.isLoadingPopup.value;
            final isGuest = homeController.isGuest!.value;
            final show = navigationController.showPopupHome.value;
            final index = navigationController.currentIndex.value;

            if (isLoading || isGuest || !show) return const SizedBox.shrink();

            final list =
                index == 0 ? mrController.popupList : mrController.MRpopupList;
            final pageKey = index == 0 ? 'homePopups' : 'mrPopups';
            final visible = index == 0
                ? notiController.isPopupVisibleHome.value
                : notiController.isPopupVisibleMR.value;

            if (!visible || list.isEmpty) return const SizedBox.shrink();

            final currentIndex = notiController.currentPageIndex.value;
            if (currentIndex >= list.length) return const SizedBox.shrink();

            final currentPopup = list[currentIndex];
            if (shouldShowPopup(currentPopup.popupId.toString(), pageKey)) {
              return buildPopupCarousel(context, list, pageKey, index);
            }

            // ถ้าเคยแสดงไปแล้ว
            if (index == 0) {
              notiController.isPopupVisibleHome.value = false;
            } else {
              notiController.isPopupVisibleMR.value = false;
            }

            return const SizedBox.shrink();
          })
        ],
      );
    });
  }

  BottomNavigationBarItem _buildNavItem({
    required String icon,
    required String label,
    required bool isActive,
  }) {
    return BottomNavigationBarItem(
      icon: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 24.h, // Icon's fixed height
            child: SvgPicture.string(
              icon,
              fit: BoxFit.contain,
              color: isActive
                  ? configTheme().bottomNavigationBarTheme.selectedItemColor
                  : configTheme().bottomNavigationBarTheme.unselectedItemColor,
            ),
          ),
          SizedBox(
            height: 4.h,
          ),
          FittedBox(
            fit: BoxFit.scaleDown,
            alignment: Alignment.center,
            child: Text(
              label,
              style: TextStyle(
                fontFamily:
                    configTheme().primaryTextTheme.bodySmall!.fontFamily,
                fontSize: 10.sp,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                color: isActive
                    ? configTheme().bottomNavigationBarTheme.selectedItemColor
                    : configTheme()
                        .bottomNavigationBarTheme
                        .unselectedItemColor,
              ),
            ),
          ),
        ],
      ),
      label: '',
    );
  }

  Widget buildPopUp(BuildContext context, String img_url, String path,
      int totalLength, String popupId, String pageKey, int screenIndex) {
    print("buildPopUp");
    print(img_url);
    print(totalLength);
    print(popupId);
    print(pageKey);
    print(screenIndex);

    final WebViewPointController likepointCtl =
        Get.put(WebViewPointController());
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Container(
        width: Get.width,
        height: Get.height,
        color: Colors.transparent,
        child: Center(
          child: SizedBox(
            height: 505.h,
            width: Get.width,
            child: Column(
              children: [
                Container(
                  height: 434.h,
                  width: 330.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Image.network(
                          img_url != ''
                              ? img_url
                              : 'https://s3-ap-southeast-1.amazonaws.com/pkg-aam/eventAAM/1746519959xxx.png',
                          fit: BoxFit.cover,
                          alignment: Alignment.topCenter,
                        ),
                      ),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: GestureDetector(
                          onTap: () {
                            final controller = notiController.pageController;
                            final currentPage =
                                notiController.currentPageIndex.value;

                            if (currentPage < totalLength - 1) {
                              if (controller.hasClients &&
                                  controller.positions.length == 1) {
                                controller.nextPage(
                                  duration: Duration(milliseconds: 300),
                                  curve: Curves.ease,
                                );
                                notiController.currentPageIndex.value++;
                              }
                            } else {
                              // ✅ ปิด popup ให้ถูกตัวตามหน้าที่เปิด
                              if (screenIndex == 0) {
                                notiController.isPopupVisibleHome.value = false;
                              } else {
                                notiController.isPopupVisibleMR.value = false;
                              }
                              notiController.currentPageIndex.value = 0;
                            }
                          },
                          child: Container(
                            height: 34.h,
                            width: 34.w,
                            decoration: BoxDecoration(
                              color: Colors.black45,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.close, color: Colors.white),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 68.h,
                  width: Get.width,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Material(
                        borderRadius: BorderRadius.circular(16),
                        child: InkWell(
                          onTap: () async {
                            try {
                              final controller = notiController.pageController;
                              final currentPage =
                                  notiController.currentPageIndex.value;

                              // ✅ บันทึกว่าแสดง popup นี้ไปแล้ว
                              markPopupAsShown(popupId, pageKey);

                              // ✅ ทำ action ตาม path
                              switch (path) {
                                case '' || 'close':
                                  notiController.currentPageIndex.value++;
                                  break;
                                case 'digitalloan':
                                  homeController.setIndexMenu(1);
                                  break;
                                case 'new':
                                  homeController.setIndexMenu(2);
                                  break;
                                case 'QrReferalPage':
                                  final mrId = mrController.mrData.value.mr_id;
                                  if (mrId == null) {
                                    await Get.to(() => MrRegister());
                                  } else {
                                    final refId = mrController.mrData.value
                                            .toString()
                                            .isNotEmpty
                                        ? mrController.mrData.value.toString()
                                        : profileController
                                            .profile.value.ref_code
                                            .toString();
                                    await Get.to(
                                        () => QrReferalPage(id: refId));
                                  }
                                  break;
                                case 'AAMpoint':
                                  if (!homeController.isGuest!.value &&
                                      likepointCtl.isOpen!.value &&
                                      likepointCtl.isAlreadyMigrated!.value) {
                                    final status =
                                        await Permission.camera.request();
                                    debugPrint(status.isGranted
                                        ? "✅ Have camera permission"
                                        : "❌ No camera permission");

                                    if (kIsWeb) {
                                      final url =
                                          "${likepointCtl.urlLikePoint!.value}/?phone=${likepointCtl.phoneEncode!.value}&merchant=${likepointCtl.merchantID!.value}&firstName=${likepointCtl.firstName.value}&lastName=${likepointCtl.lastName.value}";
                                      final uri = Uri.parse(url);
                                      if (await canLaunchUrl(uri)) {
                                        await launchUrl(uri,
                                            mode:
                                                LaunchMode.externalApplication);
                                      } else {
                                        throw "ไม่สามารถเปิด URL ได้: $url";
                                      }
                                    } else {
                                      await Get.to(
                                          () => const WebViewMinilike());
                                    }
                                  } else {
                                    debugPrint(
                                        "Not Migrate or Not Open Status");
                                  }
                                  break;
                                case 'branchScreen':
                                  await Get.to(() => BranchScreen());
                                  break;
                                case 'LoanRequestDetail':
                                  await Get.to(() => LoanRequestDetail());
                                  break;
                                case 'MRpage':
                                  navigationController.currentIndex.value = 1;
                                  break;
                                case 'Notipage':
                                  navigationController.currentIndex.value = 2;
                                  break;
                                case 'invitefriend':
                                  final mrId = mrController.mrData.value.mr_id;
                                  if (mrId == null) {
                                    await Get.to(() => MrRegister());
                                  } else {
                                    final refId = mrController.mrData.value
                                            .toString()
                                            .isNotEmpty
                                        ? mrController.mrData.value.toString()
                                        : profileController
                                            .profile.value.ref_code
                                            .toString();
                                    await Get.to(() => ReferFriend());
                                  }
                                  break;
                              }

                              // ✅ กลับมาแล้ว → ไปหน้า popup ถัดไป ถ้ายังไม่หมด
                              if (currentPage < totalLength - 1) {
                                if (controller.hasClients &&
                                    controller.positions.length == 1) {
                                  await controller.nextPage(
                                    duration: Duration(milliseconds: 300),
                                    curve: Curves.ease,
                                  );
                                  notiController.currentPageIndex.value++;
                                }
                              } else {
                                // ✅ ถ้าอันสุดท้าย → ปิด popup
                                if (screenIndex == 0) {
                                  notiController.isPopupVisibleHome.value =
                                      false;
                                } else {
                                  notiController.isPopupVisibleMR.value = false;
                                }
                                notiController.currentPageIndex.value = 0;
                              }

                              print(
                                  "📦 Path: $path → indexMenu: ${homeController.indexMenu!.value}");
                            } catch (e) {
                              print('❌ Exception: $e');
                            }
                          },
                          child: Container(
                            width: 120,
                            height: 52,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                width: 1,
                                color: Color(
                                    0x331A1818), // เทียบกับ rgba(26,24,24,0.2)
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.touch_app,
                                    size: 20, color: Colors.black),
                                SizedBox(width: 10),
                                Text(
                                  path == '' ? onBoardingClose.tr : 'click',
                                  style: TextStyle(
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontFamily,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

void showPopUpMR(BuildContext context, String img_url, int screenIndex) {
  if (img_url.isEmpty) {
    return;  // Early return if there's no URL
  } else {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32.0),
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.transparent,
            child: Center(
              child: SizedBox(
                height: 505,  // adjust these to use your scaling if necessary
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    Container(
                      height: 434,
                      width: 330,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: Image.network(
                              img_url.isNotEmpty ? img_url : 'https://s3-ap-southeast-1.amazonaws.com/pkg-aam/eventAAM/1746519959xxx.png',
                              fit: BoxFit.cover,
                              alignment: Alignment.topCenter,
                            ),
                          ),
                          Positioned(
                            top: 8,
                            right: 8,
                            child: GestureDetector(
                              onTap: () => Navigator.of(context).pop(), // Close on tap
                              child: Container(
                                height: 34,
                                width: 34,
                                decoration: BoxDecoration(
                                  color: Colors.black45,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(Icons.close, color: Colors.white),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 68,
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Material(
                            borderRadius: BorderRadius.circular(16),
                            child: InkWell(
                              onTap: () => Navigator.of(context).pop(), // Close on tap
                              child: Container(
                                width: 120,
                                height: 52,
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.9),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    width: 1,
                                    color: Color(0x331A1818),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.touch_app, size: 20, color: Colors.black),
                                    SizedBox(width: 10),
                                    Text(
                                      onBoardingClose.tr,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

  Widget buildPopupCarousel(BuildContext context, List<PopUpModel> items,
      String pageKey, int screenIndex) {
    final currentIndex = screenIndex == 0
        ? notiController.currentPageIndexHome.value
        : notiController.currentPageIndexMR.value;

    // 👇 set initial page to remember last seen
    notiController.pageController = PageController(initialPage: currentIndex);

    return Container(
      width: Get.width,
      height: Get.height,
      color: Colors.black.withOpacity(0.6),
      child: Center(
        child: SizedBox(
          height: 520.h,
          width: 360.w,
          child: PageView.builder(
            itemCount: items.length,
            controller: notiController.pageController,
            physics: NeverScrollableScrollPhysics(),
            onPageChanged: (index) {
              if (screenIndex == 0) {
                notiController.currentPageIndexHome.value = index;
              } else {
                notiController.currentPageIndexMR.value = index;
              }
            },
            itemBuilder: (_, index) {
              final popup = items[index];
              return buildPopUp(
                context,
                popup.notifyPic,
                popup.popupPath,
                items.length,
                popup.popupId.toString(),
                pageKey,
                screenIndex,
              );
            },
          ),
        ),
      ),
    );
  }
}
