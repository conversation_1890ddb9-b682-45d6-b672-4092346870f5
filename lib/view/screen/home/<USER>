import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/likepoint/likepoint.controller.dart';
import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/transalation/transalation.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/widgets/pop_up_tutorial/tutorial_content.dart';
import 'package:AAMG/view/screen/aamg_service/aamg_service.dart';
import 'package:AAMG/view/screen/branch/branch_screen.dart';
import 'package:AAMG/view/screen/register/register_page.dart';
import 'package:AAMG/view/screen/update_patch.dart';
import 'package:app_tutorial/app_tutorial.dart';
import 'package:buttons_tabbar/buttons_tabbar.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'dart:io';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/news_promotions/news.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/dailyactiveusers/dailyactiveusers.controller.dart';
import 'package:AAMG/view/componance/AppBackgound.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:intl/intl.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../controller/navigator.controller.dart';
import '../../../controller/social_login/facebook.controller.dart';
import '../../../controller/social_login/google.controller.dart';
import '../../componance/widgets/aam_pay/aampay_componance.dart';
import '../../componance/widgets/app_pop_up/home_popups/home_popup.dart';
import '../../componance/widgets/app_pop_up/policy_popups/terms_condition.dart';
import '../../componance/widgets/app_pop_up/request_loan/request_success.dart';
import '../../componance/widgets/home_widget/bill_widget.dart';
import '../../componance/widgets/home_widget/component_widget.dart';
import '../../componance/widgets/home_widget/loan_widget.dart';
import '../../componance/widgets/home_widget/news_widget.dart';
import '../../componance/widgets/home_widget/social_media_widget.dart';
import '../aampay/aampay_comfirm.dart';
import '../aicp/home_aicp.dart';
import '../kyc/bookbank.dart';
import '../loan_screen/loan_details_screen.dart';
import '../loan_screen/loan_main_screen.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  final ProfileController profileController = Get.find<ProfileController>();
  final HomeController homeController = Get.put(HomeController());
  final NewsController newsController = Get.put(NewsController());

  final LikePointController likepointController =
      Get.put(LikePointController());
  final ContractListController contractListController =
      Get.put(ContractListController());
  final TransalationController transalationCtl =
      Get.put(TransalationController());
  final NavigationController navigationController =
      Get.put(NavigationController());
  var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
  final box = GetStorage();
  bool show = false;
  final MRController mrController = Get.put(MRController());
  // final HomeController homeController = Get.put(HomeController());
  late TabController _tabController;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _checkForUpdates();
      navigationController.showTutorial();

      Future.delayed(Duration(seconds: 5), () {
        profileController.profile.value.phone.obs.string.isNotEmpty
            ? Get.put(DailyActiveUsersController()).saveLogMenu("home")
            : null;
      });
    });
    final int length = homeController.isGuest!.value ? 2 : 3;
    _tabController = TabController(
      length: length,
      vsync: this,
      initialIndex: homeController.indexMenu!.value,
    );
    ever<int>(homeController.indexMenu!, (value) {
      if (_tabController.index != value) {
        _tabController.animateTo(value);
      }
    });

    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        homeController.setIndexMenu(_tabController.index);
      }
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _tabController.dispose();
    super.dispose();
  }

  /// pop loading patch
  // Future<void> _checkForUpdates() async {
  //   final isUpdateAvailable =
  //   await shorebirdCodePush.isNewPatchAvailableForDownload();
  //   if (isUpdateAvailable) {
  //     // Show the update popup.
  //     showDialog(
  //         barrierDismissible: false,
  //         context: context,
  //         useSafeArea: false,
  //         builder: (_) => UpdatePatch());
  //   }
  // }
  Future<void> _checkForUpdates() async {
    // Check whether a patch is available to install.
    final shorebirdCodePush = ShorebirdCodePush();
    // final updateAvailable =
    //     await shorebirdCodePush.isNewPatchAvailableForDownload();
    // if (updateAvailable) {
    //   await shorebirdCodePush.downloadUpdateIfAvailable();
    //   final isNewPatchReadyToInstall =
    //       await shorebirdCodePush.isNewPatchReadyToInstall();
    //   if (isNewPatchReadyToInstall) {
    final isUpdateAvailable =
        await shorebirdCodePush.isNewPatchAvailableForDownload();
    debugPrint('## isUpdateAvailable : ${isUpdateAvailable}');

    if (isUpdateAvailable) {
      showDialog(
          barrierDismissible: false,
          context: context,
          useSafeArea: false,
          builder: (_) => UpdatePatch());
    }
    // Restart.restartApp;
    // AppWidget.showDialogPageSlide(context, const AlertUpdatePatchPage());
    // Patch Ready to Install;
    //   } else {
    //     //  Patch not Ready to Install ;
    //   }
    // } else {
    //   // No Update Available;
    // }
  }

  // Future<void> _checkForUpdates() async {
  //   final shorebirdCodePush = ShorebirdCodePush();
  //   final isUpdateAvailable =
  //       await shorebirdCodePush.isNewPatchAvailableForDownload();
  //   if (isUpdateAvailable) {
  //     // Show the update popup.
  //     showDialog(
  //         barrierDismissible: false,
  //         context: context,
  //         useSafeArea: false,
  //         builder: (_) => const UpdatePatch());
  //   }
  // }
  //
  // bool isShow = false;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // ป้องกันการ pop อัตโนมัติ
      onPopInvoked: (didPop) async {
        debugPrint('onPopInvoked');
      },
      child: Scaffold(
        // key: incrementKey,
        // floatingActionButton: FloatingActionButton(
        //   backgroundColor: Colors.red,
        //   onPressed: () {
        //     HomePopup.ADsHomePopup(context);
        //   },
        //   tooltip: 'Increment',
        // ),
        body: Stack(
          children: [
            Stack(
              // key: incrementKey,
              children: [
                SingleChildScrollView(
                  child: Container(
                    // color: Colors.white,
                    // height: Get.height,
                    height: 954.h,
                    child: GetBuilder<HomeController>(builder: (homeCtl) {
                      return Container(
                        child: Column(
                          children: [
                            AppBackground.backgroundColorHomePage(context),
                            SizedBox(
                              height: 106.h,
                            ),
                            SizedBox(
                              height: Get.height,
                              child: Column(
                                children: [
                                  // TODO name , point
                                  HomePageCoponentWidget.welcomeAndPoint(
                                      context),
                                  //TODO POI Update Personal Data
                                  // homeCtl.showUpdateData!.value.isEmpty &&
                                  //         !homeCtl.isGuest!.value
                                  //     ? SizedBox(
                                  //         height: 82.h,
                                  //         child: Column(
                                  //           children: [
                                  //             SizedBox(height: 12.h),
                                  //             const UpdatePersonalDataPage(),
                                  //             SizedBox(height: 14.h),
                                  //           ],
                                  //         ))
                                  //     : Container(
                                  //         height: 18.h,
                                  //       ),
                                  Container(
                                    height: 18.h,
                                  ),
                                  !profileController.isShow.value
                                      ? Container()
                                      :
                                      //TODO AAM Note ( อยู่ภายใต้ธนาคาร....)
                                      homeCtl.isShowAAMNote!.value &&
                                              !homeCtl.isGuest!.value
                                          ? Column(
                                              children: [
                                                HomePageCoponentWidget
                                                    .appDetail(context),
                                                SizedBox(
                                                  height: 18.h,
                                                ),
                                              ],
                                            )
                                          : Container(),
                                  serviceTab(context),
                                  SizedBox(
                                    height: 18.h,
                                  ),
                                  // TODO ** AAM Pay menu เฉพาะ AAM
                                  !profileController.isShow.value &&
                                          homeCtl.isShowTutorial!.value
                                      ? Container()
                                      : GetBuilder<ContractListController>(
                                          builder: (contractCtl) {
                                          //todo test pending
                                          // return AAMPAY_Componance
                                          //      .buildStatusCreateContract(
                                          //      context);
                                          //todo test init
                                          // return AAMPAY_Componance.aampayAds(
                                          //                 contractCtl.contractDigi
                                          //                     .value.remain_loan
                                          //                     .toString());
                                          if (contractCtl.chk_aampay!.value ==
                                                  false ||
                                              homeCtl.isGuest!.value &&
                                                  appConfigService
                                                          .countryConfigCollection !=
                                                      'aam') {
                                            return Container();
                                          } else {
                                            return Obx(() {
                                              var aampayCtl;
                                              if (Get.isRegistered<
                                                  AAMPayController>()) {
                                                aampayCtl = Get.find<
                                                    AAMPayController>();
                                              } else {
                                                aampayCtl =
                                                    Get.put(AAMPayController());
                                              }
                                              aampayCtl.checkPendingStatus();
                                              return Column(
                                                children: [
                                                  // todo อยู่ระหว่างกระบวนการสร้างสัญญา
                                                  Get.find<AAMPayController>()
                                                                  .aampay_contract_status !=
                                                              null &&
                                                          Get.find<
                                                                  AAMPayController>()
                                                              .aampay_contract_status!
                                                              .value
                                                              .toString()
                                                              .isNotEmpty
                                                      ? AAMPAY_Componance
                                                          .buildStatusCreateContract(
                                                              context)
                                                      : AAMPAY_Componance
                                                          .aampayAds(contractCtl
                                                              .contractDigi
                                                              .value
                                                              .remain_loan
                                                              .toString()), // todo สามารถขอ aam pay ได้
                                                  SizedBox(
                                                    height: 18.h,
                                                  ),
                                                ],
                                              );
                                            });
                                          }
                                        }),
                                  Container(
                                    // margin: EdgeInsets.only(left: 24.w, right: 24.w),
                                    child: Column(
                                      children: [
                                        // TODO หมวดหมู่ รายการของฉัน , สินเชื่อ , มีอะไรใหม่(ข่าวสาร)
                                        Obx(() => buildContent(context)),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        Padding(
                                          padding: EdgeInsets.only(
                                              left: 24.w, right: 24.w),
                                          //TODO บริการ (icon menu)
                                          child: serviceMenu(context),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container( height: 18.h, ),
                                  SocialMediaWidget(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
                // buildTabBarView(),
                // buildFloatingButton(),
                //TODO migration point
                // Positioned(
                //   top: -Get.height,
                //   child: AnimatedContainer(
                //     width: Get.width,
                //     height: Get.find<HomeController>().closeHead.value == false
                //         ? Get.height
                //         : Get.height * 2,
                //     duration: const Duration(milliseconds: 300),
                //     alignment: Alignment.bottomCenter,
                //     // color: Colors.transparent,
                //     child: Stack(
                //       children: [
                //         Positioned(
                //           top: Get.height,
                //           child: MigrationWidget.migration(),
                //         )
                //       ],
                //     ),
                //   ),
                // ),
                //TODO app bar เมนูด้านบนสุด
                HomePageCoponentWidget.heading_bar(context),
                // show ? showTutorial() : Container(),
                // homeController.isShowTutorial!.value
                //     ?Container():showTutorial()
                // showTutorial()
                // Container(
                //   height: 954.h,
                //   color: Colors.transparent,
                //   key: incrementKey,
                // )
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildContent(BuildContext context) {
    // debugPrint('buildContent index : ${homeController.indexMenu!.value}');
    //TODO content guest mode
    if (homeController.isGuest!.value) {
      switch (homeController.indexMenu!.value) {
        case 0:
          return HomePageLoanWidget.myloanDefaultContent(context);
        case 1:
          return HomePageNewsWidget.newsContent(context);
        default:
          return Container();
      }
    } else {
      //TODO already login has session
      switch (homeController.indexMenu!.value) {
        case 0:
          //TODO มีข้อมูลสัญญา
          if (homeController.showLoanData!.value) {
            return HomePageBillWidget.billContent(context);
          } else {
            return HomePageLoanWidget.myloanDefaultContent(context);
          }
        case 1:
          //TODO มีข้อมูลสัญญา
          if (homeController.showLoanData!.value) {
            return HomePageLoanWidget.myloanContent(context);
          } else {
            return HomePageLoanWidget.myloanDefaultContent(context);
          }
        case 2:
          return HomePageNewsWidget.newsContent(context);
        default:
          return Container();
      }
    }
  }

  Widget serviceTab(context) {
    final isGuest = homeController.isGuest!.value;
    final List<Tab> tabs = isGuest
        ? [
            Tab(text: homeLoan.tr),
            Tab(text: homeNewItem.tr),
          ]
        : [
            Tab(text: homeImportant.tr),
            Tab(text: homeLoan.tr),
            Tab(text: homeNewItem.tr),
          ];

    return SizedBox(
      width: 327.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            homeCategory.tr,
            style: TextStyle(
              color: configTheme().textTheme.bodyMedium?.color,
              fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
              fontFamily: configTheme().primaryTextTheme.bodyLarge?.fontFamily,
              fontWeight: configTheme().primaryTextTheme.bodyLarge?.fontWeight,
              height: 0,
            ),
          ),
          SizedBox(height: 6.h),
          ButtonsTabBar(
            controller: _tabController,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
            radius: 8.r,
            backgroundColor: _getBGColor(),
            unselectedBackgroundColor: const Color(0xFFF9F9F9),
            unselectedLabelStyle: TextStyle(
              color:
                  configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
              fontSize: configTheme().primaryTextTheme.labelMedium?.fontSize,
              fontFamily:
                  configTheme().primaryTextTheme.labelMedium?.fontFamily,
              fontWeight:
                  configTheme().primaryTextTheme.labelMedium?.fontWeight,
              height: 0,
            ),
            labelStyle: TextStyle(
              color: configTheme().colorScheme.onSecondary,
              fontSize: configTheme().primaryTextTheme.labelMedium?.fontSize,
              fontFamily:
                  configTheme().primaryTextTheme.labelMedium?.fontFamily,
              fontWeight:
                  configTheme().primaryTextTheme.labelMedium?.fontWeight,
              height: 0,
            ),
            tabs: tabs,
            onTap: (index) {
              homeController.setIndexMenu(index);
              print(
                  "📦 buildContent: index ${homeController.indexMenu!.value}");
            },
          ),
        ],
      ),
    );
  }

  Widget serviceMenu(context) {
    return SizedBox(
      // key: textKey,
      width: Get.width,
      // height: 226.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 21.h,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 21.h,
                  child: Image.asset(
                    'assets/home/<USER>',
                    fit: BoxFit.fill,
                    color: configTheme().colorScheme.onSecondary,
                  ),
                ),
                SizedBox(width: 10.w),
                Text(
                  homeMenu.tr,
                  style: TextStyle(
                    color: configTheme().textTheme.bodyMedium?.color,
                    fontSize:
                        configTheme().primaryTextTheme.bodyLarge?.fontSize,
                    fontFamily:
                        configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                    fontWeight:
                        configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                    height: 0,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          SizedBox(
            width: Get.width,
            // height: 197.h,
            child: Column(
              children: [
                appConfigService.countryConfigCollection.toString() == 'aam'
                    ? Container(
                        child: StaggeredGrid.count(
                          crossAxisCount: 4,
                          mainAxisSpacing: 4,
                          crossAxisSpacing: 9.w,
                          children: [
                            /// สินเชื่อ
                            InkWell(
                              onTap: () {
                                if (homeController.isGuest!.value) {
                                  Get.to(() => const RegisterPage());
                                }
                                if (Get.find<ContractListController>()
                                    .contractList
                                    .isEmpty) {
                                  Get.to(() => const LoanRequestDetail());
                                } else {
                                  Get.to(() => const LoanMainScreen());
                                }
                              },
                              child: HomePageCoponentWidget.serviceMenuCard(
                                  context,
                                  appConfigService.countryConfigCollection
                                              .toString() ==
                                          'aam'
                                      ? '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20801" y="8.33331" width="11.4583" height="13.5417" rx="2" fill="#792AFF" fill-opacity="0.75"/><path d="M8.33301 14.5833L14.583 14.5833" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33301 11.4583L12.4997 11.4583" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33301 17.7083L12.4997 17.7083" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7913 13.5417V15.875C19.7913 18.7034 19.7913 20.1176 18.9127 20.9963C18.034 21.875 16.6198 21.875 13.7913 21.875H11.208C8.37958 21.875 6.96537 21.875 6.08669 20.9963C5.20801 20.1176 5.20801 18.7034 5.20801 15.875V9.125C5.20801 6.29657 5.20801 4.88236 6.08669 4.00368C6.96537 3.125 8.37958 3.125 11.208 3.125H12.4997" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                      : appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'rplc'
                                          ? '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20898" y="8.33337" width="11.4583" height="13.5417" rx="2" fill="#6A7165" fill-opacity="0.75"/><path d="M8.33398 14.5834L14.584 14.5834" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33398 11.4584L12.5007 11.4584" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33398 17.7084L12.5007 17.7084" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7923 13.5417V15.875C19.7923 18.7034 19.7923 20.1176 18.9136 20.9963C18.035 21.875 16.6207 21.875 13.7923 21.875H11.209C8.38056 21.875 6.96634 21.875 6.08766 20.9963C5.20898 20.1176 5.20898 18.7034 5.20898 15.875V9.125C5.20898 6.29657 5.20898 4.88236 6.08766 4.00368C6.96634 3.125 8.38056 3.125 11.209 3.125H12.5007" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                          : '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20825" y="8.33334" width="11.4583" height="13.5417" rx="2" fill="#EA1B23" fill-opacity="0.75"/><path d="M8.33325 14.5833L14.5833 14.5833" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33325 11.4583L12.4999 11.4583" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33325 17.7083L12.4999 17.7083" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7916 13.5417V15.875C19.7916 18.7034 19.7916 20.1176 18.9129 20.9963C18.0342 21.875 16.62 21.875 13.7916 21.875H11.2082C8.37982 21.875 6.96561 21.875 6.08693 20.9963C5.20825 20.1176 5.20825 18.7034 5.20825 15.875V9.125C5.20825 6.29657 5.20825 4.88236 6.08693 4.00368C6.96561 3.125 8.37982 3.125 11.2083 3.125H12.4999" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>',
                                  homeApply.tr),
                            ),

                            // /// พร้อมใช้
                            // GetBuilder<ContractListController>(
                            //     builder: (contractCtl) {
                            //   if (contractCtl.chk_aampay!.value) {
                            //     return InkWell(
                            //       onTap: () {
                            //         Get.to(() => AAMPAYFormScreen());
                            //       },
                            //       child: HomePageCoponentWidget.serviceMenuCard(
                            //           context,
                            //           '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3124_37792)"><path d="M9.03976 17.0781C4.57094 17.0781 0.948242 13.4554 0.948242 8.98654C0.948242 4.51771 4.57094 0.89502 9.03976 0.89502C13.5086 0.89502 17.1313 4.51771 17.1313 8.98654C17.1313 9.36222 17.1057 9.73189 17.0561 10.094" fill="#792AFF" fill-opacity="0.75"/><path d="M9.03976 17.0781C4.57094 17.0781 0.948242 13.4554 0.948242 8.98654C0.948242 4.51771 4.57094 0.89502 9.03976 0.89502C13.5086 0.89502 17.1313 4.51771 17.1313 8.98654C17.1313 9.36221 17.1057 9.73189 17.0561 10.094" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M12.4434 24.105V14.7299C12.4434 14.1379 12.6785 13.5702 13.0971 13.1516C13.5158 12.733 14.0835 12.4978 14.6755 12.4978C15.2675 12.4978 15.8353 12.733 16.2539 13.1516C16.6725 13.5702 16.9076 14.1379 16.9076 14.7299V19.6407H20.4792C21.4263 19.6407 22.3347 20.0169 23.0045 20.6868C23.6742 21.3564 24.0506 22.2648 24.0506 23.2121V24.105" fill="white"/><path d="M12.4434 24.105V14.7299C12.4434 14.1379 12.6785 13.5702 13.0971 13.1516C13.5158 12.733 14.0835 12.4978 14.6755 12.4978C15.2675 12.4978 15.8353 12.733 16.2539 13.1516C16.6725 13.5702 16.9076 14.1379 16.9076 14.7299V19.6407H20.4792C21.4263 19.6407 22.3347 20.0169 23.0045 20.6868C23.6742 21.3564 24.0506 22.2648 24.0506 23.2121V24.105" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/><path d="M8.57143 5.66667V4M8.57143 14V12.3333M6 9H9.42857C9.88323 9 10.3193 8.8244 10.6408 8.51184C10.9622 8.19928 11.1429 7.77536 11.1429 7.33333C11.1429 6.89131 10.9622 6.46738 10.6408 6.15482C10.3193 5.84226 9.88323 5.66667 9.42857 5.66667H6V9ZM6 9H10.2857C10.7404 9 11.1764 9.17559 11.4979 9.48816C11.8194 9.80072 12 10.2246 12 10.6667C12 11.1087 11.8194 11.5326 11.4979 11.8452C11.1764 12.1577 10.7404 12.3333 10.2857 12.3333H6V9Z" stroke="white" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3124_37792"><rect width="25" height="25" fill="white"/></clipPath></defs></svg>',
                            //           "${f.format(int.parse(contractCtl.contractDigi.value.remain_loan.toString()))}\n${homeReady.tr}"),
                            //     );
                            //   } else {
                            //     return Container();
                            //   }
                            // }),

                            /// สาขา
                            InkWell(
                              onTap: () {
                                debugPrint('Branch Page');
                                Get.to(() => BranchScreen());
                              },
                              child: HomePageCoponentWidget.serviceMenuCard(
                                  context,
                                  '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8975 20.6379C14.4198 19.851 19.7913 16.7029 19.7913 11.4584C19.7913 7.43128 16.5268 4.16669 12.4997 4.16669C8.4726 4.16669 5.20801 7.43128 5.20801 11.4584C5.20801 16.7029 10.5795 19.851 12.1019 20.6379C12.3549 20.7687 12.6444 20.7687 12.8975 20.6379ZM12.4997 14.5834C14.2256 14.5834 15.6247 13.1842 15.6247 11.4584C15.6247 9.73246 14.2256 8.33335 12.4997 8.33335C10.7738 8.33335 9.37467 9.73246 9.37467 11.4584C9.37467 13.1842 10.7738 14.5834 12.4997 14.5834Z" fill="#792AFF" fill-opacity="0.75"/><path d="M12.8975 20.6379L13.173 21.1709H13.173L12.8975 20.6379ZM12.1019 20.6379L11.8264 21.1709H11.8264L12.1019 20.6379ZM19.1913 11.4584C19.1913 13.8624 17.9618 15.8172 16.49 17.2965C15.0187 18.7752 13.3563 19.7253 12.6219 20.1049L13.173 21.1709C13.961 20.7636 15.7455 19.7461 17.3406 18.1429C18.9351 16.5404 20.3913 14.2988 20.3913 11.4584H19.1913ZM12.4997 4.76669C16.1954 4.76669 19.1913 7.76265 19.1913 11.4584H20.3913C20.3913 7.09991 16.8581 3.56669 12.4997 3.56669V4.76669ZM5.80801 11.4584C5.80801 7.76265 8.80397 4.76669 12.4997 4.76669V3.56669C8.14123 3.56669 4.60801 7.09991 4.60801 11.4584H5.80801ZM12.3774 20.1049C11.6431 19.7253 9.98062 18.7752 8.5094 17.2965C7.0375 15.8172 5.80801 13.8624 5.80801 11.4584H4.60801C4.60801 14.2988 6.06427 16.5404 7.65873 18.1429C9.25385 19.7461 11.0384 20.7636 11.8264 21.1709L12.3774 20.1049ZM12.6219 20.1049C12.5417 20.1463 12.4576 20.1463 12.3774 20.1049L11.8264 21.1709C12.2522 21.391 12.7471 21.391 13.173 21.1709L12.6219 20.1049ZM15.0247 11.4584C15.0247 12.8529 13.8942 13.9834 12.4997 13.9834V15.1834C14.5569 15.1834 16.2247 13.5156 16.2247 11.4584H15.0247ZM12.4997 8.93335C13.8942 8.93335 15.0247 10.0638 15.0247 11.4584H16.2247C16.2247 9.40109 14.5569 7.73335 12.4997 7.73335V8.93335ZM9.97467 11.4584C9.97467 10.0638 11.1052 8.93335 12.4997 8.93335V7.73335C10.4424 7.73335 8.77467 9.40109 8.77467 11.4584H9.97467ZM12.4997 13.9834C11.1052 13.9834 9.97467 12.8529 9.97467 11.4584H8.77467C8.77467 13.5156 10.4424 15.1834 12.4997 15.1834V13.9834Z" fill="#1A1818"/></svg>',
                                  homeBranch.tr),
                            ),

                            Obx(() {
                              return

                                  ///AAMGService
                                  profileController.isMember!.value == true
                                      ? InkWell(
                                          onTap: () async {
                                            debugPrint('AAM Service Page');
                                            if (kIsWeb) {
                                              final String url = Get.find<
                                                              ProfileController>()
                                                          .member_id
                                                          .value
                                                          .isEmpty ||
                                                      Get.find<
                                                              ProfileController>()
                                                          .member_email
                                                          .value
                                                          .isEmpty
                                                  ? "https://aamg-service.web.app"
                                                  : "https://aamg-service.web.app/?memberid=${Get.find<ProfileController>().member_id.value}&memberemail=${Get.find<ProfileController>().member_email.value}";
                                              final Uri uri = Uri.parse(url);
                                              if (await canLaunchUrl(uri)) {
                                                await launchUrl(uri,
                                                    mode: LaunchMode
                                                        .externalApplication);
                                              } else {
                                                throw "ไม่สามารถเปิด URL ได้: $url";
                                              }
                                            } else {
                                              Get.to(() => AAMGService());
                                            }
                                          },
                                          child: HomePageCoponentWidget
                                              .serviceMenuCard(
                                                  context,
                                                  '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 2.50001V2.50001C16.3807 2.50001 17.5 3.61929 17.5 5.00001L17.5 6.78572C17.5 6.98521 17.5 7.08495 17.4719 7.16514C17.4217 7.30876 17.3088 7.42169 17.1651 7.47194C17.085 7.5 16.9852 7.5 16.7857 7.5L12.5 7.5M15 2.50001V2.50001C13.6193 2.50001 12.5 3.61929 12.5 5.00001L12.5 7.5M15 2.50001L6.5 2.50001C4.61438 2.50001 3.67157 2.50001 3.08579 3.08579C2.5 3.67158 2.5 4.61439 2.5 6.50001L2.5 17.5L5 16.6667L7.5 17.5L10 16.6667L12.5 17.5L12.5 7.5" stroke="#792AFF"/><path d="M5.83301 5.83325L9.16634 5.83325" stroke="#792AFF" stroke-linecap="round"/><path d="M6.66699 9.16675H5.83366" stroke="#792AFF" stroke-linecap="round"/><path d="M5.83301 12.5L8.33301 12.5" stroke="#792AFF" stroke-linecap="round"/></svg>',
                                                  "AAMService"),
                                        )
                                      : Container();
                            }),

                            // serviceMenuCard(
                            //     context,
                            //     appConfigService.countryConfigCollection.toString() ==
                            //             'aam'
                            //         ? 'assets/menu_icon/aam/mr.png'
                            //         : appConfigService.countryConfigCollection
                            //                     .toString() ==
                            //                 'rplc'
                            //             ? 'assets/menu_icon/rplc/mr.png'
                            //             : 'assets/menu_icon/rafco/mr.png',
                            //     homeIncome.tr),
                            // serviceMenuCard(
                            //     context,
                            //     appConfigService.countryConfigCollection.toString() ==
                            //             'aam'
                            //         ? 'assets/menu_icon/aam/bill.png'
                            //         : appConfigService.countryConfigCollection
                            //                     .toString() ==
                            //                 'rplc'
                            //             ? 'assets/menu_icon/rplc/bill.png'
                            //             : 'assets/menu_icon/rafco/bill.png',
                            //     homePayInstallments.tr),
                            // context, 'assets/menu_icon/aam/bill.png', 'ชำระบิล'),
                            // serviceMenuCard(
                            //     context,
                            //     appConfigService.countryConfigCollection.toString() ==
                            //             'aam'
                            //         ? 'assets/menu_icon/aam/scan.png'
                            //         : appConfigService.countryConfigCollection
                            //                     .toString() ==
                            //                 'rplc'
                            //             ? 'assets/menu_icon/rplc/scan.png'
                            //             : 'assets/menu_icon/rafco/scan.png',
                            //     homeScan.tr),
                          ],
                        ),
                      )
                    : appConfigService.countryConfigCollection.toString() ==
                            'rafco'
                        ? Container(
                            child: StaggeredGrid.count(
                              crossAxisCount: 4,
                              mainAxisSpacing: 4,
                              crossAxisSpacing: 9.w,
                              children: [
                                /// สินเชื่อ
                                InkWell(
                                  onTap: () {
                                    if (homeController.isGuest!.value) {
                                      Get.to(() => const RegisterPage());
                                    }
                                    if (Get.find<ContractListController>()
                                        .contractList
                                        .isEmpty) {
                                      Get.to(() => const LoanRequestDetail());
                                    } else {
                                      Get.to(() => const LoanMainScreen());
                                    }
                                  },
                                  child: HomePageCoponentWidget.serviceMenuCard(
                                      context,
                                      appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'aam'
                                          ? '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20801" y="8.33331" width="11.4583" height="13.5417" rx="2" fill="#792AFF" fill-opacity="0.75"/><path d="M8.33301 14.5833L14.583 14.5833" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33301 11.4583L12.4997 11.4583" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33301 17.7083L12.4997 17.7083" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7913 13.5417V15.875C19.7913 18.7034 19.7913 20.1176 18.9127 20.9963C18.034 21.875 16.6198 21.875 13.7913 21.875H11.208C8.37958 21.875 6.96537 21.875 6.08669 20.9963C5.20801 20.1176 5.20801 18.7034 5.20801 15.875V9.125C5.20801 6.29657 5.20801 4.88236 6.08669 4.00368C6.96537 3.125 8.37958 3.125 11.208 3.125H12.4997" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                          : appConfigService
                                                      .countryConfigCollection
                                                      .toString() ==
                                                  'rplc'
                                              ? '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20898" y="8.33337" width="11.4583" height="13.5417" rx="2" fill="#6A7165" fill-opacity="0.75"/><path d="M8.33398 14.5834L14.584 14.5834" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33398 11.4584L12.5007 11.4584" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33398 17.7084L12.5007 17.7084" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7923 13.5417V15.875C19.7923 18.7034 19.7923 20.1176 18.9136 20.9963C18.035 21.875 16.6207 21.875 13.7923 21.875H11.209C8.38056 21.875 6.96634 21.875 6.08766 20.9963C5.20898 20.1176 5.20898 18.7034 5.20898 15.875V9.125C5.20898 6.29657 5.20898 4.88236 6.08766 4.00368C6.96634 3.125 8.38056 3.125 11.209 3.125H12.5007" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                              : '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20825" y="8.33334" width="11.4583" height="13.5417" rx="2" fill="#EA1B23" fill-opacity="0.75"/><path d="M8.33325 14.5833L14.5833 14.5833" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33325 11.4583L12.4999 11.4583" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33325 17.7083L12.4999 17.7083" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7916 13.5417V15.875C19.7916 18.7034 19.7916 20.1176 18.9129 20.9963C18.0342 21.875 16.62 21.875 13.7916 21.875H11.2082C8.37982 21.875 6.96561 21.875 6.08693 20.9963C5.20825 20.1176 5.20825 18.7034 5.20825 15.875V9.125C5.20825 6.29657 5.20825 4.88236 6.08693 4.00368C6.96561 3.125 8.37982 3.125 11.2083 3.125H12.4999" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>',
                                      homeApply.tr),
                                ),

                                /// สาขา
                                InkWell(
                                  onTap: () {
                                    debugPrint('Branch Page');
                                    Get.to(() => BranchScreen());
                                  },
                                  child: HomePageCoponentWidget.serviceMenuCard(
                                      context,
                                      '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8977 20.6379C14.4201 19.8509 19.7916 16.7028 19.7916 11.4583C19.7916 7.43125 16.527 4.16666 12.4999 4.16666C8.47284 4.16666 5.20825 7.43125 5.20825 11.4583C5.20825 16.7028 10.5798 19.8509 12.1021 20.6379C12.3552 20.7687 12.6446 20.7687 12.8977 20.6379ZM12.4999 14.5833C14.2258 14.5833 15.6249 13.1842 15.6249 11.4583C15.6249 9.73244 14.2258 8.33333 12.4999 8.33333C10.774 8.33333 9.37492 9.73244 9.37492 11.4583C9.37492 13.1842 10.774 14.5833 12.4999 14.5833Z" fill="#EA1B23" fill-opacity="0.75"/><path d="M12.8977 20.6379L13.1732 21.1709H13.1732L12.8977 20.6379ZM12.1021 20.6379L11.8266 21.1709H11.8266L12.1021 20.6379ZM19.1916 11.4583C19.1916 13.8624 17.9621 15.8171 16.4902 17.2965C15.019 18.7752 13.3565 19.7253 12.6222 20.1048L13.1732 21.1709C13.9612 20.7635 15.7457 19.7461 17.3409 18.1429C18.9353 16.5403 20.3916 14.2988 20.3916 11.4583H19.1916ZM12.4999 4.76666C16.1956 4.76666 19.1916 7.76263 19.1916 11.4583H20.3916C20.3916 7.09988 16.8584 3.56666 12.4999 3.56666V4.76666ZM5.80825 11.4583C5.80825 7.76263 8.80421 4.76666 12.4999 4.76666V3.56666C8.14147 3.56666 4.60825 7.09988 4.60825 11.4583H5.80825ZM12.3776 20.1048C11.6433 19.7253 9.98087 18.7752 8.50964 17.2965C7.03775 15.8171 5.80825 13.8624 5.80825 11.4583H4.60825C4.60825 14.2988 6.06451 16.5403 7.65897 18.1429C9.2541 19.7461 11.0386 20.7635 11.8266 21.1709L12.3776 20.1048ZM12.6222 20.1048C12.542 20.1463 12.4579 20.1463 12.3776 20.1048L11.8266 21.1709C12.2525 21.391 12.7473 21.391 13.1732 21.1709L12.6222 20.1048ZM15.0249 11.4583C15.0249 12.8529 13.8944 13.9833 12.4999 13.9833V15.1833C14.5572 15.1833 16.2249 13.5156 16.2249 11.4583H15.0249ZM12.4999 8.93333C13.8944 8.93333 15.0249 10.0638 15.0249 11.4583H16.2249C16.2249 9.40107 14.5572 7.73333 12.4999 7.73333V8.93333ZM9.97492 11.4583C9.97492 10.0638 11.1054 8.93333 12.4999 8.93333V7.73333C10.4427 7.73333 8.77492 9.40107 8.77492 11.4583H9.97492ZM12.4999 13.9833C11.1054 13.9833 9.97492 12.8528 9.97492 11.4583H8.77492C8.77492 13.5156 10.4427 15.1833 12.4999 15.1833V13.9833Z" fill="#1A1818"/></svg>',
                                      homeBranch.tr),
                                ),

                                /// aicp
                                // InAppWebView(
                                //   initialUrlRequest: URLRequest(url: Uri.parse('https://aicpmotor.web.app/')),
                                //   initialOptions: InAppWebViewGroupOptions(
                                //     crossPlatform: InAppWebViewOptions(
                                //     ),
                                //   ),
                                // )
                                InkWell(
                                    onTap: () async {
                                      /// เปิดเว็บวิว aicp
                                      if (Platform.isIOS) {
                                        var phoneUser = base64.encode(
                                            utf8.encode(profileController
                                                .profile.value.phoneFirebase
                                                .toString()));
                                        print("phoneUser");

                                        final String url =
                                            "https://aicpmotor.web.app/?phone=$phoneUser";
                                        final Uri uri = Uri.parse(url);
                                        if (await canLaunchUrl(uri)) {
                                          await launchUrl(uri,
                                              mode: LaunchMode
                                                  .externalApplication);
                                        } else {
                                          throw "ไม่สามารถเปิด URL ได้: $url";
                                        }
                                      } else {
                                        Get.to(() => const HomeAicp());
                                      }
                                    },
                                    child: Container(
                                      width: 75.w,
                                      // height: 90.h,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                              width: 60.w,
                                              height: 46.h,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(12.r),
                                                color: configTheme()
                                                    .colorScheme
                                                    .onSecondary
                                                    .withOpacity(0.05),
                                              ),
                                              child: Center(
                                                child: Container(
                                                  height: 40.h,
                                                  width: 40.w,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12.r),
                                                      color: Color(0xFF0a3069)
                                                      // color:  Color(0xFF006ebd)
                                                      // color:  AppColors.RAFCOMyloanCard,
                                                      ),
                                                  child: Center(
                                                    child: SvgPicture.string(
                                                      width: 20.w,
                                                      height: 20.h,
                                                      '<svg width="20" height="20" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M37.09 15.405C36.3912 15.405 35.7543 14.9636 35.5067 14.3015C35.3563 13.9042 35.1971 13.507 35.0202 13.1273C34.7194 12.4829 34.8609 11.7237 35.3563 11.2205C35.9224 10.6555 36.232 9.9051 36.232 9.11057C36.232 8.31604 35.9224 7.56566 35.3563 7.00066L33.0653 4.71419C31.8977 3.54889 30.0048 3.54889 28.8372 4.71419C28.3418 5.20856 27.5722 5.34098 26.9265 5.04966C26.5373 4.8731 26.1393 4.70536 25.7501 4.56411C25.0867 4.31693 24.6444 3.68131 24.6444 2.98389C24.6444 1.34187 23.2999 0 21.6546 0H18.4172C16.7719 0 15.4274 1.34187 15.4274 2.98389C15.4274 3.68131 14.9851 4.31693 14.3217 4.56411C13.9237 4.71419 13.5345 4.8731 13.1453 5.04966C12.4995 5.34981 11.7388 5.20856 11.2346 4.71419C10.067 3.54889 8.17408 3.54889 7.00648 4.71419L4.7155 7.00066C3.54789 8.16597 3.54789 10.0552 4.7155 11.2205C5.21084 11.7149 5.34353 12.4829 5.05163 13.1273C4.87472 13.5158 4.70665 13.9042 4.56512 14.3015C4.31745 14.9636 3.68057 15.405 2.98178 15.405C1.33652 15.405 -0.00799561 16.7469 -0.00799561 18.3889V21.62C-0.00799561 23.262 1.33652 24.6038 2.98178 24.6038C3.68057 24.6038 4.31745 25.0452 4.56512 25.7073C4.7155 26.1046 4.87472 26.5019 5.05163 26.8815C5.35237 27.5259 5.21084 28.2851 4.7155 28.7883C3.54789 29.9537 3.54789 31.8429 4.7155 33.0082L7.00648 35.2946C7.57259 35.8596 8.32446 36.1686 9.12055 36.1686C9.91665 36.1686 10.6685 35.8596 11.2346 35.2946C11.73 34.8003 12.4995 34.6678 13.1453 34.9592C13.5345 35.1357 13.9237 35.3035 14.3217 35.4447C14.9851 35.6919 15.4274 36.3275 15.4274 37.0249C15.4274 38.667 16.7719 40.0088 18.4172 40.0088H21.6546C23.2999 40.0088 24.6444 38.667 24.6444 37.0249C24.6444 36.3275 25.0867 35.6919 25.7501 35.4447C26.1481 35.2946 26.5373 35.1357 26.9265 34.9592C27.5722 34.659 28.333 34.8003 28.8372 35.2946C30.0048 36.4599 31.8977 36.4599 33.0653 35.2946L35.3563 33.0082C35.9224 32.4432 36.232 31.6928 36.232 30.8983C36.232 30.1037 35.9224 29.3533 35.3563 28.7883C34.8609 28.294 34.7282 27.5259 35.0202 26.8815C35.1971 26.5019 35.3651 26.1046 35.5067 25.7073C35.7543 25.0452 36.3912 24.6038 37.09 24.6038C38.7353 24.6038 40.0798 23.262 40.0798 21.62V18.3889C40.0798 16.7469 38.7353 15.405 37.09 15.405ZM38.7795 21.62C38.7795 22.5469 38.0188 23.3061 37.09 23.3061C35.8516 23.3061 34.7194 24.0918 34.286 25.2571C34.1533 25.6191 34.0029 25.9898 33.8349 26.343C33.313 27.473 33.5518 28.8325 34.4275 29.7065C34.7459 30.0243 34.9229 30.448 34.9229 30.8983C34.9229 31.3485 34.7459 31.7722 34.4275 32.09L32.1365 34.3765C31.482 35.0298 30.4028 35.0298 29.7482 34.3765C28.8637 33.4937 27.5103 33.2554 26.3781 33.785C26.0243 33.9528 25.6528 34.1028 25.2901 34.2353C24.1225 34.6678 23.3353 35.789 23.3353 37.0338C23.3353 37.9607 22.5745 38.7199 21.6458 38.7199H18.4083C17.4795 38.7199 16.7188 37.9607 16.7188 37.0338C16.7188 35.7978 15.9316 34.6678 14.764 34.2353C14.4013 34.1028 14.0298 33.9528 13.676 33.785C13.2779 33.5996 12.8445 33.5114 12.4199 33.5114C11.6415 33.5114 10.872 33.8115 10.3058 34.3853C9.98741 34.7032 9.56283 34.8797 9.11171 34.8797C8.66059 34.8797 8.236 34.7032 7.91757 34.3853L5.62658 32.0989C4.97202 31.4456 4.97202 30.3686 5.62658 29.7153C6.51113 28.8325 6.74996 27.4818 6.21923 26.3518C6.05117 25.9987 5.90079 25.6279 5.76811 25.2659C5.33468 24.1006 4.2113 23.3149 2.96409 23.3149C2.03531 23.3149 1.2746 22.5557 1.2746 21.6288V18.3977C1.2746 17.4708 2.03531 16.7115 2.96409 16.7115C4.20246 16.7115 5.33468 15.9258 5.76811 14.7605C5.90079 14.3986 6.05117 14.0366 6.21923 13.6747C6.74112 12.5447 6.50229 11.1852 5.62658 10.3112C4.97202 9.65791 4.97202 8.58089 5.62658 7.92761L7.91757 5.64114C8.57213 4.98786 9.65128 4.98786 10.3058 5.64114C11.1904 6.52395 12.5438 6.7623 13.676 6.23262C14.0298 6.06489 14.4013 5.91481 14.764 5.78239C15.9316 5.34981 16.7188 4.22865 16.7188 2.98389C16.7188 2.05694 17.4795 1.29773 18.4083 1.29773H21.6458C22.5745 1.29773 23.3353 2.05694 23.3353 2.98389C23.3353 4.21982 24.1225 5.34981 25.2901 5.78239C25.6528 5.91481 26.0154 6.06489 26.3781 6.23262C27.5103 6.75348 28.8725 6.51512 29.7482 5.64114C30.4028 4.98786 31.482 4.98786 32.1365 5.64114L34.4275 7.92761C34.7459 8.24542 34.9229 8.66917 34.9229 9.1194C34.9229 9.56963 34.7459 9.99338 34.4275 10.3112C33.543 11.194 33.3041 12.5447 33.8349 13.6747C34.0029 14.0278 34.1533 14.3898 34.286 14.7605C34.7194 15.9258 35.8428 16.7115 37.09 16.7115C38.0188 16.7115 38.7795 17.4708 38.7795 18.3977V21.6288V21.62Z" fill="#FE0000"/><path d="M30.3666 16.8087C30.1631 16.5615 29.9774 16.3319 29.7916 16.1201L28.6063 14.8665C28.0314 14.2927 27.2618 13.9748 26.4392 13.9748C26.3065 13.9748 26.1738 13.9837 26.0411 14.0013C26.1561 13.092 25.8642 12.1386 25.1654 11.4324L23.9094 10.2494C23.7148 10.0728 23.4848 9.89628 23.2371 9.69323C22.3968 9.0223 21.353 8.17481 20.6012 6.92122L20.0439 5.98544L19.4866 6.92122C18.7348 8.18363 17.691 9.0223 16.8507 9.69323C16.603 9.89628 16.373 10.0817 16.1607 10.2671L14.9047 11.45C14.2059 12.1474 13.9228 13.1009 14.0378 14.0102C13.1267 13.8954 12.1626 14.1867 11.4638 14.8841L10.2785 16.1377C10.1016 16.3319 9.92466 16.5615 9.72121 16.8087C9.04895 17.6473 8.19979 18.689 6.94373 19.4394L6.0061 19.9956L6.94373 20.5518C8.20863 21.3022 9.04895 22.3439 9.72121 23.1825C9.92466 23.4297 10.1104 23.6592 10.2962 23.8711L11.4815 25.1247C12.083 25.725 12.8614 26.0163 13.6486 26.0163C13.7813 26.0163 13.914 26.0075 14.0467 25.9899C13.9317 26.8991 14.2147 27.8614 14.9224 28.5588L16.1784 29.7418C16.373 29.9183 16.603 30.0949 16.8507 30.298C17.691 30.9777 18.7348 31.8164 19.4866 33.07L20.0439 34.0057L20.6012 33.07C21.353 31.8076 22.3968 30.9689 23.2371 30.298C23.4848 30.0949 23.7148 29.9095 23.9271 29.7241L25.1831 28.5412C25.8819 27.8438 26.165 26.8903 26.05 25.981C26.1827 25.9987 26.3154 26.0075 26.448 26.0075C27.2618 26.0075 28.0314 25.6897 28.624 25.0982L29.8093 23.8446C29.9862 23.6504 30.1631 23.4209 30.3666 23.1737C31.0388 22.335 31.888 21.2933 33.1441 20.5429L34.0817 19.9868L33.1441 19.4306C31.8792 18.6802 31.0388 17.6385 30.3666 16.7998V16.8087ZM23.9713 19.9073C23.9713 20.7725 23.6882 21.5758 23.2017 22.2203L22.2199 21.2404C21.8838 20.9049 21.7069 20.4723 21.7069 20.0044C21.7069 19.5365 21.8926 19.0951 22.2199 18.7685L23.2813 17.7091C23.7148 18.3359 23.9713 19.104 23.9713 19.9161V19.9073ZM16.1165 19.9073C16.1165 19.0863 16.373 18.3271 16.7976 17.7003L17.8591 18.7597C18.1952 19.0951 18.3721 19.5277 18.3721 19.9956C18.3721 20.4635 18.1863 20.9049 17.8591 21.2315L16.8772 22.2114C16.3996 21.5582 16.1165 20.7636 16.1165 19.8985V19.9073ZM21.2823 17.8239C20.9461 18.1594 20.5127 18.3359 20.0439 18.3359C19.5751 18.3359 19.1328 18.1505 18.8055 17.8239L17.7352 16.7557C18.3898 16.279 19.1859 15.9877 20.0527 15.9877C20.9196 15.9877 21.7157 16.2701 22.3703 16.7469L21.2911 17.8239H21.2823ZM20.4331 19.6072C20.4154 19.7484 20.4066 19.8897 20.4066 20.0309C20.4066 20.1457 20.4154 20.2693 20.4331 20.384C20.3004 20.3664 20.1766 20.3575 20.0439 20.3575C19.9112 20.3575 19.7785 20.3664 19.6547 20.384C19.6724 20.2693 19.6812 20.1457 19.6812 20.0309C19.6812 19.8897 19.6724 19.7484 19.6547 19.6072C19.7874 19.6248 19.9112 19.6336 20.0439 19.6336C20.1766 19.6336 20.3093 19.6248 20.4331 19.6072ZM18.7967 22.1761C19.1328 21.8407 19.5662 21.6641 20.0351 21.6641C20.5039 21.6641 20.9461 21.8495 21.2734 22.1761L22.2464 23.1472C21.6184 23.5798 20.8577 23.827 20.0351 23.827C19.2124 23.827 18.4517 23.571 17.8237 23.1384L18.7967 22.1673V22.1761ZM15.7981 12.377L17.0276 11.2205C17.2045 11.0616 17.4168 10.8939 17.6556 10.6996C18.3544 10.1346 19.2566 9.41074 20.0351 8.39551C20.8135 9.41074 21.7157 10.1346 22.4145 10.6996C22.6445 10.885 22.8568 11.0528 23.0248 11.2028L24.2544 12.3593C24.9443 13.0479 24.9443 14.1602 24.2544 14.8488L23.2813 15.8199C22.388 15.1137 21.2646 14.6899 20.0351 14.6899C18.8055 14.6899 17.6822 15.1137 16.7888 15.8287L15.8069 14.8488C15.117 14.1602 15.117 13.0479 15.7892 12.377H15.7981ZM12.3925 24.2331L11.2338 23.006C11.0746 22.8294 10.9065 22.6175 10.7119 22.3792C10.1458 21.6818 9.42046 20.7813 8.40323 20.0044C9.42046 19.2276 10.1458 18.3271 10.7119 17.6297C10.8977 17.4001 11.0657 17.1883 11.2161 17.0205L12.3749 15.7934C13.0648 15.1048 14.1793 15.1048 14.8693 15.7934L15.86 16.7822C15.1966 17.6562 14.8074 18.742 14.8074 19.9161C14.8074 21.0903 15.232 22.2644 15.9396 23.156L14.8693 24.2242C14.1793 24.9128 13.0648 24.9128 12.3925 24.2419V24.2331ZM24.272 27.6319L23.0425 28.7884C22.8656 28.9473 22.6533 29.115 22.4145 29.3092C21.7157 29.8742 20.8135 30.5981 20.0351 31.6133C19.2566 30.5981 18.3544 29.8742 17.6556 29.3092C17.4256 29.1238 17.2133 28.9561 17.0453 28.806L15.8158 27.6495C15.1258 26.9609 15.1258 25.8486 15.8158 25.16L16.9037 24.0742C17.7795 24.7363 18.8674 25.1335 20.0439 25.1335C21.2203 25.1335 22.3083 24.7363 23.184 24.083L24.2632 25.16C24.9531 25.8486 24.9531 26.9609 24.2809 27.6319H24.272ZM29.3494 22.388C29.1636 22.6175 28.9955 22.8294 28.8452 22.9971L27.6864 24.2242C27.3503 24.5597 26.908 24.7363 26.4392 24.7363C25.9704 24.7363 25.5281 24.5509 25.192 24.2242L24.1217 23.156C24.8293 22.2644 25.2627 21.1432 25.2627 19.9161C25.2627 18.689 24.8647 17.6562 24.2013 16.7822L25.192 15.7934C25.5281 15.458 25.9704 15.2814 26.4392 15.2814C26.908 15.2814 27.3503 15.4668 27.6687 15.7846L28.8275 17.0117C28.9867 17.1883 29.1548 17.4001 29.3494 17.6385C29.9155 18.3359 30.6408 19.2364 31.658 20.0133C30.6408 20.7901 29.9155 21.6906 29.3494 22.388Z" fill="#FE0000"/></svg>',
                                                    ),
                                                  ),
                                                ),
                                              )),
                                          SizedBox(height: 9.h),
                                          Text(
                                            aicp.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontFamily,
                                              fontWeight: FontWeight.w500,
                                              // height: 0,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    )
                                    // HomePageCoponentWidget.serviceMenuCard(
                                    //     context,
                                    //     '<svg width="20" height="20" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M37.09 15.405C36.3912 15.405 35.7543 14.9636 35.5067 14.3015C35.3563 13.9042 35.1971 13.507 35.0202 13.1273C34.7194 12.4829 34.8609 11.7237 35.3563 11.2205C35.9224 10.6555 36.232 9.9051 36.232 9.11057C36.232 8.31604 35.9224 7.56566 35.3563 7.00066L33.0653 4.71419C31.8977 3.54889 30.0048 3.54889 28.8372 4.71419C28.3418 5.20856 27.5722 5.34098 26.9265 5.04966C26.5373 4.8731 26.1393 4.70536 25.7501 4.56411C25.0867 4.31693 24.6444 3.68131 24.6444 2.98389C24.6444 1.34187 23.2999 0 21.6546 0H18.4172C16.7719 0 15.4274 1.34187 15.4274 2.98389C15.4274 3.68131 14.9851 4.31693 14.3217 4.56411C13.9237 4.71419 13.5345 4.8731 13.1453 5.04966C12.4995 5.34981 11.7388 5.20856 11.2346 4.71419C10.067 3.54889 8.17408 3.54889 7.00648 4.71419L4.7155 7.00066C3.54789 8.16597 3.54789 10.0552 4.7155 11.2205C5.21084 11.7149 5.34353 12.4829 5.05163 13.1273C4.87472 13.5158 4.70665 13.9042 4.56512 14.3015C4.31745 14.9636 3.68057 15.405 2.98178 15.405C1.33652 15.405 -0.00799561 16.7469 -0.00799561 18.3889V21.62C-0.00799561 23.262 1.33652 24.6038 2.98178 24.6038C3.68057 24.6038 4.31745 25.0452 4.56512 25.7073C4.7155 26.1046 4.87472 26.5019 5.05163 26.8815C5.35237 27.5259 5.21084 28.2851 4.7155 28.7883C3.54789 29.9537 3.54789 31.8429 4.7155 33.0082L7.00648 35.2946C7.57259 35.8596 8.32446 36.1686 9.12055 36.1686C9.91665 36.1686 10.6685 35.8596 11.2346 35.2946C11.73 34.8003 12.4995 34.6678 13.1453 34.9592C13.5345 35.1357 13.9237 35.3035 14.3217 35.4447C14.9851 35.6919 15.4274 36.3275 15.4274 37.0249C15.4274 38.667 16.7719 40.0088 18.4172 40.0088H21.6546C23.2999 40.0088 24.6444 38.667 24.6444 37.0249C24.6444 36.3275 25.0867 35.6919 25.7501 35.4447C26.1481 35.2946 26.5373 35.1357 26.9265 34.9592C27.5722 34.659 28.333 34.8003 28.8372 35.2946C30.0048 36.4599 31.8977 36.4599 33.0653 35.2946L35.3563 33.0082C35.9224 32.4432 36.232 31.6928 36.232 30.8983C36.232 30.1037 35.9224 29.3533 35.3563 28.7883C34.8609 28.294 34.7282 27.5259 35.0202 26.8815C35.1971 26.5019 35.3651 26.1046 35.5067 25.7073C35.7543 25.0452 36.3912 24.6038 37.09 24.6038C38.7353 24.6038 40.0798 23.262 40.0798 21.62V18.3889C40.0798 16.7469 38.7353 15.405 37.09 15.405ZM38.7795 21.62C38.7795 22.5469 38.0188 23.3061 37.09 23.3061C35.8516 23.3061 34.7194 24.0918 34.286 25.2571C34.1533 25.6191 34.0029 25.9898 33.8349 26.343C33.313 27.473 33.5518 28.8325 34.4275 29.7065C34.7459 30.0243 34.9229 30.448 34.9229 30.8983C34.9229 31.3485 34.7459 31.7722 34.4275 32.09L32.1365 34.3765C31.482 35.0298 30.4028 35.0298 29.7482 34.3765C28.8637 33.4937 27.5103 33.2554 26.3781 33.785C26.0243 33.9528 25.6528 34.1028 25.2901 34.2353C24.1225 34.6678 23.3353 35.789 23.3353 37.0338C23.3353 37.9607 22.5745 38.7199 21.6458 38.7199H18.4083C17.4795 38.7199 16.7188 37.9607 16.7188 37.0338C16.7188 35.7978 15.9316 34.6678 14.764 34.2353C14.4013 34.1028 14.0298 33.9528 13.676 33.785C13.2779 33.5996 12.8445 33.5114 12.4199 33.5114C11.6415 33.5114 10.872 33.8115 10.3058 34.3853C9.98741 34.7032 9.56283 34.8797 9.11171 34.8797C8.66059 34.8797 8.236 34.7032 7.91757 34.3853L5.62658 32.0989C4.97202 31.4456 4.97202 30.3686 5.62658 29.7153C6.51113 28.8325 6.74996 27.4818 6.21923 26.3518C6.05117 25.9987 5.90079 25.6279 5.76811 25.2659C5.33468 24.1006 4.2113 23.3149 2.96409 23.3149C2.03531 23.3149 1.2746 22.5557 1.2746 21.6288V18.3977C1.2746 17.4708 2.03531 16.7115 2.96409 16.7115C4.20246 16.7115 5.33468 15.9258 5.76811 14.7605C5.90079 14.3986 6.05117 14.0366 6.21923 13.6747C6.74112 12.5447 6.50229 11.1852 5.62658 10.3112C4.97202 9.65791 4.97202 8.58089 5.62658 7.92761L7.91757 5.64114C8.57213 4.98786 9.65128 4.98786 10.3058 5.64114C11.1904 6.52395 12.5438 6.7623 13.676 6.23262C14.0298 6.06489 14.4013 5.91481 14.764 5.78239C15.9316 5.34981 16.7188 4.22865 16.7188 2.98389C16.7188 2.05694 17.4795 1.29773 18.4083 1.29773H21.6458C22.5745 1.29773 23.3353 2.05694 23.3353 2.98389C23.3353 4.21982 24.1225 5.34981 25.2901 5.78239C25.6528 5.91481 26.0154 6.06489 26.3781 6.23262C27.5103 6.75348 28.8725 6.51512 29.7482 5.64114C30.4028 4.98786 31.482 4.98786 32.1365 5.64114L34.4275 7.92761C34.7459 8.24542 34.9229 8.66917 34.9229 9.1194C34.9229 9.56963 34.7459 9.99338 34.4275 10.3112C33.543 11.194 33.3041 12.5447 33.8349 13.6747C34.0029 14.0278 34.1533 14.3898 34.286 14.7605C34.7194 15.9258 35.8428 16.7115 37.09 16.7115C38.0188 16.7115 38.7795 17.4708 38.7795 18.3977V21.6288V21.62Z" fill="#FE0000"/><path d="M30.3666 16.8087C30.1631 16.5615 29.9774 16.3319 29.7916 16.1201L28.6063 14.8665C28.0314 14.2927 27.2618 13.9748 26.4392 13.9748C26.3065 13.9748 26.1738 13.9837 26.0411 14.0013C26.1561 13.092 25.8642 12.1386 25.1654 11.4324L23.9094 10.2494C23.7148 10.0728 23.4848 9.89628 23.2371 9.69323C22.3968 9.0223 21.353 8.17481 20.6012 6.92122L20.0439 5.98544L19.4866 6.92122C18.7348 8.18363 17.691 9.0223 16.8507 9.69323C16.603 9.89628 16.373 10.0817 16.1607 10.2671L14.9047 11.45C14.2059 12.1474 13.9228 13.1009 14.0378 14.0102C13.1267 13.8954 12.1626 14.1867 11.4638 14.8841L10.2785 16.1377C10.1016 16.3319 9.92466 16.5615 9.72121 16.8087C9.04895 17.6473 8.19979 18.689 6.94373 19.4394L6.0061 19.9956L6.94373 20.5518C8.20863 21.3022 9.04895 22.3439 9.72121 23.1825C9.92466 23.4297 10.1104 23.6592 10.2962 23.8711L11.4815 25.1247C12.083 25.725 12.8614 26.0163 13.6486 26.0163C13.7813 26.0163 13.914 26.0075 14.0467 25.9899C13.9317 26.8991 14.2147 27.8614 14.9224 28.5588L16.1784 29.7418C16.373 29.9183 16.603 30.0949 16.8507 30.298C17.691 30.9777 18.7348 31.8164 19.4866 33.07L20.0439 34.0057L20.6012 33.07C21.353 31.8076 22.3968 30.9689 23.2371 30.298C23.4848 30.0949 23.7148 29.9095 23.9271 29.7241L25.1831 28.5412C25.8819 27.8438 26.165 26.8903 26.05 25.981C26.1827 25.9987 26.3154 26.0075 26.448 26.0075C27.2618 26.0075 28.0314 25.6897 28.624 25.0982L29.8093 23.8446C29.9862 23.6504 30.1631 23.4209 30.3666 23.1737C31.0388 22.335 31.888 21.2933 33.1441 20.5429L34.0817 19.9868L33.1441 19.4306C31.8792 18.6802 31.0388 17.6385 30.3666 16.7998V16.8087ZM23.9713 19.9073C23.9713 20.7725 23.6882 21.5758 23.2017 22.2203L22.2199 21.2404C21.8838 20.9049 21.7069 20.4723 21.7069 20.0044C21.7069 19.5365 21.8926 19.0951 22.2199 18.7685L23.2813 17.7091C23.7148 18.3359 23.9713 19.104 23.9713 19.9161V19.9073ZM16.1165 19.9073C16.1165 19.0863 16.373 18.3271 16.7976 17.7003L17.8591 18.7597C18.1952 19.0951 18.3721 19.5277 18.3721 19.9956C18.3721 20.4635 18.1863 20.9049 17.8591 21.2315L16.8772 22.2114C16.3996 21.5582 16.1165 20.7636 16.1165 19.8985V19.9073ZM21.2823 17.8239C20.9461 18.1594 20.5127 18.3359 20.0439 18.3359C19.5751 18.3359 19.1328 18.1505 18.8055 17.8239L17.7352 16.7557C18.3898 16.279 19.1859 15.9877 20.0527 15.9877C20.9196 15.9877 21.7157 16.2701 22.3703 16.7469L21.2911 17.8239H21.2823ZM20.4331 19.6072C20.4154 19.7484 20.4066 19.8897 20.4066 20.0309C20.4066 20.1457 20.4154 20.2693 20.4331 20.384C20.3004 20.3664 20.1766 20.3575 20.0439 20.3575C19.9112 20.3575 19.7785 20.3664 19.6547 20.384C19.6724 20.2693 19.6812 20.1457 19.6812 20.0309C19.6812 19.8897 19.6724 19.7484 19.6547 19.6072C19.7874 19.6248 19.9112 19.6336 20.0439 19.6336C20.1766 19.6336 20.3093 19.6248 20.4331 19.6072ZM18.7967 22.1761C19.1328 21.8407 19.5662 21.6641 20.0351 21.6641C20.5039 21.6641 20.9461 21.8495 21.2734 22.1761L22.2464 23.1472C21.6184 23.5798 20.8577 23.827 20.0351 23.827C19.2124 23.827 18.4517 23.571 17.8237 23.1384L18.7967 22.1673V22.1761ZM15.7981 12.377L17.0276 11.2205C17.2045 11.0616 17.4168 10.8939 17.6556 10.6996C18.3544 10.1346 19.2566 9.41074 20.0351 8.39551C20.8135 9.41074 21.7157 10.1346 22.4145 10.6996C22.6445 10.885 22.8568 11.0528 23.0248 11.2028L24.2544 12.3593C24.9443 13.0479 24.9443 14.1602 24.2544 14.8488L23.2813 15.8199C22.388 15.1137 21.2646 14.6899 20.0351 14.6899C18.8055 14.6899 17.6822 15.1137 16.7888 15.8287L15.8069 14.8488C15.117 14.1602 15.117 13.0479 15.7892 12.377H15.7981ZM12.3925 24.2331L11.2338 23.006C11.0746 22.8294 10.9065 22.6175 10.7119 22.3792C10.1458 21.6818 9.42046 20.7813 8.40323 20.0044C9.42046 19.2276 10.1458 18.3271 10.7119 17.6297C10.8977 17.4001 11.0657 17.1883 11.2161 17.0205L12.3749 15.7934C13.0648 15.1048 14.1793 15.1048 14.8693 15.7934L15.86 16.7822C15.1966 17.6562 14.8074 18.742 14.8074 19.9161C14.8074 21.0903 15.232 22.2644 15.9396 23.156L14.8693 24.2242C14.1793 24.9128 13.0648 24.9128 12.3925 24.2419V24.2331ZM24.272 27.6319L23.0425 28.7884C22.8656 28.9473 22.6533 29.115 22.4145 29.3092C21.7157 29.8742 20.8135 30.5981 20.0351 31.6133C19.2566 30.5981 18.3544 29.8742 17.6556 29.3092C17.4256 29.1238 17.2133 28.9561 17.0453 28.806L15.8158 27.6495C15.1258 26.9609 15.1258 25.8486 15.8158 25.16L16.9037 24.0742C17.7795 24.7363 18.8674 25.1335 20.0439 25.1335C21.2203 25.1335 22.3083 24.7363 23.184 24.083L24.2632 25.16C24.9531 25.8486 24.9531 26.9609 24.2809 27.6319H24.272ZM29.3494 22.388C29.1636 22.6175 28.9955 22.8294 28.8452 22.9971L27.6864 24.2242C27.3503 24.5597 26.908 24.7363 26.4392 24.7363C25.9704 24.7363 25.5281 24.5509 25.192 24.2242L24.1217 23.156C24.8293 22.2644 25.2627 21.1432 25.2627 19.9161C25.2627 18.689 24.8647 17.6562 24.2013 16.7822L25.192 15.7934C25.5281 15.458 25.9704 15.2814 26.4392 15.2814C26.908 15.2814 27.3503 15.4668 27.6687 15.7846L28.8275 17.0117C28.9867 17.1883 29.1548 17.4001 29.3494 17.6385C29.9155 18.3359 30.6408 19.2364 31.658 20.0133C30.6408 20.7901 29.9155 21.6906 29.3494 22.388Z" fill="#FE0000"/></svg>',
                                    //     aicp.tr),
                                    ),

                                Obx(() {
                                  return

                                      ///AAMGService
                                      profileController.isMember!.value == true
                                          ? InkWell(
                                              onTap: () {
                                                debugPrint('Branch Page');
                                                Get.to(() => AAMGService());
                                              },
                                              child: HomePageCoponentWidget
                                                  .serviceMenuCard(
                                                      context,
                                                      '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 2.50001V2.50001C16.3807 2.50001 17.5 3.61929 17.5 5.00001L17.5 6.78572C17.5 6.98521 17.5 7.08495 17.4719 7.16514C17.4217 7.30876 17.3088 7.42169 17.1651 7.47194C17.085 7.5 16.9852 7.5 16.7857 7.5L12.5 7.5M15 2.50001V2.50001C13.6193 2.50001 12.5 3.61929 12.5 5.00001L12.5 7.5M15 2.50001L6.5 2.50001C4.61438 2.50001 3.67157 2.50001 3.08579 3.08579C2.5 3.67158 2.5 4.61439 2.5 6.50001L2.5 17.5L5 16.6667L7.5 17.5L10 16.6667L12.5 17.5L12.5 7.5" stroke="#EA1B23"/> <path d="M5.83301 5.83325L9.16634 5.83325" stroke="#EA1B23" stroke-linecap="round"/> <path d="M6.66699 9.16675H5.83366" stroke="#EA1B23" stroke-linecap="round"/> <path d="M5.83301 12.5L8.33301 12.5" stroke="#EA1B23" stroke-linecap="round"/></svg>',
                                                      "AAMService"),
                                            )
                                          : Container();
                                }),
                              ],
                            ),
                          )
                        : Container(
                            child: StaggeredGrid.count(
                              crossAxisCount: 4,
                              mainAxisSpacing: 4,
                              crossAxisSpacing: 9.w,
                              children: [
                                /// สินเชื่อ
                                InkWell(
                                  onTap: () {
                                    if (homeController.isGuest!.value) {
                                      Get.to(() => const RegisterPage());
                                    }
                                    if (Get.find<ContractListController>()
                                        .contractList
                                        .isEmpty) {
                                      Get.to(() => const LoanRequestDetail());
                                    } else {
                                      Get.to(() => const LoanMainScreen());
                                    }
                                  },
                                  child: HomePageCoponentWidget.serviceMenuCard(
                                      context,
                                      appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'aam'
                                          ? '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20831" y="8.3335" width="11.4583" height="13.5417" rx="2" fill="#792AFF" fill-opacity="0.75"/><path d="M8.33331 14.5835L14.5833 14.5835" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33331 11.4585L12.5 11.4585" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33331 17.7085L12.5 17.7085" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7916 13.5417V15.875C19.7916 18.7034 19.7916 20.1176 18.913 20.9963C18.0343 21.875 16.6201 21.875 13.7916 21.875H11.2083C8.37988 21.875 6.96567 21.875 6.08699 20.9963C5.20831 20.1176 5.20831 18.7034 5.20831 15.875V9.125C5.20831 6.29657 5.20831 4.88236 6.08699 4.00368C6.96567 3.125 8.37989 3.125 11.2083 3.125H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                          : appConfigService
                                                      .countryConfigCollection
                                                      .toString() ==
                                                  'rplc'
                                              ? '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="5.20833" y="8.3335" width="11.4583" height="13.5417" rx="2" fill="#6A7165" fill-opacity="0.75"/><path d="M8.33334 14.5835L14.5833 14.5835" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33334 11.4585L12.5 11.4585" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M8.33334 17.7085L12.5 17.7085" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M19.7917 13.5417V15.875C19.7917 18.7034 19.7917 20.1176 18.913 20.9963C18.0343 21.875 16.6201 21.875 13.7917 21.875H11.2083C8.3799 21.875 6.96569 21.875 6.08701 20.9963C5.20833 20.1176 5.20833 18.7034 5.20833 15.875V9.125C5.20833 6.29657 5.20833 4.88236 6.08701 4.00368C6.96569 3.125 8.3799 3.125 11.2083 3.125H12.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M18.75 3.125L18.75 9.375" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M21.875 6.25L15.625 6.25" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                              : '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8978 20.6377C14.4202 19.8508 19.7917 16.7027 19.7917 11.4582C19.7917 7.43109 16.5271 4.1665 12.5 4.1665C8.47293 4.1665 5.20834 7.43109 5.20834 11.4582C5.20834 16.7027 10.5799 19.8508 12.1022 20.6377C12.3553 20.7685 12.6447 20.7685 12.8978 20.6377ZM12.5 14.5832C14.2259 14.5832 15.625 13.1841 15.625 11.4582C15.625 9.73228 14.2259 8.33317 12.5 8.33317C10.7741 8.33317 9.37501 9.73228 9.37501 11.4582C9.37501 13.1841 10.7741 14.5832 12.5 14.5832Z" fill="#6A7165" fill-opacity="0.75"/><path d="M12.8978 20.6377L13.1733 21.1707H13.1733L12.8978 20.6377ZM12.1022 20.6377L11.8267 21.1707H11.8267L12.1022 20.6377ZM19.1917 11.4582C19.1917 13.8622 17.9622 15.817 16.4903 17.2963C15.0191 18.775 13.3566 19.7251 12.6223 20.1047L13.1733 21.1707C13.9613 20.7634 15.7458 19.7459 17.341 18.1427C18.9354 16.5402 20.3917 14.2987 20.3917 11.4582H19.1917ZM12.5 4.7665C16.1957 4.7665 19.1917 7.76247 19.1917 11.4582H20.3917C20.3917 7.09972 16.8585 3.5665 12.5 3.5665V4.7665ZM5.80834 11.4582C5.80834 7.76247 8.8043 4.7665 12.5 4.7665V3.5665C8.14156 3.5665 4.60834 7.09972 4.60834 11.4582H5.80834ZM12.3777 20.1047C11.6434 19.7251 9.98096 18.775 8.50973 17.2963C7.03784 15.817 5.80834 13.8622 5.80834 11.4582H4.60834C4.60834 14.2987 6.0646 16.5402 7.65906 18.1427C9.25419 19.7459 11.0387 20.7634 11.8267 21.1707L12.3777 20.1047ZM12.6223 20.1047C12.542 20.1462 12.458 20.1462 12.3777 20.1047L11.8267 21.1707C12.2526 21.3908 12.7474 21.3908 13.1733 21.1707L12.6223 20.1047ZM15.025 11.4582C15.025 12.8527 13.8945 13.9832 12.5 13.9832V15.1832C14.5573 15.1832 16.225 13.5154 16.225 11.4582H15.025ZM12.5 8.93317C13.8945 8.93317 15.025 10.0637 15.025 11.4582H16.225C16.225 9.40091 14.5573 7.73317 12.5 7.73317V8.93317ZM9.97501 11.4582C9.97501 10.0637 11.1055 8.93317 12.5 8.93317V7.73317C10.4427 7.73317 8.77501 9.40091 8.77501 11.4582H9.97501ZM12.5 13.9832C11.1055 13.9832 9.97501 12.8527 9.97501 11.4582H8.77501C8.77501 13.5154 10.4427 15.1832 12.5 15.1832V13.9832Z" fill="#1A1818"/></svg>',
                                      homeApply.tr),
                                ),

                                /// พร้อมใช้
                                // serviceMenuCard(
                                //     context,
                                //     'assets/menu_icon/aam/aam_pay.png',
                                //     '5,000\n${homeReady.tr}'),
                                /// สาขา
                                /// สาขา
                                InkWell(
                                  onTap: () {
                                    debugPrint('Branch Page');
                                    Get.to(() => BranchScreen());
                                  },
                                  child: HomePageCoponentWidget.serviceMenuCard(
                                      context,
                                      '<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8978 20.6377C14.4202 19.8508 19.7917 16.7027 19.7917 11.4582C19.7917 7.43109 16.5271 4.1665 12.5 4.1665C8.47293 4.1665 5.20834 7.43109 5.20834 11.4582C5.20834 16.7027 10.5799 19.8508 12.1022 20.6377C12.3553 20.7685 12.6447 20.7685 12.8978 20.6377ZM12.5 14.5832C14.2259 14.5832 15.625 13.1841 15.625 11.4582C15.625 9.73228 14.2259 8.33317 12.5 8.33317C10.7741 8.33317 9.37501 9.73228 9.37501 11.4582C9.37501 13.1841 10.7741 14.5832 12.5 14.5832Z" fill="#6A7165" fill-opacity="0.75"/><path d="M12.8978 20.6377L13.1733 21.1707H13.1733L12.8978 20.6377ZM12.1022 20.6377L11.8267 21.1707H11.8267L12.1022 20.6377ZM19.1917 11.4582C19.1917 13.8622 17.9622 15.817 16.4903 17.2963C15.0191 18.775 13.3566 19.7251 12.6223 20.1047L13.1733 21.1707C13.9613 20.7634 15.7458 19.7459 17.341 18.1427C18.9354 16.5402 20.3917 14.2987 20.3917 11.4582H19.1917ZM12.5 4.7665C16.1957 4.7665 19.1917 7.76247 19.1917 11.4582H20.3917C20.3917 7.09972 16.8585 3.5665 12.5 3.5665V4.7665ZM5.80834 11.4582C5.80834 7.76247 8.8043 4.7665 12.5 4.7665V3.5665C8.14156 3.5665 4.60834 7.09972 4.60834 11.4582H5.80834ZM12.3777 20.1047C11.6434 19.7251 9.98096 18.775 8.50973 17.2963C7.03784 15.817 5.80834 13.8622 5.80834 11.4582H4.60834C4.60834 14.2987 6.0646 16.5402 7.65906 18.1427C9.25419 19.7459 11.0387 20.7634 11.8267 21.1707L12.3777 20.1047ZM12.6223 20.1047C12.542 20.1462 12.458 20.1462 12.3777 20.1047L11.8267 21.1707C12.2526 21.3908 12.7474 21.3908 13.1733 21.1707L12.6223 20.1047ZM15.025 11.4582C15.025 12.8527 13.8945 13.9832 12.5 13.9832V15.1832C14.5573 15.1832 16.225 13.5154 16.225 11.4582H15.025ZM12.5 8.93317C13.8945 8.93317 15.025 10.0637 15.025 11.4582H16.225C16.225 9.40091 14.5573 7.73317 12.5 7.73317V8.93317ZM9.97501 11.4582C9.97501 10.0637 11.1055 8.93317 12.5 8.93317V7.73317C10.4427 7.73317 8.77501 9.40091 8.77501 11.4582H9.97501ZM12.5 13.9832C11.1055 13.9832 9.97501 12.8527 9.97501 11.4582H8.77501C8.77501 13.5154 10.4427 15.1832 12.5 15.1832V13.9832Z" fill="#1A1818"/></svg>',
                                      homeBranch.tr),
                                ),

                                Obx(() {
                                  return

                                      ///AAMGService
                                      profileController.isMember!.value == true
                                          ? InkWell(
                                              onTap: () {
                                                debugPrint('Branch Page');
                                                Get.to(() => AAMGService());
                                              },
                                              child: HomePageCoponentWidget
                                                  .serviceMenuCard(
                                                      context,
                                                      '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 2.50001V2.50001C16.3807 2.50001 17.5 3.61929 17.5 5.00001L17.5 6.78572C17.5 6.98521 17.5 7.08495 17.4719 7.16514C17.4217 7.30876 17.3088 7.42169 17.1651 7.47194C17.085 7.5 16.9852 7.5 16.7857 7.5L12.5 7.5M15 2.50001V2.50001C13.6193 2.50001 12.5 3.61929 12.5 5.00001L12.5 7.5M15 2.50001L6.5 2.50001C4.61438 2.50001 3.67157 2.50001 3.08579 3.08579C2.5 3.67158 2.5 4.61439 2.5 6.50001L2.5 17.5L5 16.6667L7.5 17.5L10 16.6667L12.5 17.5L12.5 7.5" stroke="#FFC20E"/> <path d="M5.83398 5.8335L9.16732 5.8335" stroke="#FFC20E" stroke-linecap="round"/> <path d="M6.66602 9.1665H5.83268" stroke="#FFC20E" stroke-linecap="round"/> <path d="M5.83398 12.5L8.33398 12.5" stroke="#FFC20E" stroke-linecap="round"/></svg>',
                                                      "AAMService"),
                                            )
                                          : Container();
                                }),

                                // serviceMenuCard(
                                //     context,
                                //     appConfigService.countryConfigCollection.toString() ==
                                //             'aam'
                                //         ? 'assets/menu_icon/aam/mr.png'
                                //         : appConfigService.countryConfigCollection
                                //                     .toString() ==
                                //                 'rplc'
                                //             ? 'assets/menu_icon/rplc/mr.png'
                                //             : 'assets/menu_icon/rafco/mr.png',
                                //     homeIncome.tr),
                                // serviceMenuCard(
                                //     context,
                                //     appConfigService.countryConfigCollection.toString() ==
                                //             'aam'
                                //         ? 'assets/menu_icon/aam/bill.png'
                                //         : appConfigService.countryConfigCollection
                                //                     .toString() ==
                                //                 'rplc'
                                //             ? 'assets/menu_icon/rplc/bill.png'
                                //             : 'assets/menu_icon/rafco/bill.png',
                                //     homePayInstallments.tr),
                                // context, 'assets/menu_icon/aam/bill.png', 'ชำระบิล'),
                                // serviceMenuCard(
                                //     context,
                                //     appConfigService.countryConfigCollection.toString() ==
                                //             'aam'
                                //         ? 'assets/menu_icon/aam/scan.png'
                                //         : appConfigService.countryConfigCollection
                                //                     .toString() ==
                                //                 'rplc'
                                //             ? 'assets/menu_icon/rplc/scan.png'
                                //             : 'assets/menu_icon/rafco/scan.png',
                                //     homeScan.tr),
                              ],
                            ),
                          ),
                // Container(
                //   child: appConfigService.countryConfigCollection.toString() ==
                //           'aam'
                //       ? StaggeredGrid.count(
                //           crossAxisCount: 4,
                //           mainAxisSpacing: 4,
                //           crossAxisSpacing: 9.w,
                //           children: [
                //             serviceMenuCard(
                //                 context,
                //                 'assets/menu_icon/aam/branch.png',
                //                 homeBranch.tr),
                //             serviceMenuCard(
                //                 context,
                //                 'assets/menu_icon/aam/aam_pay.png',
                //                 '5,000\n${homeReady.tr}'),
                //             serviceMenuCard(
                //                 context,
                //                 'assets/menu_icon/aam/pay_at_home.png',
                //                 homeService.tr),
                //           ],
                //         )
                //       : StaggeredGrid.count(
                //           crossAxisCount: 4,
                //           mainAxisSpacing: 4,
                //           crossAxisSpacing: 9.w,
                //           children: [
                //             serviceMenuCard(
                //                 context,
                //                 appConfigService.countryConfigCollection
                //                             .toString() ==
                //                         'rplc'
                //                     ? 'assets/menu_icon/rplc/branch.png'
                //                     : 'assets/menu_icon/rafco/branch.png',
                //                 homeBranch.tr),
                //             serviceMenuCard(
                //                 context,
                //                 appConfigService.countryConfigCollection
                //                             .toString() ==
                //                         'rplc'
                //                     ? 'assets/menu_icon/rplc/pay_at_home.png'
                //                     : 'assets/menu_icon/rafco/pay_at_home.png',
                //                 homeService.tr),
                //           ],
                //         ),
                // ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Color _getBGColor() {
    final country = appConfigService.countryConfigCollection.toString();
    if (country == 'aam') return const Color(0x0C792AFF).withOpacity(0.05);
    if (country == 'rplc') return const Color(0x0CFFC20E).withOpacity(0.1);
    return const Color(0xFFFEF4F4);
  }
}
