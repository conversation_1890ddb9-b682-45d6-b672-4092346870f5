import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:AAMG/controller/login/login.controller.dart';

import '../../componance/themes/app_colors.dart';
import '../../componance/utils/AppSvgImage.dart';

class VerifyOTPScreen extends StatefulWidget {
  const VerifyOTPScreen({Key? key}) : super(key: key);

  @override
  State<VerifyOTPScreen> createState() => _VerifyOTPScreenState();
}

class _VerifyOTPScreenState extends State<VerifyOTPScreen> {
  final FocusNode focusNode = FocusNode();

  final LoginController loginController = Get.put(LoginController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Future.delayed(Duration.zero, () {
      loginController.startOTPTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    final customPinTheme = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 24.w,
          height: 2.5.h,
          decoration: BoxDecoration(
            color: const Color(0xFFFF1A1818).withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
        color: configTheme().textTheme.bodyMedium?.color,
        fontSize: 20,
        fontFamily: configTheme().primaryTextTheme.headlineLarge?.fontFamily,
        fontWeight: configTheme().primaryTextTheme.headlineLarge?.fontWeight,
        height: 0,
      ),
      decoration: BoxDecoration(),
    );
    final preFilledWidget = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 24.w,
          height: 2.5.h,
          decoration: BoxDecoration(
            color: const Color(0xFFFF1A1818).withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
    return Scaffold(
      body: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(top: 81.h),
            child: GetBuilder<LoginController>(builder: (loginController) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/register/icon/verify.png',
                        fit: BoxFit.fill,
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 28.h),
                    height: 55.h,
                    child: Center(
                      child: Obx(() {
                        return FittedBox(
                          fit: BoxFit.contain,
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: signUpOTP.tr,
                                  style: TextStyle(
                                    color:
                                        configTheme().textTheme.bodyMedium?.color,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .displayMedium
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .displayMedium
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .displayMedium
                                        ?.fontWeight,
                                  ),
                                ),
                                TextSpan(
                                  text:
                                      '${signUpSendPhone.tr} ${loginController.phone_code}${loginController.phone_login.value.text.substring(1)}',
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(0.5),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                  ),
                  SizedBox(
                    width: 254.w,
                    child: Column(
                      children: [
                        SizedBox(
                          // color: Colors.brown,
                          width: 242.w,
                          height: 30.h,
                          child: Obx(() {
                            return Pinput(
                              // androidSmsAutofillMethod:
                              //     AndroidSmsAutofillMethod.smsRetrieverApi,
                              pinAnimationType: PinAnimationType.scale,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              defaultPinTheme: defaultPinTheme,
                              // preFilledWidget: preFilledWidget,
                              length: 6,
                              controller: loginController.otpController.value,
                              focusNode: focusNode,
                              // listenForMultipleSmsOnAndroid: true,
                              showCursor: false,
                              closeKeyboardWhenCompleted: true,
                              animationCurve: Curves.easeInOut,
                              separatorBuilder: (index) =>
                                  const SizedBox(width: 22),
                              hapticFeedbackType: HapticFeedbackType.vibrate,
                              onCompleted: (pin) async {
                                print("YourCodeOTP = $pin");
                              },
                              onChanged: (value) {
                                if (value.length == 6) {
                                  loginController.checkOtpFormat(context, value);
                                } else {
                                  loginController.isAlert!.value = false;
                                  loginController.setVerify(false);
                                  loginController.update();
                                }
                              },
                            );
                          }),
                        ),
                        Obx(() {
                          return SizedBox(
                              width: 254.w,
                              child: Image.asset(
                                loginController.isAlert!.value == true &&
                                        loginController.otpController.value.text
                                                .length ==
                                            6 &&
                                        loginController.counter_OTP
                                                .toString() !=
                                            '0'
                                    ? 'assets/register/icon/OTP_error.png'
                                    : loginController.otpController.value.text
                                                .isNotEmpty &&
                                            loginController.counter_OTP
                                                    .toString() !=
                                                '0'
                                        ? 'assets/register/icon/OTP_success.png'
                                        : 'assets/register/icon/OTP_defalt.png',
                                fit: BoxFit.fill,
                                color: loginController.otpController.value.text
                                            .isNotEmpty &&
                                        appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rafco' &&
                                        loginController.isAlert!.value == false
                                    ? const Color(0xFF22409A)
                                    : loginController.otpController.value.text
                                                .isNotEmpty &&
                                            appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rplc' &&
                                            loginController.isAlert!.value ==
                                                false
                                        ? const Color(0xFF6A7165)
                                        : null,
                              ));
                        }),
                      ],
                    ),
                  ),
                  Container(
                    height: 74.h,
                    child: Stack(
                      children: [
                        Obx(() {
                          return loginController.isAlert!.value == true &&
                                  loginController
                                          .otpController.value.text.length ==
                                      6 &&
                                  loginController.counter_OTP.toString() != '0'
                              ? Container(
                                  height: 20.h,
                                  alignment: Alignment.center,
                                  margin:
                                      EdgeInsets.only(top: 30.h, bottom: 24.h),
                                  child: Center(
                                    child: Text(
                                      signUpCilkRefCodeAgain.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            configTheme().colorScheme.onError,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontWeight,
                                        height: 0.14.h,
                                      ),
                                    ),
                                  ),
                                )
                              : Container();
                        }),
                        Obx(() {
                          return loginController.isVerify!.value == true &&
                                  loginController.counter_OTP.toString() != '0'
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 14.w,
                                      height: 14.h,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(
                                          top: 30.h, bottom: 30.h),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          backgroundColor: configTheme()
                                              .colorScheme
                                              .secondary,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  configTheme()
                                                      .colorScheme
                                                      .primary
                                                      .withOpacity(1.0)),
                                          strokeWidth: 1.0,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : Container();
                        })
                      ],
                    ),
                  ),
                  SizedBox(
                      height: 40.h,
                      child: Obx(() {
                        if (loginController.counter_OTP.toString() != '0') {
                          return Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text:
                                      '${signUpRefCode.tr} ${loginController.refCode!.value} ',
                                  style: TextStyle(
                                    color: const Color(0xFF1A1818),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                    height: 0.18.h,
                                  ),
                                ),
                                TextSpan(
                                  text: signUpRefCodeAgain.tr,
                                  style: TextStyle(
                                    color: const Color(0x7F1A1818),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                    height: 0.18.h,
                                  ),
                                ),
                                TextSpan(
                                  text: loginController.counter_OTP.toString(),
                                  style: TextStyle(
                                    color: appConfigService
                                                .countryConfigCollection
                                                .toString() ==
                                            'aam'
                                        ? const Color(0xFFFF9300)
                                        : appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rafco'
                                            ? const Color(0xFFEA1B23)
                                            : const Color(0xFFFFC20E),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelLarge
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelLarge
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelLarge
                                        ?.fontWeight,
                                    height: 0.18.h,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          );
                        } else {
                          return Text(
                            signUpRefCodeError.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                            ),
                          );
                        }
                      })),
                  GestureDetector(
                    onTap: () {
                      //TODO : ส่ง OTP ไปใหม่
                      print("Resend OTP");
                      loginController.checkResendOTPConfig(context);
                      // loginController.resendOtp(context);
                      // Get.to(PinCodePage());
                    },
                    child: Container(
                      width: 105.w,
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: loginController.counter_OTP.value == 0
                            ? configTheme().colorScheme.primary
                            : const Color(0x0C1A1818),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Container(
                        alignment: Alignment.center,
                        child: Text(
                          // 'รับรหัสอีกครั้ง',
                          signUpResend.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: loginController.counter_OTP.value == 0
                                ? Colors.white
                                : const Color(0x331A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .labelMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelMedium
                                ?.fontWeight,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              );
            }),
          ),
          headerVerify(context)
        ],
      ),
    );
  }

  headerVerify(context) {
    return Container(
        margin: EdgeInsets.only(top: 60.h, left: 24.w, right: 24.w),
        height: 46.h,
        width: 327.w,
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child: SizedBox(
                  height: 24.h,
                  width: 24.w,
                  child: SvgPicture.string(AppSvgImage.back_btn)
              )
            ),
          ],
        ));
  }
}
