import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/chatinapp/chatinapp.controller.dart';
import '../../../controller/profile/profile.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/componance_widget.dart';
import '../../componance/widgets/header_widgets/header_general.dart';
import '../../componance/widgets/kyc/bookbank_widget.dart';
import 'bookbank_scan.dart';
import 'bookbank_verify.dart';

class KYCBookBankScreen extends StatelessWidget {
  KYCBookBankScreen({Key? key}) : super(key: key);

  final ProfileController profileCtl = Get.find<ProfileController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: GetBuilder<KYCController>(builder: (kycCtl) {
      print('kycCtl.isVerifyBankData!.value ${kycCtl.isVerifyBankData!.value}');
      if (kycCtl.isVerifyBankData!.value == true) {
        return BookBankVerify(); // หน้าโหลด
      }

      return Stack(children: [
        Container(
          width: 327.w,
          // height: 144.h,
          margin: EdgeInsets.symmetric(vertical: 116.h, horizontal: 24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //todo Alert Book Bank
              kycCtl.rejectVerifyBank.value == true
                  ? Column(
                children: [
                  SizedBox(height: 10.h),
                  buildAlertBookBank(context),
                  SizedBox(height: 10.h),
                ],
              )
                  : Container(),
              //todo end Alert Book Bank
              GestureDetector(
                onTap: () {
                  //todo drop down
                  print('drop down');
                  BookBankComponance.buildDropDownBookBank(
                      context); //todo drop down bank name
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          bank.tr,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        // width: 113.w,
                        height: 24.h,
                        color: Colors.transparent,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  kycCtl.selectedBankIcon!
                                      .value
                                      .toString()
                                      .obs
                                      .value
                                      .isNotEmpty
                                      ? Container(
                                    width: 24.w,
                                    height: 24.h,
                                    child: appConfigService
                                        .countryConfigCollection.toString() ==
                                        'aam'
                                        ? SvgPicture.string(kycCtl
                                        .selectedBankIcon!
                                        .value
                                        .toString()
                                        .obs
                                        .value)
                                        : Image.asset(kycCtl
                                        .selectedBankIcon!
                                        .value
                                        .toString()
                                        .obs
                                        .value),
                                  )
                                      : Container(
                                    width: 24.w,
                                    height: 24.h,
                                  ),
                                  SizedBox(width: 8.w),
                                  SizedBox(
                                    height: 22.h,
                                    child: Text(
                                      kycCtl.selectedBank!
                                          .value
                                          .toString()
                                          .obs
                                          .value
                                          .isNotEmpty
                                          ? kycCtl.selectedBank!
                                          .value
                                          .toString()
                                          .obs
                                          .value
                                          : choose_bank.tr,
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                        color: Color(0xFF1A1818),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium!
                                            .fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium!
                                            .fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium!
                                            .fontWeight,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 4.w),
                            Container(
                              width: 22.w,
                              height: 22.h,
                              child: SvgPicture.string(
                                AppSvgImage.dropdown_icon,
                                width: 22.w,
                                height: 22.h,
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0.30.h,
                color: Colors.black.withOpacity(0.2),
              ),
              GestureDetector(
                onTap: () {
                  _buildUpdateBankCustName(context);
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          accountName.tr,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            kycCtl.bank_custname_text.value.isNotEmpty
                                ? kycCtl.bank_custname_text.value
                                : kycCtl.bookbankData.value.bookbank_custName
                                .obs
                                .toString()
                                .isNotEmpty
                                ? kycCtl.bookbankData.value
                                .bookbank_custName.obs
                                .toString()
                                : '${profileCtl.profile.value.firstname
                                .toString()} ${profileCtl.profile.value.lastname
                                .toString()}',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0.30.h,
                color: Colors.black.withOpacity(0.2),
              ),

              GestureDetector(
                onTap: () {
                  _buildUpdateBankNumber(context);
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          accountNumber.tr,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            kycCtl.formatBankAccountNumber(kycCtl
                                .bank_number_text.value.isNotEmpty
                                ? kycCtl.bank_number_text.value
                                : kycCtl.bookbankData.value.bookbank_number.obs
                                .toString()),
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        //todo Alert Book Bank
        Obx(() {
          if (kycCtl.rejectVerifyBank.value == true) {
            return GestureDetector(
              onTap: () {
                final chatInAppController = Get.put(ChatInAppController());
                chatInAppController.fullProcessRegister(context);
              },
              child: Container(
                width: 327.w,
                height: 52.h,
                margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 650.h),
                decoration: ShapeDecoration(
                  color: AppColors.AAMPurpleSolid.withOpacity(0.08),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(child: SvgPicture.string(AppSvgImage.chat_icon)),
                    SizedBox(width: 10.w),
                    SizedBox(
                      child: Text(
                        menuGetLoanContact.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: AppColors.AAMPurple,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontFamily,
                          fontWeight: FontWeight.w500,
                          // height: 0.14.h,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return Container();
          }
        }),
        //todo end Alert Book Bank
        Container(
          margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 712.h),
          child: PrimaryButton(
            title: accountSave.tr,
            onPressed: () {
              print("dsdsddds");
              if (kycCtl.isChangeBankData.value) {
                kycCtl.saveBookBank();
              }

              // Get.off(BookBankVerify());
            },
            // height: 712.h,
            buttonWidth: 327.w,
            backgroundColor: appConfigService.countryConfigCollection
                .toString() == 'aam'
                ? AppColors.AAMPurpleSolid
                : appConfigService.countryConfigCollection.toString() == 'rplc'
                ? AppColors.primaryRPLC_Grey
                : AppColors.primaryRafco,
            backgroundInactiveColor:
            configTheme().buttonTheme.colorScheme!.tertiary,
            isActive: kycCtl.isChangeBankData.value,
            textColor: Colors.white,
          ),
        ),
        HeaderGeneral(
          title: bankAccount.tr,
          firstIcon: SizedBox(
              width: 24.w,
              height: 24.h,
              child: SvgPicture.string(AppSvgImage.back_btn)),
          secondIcon: SizedBox(
              width: 24.w,
              height: 24.h,
              child: SvgPicture.string(AppSvgImage.camera_icon)),
          firstOnPressed: () {
            Get.back();
          },
          secondOnPressed: () {
            print('open camera');
            Get.to(() => BookbankScanScreen());
          },
        ),
      ]);
    }));
  }

  Widget buildAlertBookBank(context) {
    return Container(
      width: 327.w,
      height: 72.h,
      decoration: ShapeDecoration(
        color: Color(0xFFFFECED),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
      ),
      child: Container(
        width: 299.w,
        margin: EdgeInsets.symmetric(horizontal: 14.w, vertical: 17.h),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 38.w,
              height: 38.h,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(),
              child: SvgPicture.string(
                  appConfigService.countryConfigCollection.toString() == 'aam'
                      ? AppSvgImage.alertBookBank_icon
                      : AppSvgImage.rplc_book_alert),
            ),
            SizedBox(width: 8.w),
            SizedBox(
              width: 253.w,
              height: 38.h,
              child: Text.rich(
                overflow: TextOverflow.ellipsis,
                TextSpan(
                  children: [
                    TextSpan(
                      text: bank_error.tr,
                      style: TextStyle(
                        color: Color(0xFFFF3B30),
                        fontSize:
                        configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                        // height: 0.8.h,
                      ),
                    ),
                    TextSpan(
                      text:
                      bank_error_desc.tr,
                      style: TextStyle(
                        color: Color(0xFFFF3B30),
                        fontSize:
                        configTheme().primaryTextTheme.labelSmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .labelSmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .labelSmall!
                            .fontWeight,
                        // height: 1.1.h,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _buildUpdateBankNumber(context) {
    final KYCController kycController = Get.find<KYCController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 513.h,
            color: Color(0xFFFFFFFF),
            child: Container(
              // width: 327.w,
              // height: 158.h,
              margin: EdgeInsets.only(top: 20.h, left: 24.w, right: 24.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: Get.width,
                    height: 34.h,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 24.h,
                          child: Center(
                            child: Text(
                              accountNumber.tr,
                              style: TextStyle(
                                color: Color(0xFF1A1818),
                                fontSize: 14.sp,
                                fontFamily:
                                TextStyleTheme.text_Regular.fontFamily,
                                fontWeight: FontWeight.w600,
                                height: 0.10,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.topRight,
                            child: SvgPicture.string(AppSvgImage.close_btn),
                          ),
                        ),
                      ],
                    ),
                  ),
                  ComponanceWidget.buildDivider(),
                  SizedBox(height: 12.h),
                  Container(
                    width: Get.width,
                    height: 48.h,
                    decoration: ShapeDecoration(
                      color: Color(0xFFF9F9F9),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: TextFormField(
                      textAlign: TextAlign.start,
                      cursorWidth: 2.w,
                      cursorColor: configTheme().colorScheme.primary,
                      // cursorHeight: 24.h,
                      controller: kycController.bank_number,
                      keyboardType: TextInputType.number,
                      style: TextStyle(
                        color: Color(0xFF1A1818),
                        fontSize: 14.sp,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w600,
                        height: 0.12,
                      ),
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 14.w),
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        // labelText: '',

                        labelStyle: TextStyle(
                          color: Color(0xFF1A1818),
                          fontSize: 12.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w700,
                          height: 0.12,
                        ),
                        hintText: '0-0000-00-000-00-0',
                        hintStyle: TextStyle(
                          color: Color(0x7F1A1818),
                          fontSize: 12.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w400,
                          height: 0.12,
                        ),
                      ),
                      onSaved: (value) {
                        kycController.rejectVerifyBank.value = false;
                      },
                      onTapOutside: (val) {
                        kycController.rejectVerifyBank.value = false;
                      },
                      onChanged: (value) {
                        kycController.bank_number.text = value;
                        kycController.update();
                        // print(value);
                        // print(kycController.bank_number);
                        // if (appConfigService.countryConfigCollection
                        //     .toString() ==
                        //     'aam') {
                        //   if (value.length == 13) {
                        //     editProfileController.setIsIdCardValid(true, value);
                        //   } else {
                        //     editProfileController.setIsIdCardValid(
                        //         false, value);
                        //   }
                        // } else if (value.length > 0) {
                        //   editProfileController.setIsIdCardValid(true, value);
                        // } else {
                        //   editProfileController.setIsIdCardValid(false, value);
                        // }
                      },
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                        // LengthLimitingTextInputFormatter(10),
                      ],
                    ),
                  ),
                  SizedBox(height: 12.h),

                  PrimaryButton(
                    title: accountSave.tr,
                    onPressed: () async {
                      print("dsdsddds");
                      if (kycController.bank_number.text.isNotEmpty) {
                        print('save');
                        await kycController.updateBankNumber(
                            kycController.bank_number.text);
                      }
                      Navigator.pop(context);
                    },
                    // height: 712.h,
                    buttonWidth: 327.w,
                    backgroundColor: appConfigService.countryConfigCollection
                        .toString() ==
                        'aam'
                        ? AppColors.AAMPurpleSolid
                        : appConfigService.countryConfigCollection
                        .toString() ==
                        'rplc'
                        ? AppColors.primaryRPLC_Grey
                        : AppColors.primaryRafco,
                    backgroundInactiveColor:
                    configTheme().buttonTheme.colorScheme!.tertiary,
                    isActive: kycController.bank_number.text.isNotEmpty,
                    textColor: Colors.white,
                  ),
                ],
              ),
            ),
          );
        });
  }

  _buildUpdateBankCustName(context) {
    final KYCController kycController = Get.find<KYCController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 513.h,
            color: Color(0xFFFFFFFF),
            child: Container(
              // width: 327.w,
              // height: 158.h,
              margin: EdgeInsets.only(top: 20.h, left: 24.w, right: 24.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: Get.width,
                    height: 34.h,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 24.h,
                          child: Center(
                            child: Text(
                              bankAccount.tr,
                              style: TextStyle(
                                color: Color(0xFF1A1818),
                                fontSize: 14.sp,
                                fontFamily:
                                TextStyleTheme.text_Regular.fontFamily,
                                fontWeight: FontWeight.w600,
                                height: 0.10,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.topRight,
                            child: SvgPicture.string(AppSvgImage.close_btn),
                          ),
                        ),
                      ],
                    ),
                  ),
                  ComponanceWidget.buildDivider(),
                  SizedBox(height: 12.h),
                  Container(
                    width: Get.width,
                    height: 48.h,
                    decoration: ShapeDecoration(
                      color: Color(0xFFF9F9F9),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: TextFormField(
                      textAlign: TextAlign.start,
                      cursorWidth: 2.w,
                      cursorColor: configTheme().colorScheme.primary,
                      // cursorHeight: 24.h,
                      controller: kycController.bank_custname,
                      keyboardType: TextInputType.text,
                      style: TextStyle(
                        color: Color(0xFF1A1818),
                        fontSize: 14.sp,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w600,
                        height: 0.12,
                      ),
                      textCapitalization: appConfigService
                          .countryConfigCollection.toString() == 'rplc'
                          ? TextCapitalization.characters
                          : TextCapitalization.none,
                      inputFormatters: [
                        // FilteringTextInputFormatter.deny(RegExp(r'\s{2,}')), // ห้ามเว้นวรรคซ้ำติดกัน
                        // FilteringTextInputFormatter.deny(RegExp(r'\s+$')), // ห้ามเว้นวรรคท้ายสุด
                        if (appConfigService.countryConfigCollection
                            .toString() == 'rplc') FilteringTextInputFormatter
                            .allow(RegExp(r'[A-Z\s]')),
                        // อนุญาตเฉพาะตัวอักษรภาษาอังกฤษและช่องว่าง
                      ],
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(left: 14.w),
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        // labelText: '',

                        labelStyle: TextStyle(
                          color: Color(0xFF1A1818),
                          fontSize: 12.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w700,
                          height: 0.12,
                        ),
                        hintText: 'XXXX XXXX',
                        hintStyle: TextStyle(
                          color: Color(0x7F1A1818),
                          fontSize: 12.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w400,
                          height: 0.12,
                        ),
                      ),
                      onTapOutside: (value) {
                        print('onTapOutside');
                        // kycController.bank_custname.text = value;
                        // kycController.update();

                        String trimmedValue = kycController.bank_custname.text
                            .replaceAll(RegExp(r'\s+$'),
                            ''); // ตัดช่องว่างท้ายออก
                        kycController.bank_custname.text = trimmedValue;
                        kycController.bank_custname.selection =
                            TextSelection.fromPosition(
                              TextPosition(offset: trimmedValue.length),
                            );
                        kycController.rejectVerifyBank.value = false;
                        // ตรวจสอบว่ามีเว้นวรรค "ตรงกลาง" เท่านั้น และไม่มีช่องว่างท้าย
                        kycController.isValidName.value =
                            RegExp(r'^[^\s]+ [^\s]+$').hasMatch(trimmedValue);
                      },
                      onSaved: (value) {
                        String trimmedValue = value!.replaceAll(RegExp(r'\s+$'),
                            ''); // ตัดช่องว่างท้ายออก
                        kycController.bank_custname.text = trimmedValue;
                        kycController.bank_custname.selection =
                            TextSelection.fromPosition(
                              TextPosition(offset: trimmedValue.length),
                            );
                        kycController.rejectVerifyBank.value = false;
                        // ตรวจสอบว่ามีเว้นวรรค "ตรงกลาง" เท่านั้น และไม่มีช่องว่างท้าย
                        kycController.isValidName.value =
                            RegExp(r'^[^\s]+ [^\s]+$').hasMatch(trimmedValue);
                        print('onSaved');
                      },
                    ),
                  ),
                  SizedBox(height: 12.h),
                  PrimaryButton(
                    title: accountSave.tr,
                    onPressed: () {
                      print("dsdsddds");
                      if (kycController.bank_custname.value.text
                          .isNotEmpty) {
                        print('save');
                        kycController.updateBankCustName(
                            kycController.bank_custname.value.text);
                      }else{
                        Get.snackbar(
                            alert_bank_name.tr, alert_bank_name_desc.tr);
                      }
                      Navigator.pop(context);
                    },
                    // height: 712.h,
                    buttonWidth: 327.w,
                    backgroundColor: appConfigService.countryConfigCollection
                        .toString() ==
                        'aam'
                        ? AppColors.AAMPurpleSolid
                        : appConfigService.countryConfigCollection
                        .toString() ==
                        'rplc'
                        ? AppColors.primaryRPLC_Grey
                        : AppColors.primaryRafco,
                    backgroundInactiveColor:
                    configTheme().buttonTheme.colorScheme!.tertiary,
                    isActive: kycController.bank_custname.text.isNotEmpty,
                    textColor: Colors.white,
                  ),
                ],
              ),
            ),
          );
        });
  }
}
