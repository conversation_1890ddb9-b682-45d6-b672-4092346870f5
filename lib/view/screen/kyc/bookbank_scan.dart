import 'dart:io';

import 'package:AAMG/controller/service/AppService.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../controller/register/scan.controller.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/header_widgets/header_general.dart';

class BookbankScanScreen extends StatefulWidget {
  const BookbankScanScreen({Key? key}) : super(key: key);

  @override
  State<BookbankScanScreen> createState() => _BookbankScanScreenState();
}

class _BookbankScanScreenState extends State<BookbankScanScreen> {
  final ScanController cameraCtl = Get.put(ScanController());
  final ImagePicker _picker = ImagePicker();

  CameraController? _cameraController;
  Future<void>? _initializeControllerFuture;

  @override
  void initState() {
    super.initState();
    // initializeCamera();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    cameraCtl.cameraController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<ScanController>(
          init: ScanController(),
          builder: (scanCtl) {
            return Stack(
              children: [
                scanCtl.isCameraReady!.value && scanCtl.imgBlob.value.isEmpty
                    ? Container(
                        width: 375.w,
                        height: 706.h,
                        margin: EdgeInsets.only(top: 106.h),
                        child: Stack(
                          children: [
                            Container(
                                height: 706.h,
                                width: 375.w,
                                child:
                                    CameraPreview(cameraCtl.cameraController!)),
                            Container(
                              height: 706.h,
                              width: 375.w,
                              child: Stack(
                                children: [
                                  CustomPaint(
                                    size: MediaQuery.of(context).size,
                                    painter: OverlayPainter(),
                                  ),
                                  Container(
                                  width: 375.w,
                                  height: 245.0.h,
                                    margin: EdgeInsets.only(top: 79.0.h, left: 22.0.w, right: 22.0.w),
                                    child: SvgPicture.string(
                                      AppSvgImage.frameCamera_icon,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 465.h),
                              alignment: Alignment.center,
                              height: 46.h,
                              child: Text(
                                'วางหน้าสมุดบัญชีธนาคาร ให้อยู่ในพื้นที่กรอบที่กำหนด\nและมีแสงสว่างเพียงพอ เพื่อความชัดเจน',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodySmall!
                                      .fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall!
                                      .fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () async {
                                try {
                                  final image = await cameraCtl
                                      .cameraController!
                                      .takePicture();
                                  var img = cameraCtl.cameraController!
                                      .pausePreview();
                                  cameraCtl.imgBlob.value = image.path;
                                  cameraCtl.imgXFile = image;
                                  cameraCtl.update();
                                } catch (e) {
                                  print(e);
                                }
                              },
                              child: Container(
                                width: 327.w,
                                height: 52.h,
                                margin: EdgeInsets.only(
                                    top: 544.h, left: 24.w, right: 24.w),
                                decoration: ShapeDecoration(
                                  color: Colors.white
                                      .withOpacity(0.10000000149011612),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 24.w,
                                      height: 24.h,
                                      child: SvgPicture.string(
                                          AppSvgImage.camera_white_icon),
                                    ),
                                    SizedBox(width: 10.w),
                                    Container(
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            'ถ่ายรูป',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontWeight,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            GestureDetector(
                              onTap: () async {
                                try {
                                  final image = await _picker.pickImage(
                                      source: ImageSource.gallery);
                                  var img = cameraCtl.cameraController!
                                      .pausePreview();
                                  cameraCtl.imgBlob.value = image!.path;
                                  cameraCtl.imgXFile = image;
                                  cameraCtl.update();
                                } catch (e) {
                                  print(e);
                                }
                              },
                              child: Container(
                                width: 327.w,
                                height: 52.h,
                                margin: EdgeInsets.only(
                                    top: 606.h, left: 24.w, right: 24.w),
                                decoration: ShapeDecoration(
                                  color: Colors.white
                                      .withOpacity(0.10000000149011612),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 24.w,
                                      height: 24.h,
                                      child: SvgPicture.string(
                                          AppSvgImage.gallary_icon),
                                    ),
                                    SizedBox(width: 10.w),
                                    Container(
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            'อัพโหลดรูปจากเครื่อง',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontWeight,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Center(child: CircularProgressIndicator()),

                // TODO :: PREVIEW IMAGE
                scanCtl.imgBlob.value != ""
                    ? Container(
                        width: 375.w,
                        height: 706.h,
                        margin: EdgeInsets.only(top: 106.h),
                        child: Stack(
                          children: [
                            Container(
                              width: Get.width,
                              height: Get.height,
                              child: Image.file(File(scanCtl.imgBlob.value),
                                  fit: BoxFit.cover),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                  top: 544.h, left: 24.w, right: 24.w),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    onTap: () async {
                                      try {
                                        cameraCtl.cameraController!
                                            .resumePreview();
                                        cameraCtl.imgBlob.value = "";
                                        cameraCtl.update();
                                      } catch (e) {
                                        print(e);
                                      }
                                    },
                                    child: Container(
                                      width: 158.w,
                                      height: 52.h,
                                      decoration: ShapeDecoration(
                                        color: Colors.white.withOpacity(0.3),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            width: 24.w,
                                            height: 24.h,
                                            child: SvgPicture.string(
                                                AppSvgImage.reload_icon),
                                          ),
                                          SizedBox(width: 10.w),
                                          Container(
                                            decoration: ShapeDecoration(
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(50),
                                              ),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  'ถ่ายใหม่',
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    color: configTheme()
                                                        .textTheme
                                                        .bodyMedium!
                                                        .color,
                                                    fontSize: configTheme()
                                                        .primaryTextTheme
                                                        .titleMedium!
                                                        .fontSize,
                                                    fontFamily: configTheme()
                                                        .primaryTextTheme
                                                        .titleMedium!
                                                        .fontFamily,
                                                    fontWeight: configTheme()
                                                        .primaryTextTheme
                                                        .titleMedium!
                                                        .fontWeight,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () async {
                                      try {
                                        //todo :: upload image  และ ส่งไปที่ api OCR
                                        cameraCtl.uploadImage(context);
                                      } catch (e) {
                                        print(e);
                                      }
                                    },
                                    child: Container(
                                      width: 158.w,
                                      height: 52.h,
                                      decoration: ShapeDecoration(
                                        color: Colors.white.withOpacity(0.8),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            width: 24.w,
                                            height: 24.h,
                                            child: SvgPicture.string(
                                                AppSvgImage.imgChecked_icon),
                                          ),
                                          SizedBox(width: 10.w),
                                          Container(
                                            decoration: ShapeDecoration(
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(50),
                                              ),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  'ตกลง',
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    color: AppColors
                                                        .AAMPurpleSolid,
                                                    fontSize: configTheme()
                                                        .primaryTextTheme
                                                        .titleMedium!
                                                        .fontSize,
                                                    fontFamily: configTheme()
                                                        .primaryTextTheme
                                                        .titleMedium!
                                                        .fontFamily,
                                                    fontWeight: configTheme()
                                                        .primaryTextTheme
                                                        .titleMedium!
                                                        .fontWeight,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            GestureDetector(
                              onTap: () async {
                                try {
                                  final image = await _picker.pickImage(
                                      source: ImageSource.gallery);
                                  var img = cameraCtl.cameraController!
                                      .pausePreview();
                                  cameraCtl.imgBlob.value = image!.path;
                                  cameraCtl.imgXFile = image;
                                  cameraCtl.update();
                                } catch (e) {
                                  print(e);
                                }
                              },
                              child: Container(
                                width: 327.w,
                                height: 52.h,
                                margin: EdgeInsets.only(
                                    top: 606.h, left: 24.w, right: 24.w),
                                decoration: ShapeDecoration(
                                  color: Colors.white.withOpacity(0.3),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 24.w,
                                      height: 24.h,
                                      child: SvgPicture.string(
                                        AppSvgImage.gallary_icon,
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium!
                                            .color,
                                      ),
                                    ),
                                    SizedBox(width: 10.w),
                                    Container(
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            'อัพโหลดรูปจากเครื่อง',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium!
                                                  .color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .titleMedium!
                                                  .fontWeight,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Container(),
                HeaderGeneral(
                  title: 'ถ่ายรูปหน้าสมุดบัญชีธนาคาร',
                  firstIcon: SizedBox(
                      width: 24.w,
                      height: 24.h,
                      child: SvgPicture.string(AppSvgImage.close_icon)),
                  secondIcon: SizedBox(
                      width: 24.w,
                      height: 24.h,
                      child: SvgPicture.string(AppSvgImage.flashlight_icon)),
                  firstOnPressed: () {
                    Get.back();
                  },
                  secondOnPressed: () {
                    print('open flash');
                    cameraCtl.toggleFlash();
                  },
                ),
              ],
            );
          }),
    );
  }
}

class OverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final holeWidth = 327.0.w;
    final holeHeight = 239.0.w;
    final cornerSize = 35.0.r;
    final marginTop = 80.0.h; // Margin from the top
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final holeRect = Rect.fromLTWH(
        (size.width - holeWidth) / 2, marginTop, holeWidth, holeHeight);

    // Create a path for the entire screen
    final path = Path()..addRect(rect);

    // Create a path for the hole (transparent area)
    final holePath = Path()
      ..addRRect(RRect.fromRectAndCorners(
        holeRect,
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      ))
      ..close();

    // Combine the paths to cut out the hole from the filled area
    path.addPath(holePath, Offset.zero);
    path.fillType = PathFillType.evenOdd;

    // Draw the overlay with the hole
    canvas.drawPath(path, paint);

    // // Draw corner rectangles
    // final cornerPaint = Paint()
    //   ..color = Colors.white
    //   ..style = PaintingStyle.stroke
    //   ..strokeWidth = 2.0;
    //
    // // Top-left corner
    // canvas.drawLine(
    //   Offset(holeRect.left, holeRect.top),
    //   Offset(holeRect.left + cornerSize, holeRect.top),
    //   cornerPaint,
    // );
    // canvas.drawLine(
    //   Offset(holeRect.left, holeRect.top),
    //   Offset(holeRect.left, holeRect.top + cornerSize),
    //   cornerPaint,
    // );
    //
    // // Top-right corner
    // canvas.drawLine(
    //   Offset(holeRect.right, holeRect.top),
    //   Offset(holeRect.right - cornerSize, holeRect.top),
    //   cornerPaint,
    // );
    // canvas.drawLine(
    //   Offset(holeRect.right, holeRect.top),
    //   Offset(holeRect.right, holeRect.top + cornerSize),
    //   cornerPaint,
    // );
    //
    // // Bottom-left corner
    // canvas.drawLine(
    //   Offset(holeRect.left, holeRect.bottom),
    //   Offset(holeRect.left + cornerSize, holeRect.bottom),
    //   cornerPaint,
    // );
    // canvas.drawLine(
    //   Offset(holeRect.left, holeRect.bottom),
    //   Offset(holeRect.left, holeRect.bottom - cornerSize),
    //   cornerPaint,
    // );
    //
    // // Bottom-right corner
    // canvas.drawLine(
    //   Offset(holeRect.right, holeRect.bottom),
    //   Offset(holeRect.right - cornerSize, holeRect.bottom),
    //   cornerPaint,
    // );
    // canvas.drawLine(
    //   Offset(holeRect.right, holeRect.bottom),
    //   Offset(holeRect.right, holeRect.bottom - cornerSize),
    //   cornerPaint,
    // );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class BorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double sh = size.height; // for convenient shortage
    double sw = size.width; // for convenient shortage
    double cornerSide = sh * 0.1; // desirable value for corners side

    Path path = Path()
      ..moveTo(cornerSide, 0)
      ..quadraticBezierTo(0, 0, 0, cornerSide)
      ..moveTo(0, sh - cornerSide)
      ..quadraticBezierTo(0, sh, cornerSide, sh)
      ..moveTo(sw - cornerSide, sh)
      ..quadraticBezierTo(sw, sh, sw, sh - cornerSide)
      ..moveTo(sw, cornerSide)
      ..quadraticBezierTo(sw, 0, sw - cornerSide, 0);

    Paint paint = Paint()
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final gradient = const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [Color(0xFF85A0FF), Color(0xFF7D83FF), Color(0xFF667EFF)],
      stops: [0.0, 0.6042, 1.0],
    );

    paint.shader = gradient
        .createShader(Rect.fromPoints(const Offset(0, 0), Offset(sw, sh)));

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(BorderPainter oldDelegate) => false;

  @override
  bool shouldRebuildSemantics(BorderPainter oldDelegate) => false;
}

class BorderClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double sh = size.height; // for convenient shortage
    double sw = size.width; // for convenient shortage
    double cornerSide = sh * 0.1;

    Path path = Path()
      ..moveTo(cornerSide, 0)
      ..quadraticBezierTo(0, 0, 0, cornerSide)
      ..moveTo(0, sh - cornerSide)
      ..quadraticBezierTo(0, sh, cornerSide, sh)
      ..moveTo(sw - cornerSide, sh)
      ..quadraticBezierTo(sw, sh, sw, sh - cornerSide)
      ..moveTo(sw, cornerSide)
      ..quadraticBezierTo(sw, 0, sw - cornerSide, 0);
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
