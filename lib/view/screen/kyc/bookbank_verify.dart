import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../componance/themes/app_colors.dart';
import 'bookbank.dart';

class BookBankVerify extends StatelessWidget {
  const BookBankVerify({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Future.delayed(const Duration(seconds: 5), () {
    //   Get.to(() =>  KYCBookBankScreen());
    // });
    return PopScope(
      canPop: false, // ป้องกันการ pop อัตโนมัติ
      onPopInvoked: (didPop) async {
        debugPrint('onPopInvoked');
      },
      child: Scaffold(
        body: Column(
          children: [
            Container(
              width: 291.w,
              height: 208.h,
              margin: EdgeInsets.only(top: 198.h, left: 42.w, right: 42.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 100.w,
                    height: 100.h,
                    child: SvgPicture.string(
                      appConfigService.countryConfigCollection.toString() == 'aam'
                        ? AppSvgImage.pendingVerify_icon
                    : appConfigService.countryConfigCollection.toString() == 'rplc'
                        ? AppSvgImage.rplc_pending_book
                        : AppSvgImage.pendingVerify_icon,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  Container(
                    height: 84.h,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 28.h,
                          child: Text(
                            bank_pending.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: const Color(0xFF1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontWeight,
                            ),
                          ),
                        ),
                        SizedBox(height: 12.h),
                        SizedBox(
                          height: 44.h,
                          child: Text(
                            bank_pending_text.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: const Color(0xFF1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 136.h,
            ),
            CircleLoader(
              width: 25.w, // กำหนดความกว้าง 100.0
              firstColor: AppColors.AAMPurpleSolid.withOpacity(0.5),
              secondColor: AppColors.loadingColor.withOpacity(0.5),
              strokeWidth: 1.w,
            ),
          ],
        ),
      ),
    );
  }
}
