import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/service/AppService.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/chatinapp/chatinapp.controller.dart';
import '../../componance/widgets/app_pop_up/request_loan/request_success.dart';
import '../../componance/widgets/header_widgets/header_widget.dart';
import 'loan_details_screen.dart';
import 'loan_main_screen.dart';

class LoanStatus extends StatefulWidget {
   LoanStatus({Key? key}) : super(key: key);

  @override
  State<LoanStatus> createState() => _LoanStatusState();
}

class _LoanStatusState extends State<LoanStatus> {
  final ContractListController contractListCtl = Get.put(ContractListController());
  final chatInAppController = Get.put(ChatInAppController());
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    //TODO : Only Show when Request Loan Success First Time
    Future.delayed(Duration.zero, () {
        contractListCtl.isNewRequest.value?
      LoanPopUp.AlertRequestSuccess(context):null;

        Future.delayed(Duration(seconds: 1), () {
          if(contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0){
            print('เข้า อัปเดตสถานะ');
            contractListCtl.acceptRejectedLoanStatus(contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value].grant_id.toString(), contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value].grant_status.toString(), contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value].loan_status.toString());
          }
        });
    });
  }

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return WillPopScope(
      onWillPop: () async {
        // Get.to(() => const LoanMainScreen());
        return false;
      },
      child: Scaffold(
        body: Container(
            height: Get.height,
            width: Get.width,
            color: Colors.white,
            child: Stack(
              children: [
                Column(
                    children: [
                      HeaderWidget(title: menuGetLoanStatus.tr, onPressed: () {
                        Get.to(() => const LoanMainScreen());
                      }),
                      Container(
                        width: 326.w,
                        height: 273.h,
                        padding: EdgeInsets.only(
                          top: 20.h,
                          left: 20.w,
                          right: 10.w,
                          bottom: 10.h,
                        ),
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(width: 1, color: Color(0x141A1818)),
                            borderRadius: BorderRadius.circular(14),
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Container(
                                    width: 20.w,
                                    child: SvgPicture.string(
                                        AppSvgImage.aam_loan_statusstep1,
                                        color: configTheme().colorScheme.onSecondary)),
                                SizedBox(
                                  width: 14.w,
                                ),
                                Container(
                                  child: Text(
                                    menuGetLoanStatusSend.tr,
                                    style: TextStyle(
                                      color: configTheme().colorScheme.onSecondary,
                                      fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                      fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                      fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 1
                             ?_buildLoanDescription1(context):loanStatusTimeLineWidget(context, configTheme().colorScheme.onSecondary.withOpacity(0.2)),
                            Row(
                              children: [
                                Container(
                                    width: 20.w,
                                    child: SvgPicture.string(
                                        AppSvgImage.aam_loan_statusstep2,
                                        color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] >= 2  || contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0  ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2))),
                                SizedBox(
                                  width: 14.w,
                                ),
                                Container(
                                  child: Text(
                                    menuGetLoanStatusCheck.tr,
                                    style: TextStyle(
                                      color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] >= 2  || contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0  ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2),
                                      fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                      fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                      fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 2
                             ?_buildLoanDescription2(context)
                                :loanStatusTimeLineWidget(context, configTheme().colorScheme.onSecondary.withOpacity(0.2)),
                            Row(
                              children: [
                                Container(
                                    width: 20.w,
                                    child: SvgPicture.string(
                                        AppSvgImage.aam_loan_statusstep3,
                                        color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] >= 3 || contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0 ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2))),
                                SizedBox(
                                  width: 14.w,
                                ),
                                Container(
                                  child: Text(
                                    menuGetLoanStatusConsider.tr,
                                    style: TextStyle(
                                      color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] >= 3  || contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0  ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2),
                                      fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                      fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                      fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 3
                             ?_buildLoanDescription3(context):loanStatusTimeLineWidget(context, configTheme().colorScheme.onSecondary.withOpacity(0.2)),
                            Row(
                              children: [
                                Container(
                                    width: 20.w,
                                    child: SvgPicture.string(
                                        contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0
                                        ? AppSvgImage.aam_loan_statusfailed //rejected
                                         : AppSvgImage
                                            .aam_loan_statusstep4, //success
                                        color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] >= 4 || contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0 ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2))),
                                SizedBox(
                                  width: 14.w,
                                ),
                                Container(
                                  child: Text(
                                    contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 4
                                        ? menuGetLoanStatusPassConsider.tr //success,
                                    : contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0 ? menuGetLoanStatusReject.tr : menuGetLoanStatusPassConsider.tr, //rejected
                                    style: TextStyle(
                                      color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] >= 4 || contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0  ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2),
                                      fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                      fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                      fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 4
                             ?_buildLoanDescription4(context)
                                : contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 0
                                ? _buildLoanDescriptionRejected(context)
                                :loanStatusTimeLineWidget(context, configTheme().colorScheme.onSecondary.withOpacity(0.2)),
                            // _buildLoanDescriptionRejected(context)  ,
                            // loanStatusTimeLineWidget(context, configTheme().colorScheme.onSecondary),
                            Row(
                              children: [
                                Container(
                                    width: 20.w,
                                    child: SvgPicture.string(
                                        AppSvgImage.aam_loan_statussuccess,
                                        color:  contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 5 ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2))),
                                SizedBox(
                                  width: 14.w,
                                ),
                                Text(menuGetLoanStatusApprove.tr,
                                    style: TextStyle(
                                      color: contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] ==5 ?configTheme().colorScheme.onSecondary :configTheme().textTheme.bodyMedium?.color?.withOpacity(0.2),
                                      fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                      fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                      fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                    )),
                              ],
                            ),
                            contractListCtl.loanStatusStepList![contractListCtl.selectedStatusContractIndex!.value] == 5
                             ?_buildLoanDescriptionSuccess(context)
                                :Container(),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      Container(
                          width: 327.w,
                          height: 143.h,
                          padding: const EdgeInsets.only(
                            top: 20,
                            left: 20,
                            right: 20,
                            bottom: 14,
                          ),
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(width: 1.w, color: Color(0x141A1818)),
                              borderRadius: BorderRadius.circular(14),
                            ),
                          ),
                          child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: Get.width,
                                  height: 26.h,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        menuGetLoanAmountAdd.tr,
                                        style: TextStyle(
                                          color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                          fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                                          fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                          fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                        ),
                                      ),
                                      // SizedBox(width: 156),
                                      Text(
                                        // "2000",
                                        AppService.formatCurrencyWithoutDecimal(contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value].loan_amount.toString()),
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: configTheme().textTheme.bodyMedium?.color,
                                          fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                          fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                          fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Container(
                                    width: Get.width,
                                    height: 26.h,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 26.h,
                                          child: Text(
                                            homeLoanGuarantee.tr,
                                            style: TextStyle(
                                              color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                              fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                                              fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                              fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                            ),
                                          ),


                                          //                 // SizedBox(
                                          //                 //   height: 26.h,
                                          //                 //   child: Opacity(
                                          //                 //     opacity: 0,
                                          //                 //     child: Obx(() {
                                          //                 //       return Text(
                                          //                 //         contractListCtl.contractStatus[0]
                                          //                 //                     .guarantee_type
                                          //                 //                     .toString() ==
                                          //                 //                 '1'
                                          //                 //             ? 'รถยนต์'
                                          //                 //             : contractListCtl.contractStatus[0]
                                          //                 //                         .guarantee_type
                                          //                 //                         .toString() ==
                                          //                 //                     '2'
                                          //                 //                 ? 'มอเตอร์ไซค์'
                                          //                 //                 : contractListCtl
                                          //                 //                             .contractStatus[0]
                                          //                 //                             .guarantee_type
                                          //                 //                             .toString() ==
                                          //                 //                         '3'
                                          //                 //                     ? 'ที่ดิน'
                                          //                 //                     : '',
                                          //                 //         textAlign: TextAlign.right,
                                          //                 //         style: TextStyle(
                                          //                 //           color: Color(0xFF1A1818),
                                          //                 //           fontSize: 14,
                                          //                 //           fontFamily: TextStyleTheme
                                          //                 //               .text_Regular.fontFamily,
                                          //                 //           fontWeight: FontWeight.w600,
                                          //                 //         ),
                                          //                 //       );
                                          //                 //     }),
                                          //                 //   ),
                                          //                 // ),
                                          //                 // Container(
                                          //                 //   width: 26.w,
                                          //                 //   height: 26.h,
                                          //                 //   child: Image.asset(
                                          //                 //     contractListCtl
                                          //                 //                 .contractStatus[0].guarantee_type
                                          //                 //                 .toString() ==
                                          //                 //             '1'
                                          //                 //         ? contractListCtl.guaranteeImgList[0]
                                          //                 //         : contractListCtl.contractStatus[0]
                                          //                 //                     .guarantee_type
                                          //                 //                     .toString() ==
                                          //                 //                 '2'
                                          //                 //             ? contractListCtl.guaranteeImgList[1]
                                          //                 //             : contractListCtl.contractStatus[0]
                                          //                 //                         .guarantee_type
                                          //                 //                         .toString() ==
                                          //                 //                     '3'
                                          //                 //                 ? contractListCtl
                                          //                 //                     .guaranteeImgList[2]
                                          //                 //                 : contractListCtl
                                          //                 //                     .guaranteeImgList[3],
                                          //                 //   ),
                                          //                 // ),
                                          //               ],
                                          //             ),
                                          //           ),
                                          //           //
                                          //         ],
                                          //       ),
                                          //     ),

                                        ),
                                        SizedBox(
                                          height: 26.h,
                                          width: 26.w,
                                          child: contractListCtl
                                              .contractStatus[contractListCtl.selectedStatusContractIndex!.value].guarantee_type
                                              .toString()
                                              .isNotEmpty
                                              ? SvgPicture.string(
                                            contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value]
                                                .guarantee_type
                                                .toString() ==
                                                "1"
                                                ? contractListCtl.guaranteeImgList![0]
                                                : contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value]
                                                .guarantee_type
                                                .toString() ==
                                                "2"
                                                ? contractListCtl.guaranteeImgList![1]
                                                : contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value]
                                                .guarantee_type
                                                .toString() ==
                                                "3"
                                                ? contractListCtl
                                                .guaranteeImgList![2]
                                                : contractListCtl
                                                .guaranteeImgList![3],
                                            fit: BoxFit.fill,
                                          )
                                              : Container(),
                                          // child: Text(
                                          //   // contractListCtl.contractStatus[0]
                                          //   //     .guarantee_type
                                          //   //     .toString() ==
                                          //   //     '1'
                                          //   //     ? contractListCtl.guaranteeList[0].toString()
                                          //   //     : contractListCtl.contractStatus[0]
                                          //   //     .guarantee_type
                                          //   //     .toString() ==
                                          //   //     '2'
                                          //   //     ? contractListCtl.guaranteeList[1].toString()
                                          //   //     : contractListCtl.contractStatus[1]
                                          //   //     .guarantee_type
                                          //   //     .toString() ==
                                          //   //     '3'
                                          //   //     ? contractListCtl.guaranteeList[2].toString()
                                          //   //     : '',
                                          //   'xxxxx',
                                          //   textAlign: TextAlign.right,
                                          //   style: TextStyle(
                                          //     color:configTheme().textTheme.bodyMedium?.color,
                                          //     fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                          //     fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                          //     fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                          //   ),
                                          // ),
                                        ),
                                      ],
                                    )),
                                Container(
                                  width: Get.width,
                                  height: 26.h,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 26.h,
                                        child: Text(
                                          menuGetLoanTime.tr,
                                          style: TextStyle(
                                            color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                                            fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                                            fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                                            fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 26.h,
                                        child: Text(
                                          "${contractListCtl.contractStatus[contractListCtl.selectedStatusContractIndex!.value].loan_periods} ${menuGetLoanMoth.tr}",
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: configTheme().textTheme.bodyMedium?.color,
                                            fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                                            fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                                            fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ])),
                      Spacer(),
                      PrimaryButton(
                        title: menuGetLoanContact.tr,
                        onPressed: () {

                            chatInAppController.fullProcessRegister(context);

                        },
                        backgroundColor: AppColors.inActiveButtonColor,
                        textColor: Colors.black,
                        height: 0.h,
                        buttonWidth: 327.0.w,
                        isActive: true,
                      ),
                      SizedBox(
                        height: 48.h,
                      ),
                    ]),
              ],
            )),
      ),
    );
  }

  Widget loanStatusTimeLineWidget(context, Color color) {
    return Row(
      children: [
        Container(
          margin: EdgeInsets.only(
            left: 7.w,
          ),
          alignment: Alignment.centerLeft,
          width: 2.w,
          height: 16.h,
          decoration: ShapeDecoration(
            color: color,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(50),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoanDescription1(context) {
    return Container(
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: 7.w,
            ),
            width: 2.w,
            height: 32.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
          ),
          SizedBox(
            width: 25.w,
          ),
          Expanded(
            child: Text(
              // 'ได้รับข้อมูลการสนใจขอสินเชื่อของคุณ \nเรียบร้อย รอการติดต่อกลับ ภายใน 30 นาที',
                      LoanRequestDesc.tr,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
              //maxLines: 2,
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildLoanDescription2(context) {
    return Container(
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: 7.w,
            ),
            width: 2.w,
            height: 51.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
          ),
          SizedBox(
            width: 25.w,
          ),
          SizedBox(
            // height: 38.h,
            child: Text(
              // 'นัดหมายเรียบร้อย อยู่ระหว่างการตรวจ \nสอบเอกสาร และหลักประกัน ใช้เวลาภายใน \nไม่เกิน 45 นาที',
              LoanCheckDocDesc.tr,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildLoanDescription3(context) {
    return Container(
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: 7.w,
            ),
            width: 2.w,
            height: 38.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
          ),
          SizedBox(
            width: 25.w,
          ),
          SizedBox(
              child: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  // text: 'การพิจารณาสินเชื่อจะแล้วเสร็จ  ภายในไม่เกิน 3 วัน ',
                  text: LoanConsiderationDesc.tr,
                  style: TextStyle(
                    color: configTheme().textTheme.bodyMedium?.color,
                    fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                    fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                    fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
                  ),
                ),
                // TextSpan(
                //   text: 'โปรดอดใจรอนะคะ!',
                //   style: TextStyle(
                //     color: configTheme().textTheme.bodyMedium?.color,
                //     fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                //     fontFamily:configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                //     fontWeight:configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                //   ),
                // ),
              ],
            ),
          ))
        ],
      ),
    );
  }

  Widget _buildLoanDescription4(context) {
    return Container(
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: 7.w,
            ),
            width: 2.w,
            height: 51.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
          ),
          SizedBox(
            width: 25.w,
          ),
          SizedBox(
            // height: 38.h,
            child: Text(
              // 'คำขอสินเชื่อของคุณ ผ่านการพิจารณา \nเรียบร้อยแล้ว ยินดีด้วยค่ะ รอยืนยันเอกสาร \nเซนต์สัญญา',
              LoanApprovedDesc.tr,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildLoanDescriptionRejected(context) {
    return Container(
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(
              left: 7.w,
            ),
            width: 2.w,
            height: 51.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.onSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
            ),
          ),
          SizedBox(
            width: 25.w,
          ),
          SizedBox(
            child: Text(
              // 'วงเงินที่สมัครสินเชื่อมา ยังต้องการ \nหลักประกันเพิ่มเติม เรายินดีช่วยเหลือ \nด้วยวงเงินและประเภท สินเชื่อที่เหมาะสม',
              LoanRejectedDesc.tr,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildLoanDescriptionSuccess(context) {
    return Container(
      child: Row(
        children: [
          SizedBox(
            width: 34.w,
          ),
          SizedBox(
            // height: 38.h,
            child: Text(
              menuGetLoanStatusApproveDes.tr,
              style: TextStyle(
                color: configTheme().textTheme.bodyMedium?.color,
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
            ),
          )
        ],
      ),
    );
  }
}
