import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/widgets/header_widgets/header_widget.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/config/appConfig.controller.dart';
import '../../../controller/transalation/transalation.controller.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import 'myloan_bill_screen.dart';

class BillHistoryScreen extends StatelessWidget {
  const BillHistoryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    // Get.put(AppConfigService(context: context));
    return GetBuilder<MyloanController>(
      init: MyloanController(),
      builder: (myloanCtl) {
        return Scaffold(
            body: Container(
                child: Stack(
                  children: [
                    Container(
                        margin: EdgeInsets.only(top: 126.h),
                        width: Get.width,
                        height: Get.height,
                        child: ListView.builder(
                            padding: EdgeInsets.zero,
                            itemCount: myloanCtl.lastPayList.length,
                            itemBuilder: (context, index) {
                              return _buildBillHistory(
                                  context,
                                  index,
                                  myloanCtl.setPayedDateLocale(myloanCtl.lastPayList[index].pay_date.toString(), Get.find<TransalationController>().location.value == 'English'
                                      ? 'en'
                                      : Get.find<TransalationController>().location.value == 'Thailand'
                                      ? 'th'
                                      : Get.find<TransalationController>().location.value ==
                                      'Lao' ||
                                      appConfigService.countryConfigCollection == 'rplc'
                                      ? 'lo'
                                      : Get.find<TransalationController>().location.value ==
                                      'Cambodian' ||
                                      appConfigService.countryConfigCollection ==
                                          'rafco'
                                      ? 'km'
                                      : 'en'),
                                  // myloanCtl.lastPayList[index].pay_date_TH.toString(),
                                  myloanCtl.lastPayList[index].pay_details.toString(),
                                  myloanCtl.lastPayList[index].pay_amount.toString(),
                                  myloanCtl.selectedLoan!.value.toString(),
                                  // myloanCtl.myLoanList[index].guarantee_type.toString(),
                                  myloanCtl.lastPayList[index].pay_time.toString(),
                                  // '12:00',
                                  myloanCtl.lastPayList[index].pay_url.toString()
                              );
                            })),
                    HeaderWidget(
                      title: billHistoryDesc.tr,
                      onPressed: () {
                        Get.back();
                      },
                    )
                  ],
                )));
      },
    );
  } 

  Widget _buildBillHistory(context,int index, String date, String title, String amount,
      String ctt_code, String time, String url_Bill) {
    final MyloanController myloanCtl = Get.find<MyloanController>();
    return Container(
      child: Column(
        children: [
          //TODO date
          Container(
            width: 327.w,
            height: 24.h,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 24.h,
                  child: Text(
                    date,
                    style: TextStyle(
                      color: Color(0x591A1818),
                      fontSize: 14,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Container(
                  width: 250.w,
                  alignment: Alignment.center,
                  child: Divider(
                    height: 1.h,
                    thickness: 1.h,
                    color: Color(0x141A1818),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 16.h,
          ),
          //TODO title
          Container(
              width: 327.w,
              height: 69.h,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Row(
                      children: [
                        //TODO icon
                        Container(
                          alignment: Alignment.topLeft,
                          child: Image.asset(
                            // type_loan == '1'
                            //     ? contract
                            //     : type_loan == '2'
                            //     ? AppImageAssets.aam_loan_motorcycle_selected
                            //     :
                            AppImageAssets
                                .aam_loan_car_selected,
                            //TODO icon ตาม type ที่ส่งมา
                            width: 44.w,
                            height: 44.h,
                          ),
                        ),
                        SizedBox(
                          width: 12.w,
                        ),
                        //TODO title
                        SizedBox(
                          height: 69.h,
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '$title\n',
                                  style: TextStyle(
                                    color: Color(0xFF1A1818),
                                    fontSize: 14,
                                    fontFamily:
                                    TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                TextSpan(
                                  text: '${Get.find<AppConfigController>().currencySymbol!.value.toString()}$amount\n',
                                  style: TextStyle(
                                    color: Color(0xFF1A1818),
                                    fontSize: 14,
                                    fontFamily:
                                    TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                TextSpan(
                                  text: ctt_code,
                                  style: TextStyle(
                                    color: Color(0x591A1818),
                                    fontSize: 14,
                                    fontFamily:
                                    TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () async{
                            await myloanCtl.setBillUrl(index);
                            //TODO view bill
                            Get.to(() => MyloanBillScreen());
                          },
                          child: Container(
                            color: Colors.transparent,
                            alignment: Alignment.topRight,
                            width: 24.w,
                            height: 24.h,
                            child: SvgPicture.string(AppSvgImage.aam_view_bill),
                          ),
                        ),
                        SizedBox(
                          height: 14.h,
                        ),
                        Container(
                          child: Text(
                            time,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x591A1818),
                              fontSize: 14,
                              fontFamily:
                              TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ))
        ],
      ),
    );
  }
}