import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/home/<USER>';
// import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/config/appConfig.controller.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import '../../componance/widgets/header_widgets/header_icon.dart';
import 'loan_request_confirm_screen.dart';
import '../../../controller/transalation/th.dart';
import '../../../controller/transalation/km.dart';
import '../../../controller/transalation/lo.dart';


class LoanRequestScreen extends StatelessWidget {
  const LoanRequestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    // Get.put(AppConfigService(context: context));
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode()); // ปิด keyboard
        },
        child: GetBuilder<LoanController>(
            init: LoanController(),
            builder: (loanCtl) {
              return Container(
                color: Colors.white,
                child: Stack(
                  children: [
                    Container(
                      // color: Colors.white,
                      height: 106.h,
                      width: Get.width,
                      // color: Colors.teal,
                      // margin: EdgeInsets.only(top: 68.h),
                      child: Padding(
                        padding: EdgeInsets.only(
                          top: 60.h,
                          left: 12.w,
                          right: 12.w,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => HomeNavigator()));
                                // Get.to(const HomePage());
                                loanCtl.selectedGuarantee.value = '';
                                loanCtl.loan_amount.value.text = '';
                                loanCtl.selectedPeriod.value = '';
                              },
                              child: Container(
                                height: 50.h,
                                width: 50.w,
                                // color: Colors.red,
                                child: Center(
                                  child: SvgPicture.string(
                                    AppSvgImage.back_btn,
                                  ),
                                ),
                              ),
                            ),
                            Text(
                              menuGetLoanPlease.tr,
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .titleMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .titleMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .titleMedium
                                    ?.fontWeight,
                                // height: 0.09,
                              ),
                            ),
                            Container(
                                height: 50.h,
                                width: 50.w,
                                // color: Colors.red,
                                decoration: BoxDecoration(
                                  // color: Colors.red,
                                  image: DecorationImage(
                                    scale: 2,
                                    image: AssetImage(
                                        'assets/register/icon/Information.png'),
                                    // fit: BoxFit.contain,
                                  ),
                                ))
                          ],
                        ),
                      ),
                    ),
                    // HeaderIcon(
                    //     title: menuGetLoanPlease.tr,
                    //     onPressed: () {
                    //       Get.back();
                    //     },
                    //     icon: Image.asset(
                    //       'assets/register/icon/Information.png',
                    //       fit: BoxFit.contain,
                    //       width: 24.w,
                    //       height: 24.h,
                    //     )),
                    _buildLoanForm(context),
                  ],
                ),
              );
            }),
      ),
    );
  }

  Widget _buildLoanForm(context) {
    // AppConfigService appConfigService =
    // Get.put(AppConfigService(context: context));
    return GetBuilder<LoanController>(builder: (loanCtl) {
      return Container(
        color: Colors.white,
        width: Get.width,
        height: Get.height,
        margin: EdgeInsets.only(
          top: 118.h,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 327.w,
              height: 21.h,
              child: Text(
                menuGetLoanAmountAdd.tr,
                style: TextStyle(
                  color: configTheme()
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.5),
                  fontSize: configTheme().primaryTextTheme.labelSmall?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.labelSmall?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.labelSmall?.fontWeight,
                ),
              ),
            ),
            SizedBox(
              height: 14.h,
            ),
            _buildLoanAmount(context),
            SizedBox(
              height: 20.h,
            ),
            Container(
              width: 327.w,
              height: 21.h,
              child: Text(
                homeLoanGuarantee.tr,
                style: TextStyle(
                  color: configTheme()
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.5),
                  fontSize: configTheme().primaryTextTheme.labelSmall?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.labelSmall?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.labelSmall?.fontWeight,
                ),
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            //TODO dropdown
            _buildDropDownGuarantee(context),
            SizedBox(
              height: 20.h,
            ),
            Container(
              width: 327.w,
              height: 21.h,
              child: Text(
                menuGetLoanTime.tr,
                style: TextStyle(
                  color: configTheme()
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.5),
                  fontSize: configTheme().primaryTextTheme.labelSmall?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.labelSmall?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.labelSmall?.fontWeight,
                ),
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            //TODO dropdown
            _buildDropDownPeriod(context),
            SizedBox(
              height: 227.h,
            ),
            Obx(() {
              return PrimaryButton(
                title: homeApply.tr,
                onPressed: () {
                  if (loanCtl.selectedGuarantee.value.isNotEmpty &&
                      loanCtl.selectedPeriod.value.isNotEmpty &&
                      loanCtl.loan_amount.value.text.isNotEmpty &&
                      loanCtl.loan_amount.value.text != "0" &&
                      loanCtl.loan_amount.value.text.isNotEmpty) {
                    Get.to(() => const LoanRequestConfirmScreen());
                  } else {
                    Get.snackbar(
                      'Error',
                      'Please fill all fields',
                      snackPosition: SnackPosition.TOP,
                      backgroundColor: Colors.red,
                      colorText: Colors.white,
                    );
                  }
                },
                buttonWidth: 327.w,
                backgroundColor:
                    configTheme().buttonTheme.colorScheme?.background,
                backgroundInactiveColor:
                    configTheme().buttonTheme.colorScheme?.tertiary,
                textColor: configTheme().colorScheme.background,
                isActive: loanCtl.selectedGuarantee.value.isNotEmpty &&
                        loanCtl.selectedPeriod.value.isNotEmpty &&
                        loanCtl.loan_amount.value.text.isNotEmpty &&
                        loanCtl.loan_amount.value.text != "0" &&
                        loanCtl.loan_amount.value.text.isNotEmpty
                    ? true
                    : false,
              );
            })
          ],
        ),
      );
    });
  }

  Widget _buildLoanAmount(context) {
    return GetBuilder<LoanController>(builder: (loanCtl) {
      return Container(
        width: 327.w,
        height: 108.h,
        padding: const EdgeInsets.all(14),
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 1, color: Color(0x141A1818)),
            borderRadius: BorderRadius.circular(14.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 299.w,
              height: 32.h,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.center,
                    height: 32.h,
                    child: Text(
                      Get.find<AppConfigController>()
                          .currencySymbol!
                          .value
                          .toString(),
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize:
                            configTheme().primaryTextTheme.titleLarge?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .titleLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .titleLarge
                            ?.fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  Container(
                    height: 32.h,
                    width: 270.w,
                    alignment: Alignment.center,
                    child: TextFormField(
                        textAlignVertical: TextAlignVertical.center,
                        textAlign: TextAlign.start,
                        showCursor: true,
                        cursorWidth: 2.w,
                        cursorColor: configTheme().colorScheme.secondary,
                        cursorOpacityAnimates: true,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        minLines: 1,
                        autofocus: false,
                        readOnly: false,
                        focusNode: loanCtl.loanAmountFocus,
                        controller: loanCtl.loan_amount.value,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            isCollapsed: true,
                            contentPadding: EdgeInsets.zero,
                            labelStyle: TextStyle(
                              color: configTheme()
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withOpacity(0.3),
                              fontSize: 12.sp,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w700,
                            ),
                            hintText: '0',
                            hintStyle: TextStyle(
                              color: const Color(0x331A1818).withOpacity(0.2),
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w400,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                            )),
                        style: TextStyle(
                          color: const Color(0xFF1A1818).withOpacity(1.0),
                          fontSize: 14.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w700,
                        ),
                        onChanged: (value) {
                          loanCtl.setLoanAmount(value);
                        }),
                  )
                ],
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              height: 6.h,
              width: 299.w,
              child: Obx(() {
                return Divider(
                  color: loanCtl.loan_amount.value.text.toString() == "0"
                      ? Color(0xFFE5E5E5)
                      : configTheme().colorScheme.secondary,
                  thickness: 1.w,
                );
              }),
            ),
            SizedBox(
              height: 9.h,
            ),
            Container(
              width: 299.w,
              height: 32.h,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () {
                      loanCtl.calculateLoan(
                          appConfigService.countryConfigCollection.toString() ==
                                  'rplc'
                              ? 500000
                              : 1000,
                          // 500000,
                          'plus');
                    },
                    child: Container(
                      width: 66.04.w,
                      height: double.infinity,
                      decoration: ShapeDecoration(
                        color: configTheme().colorScheme.background,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                              width: 1,
                              color: configTheme()
                                  .colorScheme
                                  .secondary
                                  .withOpacity(0.15)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      // จำนวนเงินกีบ

                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 61,
                            height: 16,
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? '${Th()
                                            .messages[menuGetLoanThousandbaht]}'
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? '5 ${LO()
                                            .messages[menuGetLoanHundredThousand]}${LO()
                                            .messages[menuGetLoanBath]}'
                                        : appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rafco'
                                            ? '${Km()
                                            .messages[menuGetLoanThousandkm]}'
                                            : '${Th()
                                            .messages[menuGetLoanThousandbaht]}',
                                // '5 ${menuGetLoanHundredThousand.tr}${menuGetLoanBath.tr}'
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      loanCtl.calculateLoan(
                          appConfigService.countryConfigCollection.toString() ==
                                  'rplc'
                              ? 1000000
                              : 10000,
                          // 1000000,
                          'plus');
                    },
                    child: Container(
                      width: 66.04.w,
                      height: double.infinity,
                      decoration: ShapeDecoration(
                        color: configTheme().colorScheme.background,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                              width: 1,
                              color: configTheme()
                                  .colorScheme
                                  .secondary
                                  .withOpacity(0.15)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 61,
                            height: 16,
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? '${Th()
                                            .messages[menuGetLoantenThousandbaht]}'
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? '1 ${LO()
                                            .messages[menuGetLoanMillions]}${LO()
                                            .messages[menuGetLoanBath]}'
                                        : appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rafco'
                                            ? '${Km()
                                            .messages[menuGetLoantenThousandkm]}'
                                            : '${Th()
                                            .messages[menuGetLoantenThousandbaht]}',
                                // '1 ${menuGetLoanMillions.tr}${menuGetLoanBath.tr}',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      loanCtl.calculateLoan(
                          appConfigService.countryConfigCollection.toString() ==
                                  'rplc'
                              ? 5000000
                              : 100000,
                          // 5000000,
                          'plus');
                    },
                    child: Container(
                      width: 66.04.w,
                      height: double.infinity,
                      decoration: ShapeDecoration(
                        color: configTheme().colorScheme.background,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                              width: 1,
                              color: configTheme()
                                  .colorScheme
                                  .secondary
                                  .withOpacity(0.15)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 61,
                            height: 16,
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? '${Th()
                                            .messages[menuGetLoanhundredThousandbaht]}'
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? '5 ${LO()
                                            .messages[menuGetLoanMillions]}${LO()
                                            .messages[menuGetLoanBath]}'
                                        : appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rafco'
                                            ? '${Km()
                                            .messages[menuGetLoanhundredThousandkm]}'
                                            : '${Th()
                                            .messages[menuGetLoanhundredThousandbaht]}',
                                // '5 ${menuGetLoanMillions.tr}${menuGetLoanBath.tr}',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  appConfigService.countryConfigCollection.toString() == 'rplc'
                      ? InkWell(
                          onTap: () {
                            loanCtl.calculateLoan(10000000, 'plus');
                          },
                          child: Container(
                            width: 66.04.w,
                            height: double.infinity,
                            decoration: ShapeDecoration(
                              color: configTheme().colorScheme.background,
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                    width: 1,
                                    color: configTheme()
                                        .colorScheme
                                        .secondary
                                        .withOpacity(0.15)),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 61,
                                  height: 16,
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      '10 ${LO()
                                            .messages[menuGetLoanMillions]}${LO()
                                            .messages[menuGetLoanBath]}',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyLarge
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyLarge
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyLarge
                                            ?.fontWeight,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : InkWell(
                          onTap: () {
                            loanCtl.calculateLoan(0, 'delete');
                          },
                          child: Container(
                            width: 66.04.w,
                            height: double.infinity,
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                    width: 1,
                                    color: configTheme()
                                        .colorScheme
                                        .secondary
                                        .withOpacity(0.15)),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Container(
                              width: 24.w,
                              height: 24.h,
                              child: Center(
                                child: SvgPicture.string(
                                  AppSvgImage.aam_arrow_purple,
                                  color: configTheme().colorScheme.secondary,
                                  height: 10.38.h,
                                  width: 6.w,
                                  // fit: BoxFit.fill,
                                  // allowDrawingOutsideViewBox: true,
                                ),
                              ),
                            ),
                          ),
                        ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildDropDownGuarantee(context) {
    return GetBuilder<LoanController>(builder: (loanCtl) {
      return Container(
        width: 327.w,
        height: 54.h,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 1.w, color: Color(0x141A1818)),
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: InkWell(
          onTap: () {
            _buildBottomSheetGuarantee(context);
          },
          child: Stack(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 14.w,
                  ),
                  loanCtl.selectedGuarantee!.value.isNotEmpty
                      ? _listSelectLoanMenu(
                          context,
                          loanCtl.guaranteeList[
                              loanCtl.selectedGuaranteeIndex.value],
                          SvgPicture.string(
                            appConfigService.countryConfigCollection == "aam"
                                ? loanCtl.guaranteeIconListSelected[
                                    loanCtl.selectedGuaranteeIndex.value]
                                : appConfigService.countryConfigCollection ==
                                        "rplc"
                                    ? loanCtl.guaranteeIconListSelectedRPLC[
                                        loanCtl.selectedGuaranteeIndex.value]
                                    : loanCtl.guaranteeIconListSelectedRafco[loanCtl
                                        .selectedGuaranteeIndex
                                        .value], // allowDrawingOutsideViewBox: true,
                          ),
                          // Image.asset(
                          //   appConfigService.countryConfigCollection == "aam"
                          //       ? loanCtl.guaranteeIconListSelected[
                          //   loanCtl.selectedGuaranteeIndex.value]
                          //       : appConfigService.countryConfigCollection == "rplc"
                          //       ? loanCtl.guaranteeIconListSelectedRPLC[
                          //   loanCtl.selectedGuaranteeIndex.value]
                          //       : loanCtl.guaranteeIconListSelectedRafco[
                          //   loanCtl.selectedGuaranteeIndex.value],
                          // ),
                          true)
                      : Container(
                          alignment: Alignment.center,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              menuGetLoanReqTime.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color(0x331A1818),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontWeight,
                                // height: 0.14.h,
                              ),
                            ),
                          ),
                        ),
                ],
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                    child: Container(
                      margin: EdgeInsets.only(right: 15.w),
                      child: SvgPicture.string(
                        '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        color: configTheme().textTheme.bodyMedium?.color,
                        width: 11.w,
                        height: 6.h,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildDropDownPeriod(context) {
    return GetBuilder<LoanController>(builder: (loanCtl) {
      return Container(
        width: 327.w,
        height: 54.h,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 1.w, color: Color(0x141A1818)),
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: InkWell(
          onTap: () {
            _buildBottomSheetPeriod(context);
          },
          child: Stack(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 14.w,
                  ),
                  loanCtl.selectedPeriod.value.isNotEmpty
                      ? _listSelectLoanMenu(
                          context,
                          '${loanCtl.periodList[loanCtl.selectedPeriodIndex.value]} ${menuGetLoanMoth.tr}',
                          Container(),
                          false)
                      : Container(
                          alignment: Alignment.center,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              menuGetLoanReqTime.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color(0x331A1818),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontWeight,
                                // height: 0.14.h,
                              ),
                            ),
                          ),
                        ),
                ],
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                    child: Container(
                      margin: EdgeInsets.only(right: 15.w),
                      child: SvgPicture.string(
                        '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                        allowDrawingOutsideViewBox: true,
                        color: configTheme().textTheme.bodyMedium?.color,
                        width: 11.w,
                        height: 6.h,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  _buildBottomSheetGuarantee(context) {
    final loanCtl = Get.find<LoanController>();
    AppConfigService appConfigService = Get.find<AppConfigService>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: configTheme().colorScheme.background.withOpacity(1.0),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(18.0),
          ),
        ),
        builder: (context) {
          return Container(
            child: appConfigService.countryConfigCollection.toString() == "aam"
                ? Container(
                    width: Get.width,
                    height: 440.h,
                    padding: const EdgeInsets.only(bottom: 20),
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.background,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        Container(
                          height: 34.h,
                          alignment: Alignment.center,
                          child: SvgPicture.string(AppSvgImage.close_bar),
                        ),
                        SizedBox(
                          height: 26.h,
                        ),
                        _listLoanMenu(
                            context,
                            menuGetLoanTypeCar.tr,
                            menuGetLoanTypeCarDes.tr,
                            // loanCtl.guaranteeList[0],
                            // loanCtl.guaranteeListDesc[0],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[0],
                            ), () {
                          loanCtl.selectGuarantee('car', 0);
                          loanCtl.checkBu('aam');
                          Navigator.pop(context);
                        }),
                        _listLoanMenu(
                            context,
                            menuGetLoanTypeTruck.tr,
                            menuGetLoanTypeTruckDes.tr,
                            // loanCtl.guaranteeList[1],
                            // loanCtl.guaranteeListDesc[1],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[1],
                            ), () {
                          loanCtl.selectGuarantee('truck', 1);
                          loanCtl.checkBu('aam');
                          Navigator.pop(context);
                        }),
                        _listLoanMenu(
                            context,
                            menuGetLoanTypeMoto.tr,
                            menuGetLoanTypeMotoDes.tr,

                            // loanCtl.guaranteeList[2],
                            // loanCtl.guaranteeListDesc[2],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[2],
                            ), () {
                          loanCtl.selectGuarantee('motocycle', 2);
                          loanCtl.checkBu('aam');
                          Navigator.pop(context);
                        }),
                        _listLoanMenu(
                            context,
                            menuGetLoanTypeLand.tr,
                            menuGetLoanTypeLandDes.tr,
                            // loanCtl.guaranteeList[3],
                            // loanCtl.guaranteeListDesc[3],
                            SvgPicture.string(
                              loanCtl.guaranteeIconList[3],
                            ), () {
                          loanCtl.selectGuarantee('land', 3);
                          loanCtl.checkBu('aam');
                          Navigator.pop(context);
                        }),
                      ],
                    ),
                  )
                : appConfigService.countryConfigCollection.toString() == "rafco"
                    ? Container(
                        width: Get.width,
                        height: 440.h,
                        padding: const EdgeInsets.only(bottom: 20),
                        decoration: ShapeDecoration(
                          color: configTheme().colorScheme.background,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            Container(
                              height: 34.h,
                              alignment: Alignment.center,
                              child: SvgPicture.string(AppSvgImage.close_bar),
                            ),
                            SizedBox(
                              height: 26.h,
                            ),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeMoto.tr,
                                menuGetLoanTypeMotoDes.tr,
                                // loanCtl.guaranteeList[1],
                                // loanCtl.guaranteeListDesc[1],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRafco[1]
                                    .toString()), () {
                              loanCtl.selectGuarantee('motocycle', 1);
                              loanCtl.checkBu('rafco');
                              Navigator.pop(context);
                            }),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeThree.tr,
                                menuGetLoanTypeThreeDes.tr,
                                // loanCtl.guaranteeList[2],
                                // loanCtl.guaranteeListDesc[2],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRafco[2]
                                    .toString()), () {
                              loanCtl.selectGuarantee('threevehicle', 2);
                              loanCtl.checkBu('rafco');
                              Navigator.pop(context);
                            }),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeCar.tr,
                                menuGetLoanTypeCarDes.tr,
                                // loanCtl.guaranteeList[0],
                                // loanCtl.guaranteeListDesc[0],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRafco[0]
                                    .toString()),
                                // Image.asset(loanCtl.guaranteeIconListRafco[0], color: configTheme().colorScheme.onSecondary),
                                () {
                              loanCtl.selectGuarantee('car', 0);
                              loanCtl.checkBu('rafco');
                              Navigator.pop(context);
                            }),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeLand.tr,
                                menuGetLoanTypeLandDes.tr,
                                // loanCtl.guaranteeList[3],
                                // loanCtl.guaranteeListDesc[3],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRafco[3]
                                    .toString()), () {
                              loanCtl.selectGuarantee('land', 3);
                              loanCtl.checkBu('rafco');
                              Navigator.pop(context);
                            }),
                          ],
                        ),
                      )
                    : Container(
                        width: Get.width,
                        height: 350.h,
                        padding: const EdgeInsets.only(bottom: 20),
                        decoration: ShapeDecoration(
                          color: configTheme().colorScheme.background,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                        ),
                        child: Column(
                          // mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Align(
                              alignment: Alignment.topCenter,
                              child: Container(
                                height: 34.h,
                                alignment: Alignment.center,
                                child: SvgPicture.string(AppSvgImage.close_bar),
                              ),
                            ),
                            SizedBox(
                              height: 26.h,
                            ),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeMoto.tr,
                                menuGetLoanTypeMotoDes.tr,
                                // loanCtl.guaranteeList[0],
                                // loanCtl.guaranteeListDesc[0],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRPLC[0]
                                    .toString()), () {
                              loanCtl.selectGuarantee('motocycle', 0);
                              loanCtl.checkBu('rplc');
                              Navigator.pop(context);
                            }),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeCar.tr,
                                menuGetLoanTypeCarDes.tr,
                                // loanCtl.guaranteeList[1],
                                // loanCtl.guaranteeListDesc[1],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRPLC[1]
                                    .toString()), () {
                              loanCtl.selectGuarantee('car', 1);
                              loanCtl.checkBu('rplc');
                              Navigator.pop(context);
                            }),
                            _listLoanMenu(
                                context,
                                menuGetLoanTypeLand.tr,
                                menuGetLoanTypeLandDes.tr,
                                // loanCtl.guaranteeList[2],
                                // loanCtl.guaranteeListDesc[2],
                                SvgPicture.string(loanCtl
                                    .guaranteeIconListRPLC[2]
                                    .toString()),
                                // Image.asset(loanCtl.guaranteeIconListRPLC[2], color: configTheme().colorScheme.onSecondary),
                                () {
                              loanCtl.selectGuarantee('land', 2);
                              loanCtl.checkBu('rplc');
                              Navigator.pop(context);
                            }),
                            // _listLoanMenu(
                            //     context,
                            //     loanCtl.guaranteeList[3],
                            //     loanCtl.guaranteeListDesc[3],
                            //     Image.asset(loanCtl.guaranteeIconList[3],
                            //         color: configTheme().colorScheme.onSecondary), () {
                            //   loanCtl.selectGuarantee('land', 3);
                            //   Navigator.pop(context);
                            // }),
                          ],
                        ),
                      ),
          );
        });
  }

  _buildBottomSheetPeriod(context) {
    final loanCtl = Get.find<LoanController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(18.0),
          ),
        ),
        builder: (context) {
          print(loanCtl.selectedGuarantee.value);
          return Container(
            width: Get.width,
            height:
                loanCtl.selectedGuarantee.value == 'motocycle' ? 248.h : 464.h,
            padding: const EdgeInsets.only(bottom: 20),
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.background,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Column(
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 26.h,
                ),
                Container(
                  margin: EdgeInsets.only(left: 20.5.w, right: 20.5.w),
                  child: loanCtl.selectedGuarantee.value == 'motocycle'
                      ? StaggeredGrid.count(
                          crossAxisCount: 2,
                          mainAxisSpacing: 16.h,
                          crossAxisSpacing: 16.w,
                          children: [
                            serviceMenuCard(
                                context, loanCtl.periodListCar[0], 0),
                            serviceMenuCard(
                                context, loanCtl.periodListCar[1], 1),
                            serviceMenuCard(
                                context, loanCtl.periodListCar[2], 2),
                            serviceMenuCard(
                                context, loanCtl.periodListCar[3], 3),
                          ],
                        )
                      : Column(
                          children: [
                            StaggeredGrid.count(
                              crossAxisCount: 2,
                              mainAxisSpacing: 16.h,
                              crossAxisSpacing: 16.w,
                              children: [
                                serviceMenuCard(
                                    context, loanCtl.periodList[0], 0),
                                serviceMenuCard(
                                    context, loanCtl.periodList[1], 1),
                                serviceMenuCard(
                                    context, loanCtl.periodList[2], 2),
                                serviceMenuCard(
                                    context, loanCtl.periodList[3], 3),
                                serviceMenuCard(
                                    context, loanCtl.periodList[4], 4),
                                serviceMenuCard(
                                    context, loanCtl.periodList[5], 5),
                                serviceMenuCard(
                                    context, loanCtl.periodList[6], 6),
                                serviceMenuCard(
                                    context, loanCtl.periodList[7], 7),
                              ],
                            ),
                            SizedBox(
                              height: 16.h,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                serviceMenuCard(
                                    context, loanCtl.periodList[8], 8),
                              ],
                            ),
                          ],
                        ),
                ),
              ],
            ),
          );
        });
  }

  _listLoanMenu(context, String title, String sub_title, Widget icon,
      VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 376.w,
        height: 80.h,
        child: Row(
          children: [
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 46.w,
              child: icon,
            ),
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 270.w,
              // height: 44.h,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 270.w,
                    child: Text(
                      title,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Container(
                    width: 269.w,
                    child: Container(
                      height: 20.h,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 180.w,
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                sub_title,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.5),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  _listSelectLoanMenu(context, String title, Widget icon, bool isShowIcon) {
    return Container(
      // width: 376.w,
      height: 80.h,
      child: Row(
        children: [
          isShowIcon
              ? Container(
                  width: 34.w,
                  child: icon,
                )
              : Container(),
          SizedBox(
            width: 10.w,
          ),
          Container(
            height: 44.h,
            alignment: Alignment.centerLeft,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                title,
                style: TextStyle(
                  color: configTheme().textTheme.bodyMedium?.color,
                  fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  serviceMenuCard(context, title, int index) {
    return InkWell(
      onTap: () {
        Get.find<LoanController>().selectPeriod(title.toString(), index);
        Navigator.pop(context);
      },
      child: Container(
        width: 159.w,
        height: 56.h,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 1, color: Color(0x141A1818)),
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        alignment: Alignment.center,
        child: Text(
          title + " " + menuGetLoanMoth.tr,
          style: TextStyle(
            color: configTheme().textTheme.bodyMedium?.color,
            fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
            fontFamily: configTheme().primaryTextTheme.bodyLarge?.fontFamily,
            fontWeight: configTheme().primaryTextTheme.bodyLarge?.fontWeight,
          ),
        ),
      ),
    );
  }
}
