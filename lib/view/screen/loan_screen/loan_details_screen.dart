import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';

import '../../componance/AppBackgound.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import 'loan_main_screen.dart';
import 'loan_request_screen.dart';

class LoanRequestDetail extends StatelessWidget {
  const LoanRequestDetail({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService = Get.put(AppConfigService(context: context));
    return Scaffold(
        body: Stack(
      children: [
        Container(
          child: AppBackground.backgroundColorOnBoardingFinal(context),
        ),
        _buildLoanRequestDetail(),
        Container(
          margin: EdgeInsets.only(top: 343.0.h),
          child: Image.asset(
            appConfigService.countryConfigCollection == 'aam'
                ? AppImageAssets.aam_loan_detail
                : appConfigService.countryConfigCollection == 'rafco'
                    ? AppImageAssets.rafco_loan_detail
                    : AppImageAssets.rplc_loan_detail,
            width: Get.width,
            // height: 327.0.h,
          ),
        ),
        InkWell(
          onTap: () {
            Get.to(() => const LoanRequestScreen());
          },
          child: Container(
            width: Get.width,
            height: 34.h,
            margin: EdgeInsets.only(top: 67.h, right: 24.w),
            alignment: Alignment.centerRight,
            color: Colors.transparent,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                menuGetLoanSkip.tr,
                style: TextStyle(
                  color: configTheme().colorScheme.background,
                  fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize ,
                  fontFamily: configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                  fontWeight: configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                ),
              ),
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(left: 24.0.w, right: 24.0.w),
          child: PrimaryButton(
            title: menuGetLoanUnderstand.tr,
            onPressed: () {
              Get.to(() => const LoanMainScreen());
            },
            backgroundColor: configTheme().buttonTheme.colorScheme?.background,
            textColor: configTheme().colorScheme.background,
            height: 712.0.h,
            buttonWidth: 327.0.w,
            isActive: true,
          ),
        ),
      ],
    ));
  }

  Widget _buildLoanRequestDetail() {
    return Container(
      width: Get.width,
      // height: 134.h,
      margin: EdgeInsets.only(top: 146.0.h, left: 24.0.w, right: 24.0.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            // width: 327,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(
                menuGetLoan.tr,
                style: TextStyle(
                  color: configTheme().primaryColor,
                  fontSize: configTheme().primaryTextTheme.displayLarge?.fontSize,
                  fontFamily: configTheme().primaryTextTheme.displayLarge?.fontFamily,
                  fontWeight: configTheme().primaryTextTheme.displayLarge?.fontWeight,
                ),
              ),
            ),
          ),
           SizedBox(height: 16.h),
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 327,
                child: FittedBox(
                  fit: BoxFit.contain,
                  child: Text(
                    menuGetLoanDes.tr,
                    style: TextStyle(
                      color: configTheme().dialogBackgroundColor,
                      fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                      fontFamily: configTheme().primaryTextTheme.bodySmall?.fontFamily,
                      fontWeight: configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
