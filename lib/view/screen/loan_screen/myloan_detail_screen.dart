import 'dart:ui';

import 'package:AAMG/controller/bill_payment/billPayment.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/controller/dailyactiveusers/dailyactiveusers.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/widgets/aam_pay/aampay_componance.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/loan_screen/loan_main_screen.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/componance/widgets/header_widgets/header_widget.dart';
import 'package:AAMG/view/screen/loan_screen/bill_history_screen.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/config/appConfig.controller.dart';
import '../../../controller/transalation/transalation.controller.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import '../../componance/widgets/contract/contract_pay_widget.dart';
import '../bill_payment/select_payment_screen.dart';

class MyloanDetailScreen extends StatefulWidget {
  const MyloanDetailScreen({Key? key}) : super(key: key);

  @override
  State<MyloanDetailScreen> createState() => _MyloanDetailScreenState();
}

class _MyloanDetailScreenState extends State<MyloanDetailScreen> {
  final MyloanController myloanCtl = Get.put(MyloanController());
  final ContractListController contractListCtl =
      Get.put(ContractListController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return WillPopScope(
      onWillPop: () async {
        FocusScope.of(context).unfocus();
        return true;
        // Navigator.push(context, MaterialPageRoute(builder: (context) {
        //   return HomePage();
        // }));
        // return true;
      },
      child: Scaffold(
          body: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode()); // Close keyboard
          print('Keyboard closed!');
        },
        child: Container(
            child: Stack(
          children: [
            Container(
              margin: EdgeInsets.only(top: 116.h, left: 24.w, right: 24.w),
              width: 327.w,
              height: 4.h,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(contractListCtl.contractList.length,
                    (index) => _buildIndicator(index)),
              ),
            ),
            _buildMyloanContent(),
            Padding(
              padding:
                  EdgeInsets.only(top: 712.0.h, left: 24.0.w, right: 24.0.w),
              child: PrimaryButton(
                title: menuGetLoanContact.tr,
                onPressed: () {},
                backgroundColor: AppColors.inActiveButtonColor,
                textColor: configTheme().textTheme.bodyMedium?.color,
                buttonWidth: 327.0.w,
                isActive: true,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: 47.h,
              ),
              child: Container(
                  height: 59.h,
                  width: Get.width,
                  alignment: Alignment.center,
                  child: Stack(children: [
                    Center(
                      child: Text(menuGetMyLoan.tr,
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .titleLarge
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .titleLarge
                                ?.fontWeight,
                          ),
                          textAlign: TextAlign.center,
                          ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                              // height: 50.h,
                              width: 50.w,
                              child: Center(
                                  child: SvgPicture.string(
                                      '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3651_25410)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3651_25410"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'))),
                        ),
                        InkWell(
                            onTap: () async {
                              await myloanCtl
                                  .checkLoanDetail(context)
                                  .then((value) {
                                Get.to(() => const BillHistoryScreen());
                              });
                            },
                            child: Container(
                                // height: 24.h,
                                padding: EdgeInsets.only(right: 24.w),
                                child: Row(
                                  children: [
                                    Center(
                                      child: Text(billHistory.tr,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color!
                                                .withOpacity(0.5),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontWeight,
                                          )),
                                    ),
                                    SizedBox(width: 5.w),
                                    Center(
                                      child: SvgPicture.string(
                                        appConfigService.countryConfigCollection
                                                    .toString() ==
                                                'aam'
                                            ? AppSvgImage.aam_clock_myloan
                                            : appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rplc'
                                                ? AppSvgImage.rplc_clock_myloan
                                                : appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rafco'
                                                ? AppSvgImage.rafco_clock_myloan :AppSvgImage.aam_clock_myloan,
                                        fit: BoxFit.contain,
                                        width: 24.w,
                                        height: 24.h,
                                      ),
                                    ),
                                  ],
                                ))),
                      ],
                    ),
                  ])),
            ),
          ],
        )),
      )),
    );
  }

  Widget _buildIndicator(int index) {
    final myloanCtl = Get.find<MyloanController>();
    return Row(
      children: [
        SizedBox(
          width: 6.w,
        ),
        Obx(() {
          return Opacity(
            opacity: myloanCtl.indexMyloan == index ? 1.0 : 0.5,
            child: Container(
              width: myloanCtl.indexMyloan == index ? 15.0 : 4.0,
              height: 4.h,
              decoration: ShapeDecoration(
                color: configTheme().colorScheme.primary.withOpacity(1.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
            ),
          );
        }),
        SizedBox(
          width: 6.w,
        ),
      ],
    );
  }

  Widget _buildMyloanContent() {
    Get.lazyPut(() => BillPaymentController(), fenix: true);

    return GetBuilder<ContractListController>(
      init: ContractListController(),
      builder: (contractListCtl) {
        return Container(
          margin: EdgeInsets.only(top: 116.h),
          child: Column(
            children: [
              Container(
                height: 4,
              ), // dot count myloan
              SizedBox(
                height: 14.h,
              ),
              CarouselSlider(
                items: List.generate(
                  // 3,
                  contractListCtl.contractList.length,
                  (i) {
                    return Padding(
                      padding: EdgeInsets.only(left: 18.0.w, right: 18.0.w),
                      child: Container(
                        width: 327.w,
                        height: 454.h,
                        padding: EdgeInsets.all(18.h),
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                                width: 1.w, color: const Color(0x141A1818)),
                            borderRadius: BorderRadius.circular(14),
                          ),
                        ),
                        child: Column(
                          children: [
                            Container(
                              width: 291.w,
                              height: 50.h,
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 22.h,
                                        child: Text(
                                          homeLoanLimit.tr,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color
                                                ?.withOpacity(0.5),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 22.h,
                                        child: Text(
                                          homeLoanBalance.tr,
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color
                                                ?.withOpacity(0.5),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 28.h,
                                          child: Obx(() {
                                            return Text(
                                              '${contractListCtl.contractList[i].loan_amount} ${Get.find<AppConfigController>().currencySymbol!.value.toString()}',
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontWeight,
                                              ),
                                            );
                                          }),
                                        ),
                                        SizedBox(
                                          height: 28.h,
                                          child: Obx(() {
                                            return Text(
                                              contractListCtl
                                                  .contractList[i].remain
                                                  .toString(),
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontWeight,
                                              ),
                                            );
                                          }),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 4.h,
                            ),
                            //TODO % progress bar
                            Container(
                              width: 291.w,
                              height: 12.h,
                              decoration: ShapeDecoration(
                                color: configTheme()
                                    .colorScheme
                                    .secondary
                                    .withOpacity(0.2),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child: LinearPercentIndicator(
                                lineHeight: 12.h,
                                percent: contractListCtl.contactPercent![i],
                                barRadius: const Radius.circular(10),
                                widgetIndicator: contractListCtl
                                                .contractList[i].ctt_code
                                                .toString()
                                                .substring(6, 8) ==
                                            "DT" ||
                                        contractListCtl.contractList[i].ctt_code
                                                .toString()
                                                .substring(6, 8) ==
                                            "DL"
                                    ? Container()
                                    : SvgPicture.string(
                                        contractListCtl.contractList[i]
                                                    .guarantee_type
                                                    .toString() ==
                                                '1'
                                            ? contractListCtl
                                                .guaranteeImgPercentList![0]
                                            : contractListCtl.contractList[i]
                                                        .guarantee_type
                                                        .toString() ==
                                                    '2'
                                                ? contractListCtl
                                                    .guaranteeImgPercentList![1]
                                                : contractListCtl
                                                            .contractList[i]
                                                            .guarantee_type
                                                            .toString() ==
                                                        '3'
                                                    ? contractListCtl
                                                            .guaranteeImgPercentList![
                                                        2]
                                                    : contractListCtl
                                                        .guaranteeImgPercentList![3],
                                        width: 20.w,
                                        height: 20.h,
                                      ),
                                backgroundColor: Colors.transparent,
                                linearGradient: LinearGradient(
                                  begin: Alignment(0.00, -1.00),
                                  end: Alignment(0, 1),
                                  colors: [
                                    configTheme()
                                        .colorScheme
                                        .secondary
                                        .withOpacity(0.2),
                                    configTheme()
                                        .colorScheme
                                        .secondary
                                        .withOpacity(1.0)
                                  ],
                                ),
                                padding: EdgeInsets.zero,
                                maskFilter:
                                    const MaskFilter.blur(BlurStyle.solid, 3),
                                clipLinearGradient: true,
                                animateFromLastPercent: true,
                                animation: true,
                                animationDuration: 1000,
                              ),
                            ),
                            SizedBox(
                              height: 13.5.h,
                            ),
                            Container(
                              height: 44.h,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                      width: 44.w,
                                      height: 44.h,
                                      child: SvgPicture.string(
                                        contractListCtl.contractList[i].ctt_code
                                                    .toString()
                                                    .substring(6, 8) ==
                                                "DT"
                                            ? AppSvgImage.icon_aampay
                                            : contractListCtl.contractList[i]
                                                        .ctt_code
                                                        .toString()
                                                        .substring(6, 8) ==
                                                    "DL"
                                                ? AppSvgImage.rplcpay_icon
                                                : contractListCtl
                                                            .contractList[i]
                                                            .guarantee_type
                                                            .toString() ==
                                                        '1'
                                                    ? contractListCtl
                                                        .guaranteeImgList![0]
                                                    : contractListCtl
                                                                .contractList[i]
                                                                .guarantee_type
                                                                .toString() ==
                                                            '2'
                                                        ? contractListCtl
                                                                .guaranteeImgList![
                                                            1]
                                                        : contractListCtl
                                                                    .contractList[i]
                                                                    .guarantee_type
                                                                    .toString() ==
                                                                '3'
                                                            ? contractListCtl.guaranteeImgList![2]
                                                            : contractListCtl.guaranteeImgList![3],
                                      )),
                                  SizedBox(width: 12.w),
                                  SizedBox(
                                    height: 44.h,
                                    child: Obx(() {
                                      return Text.rich(
                                        TextSpan(
                                          children: [
                                            TextSpan(
                                              text:
                                                  '${contractListCtl.getGuaranteeTypeName(i)}\n',
                                              // '${contractListCtl
                                              //     .contractList[i].ctt_code
                                              //     .toString()
                                              //     .substring(6, 8) ==
                                              //     "DT"
                                              //     ? "เอเอเอ็ม เปย์"
                                              //     : contractListCtl
                                              //     .contractList[i]
                                              //     .guarantee_type.toString() ==
                                              //     '1'
                                              //     ? contractListCtl
                                              //     .guaranteeList[0]
                                              //     : contractListCtl
                                              //     .contractList[i]
                                              //     .guarantee_type.toString() ==
                                              //     '2'
                                              //     ? contractListCtl
                                              //     .guaranteeList[1]
                                              //     : contractListCtl
                                              //     .contractList[i]
                                              //     .guarantee_type.toString() ==
                                              //     '3'
                                              //     ? contractListCtl
                                              //     .guaranteeList[2]
                                              //     : contractListCtl
                                              //     .guaranteeList[3]}\n',
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontWeight,
                                              ),
                                            ),
                                            TextSpan(
                                              text: contractListCtl
                                                              .contractList[i]
                                                              .ctt_code
                                                              .toString()
                                                              .substring(
                                                                  6, 8) ==
                                                          "DT" ||
                                                      contractListCtl
                                                              .contractList[i]
                                                              .ctt_code
                                                              .toString()
                                                              .substring(
                                                                  6, 8) ==
                                                          "DL"
                                                  ? ""
                                                  : contractListCtl
                                                      .setDescByGuaType(
                                                          contractListCtl
                                                              .contractList[i]
                                                              .guarantee_type
                                                              .toString()),
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withOpacity(0.5),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall
                                                    ?.fontWeight,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 20.h,
                            ),
                            Container(
                              width: 291.w,
                              decoration: ShapeDecoration(
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                    width: 1.w,
                                    strokeAlign: BorderSide.strokeAlignCenter,
                                    color: const Color(0x141A1818),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 16.h,
                            ),
                            Container(
                              width: 291.w,
                              height: 118.h,
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    height: 22.h,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 22.h,
                                          child: Text(
                                            menuGetLoanInterest.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color
                                                  ?.withOpacity(0.5),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontWeight,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 22.h,
                                          child: Text(
                                            '${contractListCtl.contractList[i].interest.toString()}%',
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontWeight,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  Container(
                                    width: 291.w,
                                    height: 22.h,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 22.h,
                                          child: Text(
                                            menuGetLoanDate.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color
                                                  ?.withOpacity(0.5),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall
                                                  ?.fontWeight,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 22.h,
                                          child: Obx(() {
                                            return Text(
                                              contractListCtl.setDueDateLocale(
                                                  contractListCtl
                                                      .contractList[i].due_date
                                                      .toString(),
                                                  Get.find<TransalationController>()
                                                              .location
                                                              .value ==
                                                          'English'
                                                      ? 'en'
                                                      : Get.find<TransalationController>()
                                                                      .location
                                                                      .value ==
                                                                  'Thailand' ||
                                                              appConfigService
                                                                      .countryConfigCollection ==
                                                                  'aam'
                                                          ? 'th'
                                                          : Get.find<TransalationController>()
                                                                          .location
                                                                          .value ==
                                                                      'Lao' ||
                                                                  appConfigService
                                                                          .countryConfigCollection ==
                                                                      'rplc'
                                                              ? 'lo'
                                                              : Get.find<TransalationController>()
                                                                              .location
                                                                              .value ==
                                                                          'Cambodian' ||
                                                                      appConfigService
                                                                              .countryConfigCollection ==
                                                                          'rafco'
                                                                  ? 'km'
                                                                  : 'en'),
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontWeight,
                                              ),
                                            );
                                          }),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  GestureDetector(
                                    onTap: () async {
                                      print("click");
                                      if (contractListCtl
                                                  .contractList[i].ctt_code
                                                  .toString()
                                                  .substring(6, 8) ==
                                              "DT" ||
                                          contractListCtl
                                                  .contractList[i].ctt_code
                                                  .toString()
                                                  .substring(6, 8) ==
                                              "DL") {
                                        debugPrint("สินเชื่อ AAM Pay");
                                        myloanCtl.selectedLoan!.value =
                                            contractListCtl
                                                .contractList[i].ctt_code
                                                .toString();
                                        myloanCtl.update();
                                        await myloanCtl.getLoanDetail(context);
                                        AAMPAY_Componance
                                            .buildViewPeriodsAAMPayContract(
                                                context);
                                      }
                                    },
                                    child: Container(
                                      width: 291.w,
                                      height: 22.h,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            child: Row(
                                              children: [
                                                SizedBox(
                                                  height: 22.h,
                                                  child: Text(
                                                    menuGetLoanInstallment.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                contractListCtl.contractList[i]
                                                                .ctt_code
                                                                .toString()
                                                                .substring(
                                                                    6, 8) ==
                                                            "DT" ||
                                                        contractListCtl
                                                                .contractList[i]
                                                                .ctt_code
                                                                .toString()
                                                                .substring(
                                                                    6, 8) ==
                                                            "DL"
                                                    ? Container(
                                                        margin: EdgeInsets.only(
                                                            left: 4.w),
                                                        child:
                                                            SvgPicture.string(
                                                          contractListCtl
                                                                      .contractList[
                                                                          i]
                                                                      .ctt_code
                                                                      .toString()
                                                                      .substring(
                                                                          6,
                                                                          8) ==
                                                                  "DT"
                                                              ? AppSvgImage
                                                                  .icon_view_periods_aampay
                                                              : AppSvgImage
                                                                  .icon_view_periods_rplcpay,
                                                          width: 22.w,
                                                          height: 22.h,
                                                        ))
                                                    : SizedBox()
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            height: 22.h,
                                            child: Text(
                                              '${contractListCtl.contractList[i].paid_periods}/${contractListCtl.contractList[i].periods}',
                                              // '0/48',
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontWeight,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10.h),
                                  GestureDetector(
                                    onTap: () {
                                      print("click");
                                      if (contractListCtl
                                              .contractList[i].ctt_code
                                              .toString()
                                              .substring(6, 8) ==
                                          "DT") {
                                        debugPrint(
                                            "เปิดดูหน้าหนังสือสัญญา สินเชื่อ AAM Pay");
                                        myloanCtl.handleURLButtonPress(
                                            context,
                                            'https://ams4.prachakij.com/creditPolicy/${contractListCtl.contractList[i].ctt_code.toString()}',
                                            "ข้อมูลสัญญา");
                                      } else if (contractListCtl
                                              .contractList[i].ctt_code
                                              .toString()
                                              .substring(6, 8) ==
                                          "DL") {
                                        myloanCtl.handleURLButtonPress(
                                            context,
                                            'https://ams3rplc.prachakij.com/creditPolicy/${contractListCtl.contractList[i].ctt_code.toString()}',
                                            "ຂໍ້ມູນສັນຍາ");
                                      }
                                    },
                                    child: Container(
                                      width: 291.w,
                                      height: 22.h,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            child: Row(
                                              children: [
                                                SizedBox(
                                                  height: 22.h,
                                                  child: Text(
                                                    menuGetLoanNo.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodySmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                contractListCtl.contractList[i]
                                                                .ctt_code
                                                                .toString()
                                                                .substring(
                                                                    6, 8) ==
                                                            "DT" ||
                                                        contractListCtl
                                                                .contractList[i]
                                                                .ctt_code
                                                                .toString()
                                                                .substring(
                                                                    6, 8) ==
                                                            "DL"
                                                    ? Container(
                                                        margin: EdgeInsets.only(
                                                            left: 4.w),
                                                        child:
                                                            SvgPicture.string(
                                                          contractListCtl
                                                                      .contractList[
                                                                          i]
                                                                      .ctt_code
                                                                      .toString()
                                                                      .substring(
                                                                          6,
                                                                          8) ==
                                                                  "DT"
                                                              ? AppSvgImage
                                                                  .see_more_icon
                                                              : AppSvgImage
                                                                  .see_more_icon_rplc,
                                                          width: 22.w,
                                                          height: 22.h,
                                                        ))
                                                    : SizedBox()
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            height: 22.h,
                                            child: Obx(() {
                                              return Text(
                                                contractListCtl
                                                    .contractList[i].ctt_code
                                                    .toString(),
                                                textAlign: TextAlign.right,
                                                style: TextStyle(
                                                  color: configTheme()
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color,
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .bodyMedium
                                                      ?.fontWeight,
                                                ),
                                              );
                                            }),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 20.h),
                            InkWell(
                              onTap: () async {
                                print('จ่ายค่างวด');
                                // await myloanCtl.setDataPayment(
                                //     i,
                                //     contractListCtl.contractList[i].ctt_code
                                //         .toString(),
                                //     contractListCtl.contractList[i].nextpay
                                //         .toString());
                                // ContractPayWidget.alertQRPayment(context); //TODO : แก้ไขเป็นหน้าใหม่

                                if (Get.find<ContractListController>()
                                    .contractList
                                    .isNotEmpty) {
                                  await Get.find<BillPaymentController>()
                                      .setInitailContract(
                                          i,
                                          contractListCtl
                                              .contractList[i].ctt_code
                                              .toString());
                                  Get.to(() => SelectPaymentScreen());
                                }
                              },
                              child: Container(
                                width: 291.w,
                                height: 52.h,
                                decoration: ShapeDecoration(
                                  color: appConfigService
                                              .countryConfigCollection
                                              .toString() ==
                                          "aam"
                                      ? Color(0xFF792AFF).withOpacity(0.05)
                                      : appConfigService.countryConfigCollection
                                                  .toString() ==
                                              "rafco"
                                          ? Color(0xFFF2484E).withOpacity(0.10)
                                          : Color(0xFFFFC20E).withOpacity(0.10),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      '${menuGetLoanPay.tr} ${contractListCtl.contractList[i].nextpay.toString()}',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: configTheme()
                                            .colorScheme
                                            .onSecondary,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontWeight,
                                      ),
                                    ),
                                    SizedBox(width: 8.w),
                                    Container(
                                        width: 24.w,
                                        height: 24.h,
                                        child: SvgPicture.string(appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'aam'
                                            ? AppSvgImage.aam_scan_loan
                                            : appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rplc'
                                                ? '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3.1 12C3.1 9.62604 3.10127 7.90617 3.27765 6.59393C3.45168 5.29918 3.78625 4.49123 4.3883 3.88823C4.99074 3.2863 5.79856 2.9517 7.09355 2.77765C8.40591 2.60127 10.126 2.6 12.5 2.6C14.874 2.6 16.5943 2.60127 17.9067 2.77765C19.2019 2.95173 20.0093 3.28637 20.6106 3.88812L20.6107 3.88826C21.2132 4.49074 21.5481 5.29855 21.7222 6.5936C21.8987 7.90594 21.9 9.62603 21.9 12C21.9 14.374 21.8987 16.0943 21.7222 17.4067C21.548 18.7018 21.2132 19.5091 20.611 20.1104L20.6106 20.1109C20.0087 20.7132 19.2012 21.0481 17.9063 21.2222C16.5941 21.3987 14.874 21.4 12.5 21.4C10.126 21.4 8.40619 21.3987 7.09398 21.2222C5.79923 21.0481 4.99129 20.7133 4.38826 20.1107C3.78635 19.5088 3.45171 18.7013 3.27765 17.4063C3.10127 16.0941 3.1 14.374 3.1 12Z" fill="url(#paint0_linear_3890_23339)" stroke="#1A1818" stroke-width="1.2"/><path d="M10.504 5.75196C10.6025 5.75144 10.6999 5.73152 10.7907 5.69334C10.8815 5.65517 10.9639 5.59948 11.0332 5.52947C11.1024 5.45945 11.1572 5.37648 11.1944 5.28528C11.2316 5.19409 11.2505 5.09646 11.25 4.99796C11.2495 4.89947 11.2296 4.80205 11.1914 4.71126C11.1532 4.62046 11.0975 4.53808 11.0275 4.46881C10.9575 4.39953 10.8745 4.34473 10.7833 4.30752C10.6921 4.27032 10.5945 4.25144 10.496 4.25196C9.414 4.25796 8.516 4.28196 7.783 4.41096C7.031 4.54196 6.388 4.79296 5.861 5.29396C5.406 5.72696 5.101 6.16596 4.936 6.79696C4.786 7.36896 4.759 8.07196 4.752 8.99496C4.75054 9.19388 4.82816 9.38522 4.96778 9.5269C5.1074 9.66859 5.29759 9.749 5.4965 9.75046C5.69541 9.75192 5.88676 9.6743 6.02844 9.53468C6.17012 9.39506 6.25054 9.20488 6.252 9.00596C6.26 8.05696 6.292 7.53996 6.387 7.17796C6.467 6.87496 6.593 6.66796 6.896 6.37996C7.138 6.14996 7.468 5.98796 8.041 5.88796C8.633 5.78496 9.411 5.75796 10.504 5.75196ZM14.504 4.25196C14.3051 4.2509 14.1139 4.3289 13.9725 4.46881C13.8311 4.60871 13.7511 4.79905 13.75 4.99796C13.7489 5.19688 13.8269 5.38806 13.9668 5.52947C14.1067 5.67087 14.2971 5.7509 14.496 5.75196C15.589 5.75796 16.367 5.78496 16.959 5.88796C17.532 5.98796 17.862 6.14996 18.104 6.37996C18.407 6.66896 18.534 6.87496 18.613 7.17796C18.708 7.53996 18.741 8.05796 18.748 9.00596C18.7495 9.20488 18.8299 9.39506 18.9716 9.53468C19.0417 9.60382 19.1248 9.65845 19.2161 9.69548C19.3073 9.7325 19.405 9.75119 19.5035 9.75046C19.602 9.74974 19.6994 9.72963 19.7901 9.69127C19.8808 9.65291 19.9631 9.59706 20.0322 9.5269C20.1014 9.45675 20.156 9.37367 20.193 9.28239C20.23 9.19112 20.2487 9.09345 20.248 8.99496C20.241 8.07196 20.214 7.36896 20.064 6.79696C19.898 6.16696 19.594 5.72696 19.139 5.29396C18.612 4.79296 17.969 4.54196 17.217 4.41096C16.484 4.28196 15.586 4.25796 14.504 4.25196ZM5.5 11.25C5.30109 11.25 5.11032 11.329 4.96967 11.4696C4.82902 11.6103 4.75 11.8011 4.75 12C4.75 12.1989 4.82902 12.3896 4.96967 12.5303C5.11032 12.6709 5.30109 12.75 5.5 12.75H19.5C19.6989 12.75 19.8897 12.6709 20.0303 12.5303C20.171 12.3896 20.25 12.1989 20.25 12C20.25 11.8011 20.171 11.6103 20.0303 11.4696C19.8897 11.329 19.6989 11.25 19.5 11.25H5.5ZM6.252 14.995C6.25128 14.8965 6.23116 14.7991 6.1928 14.7084C6.15445 14.6177 6.0986 14.5354 6.02844 14.4662C5.95829 14.3971 5.8752 14.3425 5.78393 14.3054C5.69266 14.2684 5.59499 14.2497 5.4965 14.2505C5.39801 14.2512 5.30062 14.2713 5.20991 14.3097C5.11919 14.348 5.03691 14.4039 4.96778 14.474C4.89865 14.5442 4.84401 14.6273 4.80699 14.7185C4.76996 14.8098 4.75128 14.9075 4.752 15.006C4.759 15.929 4.786 16.632 4.936 17.204C5.102 17.834 5.406 18.274 5.861 18.707C6.388 19.207 7.031 19.458 7.783 19.59C8.516 19.718 9.414 19.743 10.496 19.749C10.5945 19.7495 10.6921 19.7306 10.7833 19.6934C10.8745 19.6562 10.9575 19.6014 11.0275 19.5321C11.0975 19.4628 11.1532 19.3805 11.1914 19.2897C11.2296 19.1989 11.2495 19.1015 11.25 19.003C11.2505 18.9045 11.2316 18.8068 11.1944 18.7156C11.1572 18.6245 11.1024 18.5415 11.0332 18.4715C10.9639 18.4014 10.8815 18.3458 10.7907 18.3076C10.6999 18.2694 10.6025 18.2495 10.504 18.249C9.411 18.243 8.633 18.216 8.041 18.112C7.468 18.012 7.138 17.851 6.896 17.62C6.593 17.332 6.466 17.125 6.387 16.823C6.292 16.461 6.26 15.943 6.252 14.995ZM20.248 15.006C20.2495 14.8071 20.1718 14.6157 20.0322 14.474C19.8926 14.3323 19.7024 14.2519 19.5035 14.2505C19.3046 14.249 19.1132 14.3266 18.9716 14.4662C18.8299 14.6059 18.7495 14.7961 18.748 14.995C18.74 15.943 18.708 16.461 18.613 16.823C18.533 17.125 18.407 17.332 18.104 17.62C17.862 17.85 17.532 18.012 16.959 18.112C16.367 18.216 15.589 18.242 14.496 18.249C14.3975 18.2495 14.3001 18.2694 14.2093 18.3076C14.1185 18.3458 14.0361 18.4014 13.9668 18.4715C13.8269 18.6129 13.7489 18.8041 13.75 19.003C13.7511 19.2019 13.8311 19.3922 13.9725 19.5321C14.1139 19.672 14.3051 19.75 14.504 19.749C15.586 19.743 16.484 19.719 17.217 19.589C17.969 19.459 18.612 19.208 19.139 18.707C19.594 18.273 19.899 17.834 20.064 17.204C20.214 16.632 20.241 15.929 20.248 15.006Z" fill="white"/><defs><linearGradient id="paint0_linear_3890_23339" x1="12.5" y1="2" x2="12.5" y2="22" gradientUnits="userSpaceOnUse"><stop stop-color="#FFC30E"/><stop offset="1" stop-color="#C08F00"/></linearGradient></defs></svg>'
                                                : '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3.1 12C3.1 9.62604 3.10127 7.90617 3.27765 6.59393C3.45168 5.29918 3.78625 4.49123 4.3883 3.88823C4.99074 3.2863 5.79856 2.9517 7.09355 2.77765C8.40591 2.60127 10.126 2.6 12.5 2.6C14.874 2.6 16.5943 2.60127 17.9067 2.77765C19.2019 2.95173 20.0093 3.28637 20.6106 3.88812L20.6107 3.88826C21.2132 4.49074 21.5481 5.29855 21.7222 6.5936C21.8987 7.90594 21.9 9.62603 21.9 12C21.9 14.374 21.8987 16.0943 21.7222 17.4067C21.548 18.7018 21.2132 19.5091 20.611 20.1104L20.6106 20.1109C20.0087 20.7132 19.2012 21.0481 17.9063 21.2222C16.5941 21.3987 14.874 21.4 12.5 21.4C10.126 21.4 8.40619 21.3987 7.09398 21.2222C5.79923 21.0481 4.99129 20.7133 4.38826 20.1107C3.78635 19.5088 3.45171 18.7013 3.27765 17.4063C3.10127 16.0941 3.1 14.374 3.1 12Z" fill="url(#paint0_linear_3781_26961)" stroke="#1A1818" stroke-width="1.2"/><path d="M10.504 5.75196C10.6025 5.75144 10.6999 5.73152 10.7907 5.69334C10.8815 5.65517 10.9639 5.59948 11.0332 5.52947C11.1024 5.45945 11.1572 5.37648 11.1944 5.28528C11.2316 5.19409 11.2505 5.09646 11.25 4.99796C11.2495 4.89947 11.2296 4.80205 11.1914 4.71126C11.1532 4.62046 11.0975 4.53808 11.0275 4.46881C10.9575 4.39953 10.8745 4.34473 10.7833 4.30752C10.6921 4.27032 10.5945 4.25144 10.496 4.25196C9.414 4.25796 8.516 4.28196 7.783 4.41096C7.031 4.54196 6.388 4.79296 5.861 5.29396C5.406 5.72696 5.101 6.16596 4.936 6.79696C4.786 7.36896 4.759 8.07196 4.752 8.99496C4.75054 9.19388 4.82816 9.38522 4.96778 9.5269C5.1074 9.66859 5.29759 9.749 5.4965 9.75046C5.69541 9.75192 5.88676 9.6743 6.02844 9.53468C6.17012 9.39506 6.25054 9.20488 6.252 9.00596C6.26 8.05696 6.292 7.53996 6.387 7.17796C6.467 6.87496 6.593 6.66796 6.896 6.37996C7.138 6.14996 7.468 5.98796 8.041 5.88796C8.633 5.78496 9.411 5.75796 10.504 5.75196ZM14.504 4.25196C14.3051 4.2509 14.1139 4.3289 13.9725 4.46881C13.8311 4.60871 13.7511 4.79905 13.75 4.99796C13.7489 5.19688 13.8269 5.38806 13.9668 5.52947C14.1067 5.67087 14.2971 5.7509 14.496 5.75196C15.589 5.75796 16.367 5.78496 16.959 5.88796C17.532 5.98796 17.862 6.14996 18.104 6.37996C18.407 6.66896 18.534 6.87496 18.613 7.17796C18.708 7.53996 18.741 8.05796 18.748 9.00596C18.7495 9.20488 18.8299 9.39506 18.9716 9.53468C19.0417 9.60382 19.1248 9.65845 19.2161 9.69548C19.3073 9.7325 19.405 9.75119 19.5035 9.75046C19.602 9.74974 19.6994 9.72963 19.7901 9.69127C19.8808 9.65291 19.9631 9.59706 20.0322 9.5269C20.1014 9.45675 20.156 9.37367 20.193 9.28239C20.23 9.19112 20.2487 9.09345 20.248 8.99496C20.241 8.07196 20.214 7.36896 20.064 6.79696C19.898 6.16696 19.594 5.72696 19.139 5.29396C18.612 4.79296 17.969 4.54196 17.217 4.41096C16.484 4.28196 15.586 4.25796 14.504 4.25196ZM5.5 11.25C5.30109 11.25 5.11032 11.329 4.96967 11.4696C4.82902 11.6103 4.75 11.8011 4.75 12C4.75 12.1989 4.82902 12.3896 4.96967 12.5303C5.11032 12.6709 5.30109 12.75 5.5 12.75H19.5C19.6989 12.75 19.8897 12.6709 20.0303 12.5303C20.171 12.3896 20.25 12.1989 20.25 12C20.25 11.8011 20.171 11.6103 20.0303 11.4696C19.8897 11.329 19.6989 11.25 19.5 11.25H5.5ZM6.252 14.995C6.25128 14.8965 6.23116 14.7991 6.1928 14.7084C6.15445 14.6177 6.0986 14.5354 6.02844 14.4662C5.95829 14.3971 5.8752 14.3425 5.78393 14.3054C5.69266 14.2684 5.59499 14.2497 5.4965 14.2505C5.39801 14.2512 5.30062 14.2713 5.20991 14.3097C5.11919 14.348 5.03691 14.4039 4.96778 14.474C4.89865 14.5442 4.84401 14.6273 4.80699 14.7185C4.76996 14.8098 4.75128 14.9075 4.752 15.006C4.759 15.929 4.786 16.632 4.936 17.204C5.102 17.834 5.406 18.274 5.861 18.707C6.388 19.207 7.031 19.458 7.783 19.59C8.516 19.718 9.414 19.743 10.496 19.749C10.5945 19.7495 10.6921 19.7306 10.7833 19.6934C10.8745 19.6562 10.9575 19.6014 11.0275 19.5321C11.0975 19.4628 11.1532 19.3805 11.1914 19.2897C11.2296 19.1989 11.2495 19.1015 11.25 19.003C11.2505 18.9045 11.2316 18.8068 11.1944 18.7156C11.1572 18.6245 11.1024 18.5415 11.0332 18.4715C10.9639 18.4014 10.8815 18.3458 10.7907 18.3076C10.6999 18.2694 10.6025 18.2495 10.504 18.249C9.411 18.243 8.633 18.216 8.041 18.112C7.468 18.012 7.138 17.851 6.896 17.62C6.593 17.332 6.466 17.125 6.387 16.823C6.292 16.461 6.26 15.943 6.252 14.995ZM20.248 15.006C20.2495 14.8071 20.1718 14.6157 20.0322 14.474C19.8926 14.3323 19.7024 14.2519 19.5035 14.2505C19.3046 14.249 19.1132 14.3266 18.9716 14.4662C18.8299 14.6059 18.7495 14.7961 18.748 14.995C18.74 15.943 18.708 16.461 18.613 16.823C18.533 17.125 18.407 17.332 18.104 17.62C17.862 17.85 17.532 18.012 16.959 18.112C16.367 18.216 15.589 18.242 14.496 18.249C14.3975 18.2495 14.3001 18.2694 14.2093 18.3076C14.1185 18.3458 14.0361 18.4014 13.9668 18.4715C13.8269 18.6129 13.7489 18.8041 13.75 19.003C13.7511 19.2019 13.8311 19.3922 13.9725 19.5321C14.1139 19.672 14.3051 19.75 14.504 19.749C15.586 19.743 16.484 19.719 17.217 19.589C17.969 19.459 18.612 19.208 19.139 18.707C19.594 18.273 19.899 17.834 20.064 17.204C20.214 16.632 20.241 15.929 20.248 15.006Z" fill="white"/><defs><linearGradient id="paint0_linear_3781_26961" x1="12.5" y1="2" x2="12.5" y2="22" gradientUnits="userSpaceOnUse"><stop stop-color="#F2484F"/><stop offset="1" stop-color="#EA1B23"/></linearGradient></defs></svg>'))
                                    // child: SvgPicture.string(AppSvgImage.aam_scan_loan)
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(height: 12.h),
                            contractListCtl.contractList[i].ctt_code
                                        .toString()
                                        .substring(6, 8) ==
                                    "DT"
                                ? SizedBox()
                                : InkWell(
                                    onTap: () {
                                      print('ขอกู้เพิ่ม');
                                      showDialog(
                                          context: context,
                                          useSafeArea: false,
                                          builder: (_) => LoanMainScreen());
                                      // Get.to(() => const MyloanDetailScreen());
                                      // Get.to(() => LoanMainScreen());
                                    },
                                    child: Container(
                                      width: 291.w,
                                      height: 52.h,
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          side: BorderSide(
                                              width: 1.w,
                                              color: const Color(0x141A1818)),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            menuGetLoanIncrease.tr,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontWeight,
                                            ),
                                          ),
                                          SizedBox(width: 4.w),
                                          Container(
                                              width: 24.w,
                                              height: 24.h,
                                              child: SvgPicture.string(appConfigService
                                                          .countryConfigCollection
                                                          .toString() ==
                                                      'aam'
                                                  ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M13.0347 1.94777H7.89434C6.71589 1.94775 5.58569 2.41589 4.75238 3.2492C3.91907 4.08249 3.45093 5.21269 3.45093 6.39116V17.6166C3.45093 20.0706 5.4403 22.06 7.89434 22.06H15.6118C15.6571 22.06 15.7014 22.0557 15.7442 22.0475C15.8549 22.0558 15.9667 22.06 16.0796 22.06C18.5319 22.06 20.523 20.069 20.523 17.6166C20.523 16.2281 19.8847 14.9874 18.8859 14.1725V7.79896C18.886 7.78861 18.8858 7.77829 18.8854 7.76796L18.8853 7.765L18.8853 7.76387C18.8783 7.60111 18.8159 7.45273 18.7165 7.3371L18.7163 7.33693C18.7136 7.33376 18.7108 7.33062 18.708 7.32747L18.7074 7.3268C18.7061 7.3253 18.7047 7.32379 18.7034 7.32231L18.7008 7.3195L18.6993 7.31786L18.6985 7.31703C18.6972 7.31564 18.6959 7.31423 18.6946 7.31281L18.6922 7.3103L18.6902 7.30819L18.6896 7.30754C18.6882 7.30617 18.6869 7.30478 18.6855 7.30341L18.6846 7.30246C18.6832 7.30105 18.6818 7.29963 18.6804 7.29824L13.5354 2.15326C13.534 2.15187 13.5326 2.15048 13.5312 2.14909L13.5303 2.14817C13.5289 2.14682 13.5275 2.14547 13.5261 2.14412L13.5234 2.1415L13.5217 2.13988L13.5209 2.1391C13.5195 2.13777 13.518 2.13645 13.5166 2.13514L13.5142 2.13285L13.5114 2.13029C13.5099 2.12893 13.5084 2.1276 13.5069 2.12625L13.5062 2.12565C13.5031 2.12284 13.4999 2.1201 13.4968 2.11736L13.4966 2.11723C13.3809 2.01777 13.2326 1.95538 13.0698 1.94842L13.0687 1.94835L13.0665 1.94829L13.0657 1.94824C13.0554 1.94786 13.0451 1.9477 13.0347 1.94777ZM12.3378 3.35095H7.89434C7.08804 3.35095 6.31474 3.67126 5.74458 4.2414C5.17442 4.81156 4.85411 5.58486 4.85411 6.39116V17.6166C4.85411 19.2957 6.21526 20.6568 7.89434 20.6568H12.8402C12.0937 19.8619 11.6362 18.7923 11.6362 17.6166C11.6362 15.1642 13.6272 13.1732 16.0796 13.1732C16.5698 13.1732 17.0416 13.2528 17.4827 13.3997V8.49593H14.9102C13.4895 8.49593 12.3378 7.3442 12.3378 5.92346V3.35095ZM16.0796 14.5764C17.7575 14.5764 19.1198 15.9387 19.1198 17.6166C19.1198 19.2946 17.7575 20.6568 16.0796 20.6568C14.4016 20.6568 13.0393 19.2946 13.0393 17.6166C13.0393 15.9387 14.4016 14.5764 16.0796 14.5764ZM6.95888 12.4716H14.9102L14.9148 12.4716C15.2999 12.4692 15.6118 12.1557 15.6118 11.77C15.6118 11.3828 15.2975 11.0684 14.9102 11.0684H6.95888C6.57167 11.0684 6.25729 11.3828 6.25729 11.77C6.25729 12.1572 6.57167 12.4716 6.95888 12.4716ZM6.95888 9.66526H10.9208L10.9253 9.66524C11.3105 9.66279 11.6224 9.34938 11.6224 8.96367C11.6224 8.57644 11.308 8.26208 10.9208 8.26208H6.95888C6.57167 8.26208 6.25729 8.57644 6.25729 8.96367C6.25729 9.35088 6.57167 9.66526 6.95888 9.66526ZM13.7409 4.34314V5.92348V5.92833C13.7436 6.57187 14.2661 7.09273 14.9103 7.09275H16.4906L13.7409 4.34314Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M15.378 16.9397H14.7013C14.3141 16.9397 13.9998 17.2541 13.9998 17.6413C13.9998 18.0286 14.3141 18.3429 14.7013 18.3429H15.378V19.0195C15.378 19.4068 15.6924 19.7211 16.0796 19.7211C16.4668 19.7211 16.7812 19.4068 16.7812 19.0195V18.3429H17.4578C17.845 18.3429 18.1594 18.0286 18.1594 17.6413C18.1594 17.2541 17.845 16.9397 17.4578 16.9397H16.7812V16.2631C16.7812 15.8759 16.4668 15.5615 16.0796 15.5615C15.6924 15.5615 15.378 15.8759 15.378 16.2631V16.9397Z" fill="#792AFF"/></svg>'
                                                  : appConfigService
                                                              .countryConfigCollection
                                                              .toString() ==
                                                          'rplc'
                                                      ? '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M13.535 1.94728H8.39458C7.21614 1.94726 6.08594 2.4154 5.25263 3.24871C4.41932 4.082 3.95117 5.2122 3.95117 6.39067V17.6161C3.95117 20.0702 5.94055 22.0595 8.39458 22.0595H16.1121C16.1573 22.0595 16.2016 22.0552 16.2444 22.047C16.3552 22.0553 16.467 22.0595 16.5798 22.0595C19.0322 22.0595 21.0232 20.0685 21.0232 17.6161C21.0232 16.2276 20.3849 14.987 19.3862 14.172V7.79848C19.3862 7.78813 19.3861 7.7778 19.3857 7.76747L19.3856 7.76451L19.3855 7.76339C19.3785 7.60062 19.3162 7.45224 19.2167 7.33662L19.2166 7.33644C19.2138 7.33327 19.2111 7.33013 19.2083 7.32699L19.2077 7.32631C19.2063 7.32481 19.205 7.3233 19.2036 7.32182L19.2011 7.31902L19.1995 7.31738L19.1988 7.31655C19.1975 7.31515 19.1962 7.31374 19.1948 7.31232L19.1924 7.30981L19.1904 7.3077L19.1898 7.30705C19.1885 7.30568 19.1871 7.30429 19.1858 7.30292L19.1848 7.30197C19.1834 7.30056 19.1821 7.29915 19.1807 7.29775L14.0357 2.15277C14.0343 2.15138 14.0329 2.14999 14.0315 2.1486L14.0305 2.14768C14.0292 2.14633 14.0278 2.14498 14.0264 2.14364L14.0236 2.14101L14.0219 2.13939L14.0211 2.13861C14.0197 2.13728 14.0183 2.13596 14.0169 2.13466L14.0144 2.13237L14.0116 2.12981C14.0101 2.12844 14.0086 2.12711 14.0071 2.12577L14.0064 2.12516C14.0033 2.12235 14.0002 2.11961 13.997 2.11687L13.9968 2.11674C13.8812 2.01728 13.7328 1.95489 13.5701 1.94793L13.5689 1.94786L13.5667 1.9478L13.566 1.94775C13.5556 1.94737 13.5453 1.94721 13.535 1.94728ZM12.838 3.35046H8.39458C7.58828 3.35046 6.81499 3.67077 6.24482 4.24091C5.67466 4.81107 5.35435 5.58437 5.35435 6.39067V17.6161C5.35435 19.2952 6.71551 20.6563 8.39458 20.6563H13.3405C12.594 19.8614 12.1364 18.7918 12.1364 17.6161C12.1364 15.1637 14.1274 13.1727 16.5798 13.1727C17.07 13.1727 17.5418 13.2523 17.983 13.3992V8.49544H15.4105C13.9897 8.49544 12.838 7.34371 12.838 5.92297V3.35046ZM16.5798 14.5759C18.2578 14.5759 19.62 15.9382 19.62 17.6161C19.62 19.2941 18.2578 20.6563 16.5798 20.6563C14.9019 20.6563 13.5396 19.2941 13.5396 17.6161C13.5396 15.9382 14.9019 14.5759 16.5798 14.5759ZM7.45912 12.4711H15.4105L15.415 12.4711C15.8001 12.4687 16.1121 12.1553 16.1121 11.7695C16.1121 11.3823 15.7977 11.068 15.4105 11.068H7.45912C7.07191 11.068 6.75753 11.3823 6.75753 11.7695C6.75753 12.1568 7.07191 12.4711 7.45912 12.4711ZM7.45912 9.66477H11.4211L11.4256 9.66475C11.8107 9.6623 12.1226 9.34889 12.1226 8.96318C12.1226 8.57595 11.8083 8.26159 11.4211 8.26159H7.45912C7.07191 8.26159 6.75753 8.57595 6.75753 8.96318C6.75753 9.35039 7.07191 9.66477 7.45912 9.66477ZM14.2412 4.34266V5.923V5.92785C14.2438 6.57138 14.7663 7.09224 15.4105 7.09226H16.9908L14.2412 4.34266Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M15.8782 16.9397H15.2016C14.8144 16.9397 14.5 17.2541 14.5 17.6413C14.5 18.0286 14.8144 18.3429 15.2016 18.3429H15.8782V19.0195C15.8782 19.4068 16.1926 19.7211 16.5798 19.7211C16.967 19.7211 17.2814 19.4068 17.2814 19.0195V18.3429H17.958C18.3453 18.3429 18.6596 18.0286 18.6596 17.6413C18.6596 17.2541 18.3453 16.9397 17.958 16.9397H17.2814V16.2631C17.2814 15.8759 16.967 15.5615 16.5798 15.5615C16.1926 15.5615 15.8782 15.8759 15.8782 16.2631V16.9397Z" fill="#FFC20E"/></svg>'
                                                      : '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path fill-rule="evenodd" clip-rule="evenodd" d="M13.5347 1.94728H8.39434C7.21589 1.94726 6.08569 2.4154 5.25238 3.24871C4.41907 4.082 3.95093 5.2122 3.95093 6.39067V17.6161C3.95093 20.0702 5.9403 22.0595 8.39434 22.0595H16.1118C16.1571 22.0595 16.2014 22.0552 16.2442 22.047C16.3549 22.0553 16.4667 22.0595 16.5796 22.0595C19.0319 22.0595 21.023 20.0685 21.023 17.6161C21.023 16.2276 20.3847 14.987 19.3859 14.172V7.79848C19.386 7.78813 19.3858 7.7778 19.3854 7.76747L19.3853 7.76451L19.3853 7.76339C19.3783 7.60062 19.3159 7.45224 19.2165 7.33662L19.2163 7.33644C19.2136 7.33327 19.2108 7.33013 19.208 7.32699L19.2074 7.32631C19.2061 7.32481 19.2047 7.3233 19.2034 7.32182L19.2008 7.31902L19.1993 7.31738L19.1985 7.31655C19.1972 7.31515 19.1959 7.31374 19.1946 7.31232L19.1922 7.30981L19.1902 7.3077L19.1896 7.30705C19.1882 7.30568 19.1869 7.30429 19.1855 7.30292L19.1846 7.30197C19.1832 7.30056 19.1818 7.29915 19.1804 7.29775L14.0354 2.15277C14.034 2.15138 14.0326 2.14999 14.0312 2.1486L14.0303 2.14768C14.0289 2.14633 14.0275 2.14498 14.0261 2.14364L14.0234 2.14101L14.0217 2.13939L14.0209 2.13861C14.0195 2.13728 14.018 2.13596 14.0166 2.13466L14.0142 2.13237L14.0114 2.12981C14.0099 2.12844 14.0084 2.12711 14.0069 2.12577L14.0062 2.12516C14.0031 2.12235 13.9999 2.11961 13.9968 2.11687L13.9966 2.11674C13.8809 2.01728 13.7326 1.95489 13.5698 1.94793L13.5687 1.94786L13.5665 1.9478L13.5657 1.94775C13.5554 1.94737 13.5451 1.94721 13.5347 1.94728ZM12.8378 3.35046H8.39434C7.58804 3.35046 6.81474 3.67077 6.24458 4.24091C5.67442 4.81107 5.35411 5.58437 5.35411 6.39067V17.6161C5.35411 19.2952 6.71526 20.6563 8.39434 20.6563H13.3402C12.5937 19.8614 12.1362 18.7918 12.1362 17.6161C12.1362 15.1637 14.1272 13.1727 16.5796 13.1727C17.0698 13.1727 17.5416 13.2523 17.9827 13.3992V8.49544H15.4102C13.9895 8.49544 12.8378 7.34371 12.8378 5.92297V3.35046ZM16.5796 14.5759C18.2575 14.5759 19.6198 15.9382 19.6198 17.6161C19.6198 19.2941 18.2575 20.6563 16.5796 20.6563C14.9016 20.6563 13.5393 19.2941 13.5393 17.6161C13.5393 15.9382 14.9016 14.5759 16.5796 14.5759ZM7.45888 12.4711H15.4102L15.4148 12.4711C15.7999 12.4687 16.1118 12.1553 16.1118 11.7695C16.1118 11.3823 15.7975 11.068 15.4102 11.068H7.45888C7.07167 11.068 6.75729 11.3823 6.75729 11.7695C6.75729 12.1568 7.07167 12.4711 7.45888 12.4711ZM7.45888 9.66477H11.4208L11.4253 9.66475C11.8105 9.6623 12.1224 9.34889 12.1224 8.96318C12.1224 8.57595 11.808 8.26159 11.4208 8.26159H7.45888C7.07167 8.26159 6.75729 8.57595 6.75729 8.96318C6.75729 9.35039 7.07167 9.66477 7.45888 9.66477ZM14.2409 4.34266V5.923V5.92785C14.2436 6.57138 14.7661 7.09224 15.4103 7.09226H16.9906L14.2409 4.34266Z" fill="#1A1818"/><path fill-rule="evenodd" clip-rule="evenodd" d="M15.878 16.9397H15.2013C14.8141 16.9397 14.4998 17.2541 14.4998 17.6413C14.4998 18.0286 14.8141 18.3429 15.2013 18.3429H15.878V19.0195C15.878 19.4068 16.1924 19.7211 16.5796 19.7211C16.9668 19.7211 17.2812 19.4068 17.2812 19.0195V18.3429H17.9578C18.345 18.3429 18.6594 18.0286 18.6594 17.6413C18.6594 17.2541 18.345 16.9397 17.9578 16.9397H17.2812V16.2631C17.2812 15.8759 16.9668 15.5615 16.5796 15.5615C16.1924 15.5615 15.878 15.8759 15.878 16.2631V16.9397Z" fill="#EA1B23"/></svg>'))
                                        ],
                                      ),
                                    ),
                                  )
                          ],
                        ),
                      ),
                    );
                  },
                ),
                options: CarouselOptions(
                  autoPlay: false,
                  initialPage: 0,
                  aspectRatio: 1,
                  viewportFraction: 1,
                  height: 454.h,
                  disableCenter: true,
                  enableInfiniteScroll: false,
                  onPageChanged: (index, reason) {
                    print('index: $index');
                    myloanCtl.setIndexMyloan(index);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}