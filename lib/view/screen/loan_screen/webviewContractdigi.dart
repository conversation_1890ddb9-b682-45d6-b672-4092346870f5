import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewContractDigi extends StatefulWidget {
  final url;
  final textappbar;

  WebViewContractDigi(this.url, this.textappbar);
  @override
  createState() => _WebViewContractDigiState(this.url, this.textappbar);
}

class _WebViewContractDigiState extends State<WebViewContractDigi> {
  _WebViewContractDigiState(this._url, this.textappbar);

  var _url;
  var textappbar;
  final _key = UniqueKey();

  late InAppWebViewController inAppWebViewController;
  double _progress = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    return WillPopScope(
      onWillPop: () async {
        var isLastPage = await inAppWebViewController.canGoBack();
        if (isLastPage) {
          inAppWebViewController.goBack();
          return false;
        }
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: appConfigService.countryConfigCollection.toString() == 'aam' ?Color(0xFF8C58F7) : appConfigService.countryConfigCollection.toString() == 'rplc' ?AppColors.primaryRPLC_Yellow:AppColors.primaryRafco,
          title: Text(textappbar),
          centerTitle: true,
          leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(Icons.arrow_back_ios),
          ),
        ),
        body: Stack(
          children: [
            InAppWebView(
              initialUrlRequest: URLRequest(url: WebUri(_url)),
              onWebViewCreated: (InAppWebViewController controller) {
                inAppWebViewController = controller;
              },
              onProgressChanged:
                  (InAppWebViewController controller, int progress) {
                setState(() {
                  _progress = progress / 100;
                });
              },
            ),
            _progress < 1
                ? Container(
                    child: LinearProgressIndicator(
                      value: _progress,
                    ),
                  )
                : SizedBox(),
          ],
        ),
      ),
    );
  }
}
