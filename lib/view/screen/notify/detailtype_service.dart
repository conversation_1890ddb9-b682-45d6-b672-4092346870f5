import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/screen/notify/receipt_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../controller/bill_payment/billPayment.controller.dart';
import '../../../controller/notification/notification.controllet.dart';
import '../../../controller/notification/notification.controllet.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/AppBackgound.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/widgets/componance_widget.dart';
import '../bill_payment/select_payment_screen.dart';
import 'notify_page.dart';

class detail_type_service extends StatefulWidget {
  detail_type_service(
      this.id,
      this.title,
      this.status,
      this.detail,
      this.day,
      this.ctt_code,
      this.nextpay,
      this.index,
      this.idNotification,
      this.readed,
      this.typeNotification,
      this.typeNotiName,
      this.notiImg,
      {super.key});
  final String? id;
  final String? title;
  final String? status;
  final String? detail;
  final String? day;
  final String? ctt_code;
  final String? nextpay;
  final int? index;
  final idNotification;
  final String readed;
  final typeNotification;
  final typeNotiName;
  final notiImg;
  @override
  State<detail_type_service> createState() => _detail_type_serviceState();
}

class _detail_type_serviceState extends State<detail_type_service> {
  @override
  Widget build(BuildContext context) {
    final NotificationController notiCtl = Get.find<NotificationController>();

    String? parts = widget.day;
    final title = widget.title; // เรียกใช้ข้อมูล title จาก widget
    final status = widget.status; // เรียกใช้ข้อมูล status จาก widget
    final detail = widget.detail; // เรียกใช้ข้อมูล detail จาก widget
    final notiImg = widget.notiImg;

    final typeNotification = widget.typeNotification;
    final typeNotiName = widget.typeNotiName;

    final id = widget.id;
    final readed = widget.readed;

    // print('parts => $parts');
    // แยกตามสัญลักษณ์ " – "
    // List<String> date_format = .split(' – ');
    List<String> result1 = notiCtl.splitDateAndTime(parts!.toString());
    // print('result1 => $result1');
    String day = result1.isEmpty ? '' : result1[0] ?? ''; // วันที่
    String time = result1.isEmpty ? '' : result1[1] ?? ''; // เวลา
    // print('day => $day');
    // print('time => $time');

    final index = widget.index; // เรียกใช้ข้อมูล index จาก widget
    final ctt_code = widget.ctt_code; // เรียกใช้ข้อมูล index จาก widget
    final nextpay = widget.nextpay; // เรียกใช้ข้อมูล index จาก widget
    print('nextpay = ${nextpay}');
    print('typeNotification = ${typeNotification}');

    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Container(
              height: Get.height,
              width: Get.width,
              child: GetBuilder<NotificationController>(builder: (notiCtl) {
                return Column(
                  children: [
                    AppBackground.backgroundColorHomePage(context),

                    typeNotification == "Nextpayaut" ||   typeNotification == "myloan"
                        ? loan_billing(id, title, status, detail, day, ctt_code,
                        nextpay, index, time, typeNotification,notiImg,typeNotiName)
                        : typeNotification == "bill"
                        ? loan_receipt(
                        id,
                        payment_completed.tr,
                        status,
                        detail,
                        day,
                        ctt_code,
                        nextpay,
                        index,
                        time,
                        typeNotification,
                        notiImg,typeNotiName)
                        : service_noti(id, title, status, detail, day,
                        ctt_code, index, time, typeNotiName),
                  ],
                );
              }),
            ),
          ),
          headerNoti(context, readed, typeNotiName),
        ],
      ),
    );

  }

  headerNoti(context, readed, typeNotification) {
    return GetBuilder<NotificationController>(builder: (notiCtl) {
      return Stack(
        children: [
          Container(
            width: 375.w,
            height: 136.h,
            padding: EdgeInsets.only(
              top: 56.h,
              left: 12.w,
              right: 12.w,
              bottom: 12.h,
            ),
            color: Colors.white.withOpacity(0.9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: Icon(Icons.arrow_back,
                        color: appConfigService.countryConfigCollection == 'aam'
                            ? null
                            : Colors.black)),
                SizedBox(
                    height: 34.h,
                    width: 34.w,
                    child: SvgPicture.string(
                      AppSvgImage.system_noti,
                      color: appConfigService.countryConfigCollection == 'aam'
                          ? null
                          : configTheme().colorScheme.onSecondary,
                    )
                    // Image.asset('assets/notify/icon.png'),
                    ),
                SizedBox(
                  width: 10.w,
                ),
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    typeNotification,
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: 14,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: Get.width,
            height: 32.h,
          ),
          Container(
            width: 375.w,
            height: 136.h,
            child: ComponanceWidget.buildDivider(),
          ),
        ],
      );
    });
  }
}

Widget loan_billing(id, title, status, detail, day, ctt_code, nextpay, index,
    time, typeNotification, notiImg,typeNotiName) {
  return GetBuilder<NotificationController>(builder: (notiCtl) {
    return SizedBox(
      height: Get.height,
      width: Get.width,
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              height: Get.height,
              width: Get.width,
              child: Obx(
                () => Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ListView.builder(
                      itemCount: notiCtl.notificationList!
                          .where((item) => item.idNotification == id)
                          .length,
                      itemBuilder: (BuildContext context, int index) {
                        // Access the notification at the current index

                        return Container(
                          height: Get.height,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // ยังไม่ได้ชำระ

                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Center(child: Text(day)),
                              ),

                              Column(
                                children: [
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(right: 8.0),
                                        child: Text(time),
                                      ),
                                      buildNotiContainer( context,typeNotification , id, title, status, detail, day, ctt_code,nextpay, index, time,notiImg,typeNotiName)
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      }),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  });
}

Widget loan_receipt(id, title, status, detail, day, ctt_code, nextpay, index,
    time, typeNotification, notiImg,typeNotiName) {
  return GetBuilder<NotificationController>(builder: (notiCtl) {
    return SizedBox(
      height: Get.height,
      width: Get.width,
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              height: Get.height,
              width: Get.width,
              child: Obx(
                () => Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ListView.builder(
                      itemCount: notiCtl.notificationList!
                          .where((item) => item.idNotification == id)
                          .length,
                      itemBuilder: (BuildContext context, int index) {
                        // Access the notification at the current index

                        return Container(
                          height: Get.height,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // ยังไม่ได้ชำระ

                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Center(child: Text(day)),
                              ),

                              Column(
                                children: [
                                  SizedBox(height: 10.h),
                                  // TODO ชำระสำเร็จ
                                  Container(
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(right: 8.0),
                                          child: Text(time),
                                        ),
                                        buildNotiContainer( context,typeNotification , id, title, status, detail, day, ctt_code,nextpay, index, time,notiImg,typeNotiName)
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 50.h),
                                ],
                              ),
                            ],
                          ),
                        );
                      }),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  });
}

Widget service_noti(
    id, title, status, detail, day, ctt_code, index, time, typeNotification) {
  return GetBuilder<NotificationController>(
    builder: (notiCtl) {
      // print(notiCtl.noti_url);
      return SizedBox(
        height: Get.height,
        width: Get.width,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                height: Get.height,
                width: Get.width,
                child: Obx(
                  () => Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ListView.builder(
                        // dragStartBehavior: DragStartBehavior.down,
                        reverse:
                            true, // This will display the most recent notifications at the top
                        itemCount: notiCtl.notificationList!
                            .where((item) => item.idNotification == id)
                            .length,
                        itemBuilder: (BuildContext context, int index) {
                          // var noti_url = notiCtl.sortNotifyLinkURL(detail);
                          // Access the notification at the current index
                          return InkWell(
                            onTap: () async {
                              await notiCtl.sortNotifyLinkURL(detail);
                              print(notiCtl.noti_url.value);
                              if (await canLaunch(notiCtl.noti_url.value!)) {
                                await launch(notiCtl.noti_url.value);
                              }
                            },
                            child: Container(
                              height: Get.height,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  // ยังไม่ได้ชำระ
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Center(child: Text(day)),
                                  ),
                                  Container(
                                    // color: Colors.orange,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(right: 8.0),
                                          child: Text(
                                            time,
                                            style: TextStyle(
                                              color: Color(0xFF1A1818)
                                                  .withOpacity(0.5),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          width: 260.w,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border(
                                              left: BorderSide(
                                                color: configTheme()
                                                    .colorScheme
                                                    .onSecondary
                                                    .withOpacity(0.1),
                                              ),
                                              top: BorderSide(
                                                color: configTheme()
                                                    .colorScheme
                                                    .onSecondary
                                                    .withOpacity(0.1),
                                              ),
                                              right: BorderSide(
                                                color: configTheme()
                                                    .colorScheme
                                                    .onSecondary
                                                    .withOpacity(0.1),
                                              ),
                                              bottom: BorderSide(
                                                width: 1.50.w,
                                                color: configTheme()
                                                    .colorScheme
                                                    .onSecondary
                                                    .withOpacity(0.1),
                                              ),
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.transparent,
                                                blurRadius: 10,
                                                offset: Offset(2, 4),
                                                spreadRadius: 0,
                                              ),
                                            ],
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(16.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 240.w,
                                                  // height: 28.h,
                                                  padding: EdgeInsets.zero,
                                                  child: Text(
                                                    title,
                                                    style: TextStyle(
                                                      color: Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular
                                                          .fontFamily,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  width: 240.w,
                                                  child: Text(
                                                    detail,
                                                    textAlign: TextAlign.start,
                                                    // maxLines: 3,
                                                    // overflow:
                                                    //     TextOverflow.ellipsis,
                                                    style: TextStyle(
                                                      color: Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular
                                                          .fontFamily,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    },
  );
}

class HorizontalDottedLine extends StatelessWidget {
  final double width;
  final double height;
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  const HorizontalDottedLine({
    Key? key,
    required this.width,
    required this.height,
    this.color = Colors.black,
    this.strokeWidth = 0.5,
    this.dashWidth = 5,
    this.dashSpace = 3,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: _HorizontalDottedLinePainter(
          color: color,
          strokeWidth: strokeWidth,
          dashWidth: dashWidth,
          dashSpace: dashSpace,
        ),
      ),
    );
  }
}

class _HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  _HorizontalDottedLinePainter({
    required this.color,
    required this.strokeWidth,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(_HorizontalDottedLinePainter oldDelegate) {
    return false;
  }
}
