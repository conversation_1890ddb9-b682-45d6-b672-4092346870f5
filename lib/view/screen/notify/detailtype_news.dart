import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:get/get.dart';

import '../../../controller/notification/notification.controllet.dart';
import '../../componance/AppBackgound.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/widgets/componance_widget.dart';

class detail_type_news extends StatefulWidget {
  const detail_type_news(this.id, this.title, this.status, this.detail,
      this.day, this.ctt_code, this.index,this.idNotification, this.readed, this.typeNotification,
      {super.key});
  final String? id;
  final String? title;
  final String? status;
  final String? detail;
  final String? day;
  final String? ctt_code;
  final int? index;
  final idNotification;
  final readed;
  final typeNotification;

  @override
  State<detail_type_news> createState() => _detail_type_newsState();
}

class _detail_type_newsState extends State<detail_type_news> {
  @override
  Widget build(BuildContext context) {
    List<String> parts = widget.day!.split(" - ");
    final title = widget.title; // เรียกใช้ข้อมูล title จาก widget
    final status = widget.status; // เรียกใช้ข้อมูล status จาก widget
    final detail = widget.detail; // เรียกใช้ข้อมูล detail จาก widget
    String day = parts[0];
    final typeNotification = widget.typeNotification;

    final id = widget.id;
    final readed = widget.readed;
    String time = parts[1];
    final index = widget.index; // เรียกใช้ข้อมูล index จาก widget
    final ctt_code = widget.ctt_code; // เรียกใช้ข้อมูล index จาก widget

    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: SizedBox(
              height: 954.h,
              child: GetBuilder<NotificationController>(builder: (notiCtl) {
                return Column(
                  children: [
                    AppBackground.backgroundColorHomePage(context),
                    detailBody(id, title, status, detail, day, ctt_code, index,
                        time, typeNotification)
                  ],
                );
              }),
            ),
          ),
          headerNoti(context),
        ],
      ),
    );
  }
  Widget _buildNotiEmptyPage(context) {
    return Container(
      width: Get.width,
      height: Get.height,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 123.w,
            height: 122.h,
            child: Image.asset(AppImageAssets.aam_empty_noti),
          ),
          SizedBox(height: 10.h),
          Container(
            width: 375.w,
            height: 24.h,
            child: Text(
              'ไม่มีรายการแจ้งเตือน',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Color(0xFF1A1818),
                fontSize: 16,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          SizedBox(height: 2.h),
          Container(
            width: 375.w,
            height: 21.h,
            child: Text(
              'ไม่พบข้อมูลแจ้งเตือนในหมวดนี้',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Color(0xFF1A1818),
                fontSize: 14,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  headerNoti(context) {
    return GetBuilder<NotificationController>(builder: (notiCtl) {
      return Stack(
        children: [
          Container(
            width: 375.w,
            height: 136.h,
            padding: EdgeInsets.only(
              top: 56.h,
              left: 12.w,
              right: 12.w,
              bottom: 12.h,
            ),
            color: Colors.white.withOpacity(0.9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: Icon(Icons.arrow_back)),
                SizedBox(height: 34.h,width: 34.w, child:
                Image.asset('assets/notify/icon3.png')

                  ,),
                SizedBox(width: 10.w,),
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    'รายละเอียด',
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: 14,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: Get.width,
            height: 32.h,
          ),
          Container(
            width: 375.w,
            height: 136.h,
            child: ComponanceWidget.buildDivider(),
          ),
        ],
      );
    });
  }

  Widget detailBody(
      id, title, status, detail, day, ctt_code, index, time, typeNotification) {
    return GetBuilder<NotificationController>(builder: (notiCtl) {
      return SizedBox(
        height: Get.height,
        width: Get.width,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                height: Get.height,
                width: Get.width,
                child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 140),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Image.asset('assets/notify/Rectangle 2227.jpg'),
                              SizedBox(height: 10.h,),
                              Text(
                                '$day',
                                textAlign: TextAlign.start,
                                maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 12,
                                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '$title',
                                    textAlign: TextAlign.start,
                                    maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  SizedBox(height: 10.h,),
                                  Text(
                                    '$detail',
                                    textAlign: TextAlign.start,
                                    maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  SizedBox(height: 20.h,),
                                ],
                              ),
                              Text(
                                '*เอเอเอ็มจัดไฟแนนซ์ เป็นสินเชื่อที่ถูกต้องตามกฎหมาย และอยู่ภายใต้การกำกับของธนาคารแห่งประเทศไทย',
                                textAlign: TextAlign.start,
                                maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: Color(0xFF792AFF),
                                  fontSize: 14,
                                  fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),

                              SizedBox(height: 20.h,),
                              Row(children: [


                                Text(
                                  'ติดต่อช่องทางอื่นๆ ได้ที่',
                                  textAlign: TextAlign.start,
                                  maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 14,
                                    fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                SizedBox(width: 10.w,),
                                InkWell(
                                  onTap: () {
                                    // ทำสิ่งที่ต้องการเมื่อปุ่มถูกกด
                                  },
                                  child: Text(
                                    'คลิ๊ก!',
                                    textAlign: TextAlign.start,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Color(0xFF792AFF),
                                      fontSize: 16,
                                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      decoration: TextDecoration.underline,
                                      decorationThickness: 2.0, // ความหนาของเส้นใต้
                                      decorationColor: Color(0xFF792AFF),// สีของเส้นใต้
                                    ),
                                  ),
                                )





                              ],)

                            ],
                          ),
                        ),

                      ],
                    )),
              ),
            ],
          ),
        ),
      );
    });
  }
}