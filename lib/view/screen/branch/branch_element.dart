import 'dart:async';

import 'package:AAMG/controller/branch/branch.controller.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:flutter/services.dart';

import '../../../controller/transalation/transalation.controller.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppImageAssets.dart';

class BranchDetails extends StatelessWidget {
  final int index;
  final BitmapDescriptor mapIcon;

  BranchDetails({
    Key? key,
    required this.index,
    required this.mapIcon,
  }) : super(key: key);

  late GoogleMapController mapController;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BranchController>(
        autoRemove: true,
        builder: (branchController) {
          return Container(
            height: 136.h,
            width: 327.w,
            margin: EdgeInsets.only(bottom: 12.h),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFFFFFFFF).withOpacity(0.9),
                  const Color(0xFFFFFFFF).withOpacity(1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: configTheme().colorScheme.primary.withOpacity(0.1),
                width: 1,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x0C000000),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Container(
              // margin: EdgeInsets.only(left: 14.w, right: 14.w),
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //TODO รายละเอียดสาขา
                  SizedBox(
                    width: 213.w,
                    height: 112.h,
                    child: Column(
                      children: [
                        SizedBox(
                          height: 22.h,
                          child: Row(
                            children: [
                              SizedBox(
                                width: 16.w,
                                child: SvgPicture.string(
                                  appConfigService.countryConfigCollection ==
                                          'aam'
                                      ? AppSvgImage.branch_pin_aam
                                      : appConfigService
                                                  .countryConfigCollection ==
                                              'rplc'
                                          ? AppSvgImage.branch_pin_rplc
                                          : AppSvgImage.branch_pin_rafco,
                                ),
                              ),
                              SizedBox(
                                width: 4.w,
                              ),
                              Text(
                                Get.find<TransalationController>()
                                            .location
                                            .value ==
                                        'English'
                                    ? branchController
                                        .branchDataList![index].branchNameEn
                                    : Get.find<TransalationController>()
                                                .location
                                                .value ==
                                            'Thailand'
                                        ? branchController
                                            .branchDataList![index].branchNameTh
                                        : branchController
                                            .branchDataList![index].branchName,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium!.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontWeight,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 6.h,
                        ),
                        SizedBox(
                          height: 84.h,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 40.h,
                                child: Text(
                                  Get.find<TransalationController>()
                                              .location
                                              .value ==
                                          'English'
                                      ? branchController
                                          .branchDataList![index].addressEn
                                      : Get.find<TransalationController>()
                                                  .location
                                                  .value ==
                                              'Thailand'
                                          ? branchController
                                              .branchDataList![index].addressTh
                                          : branchController
                                              .branchDataList![index].address,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium!
                                        .color!
                                        .withOpacity(0.5),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                  ),
                                  textAlign: TextAlign.start,
                                ),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              //TODO ติดต่อสาขา่
                              appConfigService.countryConfigCollection
                                          .toString() ==
                                      'aam'
                                  ? BranchElementWidget.BranchContractAAM(
                                      context, index)
                                  : appConfigService
                                              .countryConfigCollection
                                              .toString() ==
                                          'rplc'
                                      ? BranchElementWidget.BranchContractRPLC(
                                          context, index)
                                      : BranchElementWidget.BranchContractRAFCO(
                                          context, index)
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 6.w,
                  ),
                  //TODO map
                  ///// AI generate
                  // Container(
                  //   color: Colors.white,
                  //   width: 80.w,
                  //   height: 80.h,
                  //   child: Center(
                  //     child: Stack(
                  //       children: [
                  //         InkWell(
                  //           onTap: () {
                  //             launchUrl(
                  //               Uri.parse(
                  //                   'https://www.google.com/maps/search/?api=1&query=${branchController.branchDataList![index].latitude},${branchController.branchDataList![index].longitude}'),
                  //             );
                  //           },
                  //           child: AspectRatio(
                  //             aspectRatio: 1, // รักษาสัดส่วน 1:1
                  //             child: Container(
                  //               margin: EdgeInsets.all(16.0),
                  //               decoration: BoxDecoration(
                  //                 shape: BoxShape.circle,
                  //                 image: const DecorationImage(
                  //                   image: NetworkImage(
                  //                     'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png',
                  //                   ),
                  //                   fit: BoxFit.cover,
                  //                 ),
                  //               ),
                  //               child: ClipOval(
                  //                 child: GoogleMap(
                  //                   key: UniqueKey(),
                  //                   initialCameraPosition: CameraPosition(
                  //                     target: LatLng(
                  //                       double.parse(branchController
                  //                           .branchDataList![index].latitude),
                  //                       double.parse(branchController
                  //                           .branchDataList![index].longitude),
                  //                     ),
                  //                     zoom: 12,
                  //                   ),
                  //                   markers: {
                  //                     Marker(
                  //                       markerId: const MarkerId("1"),
                  //                       icon: mapIcon,
                  //                       position: LatLng(
                  //                         double.parse(branchController
                  //                             .branchDataList![index].latitude),
                  //                         double.parse(branchController
                  //                             .branchDataList![index].longitude),
                  //                       ),
                  //                       infoWindow: InfoWindow(
                  //                         title:
                  //                         "${branchController.branchDataList![index].branchName}",
                  //                         snippet: "กดที่นี่เพื่อนำทาง",
                  //                         onTap: () {
                  //                           launchUrl(
                  //                             Uri.parse(
                  //                                 'https://www.google.com/maps/search/?api=1&query=${branchController.branchDataList![index].latitude},${branchController.branchDataList![index].longitude}'),
                  //                           );
                  //                         },
                  //                       ),
                  //                     ),
                  //                   },
                  //                   onMapCreated: (GoogleMapController controller) {
                  //                     print('onMapCreated');
                  //                   },
                  //                   zoomControlsEnabled: false,
                  //                   myLocationButtonEnabled: false,
                  //                   compassEnabled: false,
                  //                   rotateGesturesEnabled: false,
                  //                   scrollGesturesEnabled: false,
                  //                   tiltGesturesEnabled: false,
                  //                   zoomGesturesEnabled: false,
                  //                   mapToolbarEnabled: false,
                  //                   mapType: MapType.normal,
                  //                 ),
                  //               ),
                  //             ),
                  //           ),
                  //         ),
                  //         Positioned(
                  //           top: 20.h,
                  //           right: 20.w,
                  //           child: InkWell(
                  //             onTap: () {
                  //               launchUrl(
                  //                 Uri.parse(
                  //                     'https://www.google.com/maps/search/?api=1&query=${branchController.branchDataList![index].latitude},${branchController.branchDataList![index].longitude}'),
                  //               );
                  //             },
                  //             child: SvgPicture.string(
                  //               appConfigService.countryConfigCollection.toString() == 'aam'
                  //                   ? AppSvgImage.map_option_aam
                  //                   : appConfigService.countryConfigCollection.toString() ==
                  //                   'rplc'
                  //                   ? AppSvgImage.map_option_rplc
                  //                   : AppSvgImage.map_option_rafco,
                  //               fit: BoxFit.contain,
                  //             ),
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //   ),
                  // )

                  Container(
                    color: Colors.white,
                    height: Get.height,
                    width: 80.w,
                    child: Stack(
                      children: [
                        InkWell(
                          onTap: () {
                            launchUrl(Uri.parse(
                                'https://www.google.com/maps/search/?api=1&query=${branchController.branchDataList![index].latitude},${branchController.branchDataList![index].longitude}'));
                          },
                          child: Container(
                            width: 80.w,
                            height: 80.h,
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(top: 16.h),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(80.r),
                              ),
                              image: const DecorationImage(
                                image: AssetImage(AppImageAssets
                                    .map_loading), // Place static image URL
                                fit: BoxFit.cover,
                              ),
                            ),
                            child: ClipOval(
                              clipBehavior: Clip.antiAlias,
                              child: GoogleMap(
                                key: UniqueKey(),
                                style: "map",
                                initialCameraPosition: CameraPosition(
                                  target: LatLng(
                                    double.parse(branchController
                                        .branchDataList![index].latitude),
                                    double.parse(branchController
                                        .branchDataList![index].longitude),
                                  ),
                                  zoom: 12,
                                ),
                                markers: {
                                  Marker(
                                      markerId: const MarkerId("1"),
                                      icon: mapIcon,
                                      // BitmapDescriptor.bytes(
                                      //     branchController.customIcon!
                                      //     as Uint8List),
                                      position: LatLng(
                                          double.parse(branchController
                                              .branchDataList![index].latitude),
                                          double.parse(branchController
                                              .branchDataList![index]
                                              .longitude)),
                                      infoWindow: InfoWindow(
                                          title:
                                              "${branchController.branchDataList![index].branchName}	",
                                          snippet: "กดที่นี่เพื่อนำทาง",
                                          onTap: () {
                                            launchUrl(Uri.parse(
                                                'https://www.google.com/maps/search/?api=1&query=${branchController.branchDataList![index].latitude},${branchController.branchDataList![index].longitude}'));
                                          })),
                                },
                                onMapCreated: (GoogleMapController controller) {
                                  // _controller.complete(controller);
                                  print('onMapCreated');
                                },
                                // padding: EdgeInsets.only(
                                //   top: 200, // เพิ่ม padding เพื่อเลื่อนโลโก้ลง
                                // ),
                                zoomControlsEnabled: false,
                                myLocationButtonEnabled: false,
                                compassEnabled: false,
                                rotateGesturesEnabled: false,
                                scrollGesturesEnabled: false,
                                tiltGesturesEnabled: false,
                                zoomGesturesEnabled: false,
                                mapToolbarEnabled: false,
                                mapType: MapType.normal,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            launchUrl(Uri.parse(
                                'https://www.google.com/maps/search/?api=1&query=${branchController.branchDataList![index].latitude},${branchController.branchDataList![index].longitude}'));
                          },
                          child: Container(
                            margin: EdgeInsets.only(top: 48.h, left: 48.w),
                            alignment: Alignment.bottomRight,
                            child: Center(
                              child: SvgPicture.string(
                                appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? AppSvgImage.map_option_aam
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? AppSvgImage.map_option_rplc
                                        : AppSvgImage.map_option_rafco,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });

    //   Container(
    //   width: 327.w,
    //   height: 136.h,
    //   child:
    //   GoogleMap(
    //     onMapCreated: (GoogleMapController controller) {
    //       mapController = controller;
    //     },
    //     initialCameraPosition: CameraPosition(
    //       target: LatLng(13.7563, 100.5018), // พิกัดเริ่มต้น
    //       zoom: 12.0,
    //     ),
    //     markers: {
    //       Marker(
    //         markerId: MarkerId('customMarker'),
    //         position: LatLng(13.7563, 100.5018), // พิกัด
    //         icon: customIcon,
    //         infoWindow: InfoWindow(
    //           title: 'Custom Marker',
    //           snippet: 'This is a custom icon',
    //         ),
    //       ),}),
    // );
  }
}

class BranchElementWidget {
  Future<void> loadCustomIcon() async {
    final BranchController branchController = Get.find<BranchController>();
    final BitmapDescriptor bitmapIcon = await BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(
          size: Size(48, 48)), // Size can be adjusted as needed
      'assets/menu_icon/aam/branch.png',
    );

    branchController.customIcon = bitmapIcon;
    branchController.update();
  }

  Future<BitmapDescriptor> getCustomMarker() {
    return BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(48, 48)), // Resize as needed
      AppSvgImage.aampay_icon,
    );
  }

  static BranchContractAAM(context, int index) {
    return SizedBox(
      height: 32.h,
      child: Row(
        children: [
          InkWell(
            onTap: () {
              launch(
                  "tel:+66${Get.find<BranchController>().branchDataList![index].branchPhone.substring(1)}");
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_phone_aam,
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch(
                  'https://line.me/R/ti/p/~${Get.find<BranchController>().branchDataList![index].lineId}')) {
                await launch(
                    'https://line.me/R/ti/p/~${Get.find<BranchController>().branchDataList![index].lineId}');
              } else {
                await launch(
                    'https://line.me/R/ti/p/~${Get.find<BranchController>().branchDataList![index].lineId}');
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_line_aam,
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              final branchController = Get.find<BranchController>();
              if (branchController.branchDataList == null ||
                  branchController.branchDataList!.isEmpty ||
                  branchController.branchDataList!.length <= index) {
                print("Invalid index or branch data");
                return;
              }

              // เอา URL ข้อมูลจาก controller มาใช้
              String facebookUrl = branchController
                  .branchDataList![index].facebookUrl
                  .toString();
              // แปลง URL สำหรับ Messenger
              String messengerUrl = facebookUrl.replaceFirst(
                  "https://www.facebook.com", "https://m.me");

              // เช็คและเปิด URL
              if (await canLaunch(messengerUrl)) {
                await launch(messengerUrl);
              } else {
                print(
                    "Could not launch Messenger URL, falling back to Facebook URL");
                if (await canLaunch(facebookUrl)) {
                  await launch(facebookUrl);
                } else {
                  print("Could not launch Facebook URL");
                }
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_messager_aam,
                fit: BoxFit.contain,
              ),
            ),
          ),
          //tiktok
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch(Get.find<BranchController>()
                      .branchDataList![index]
                      .tiktokUrl
                      .toString() ??
                  "https://www.tiktok.com/@aam.financing")) {
                await launch(Get.find<BranchController>()
                    .branchDataList![index]
                    .tiktokUrl
                    .toString());
              } else {
                await launch(Get.find<BranchController>()
                    .branchDataList![index]
                    .tiktokUrl
                    .toString());
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_tiktok_aam,
                fit: BoxFit.contain,
              ),
            ),
          ),
          //youtube
          // SizedBox(  width: 4.w, ),
          // InkWell( onTap: () async {   },
          //   child: SizedBox(
          //     width: 32.w,
          //     height: 32.h,
          //     child: SvgPicture.string(
          //       AppSvgImage.contract_youtube_aam,
          //       fit: BoxFit.contain,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  static BranchContractRPLC(context, int index) {
    return SizedBox(
      height: 32.h,
      child: Row(
        children: [
          InkWell(
            onTap: () {
              launch(
                  "tel:+856${Get.find<BranchController>().branchDataList![index].branchPhone.substring(1)}");
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_phone_rplc,
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch(
                  "https://wa.me/${Get.find<BranchController>().branchDataList![index].whatsapp_id}")) {
                await launch(
                    "https://wa.me/${Get.find<BranchController>().branchDataList![index].whatsapp_id}");
              } else {
                await launch(
                    "https://wa.me/${Get.find<BranchController>().branchDataList![index].whatsapp_id}");
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_whatsapp_rplc,
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch("https://m.me/rplcrpm")) {
                await launch("https://m.me/rplcrpm");
              } else {
                await launch("https://www.facebook.com/rplcrpm");
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_messager_rplc,
                fit: BoxFit.contain,
              ),
            ),
          ),
          //tiktok
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch(Get.find<BranchController>()
                      .branchDataList![index]
                      .tiktokUrl
                      .toString() ??
                  "")) {
                await launch(Get.find<BranchController>()
                    .branchDataList![index]
                    .tiktokUrl
                    .toString());
              } else {
                await launch(Get.find<BranchController>()
                    .branchDataList![index]
                    .tiktokUrl
                    .toString());
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_tiktok_rplc,
                fit: BoxFit.contain,
              ),
            ),
          ),
          //youtube
          // SizedBox(  width: 4.w, ),
          // InkWell( onTap: () async {   },
          //   child: SizedBox(
          //     width: 32.w,
          //     height: 32.h,
          //     child: SvgPicture.string(
          //       AppSvgImage.contract_youtube_rplc,
          //       fit: BoxFit.contain,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  static BranchContractRAFCO(context, int index) {
    return SizedBox(
      height: 32.h,
      child: Row(
        children: [
          InkWell(
            onTap: () {
              launch(
                  "tel:+855${Get.find<BranchController>().branchDataList![index].branchPhone.substring(1)}");
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_phone_rafco,
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch(
                  'https://t.me/${Get.find<BranchController>().branchDataList![index].telegram_id}')) {
                await launch(
                    'https://t.me/${Get.find<BranchController>().branchDataList![index].telegram_id}');
              } else {
                await launch(
                    'https://t.me/${Get.find<BranchController>().branchDataList![index].telegram_id}');
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_TG_rafco,
                fit: BoxFit.contain,
              ),
            ),
          ),
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch("https://m.me/RAFCOMICROFINANCE")) {
                await launch("https://m.me/RAFCOMICROFINANCE");
              } else {
                await launch("https://www.facebook.com/RAFCOMICROFINANCE");
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_messager_rafco,
                fit: BoxFit.contain,
              ),
            ),
          ),
          //tiktok
          SizedBox(
            width: 4.w,
          ),
          InkWell(
            onTap: () async {
              if (await canLaunch(Get.find<BranchController>()
                      .branchDataList![index]
                      .tiktokUrl
                      .toString() ??
                  "")) {
                await launch(Get.find<BranchController>()
                    .branchDataList![index]
                    .tiktokUrl
                    .toString());
              } else {
                await launch(Get.find<BranchController>()
                    .branchDataList![index]
                    .tiktokUrl
                    .toString());
              }
            },
            child: SizedBox(
              width: 32.w,
              height: 32.h,
              child: SvgPicture.string(
                AppSvgImage.contract_tiktok_rafco,
                fit: BoxFit.contain,
              ),
            ),
          ),
          //youtube
          // SizedBox(  width: 4.w, ),
          // InkWell( onTap: () async {   },
          //   child: SizedBox(
          //     width: 32.w,
          //     height: 32.h,
          //     child: SvgPicture.string(
          //       AppSvgImage.contract_youtube_rafco,
          //       fit: BoxFit.contain,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
