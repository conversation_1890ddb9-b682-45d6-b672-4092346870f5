import 'package:AAMG/controller/branch/branch.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/widgets/header_widgets/header_general.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/app_pop_up/branch_popups/branch_area.dart';
import 'branch_element.dart';

class BranchScreen extends StatefulWidget {
  BranchScreen({super.key});

  @override
  State<BranchScreen> createState() => _BranchScreenState();
}

class _BranchScreenState extends State<BranchScreen> {
  // var customMarkerIcon = BitmapDescriptor.defaultMarker;
  final BranchController branch = Get.put(BranchController());

  // Future<void> _loadCustomMarker() async {
  BitmapDescriptor customIcon = BitmapDescriptor.defaultMarker;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () {
      _setCustomMarker();
    });
  }

  void _setCustomMarker() async {
    customIcon = await BitmapDescriptor.asset(ImageConfiguration(size: Size(30, 30)),
          appConfigService.countryConfigCollection == 'aam'
              ? 'assets/branch/marker_aam.png'
              : appConfigService.countryConfigCollection == 'rplc'
              ? 'assets/branch/marker_rplc.png'
              : 'assets/branch/marker_rafco.png' // path ไปยังไฟล์ไอคอน
         );

    // fromAssetImage(
    //    ImageConfiguration(size: Size(100, 100)), // ขนาดไอคอน
    //   appConfigService.countryConfigCollection == 'aam'
    //       ? 'assets/branch/marker_aam.png'
    //       : appConfigService.countryConfigCollection == 'rplc'
    //       ? 'assets/branch/marker_rplc.png'
    //       : 'assets/branch/marker_rafco.png', // path ไปยังไฟล์ไอคอน
    // );
  }

  @override
  Widget build(BuildContext context) {
    // Get.put(AppConfigController()).accessLocation(context);
    return Scaffold(
        body: RefreshIndicator(
          onRefresh: () async {
            branch.clearConfigData();
            branch.getBranchData();
          },
          child: Stack(
            children: [
              GetBuilder<BranchController>(
                  init: BranchController(),
                  autoRemove: true,
                  builder: (branchController) {
                    print(branchController.branchDataList!.length);
                    return Container(
                      height: Get.height - 106.h,
                      width: Get.width,
                      margin: EdgeInsets.only(
                          top: 106.h, left: 24.w, right: 24.w),
                      child: Column(
                        children: [
                          //TODO search branch
                          SizedBox(
                            height: 48.h,
                            width: Get.width,
                            child: Row(
                              children: [
                                Expanded(
                                  child: Center(
                                    child: TextFormField(
                                      controller:
                                      branchController.searchController.value,
                                      focusNode: branchController.searchFocusNode,
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                        decoration : TextDecoration.none,

                                      ),
                                      keyboardType: TextInputType.text,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(
                                                r'[a-zA-Z\u0E00-\u0E7F\u0E80-\u0EFF\u1780-\u17FF]')),
                                        LengthLimitingTextInputFormatter(30),
                                      ],
                                      decoration: InputDecoration(
                                        contentPadding: EdgeInsets.only(
                                            left: 16.w,
                                            top: 13.5.h,
                                            bottom: 13.5.h),
                                        border: InputBorder.none,
                                        prefixIcon: Padding(
                                          padding: const EdgeInsets.all(13.0),
                                          child: SvgPicture.string(
                                              AppSvgImage.search_icon,
                                              width: 22.w,
                                              height: 22.h),
                                        ),
                                        labelStyle: TextStyle(
                                          color: const Color(0xFF1A1818)
                                              .withOpacity(0.5),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontWeight,
                                          decoration: TextDecoration.none,
                                        ),
                                        hintText: searchBranchTitle.tr,
                                        hintStyle: TextStyle(
                                          color: const Color(0xFF1A1818)
                                              .withOpacity(0.5),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontWeight,
                                          decoration: TextDecoration.none,
                                        ),
                                        disabledBorder: OutlineInputBorder(
                                          borderRadius:
                                          BorderRadius.circular(14.0.r),
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(14.0.r),
                                          borderSide: BorderSide.none,
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                          BorderRadius.circular(14.0.r),
                                          borderSide: BorderSide(
                                              width: 1.w,
                                              color: appConfigService
                                                  .countryConfigCollection ==
                                                  'aam'
                                                  ? AppColors.AAMPurpleSolid
                                                  : appConfigService
                                                  .countryConfigCollection ==
                                                  'rplc'
                                                  ? AppColors.primaryRPLC_Yellow
                                                  : AppColors.primaryRafco),
                                        ),
                                        fillColor: const Color(0xFFF9F9F9),
                                        filled: true,
                                      ),
                                      onTap: () {
                                        branchController
                                            .setActivateSearchZone(true);
                                      },
                                      onChanged: (value) {
                                        print("sddsdsddsddsd");
                                        print(value);
                                        if (value.length >= 3) {
                                          branchController.searchBranchData(
                                              value);
                                        } else if (value
                                            .toString()
                                            .length == 0 ||
                                            value.isEmpty || value == "") {
                                          print("value is empty");
                                          branchController
                                              .setIntialBranchData();
                                        }
                                      },
                                    ),
                                  ),
                                ),
                                SizedBox(
                                    width:
                                    8.w), // Optional spacing between elements
                                appConfigService.countryConfigCollection
                                    .toString() ==
                                    'aam'
                                    ? InkWell(
                                  onTap: () async {
                                    debugPrint("do something");
                                    branchController
                                        .setActivateSelectZone(true);
                                    final result =
                                    await AreaPopUp.AlertBranchArea(
                                        context);

                                    if (result == null) {
                                      branchController
                                          .setActivateSelectZone(false);
                                    }
                                  },
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Container(
                                    width: 48.w,
                                    height: 48.h,
                                    decoration: BoxDecoration(
                                      // color: Colors.grey.shade100,
                                      borderRadius:
                                      BorderRadius.circular(8.0),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(13.0),
                                      child: SvgPicture.string(
                                        AppSvgImage.filter_icon,
                                        width: 22.w,
                                        height: 22.h,
                                        color: branchController
                                            .isActiveSelectZone.value
                                            ? AppColors.AAMPurpleSolid
                                            : AppColors.textBlackColor,
                                      ),
                                    ),
                                  ),
                                )
                                    : const SizedBox(),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 14.h,
                          ),
                          Container(
                            height: Get.height - 168.h,
                            width: Get.width,
                            child:
                            // ListView.builder(
                            //   padding: EdgeInsets.zero,
                            //   itemCount: (branchController.branchDataList!.length <= branchController.itemsToShow.value
                            //       ? branchController.branchDataList!.length
                            //       : branchController.itemsToShow.value + 1),
                            //   itemBuilder: (context, index) {
                            //     if (index == branchController.itemsToShow.value &&
                            //         branchController.branchDataList!.length > index) {                            // ตรวจสอบว่าเป็นตำแหน่งของ "See More"
                            //       return TextButton(
                            //         onPressed: () {
                            //           branchController.itemsToShow.value += 10; // แสดงเพิ่มอีก 10 รายการ
                            //         },
                            //         child: Text('See More'),
                            //       );
                            //     }
                            //     return BranchElementWidget.BranchDetail(context, index);
                            //   },
                            // )
                            ListView.builder(
                                padding: EdgeInsets.zero,
                                itemCount:
                                branchController.branchDataList!.length,
                                itemBuilder: (context, index) {
                                  // return BranchElementWidget.BranchDetail(
                                  //     context, index);
                                  return BranchDetails(index: index, mapIcon: customIcon);
                                  // return FutureBuilder<Widget>(
                                  //   future: BranchElementWidget.BranchDetail(context, index),
                                  //   builder: (context, snapshot) {
                                  //     if (snapshot.connectionState == ConnectionState.done) {
                                  //       if (snapshot.hasError) {
                                  //         return Text('Error: ${snapshot.error}');
                                  //       } else {
                                  //         return snapshot.data ?? SizedBox.shrink();
                                  //       }
                                  //     } else {
                                  //       return CircularProgressIndicator();
                                  //     }
                                  //   },
                                  // );
                                }),
                          ),
                        ],
                      ),
                    );
                  }),
              HeaderGeneral(
                title: branchMenu.tr,
                firstIcon: SizedBox(
                    width: 24.w,
                    height: 24.h,
                    child: SvgPicture.string(AppSvgImage.back_btn)),
                secondIcon: SizedBox(width: 24.w, height: 24.h),
                firstOnPressed: () {
                  Get.back();
                },
                secondOnPressed: () {},
              )
            ],
          ),
        ));
  }
}
