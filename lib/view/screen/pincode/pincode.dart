import 'package:AAMG/controller/login/login.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/pincode/pincode.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:get_storage/get_storage.dart';

import '../../responsive/responsive_layout.dart';

class PinCodePage extends StatefulWidget {
  PinCodePage({Key? key}) : super(key: key);

  @override
  State<PinCodePage> createState() => _PinCodePageState();
}

class _PinCodePageState extends State<PinCodePage> {

  var Phone = GetStorage().read('phone_firebase');
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () {
      Get.put(PincodeController().checkBiometricLogin());
    });
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayout(
        builder: (context,isTablet) {
          debugPrint("## tablet : ${isTablet}");
        return Scaffold(
          backgroundColor: configTheme().colorScheme.background,
          body: GetBuilder<PincodeController>(
            init: PincodeController(),
              builder: (pincodeCtl) {
                // pincodeCtl.checkBiometricLogin();
                // print(pincodeCtl.isBackIn!.value);
                // print(pincodeCtl.stepPincode!.value);
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Center(
                  child: Container(
                    width: 73.90.w,
                    height: 36.h,
                    margin: EdgeInsets.only(top: 81.h),
                    child: Image.asset(
                      'assets/register/icon/pincode.png',
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
                Container(
                    height: 55,
                    // width: 163,
                    margin: EdgeInsets.only(top: 28.h),
                    child: Center(
                      child: FittedBox(
                        fit: isTablet ? BoxFit.scaleDown :BoxFit.contain,
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: pincodeCtl.isBackIn!.value
                                    ? "${backInWelcome.tr} "
                                    : pincodeCtl.stepPincode.value == 1
                                        ? '${signUpCreatePassword.tr}\n'
                                        : pincodeCtl.isResetPin!.value
                                            ? '${signUpCreatePassword.tr}\n'
                                            : '${signUpConfirmPassword.tr}\n',
                                style: TextStyle(
                                  color: configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .displayMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .displayMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .displayMedium
                                      ?.fontWeight,
                                  height: 0.07.h,
                                ),
                              ),
                              const TextSpan(
                                text: '\n\n',
                              ),
                              TextSpan(
                                text: pincodeCtl.isBackIn!.value
                                    ? backInDescription.tr
                                    : pincodeCtl.stepPincode.value == 2
                                        ? signUpConfirmPass.tr
                                        : signUpCreatePasswordDes.tr,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.5),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                  height: 0.18.h,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )),

                // TODO dot pincode
                Container(
                  // width: 200,
                  height: 25.h,
                  margin: EdgeInsets.only(top: 32.h),
                  child: Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        for (int i = 1; i <= 6; i++)
                          Obx(() {
                            return Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                pincodeCtl.alertPasscode!.value &&
                                        pincodeCtl.passcode!.value.isEmpty
                                    ? Container(
                                        width: isTablet ? 25 : 25.w,
                                        height: isTablet ? 25 : 25.h,
                                        // padding: const EdgeInsets.all(5),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Container(
                                              width: isTablet ? 15 : 15.w,
                                              height: isTablet ? 15 : 15.h,
                                              decoration: ShapeDecoration(
                                                shape: OvalBorder(
                                                  side: BorderSide(
                                                      width: 1,
                                                      color: configTheme()
                                                          .colorScheme
                                                          .error),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : pincodeCtl.passcode!.value.length >= i
                                        ? Container(
                                            width: isTablet ? 25 : 25.w,
                                            height: isTablet ? 25 : 25.h,
                                            // padding: const EdgeInsets.all(5),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Container(
                                                  width: isTablet ? 15 : 15.w,
                                                  height: isTablet ? 15 : 15.h,
                                                  decoration: ShapeDecoration(
                                                    color: configTheme()
                                                        .colorScheme
                                                        .onPrimary,
                                                    shape: OvalBorder(
                                                        side: BorderSide(
                                                      width: 1.50,
                                                      color: configTheme()
                                                          .colorScheme
                                                          .onPrimary,
                                                    )),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        : Container(
                                            width: isTablet ? 25 : 25.w,
                                            height: isTablet ? 25 : 25.h,
                                            // padding: const EdgeInsets.all(5),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Container(
                                                  width: isTablet ? 15 : 15.w,
                                                  height: isTablet ? 15 : 15.h,
                                                  decoration: ShapeDecoration(
                                                    shape: OvalBorder(
                                                      side: BorderSide(
                                                        width: 1,
                                                        color: configTheme()
                                                            .colorScheme
                                                            .onPrimary,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                i == 6
                                    ? const SizedBox()
                                    : SizedBox(
                                        width: 10.w,
                                        // height: 46.h,
                                      ),
                              ],
                            );
                          })
                      ],
                    ),
                  ),
                ),

                //TODO alert warning pincode
                Obx(() {
                  return pincodeCtl.alertPasscode!.value &&
                          pincodeCtl.passcode!.value.isEmpty
                      ? Container(
                          height: 20.h,
                          margin: EdgeInsets.only(top: 20.h),
                          child: Text(
                            signUpConfirmPasswordIncorrect.tr,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: configTheme().colorScheme.error,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                              height: 0.14.h,
                            ),
                          ),
                        )
                      : const SizedBox();
                }),

                //TODO NUMBER
                Container(
                  width: 275.w,
                  height: 300.h,
                  margin: EdgeInsets.only(top: 20.h),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PincodeKeyboard(),
                    ],
                  ),
                ),

                //TODO pincode detail
                Obx(() => pincodeCtl.isResetPin!.value &&
                        pincodeCtl.isResetPinSuccess!.value == false
                    ? Container(
                        width: 327.w,
                        padding: const EdgeInsets.only(bottom: 18),
                        decoration: ShapeDecoration(
                          gradient: AppColorsGradient.whiteGradient,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              width: 0.50.w,
                              color: configTheme().colorScheme.onSecondary,
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        child: Container(
                          margin: EdgeInsets.only(top: 18.h, left: 20.w),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                alignment: Alignment.centerLeft,
                                height: 24.h,
                                child: Text(
                                  backInResetPin.tr,
                                  style: TextStyle(
                                    color:
                                        configTheme().textTheme.bodyMedium?.color,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .bodyLarge
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .bodyLarge
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .bodyLarge
                                        ?.fontWeight,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              Container(
                                height: 48.h,
                                alignment: Alignment.centerLeft,
                                child: RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: "${backInSendOTP.tr}",
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                          height: appConfigService.countryConfigCollection == 'aam'? 0 : 0.17,
                                        ),
                                      ),
                                      TextSpan(text: "\n"),
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text: backInSendPhone.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontWeight,
                                              height: 0.17,
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                                "${Phone.substring(0, 5)}-${Phone.substring(5, Phone.length)}",
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelMedium
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelMedium
                                                  ?.fontWeight,
                                              height: appConfigService.countryConfigCollection == 'aam'? 0 : 0.17,
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(height: 14.h),
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      pincodeCtl.isResetPin!.value = false;
                                      pincodeCtl.update();
                                    },
                                    child: Container(
                                      width: 83.w,
                                      height: 42.h,
                                      decoration: ShapeDecoration(
                                        shape: RoundedRectangleBorder(
                                          side: BorderSide(
                                            width: 1.w,
                                            strokeAlign:
                                                BorderSide.strokeAlignOutside,
                                            color: configTheme()
                                                .colorScheme
                                                .onSecondary
                                                .withOpacity(0.75),
                                          ),
                                          borderRadius: BorderRadius.circular(6.r),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          backInCancel.tr,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: configTheme()
                                                .colorScheme
                                                .onSecondary
                                                .withOpacity(0.75),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 16.w),
                                  InkWell(
                                    onTap: () {
                                      pincodeCtl.sendOtpResetPin(context);
                                    },
                                    child: Container(
                                      width: 190.w,
                                      height: 42.h,
                                      decoration: ShapeDecoration(
                                        color:
                                            configTheme().colorScheme.onSecondary,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(6.r)),
                                      ),
                                      child: Center(
                                        child: Text(
                                          backInContinue.tr,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: Colors.white
                                                .withOpacity(0.8999999761581421),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )
                    : pincodeCtl.isBackIn!.value
                        ? InkWell(
                            onTap: () {
                              pincodeCtl.resetPin();
                            },
                            child: Container(
                              // width: 122.h,
                              height: 32.w,
                              margin: EdgeInsets.only(top: 113.h),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Container(
                                      width: 24.w,
                                      height: 24.h,
                                      decoration: const BoxDecoration(),
                                      child: Image.asset(
                                        'assets/register/icon/Information.png',
                                        fit: BoxFit.contain,
                                        width: 24.w,
                                        height: 24.h,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 10.w),
                                  Center(
                                    child: Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text: backInForgetPin.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontWeight,
                                              // height: 0.17.h,
                                            ),
                                          ),
                                          TextSpan(
                                            text: backInClick.tr,
                                            style: TextStyle(
                                              color: Color(0xFF1A1818),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelLarge
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelLarge
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelLarge
                                                  ?.fontWeight,
                                              // height: 0.17.h,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : Container(
                            width: 303.w,
                            height: 118.h,
                            margin: EdgeInsets.only(top: 67.h),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(left: 13.w, top: 21.h),
                                  width: 24.w,
                                  height: 24.h,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: const BoxDecoration(),
                                  child: Image.asset(
                                    'assets/register/icon/Information.png',
                                    fit: BoxFit.contain,
                                    width: 24.w,
                                    height: 24.h,
                                  ),
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Container(
                                  margin: EdgeInsets.only(top: 24.h),
                                  height: 60.h,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        // height: 20.h,
                                        child: Center(
                                          child: Text(
                                              signUpPasswordDescription.tr,
                                            // 'สร้างรหัสผ่าน เพื่อใช้ในการกลับเข้าสู่แอปพลิเคชั่น',
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontWeight,
                                              // height: 0.14.h,
                                            ),
                                            textAlign: TextAlign.start,
                                          ),
                                        ),
                                      ),
                                      // SizedBox(
                                      //   height: 20.h,
                                      //   child: Center(
                                      //     child: Text(
                                      //       'อย่างรวดเร็ว และง่ายในกรณีที่ไม่ได้ออกจากระบบ',
                                      //       style: TextStyle(
                                      //         color: configTheme()
                                      //             .textTheme
                                      //             .bodyMedium
                                      //             ?.color,
                                      //         fontSize: configTheme()
                                      //             .primaryTextTheme
                                      //             .labelSmall
                                      //             ?.fontSize,
                                      //         fontFamily: configTheme()
                                      //             .primaryTextTheme
                                      //             .labelSmall
                                      //             ?.fontFamily,
                                      //         fontWeight: configTheme()
                                      //             .primaryTextTheme
                                      //             .labelSmall
                                      //             ?.fontWeight,
                                      //         height: 0.14.h,
                                      //       ),
                                      //       textAlign: TextAlign.start,
                                      //     ),
                                      //   ),
                                      // ),
                                      // SizedBox(
                                      //   height: 20.h,
                                      //   child: Center(
                                      //     child: Text(
                                      //       'เพื่อความปลอดภัยยิ่งขึ้น',
                                      //       style: TextStyle(
                                      //         color: configTheme()
                                      //             .textTheme
                                      //             .bodyMedium
                                      //             ?.color,
                                      //         fontSize: configTheme()
                                      //             .primaryTextTheme
                                      //             .labelSmall
                                      //             ?.fontSize,
                                      //         fontFamily: configTheme()
                                      //             .primaryTextTheme
                                      //             .labelSmall
                                      //             ?.fontFamily,
                                      //         fontWeight: configTheme()
                                      //             .primaryTextTheme
                                      //             .labelSmall
                                      //             ?.fontWeight,
                                      //         height: 0.14.h,
                                      //       ),
                                      //       textAlign: TextAlign.start,
                                      //     ),
                                      //   ),
                                      // ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )),
              ],
            );
          }),
        );
      }
    );
  }
}

class PincodeKeyboard extends StatelessWidget {
  const PincodeKeyboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildKey(context, '1'),
              _buildKey(context, '2'),
              _buildKey(context, '3'),
            ],
          ),
          SizedBox(height: 20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildKey(context, '4'),
              _buildKey(context, '5'),
              _buildKey(context, '6'),
            ],
          ),
          SizedBox(height: 20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildKey(context, '7'),
              _buildKey(context, '8'),
              _buildKey(context, '9'),
            ],
          ),
          SizedBox(height: 20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildKey(context, 'biometric'),
              _buildKey(context, '0'),
              _buildKey(context, 'delete'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKey(context, String text) {
    final PincodeController pincodeController = Get.put(PincodeController());
    return GestureDetector(
      onTap: () {
        if (text == 'delete') {
          pincodeController.deletePincode();
        } else if (text == 'biometric') {
          pincodeController.checkBiometricLogin();
        } else {
          pincodeController.addPincode(text);
        }
      },
      child: Container(
        width: 66.w,
        height: 60.h,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(60.r),
          ),
          // color: Colors.red,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Container(
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    text == 'biometric' || text == 'delete'
                        ? Container(
                            width: 32.w,
                            height: 32.h,
                            clipBehavior: Clip.antiAlias,
                            decoration: const BoxDecoration(),
                            child: Image.asset(
                              text == 'biometric'
                                  ? 'assets/register/icon/Fingerprint.png'
                                  : 'assets/register/icon/Close_round.png',
                              fit: BoxFit.contain,
                              color: text == 'delete'
                                  ? configTheme().textTheme.bodyMedium?.color
                                  : configTheme().colorScheme.onPrimary,
                              width: text == 'biometric' ? 40 : 24,
                              height: text == 'biometric' ? 40 : 24,
                            ),
                          )
                        : Text(
                            text,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .headlineLarge
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .headlineLarge
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .headlineLarge
                                  ?.fontWeight,
                              height: 0.h,
                            ),
                          ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
