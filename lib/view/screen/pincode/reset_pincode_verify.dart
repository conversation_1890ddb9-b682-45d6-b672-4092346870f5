import 'package:AAMG/controller/sms/send.request.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pinput/pinput.dart';
import 'package:AAMG/controller/pincode/pincode.controller.dart';

import '../../componance/themes/app_colors.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';

class VerifyResetPincode extends StatelessWidget {
  VerifyResetPincode({Key? key}) : super(key: key);

  final FocusNode focusNode = FocusNode();

  final PincodeController pinCodeCtl = Get.put(PincodeController());

  final SendRequestSMSController smsController =
      Get.put(SendRequestSMSController());

  var Phone = GetStorage().read('phone_firebase');

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
        color: configTheme().textTheme.bodyMedium?.color,
        fontSize: 20,
        fontFamily: configTheme().primaryTextTheme.headlineLarge?.fontFamily,
        fontWeight: configTheme().primaryTextTheme.headlineLarge?.fontWeight,
        height: 0,
      ),
      decoration: const BoxDecoration(),
    );
    final preFilledWidget = Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 24.w,
          height: 2.5.h,
          decoration: BoxDecoration(
            color: const Color(0xFFFF1A1818).withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
    return Scaffold(
      body: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(top: 81.h),
            child: GetBuilder<PincodeController>(builder: (pinCodeCtl) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/register/icon/verify.png',
                        fit: BoxFit.fill,
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 28.h),
                    // color: Colors.red,
                    height: 55.h,
                    child: Center(
                      child: Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: signUpOTP.tr,
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .displayMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .displayMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .displayMedium
                                    ?.fontWeight,
                              ),
                            ),
                            TextSpan(text: '\n'),
                            TextSpan(
                              text: '${signUpSendPhone.tr} ' +
                                  Phone.substring(0, 5) +
                                  '-' +
                                  Phone.substring(5, Phone.length),
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .labelSmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .labelSmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .labelSmall
                                    ?.fontWeight,
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                  ),
                  SizedBox(
                    width: 254.w,
                    child: Column(
                      children: [
                        SizedBox(
                          // color: Colors.brown,
                          width: 242.w,
                          height: 30.h,
                          child: Obx(() {
                            return Pinput(
                              // androidSmsAutofillMethod:
                              //     AndroidSmsAutofillMethod.smsRetrieverApi,
                              pinAnimationType: PinAnimationType.scale,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              defaultPinTheme: defaultPinTheme,
                              // preFilledWidget: preFilledWidget,
                              length: 6,
                              controller: pinCodeCtl.otpController.value,
                              focusNode: focusNode,
                              // listenForMultipleSmsOnAndroid: true,
                              showCursor: false,
                              closeKeyboardWhenCompleted: true,
                              animationCurve: Curves.easeInOut,
                              separatorBuilder: (index) =>
                                  const SizedBox(width: 22),
                              hapticFeedbackType: HapticFeedbackType.vibrate,
                              onCompleted: (pin) async {
                                print("YourCodeOTP = $pin");
                              },
                              onChanged: (value) {
                                if (value.length == 6) {
                                  pinCodeCtl.checkOtpFormat(context, value);
                                } else {
                                  pinCodeCtl.isAlert!.value = false;
                                  pinCodeCtl.setVerify(false);
                                  pinCodeCtl.update();
                                }
                              },
                            );
                          }),
                        ),
                        Obx(() {
                          return SizedBox(
                              width: 254.w,
                              child: Image.asset(
                                pinCodeCtl.isAlert!.value == true &&
                                        pinCodeCtl.otpController.value.text
                                                .length ==
                                            6 &&
                                        smsController.counter_OTP.toString() !=
                                            '0'
                                    ? 'assets/register/icon/OTP_error.png'
                                    : pinCodeCtl.otpController.value.text
                                                .isNotEmpty &&
                                            smsController.counter_OTP
                                                    .toString() !=
                                                '0'
                                        ? 'assets/register/icon/OTP_success.png'
                                        : 'assets/register/icon/OTP_defalt.png',
                                fit: BoxFit.fill,
                                color:
                                    pinCodeCtl.otpController.value.text
                                                .isNotEmpty &&
                                            appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rafco' &&
                                            pinCodeCtl.isAlert!.value == false
                                        ? const Color(0xFF22409A)
                                        : pinCodeCtl.otpController.value.text
                                                    .isNotEmpty &&
                                                appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rplc' &&
                                                pinCodeCtl.isAlert!.value ==
                                                    false
                                            ? const Color(0xFF6A7165)
                                            : null,
                              ));
                        }),
                      ],
                    ),
                  ),
                  Container(
                    height: 74.h,
                    child: Stack(
                      children: [
                        Obx(() {
                          return pinCodeCtl.isAlert!.value == true &&
                                  pinCodeCtl.otpController.value.text.length ==
                                      6 &&
                                  smsController.counter_OTP.toString() != '0'
                              ? Container(
                                  height: 20.h,
                                  alignment: Alignment.center,
                                  margin:
                                      EdgeInsets.only(top: 30.h, bottom: 24.h),
                                  child: Center(
                                    child: Text(
                                      signUpCilkRefCodeAgain.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            configTheme().colorScheme.onError,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontWeight,
                                        height: 0.14.h,
                                      ),
                                    ),
                                  ),
                                )
                              : Container();
                        }),
                        Obx(() {
                          return pinCodeCtl.isVerify!.value == true &&
                                  smsController.counter_OTP.toString() != '0'
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 14.w,
                                      height: 14.h,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(
                                          top: 30.h, bottom: 30.h),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          backgroundColor: configTheme()
                                              .colorScheme
                                              .secondary,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  configTheme()
                                                      .colorScheme
                                                      .primary
                                                      .withOpacity(1.0)),
                                          strokeWidth: 1.0,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : Container();
                        })
                      ],
                    ),
                  ),
                  Obx(() {
                    return SizedBox(
                        height: 40.h,
                        child: smsController.counter_OTP.value != 0
                            ? Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text:
                                          '${signUpRefCode.tr} ${pinCodeCtl.refCode.value}',
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontWeight,
                                        height: 0.18.h,
                                      ),
                                    ),
                                    TextSpan(
                                      text: signUpRefCodeAgain.tr,
                                      style: TextStyle(
                                        color: const Color(0x7F1A1818),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontWeight,
                                        height: 0.18.h,
                                      ),
                                    ),
                                    TextSpan(
                                      text: smsController.counter_OTP.value
                                          .toString(),
                                      style: TextStyle(
                                        color: appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'aam'
                                            ? const Color(0xFFFF9300)
                                            : appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rafco'
                                                ? const Color(0xFFEA1B23)
                                                : const Color(0xFFFFC20E),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelLarge
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelLarge
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelLarge
                                            ?.fontWeight,
                                        height: 0.18.h,
                                      ),
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              )
                            : Text(
                                signUpRefCodeError.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                ),
                              ));
                  }),
                  GestureDetector(
                    onTap: () {
                      //TODO : ส่ง OTP ไปใหม่
                      print("Resend OTP");
                      if (smsController.counter_OTP.value == 0) {
                        pinCodeCtl.resendOtp(context);
                      }
                    },
                    child: Container(
                      width: 105.w,
                      height: 46.h,
                      decoration: ShapeDecoration(
                        color: smsController.counter_OTP.value == 0
                            ? AppColors.AAMPurple
                            : Color(0x0C1A1818),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: 105.w,
                            height: 46.h,
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  // width: 73.w,
                                  // height: 14.h,
                                  child: Center(
                                    child: Text(
                                      // "รรับรหัสอีกครั้ง",
                                      signUpResend.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            smsController.counter_OTP.value == 0
                                                ? Colors.white
                                                : const Color(0x331A1818),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelMedium
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelMedium
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelMedium
                                            ?.fontWeight,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              );
            }),
          ),
          headerVerify(context)
        ],
      ),
    );
  }

  headerVerify(context) {
    return Container(
        margin: EdgeInsets.only(top: 60.h, left: 24.w, right: 24.w),
        height: 46.h,
        width: 327.w,
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                Get.back();
              },
              child:SizedBox(
                  height: 24.h,
                  width: 24.w,
                  child: SvgPicture.string(AppSvgImage.back_btn)
              )
            ),
          ],
        ));
  }
}
