import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';

import '../../componance/themes/theme.dart';
import '../home/<USER>';

class ResetPinCocdeSuccess extends StatelessWidget {
  const ResetPinCocdeSuccess({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration:
            BoxDecoration(gradient: appConfigService.countryConfigCollection.toString() == 'aam'
                ? AppColorsGradient.appBgAAMGradient
                : appConfigService.countryConfigCollection.toString() ==
                'rafco'
                ? AppColorsGradient.appBgRAFCOGradient
                : AppColorsGradient.appBgRPLCGradient),
          ),
          Column(
            children: [
              Center(
                child: Container(
                    width: 73.90.w,
                    height: 40.h,
                    margin: EdgeInsets.only(top: 81.0.h),
                    child:
                    Image.asset('assets/register/icon/regis_success.png')),
              ),
              SizedBox(
                height: 24.h,
              ),
              Container(
                height: 81.h,
                child: Center(
                  child: Text.rich(
                    TextSpan(
                      children: <InlineSpan>[
                        TextSpan(
                          text: backInNewPassword.tr,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8999999761581421),
                            fontSize: 20.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w700,
                          ),
                        ),

                        const TextSpan(
                          text: '\n',
                        ),
                        TextSpan(
                          text: backInWelcome.tr,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8999999761581421),
                            fontSize: 12.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Get.offAll(HomeNavigator());
                },
                child: Container(
                  width: 327.w,
                  height: 52.h,
                  margin: EdgeInsets.only(top: 486.0.h),
                  decoration: ShapeDecoration(
                    color: const Color(0xBF1A1818),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              backInNewPasswordFinish.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.sp,
                                fontFamily: 'NotoSansThai',
                                fontWeight: FontWeight.w500,
                                height: 0.14.h,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
