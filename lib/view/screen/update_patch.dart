import 'dart:ui';

import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/controller/update_patch/updatePatch.controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:restart_app/restart_app.dart';

class UpdatePatch extends StatefulWidget {
  const UpdatePatch({super.key});

  @override
  State<UpdatePatch> createState() => _UpdatePatchState();
}

class _UpdatePatchState extends State<UpdatePatch> {
  final updatePatchCtl = Get.put(UpdatePatchController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
          width: MediaQuery
              .of(context)
              .size
              .width * 0.8,
          height: MediaQuery
              .of(context)
              .size
              .height * 0.4,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                  child: Container(
                    width: MediaQuery
                        .of(context)
                        .size
                        .width * 0.8,
                    height: MediaQuery
                        .of(context)
                        .size
                        .height * 0.35,
                    decoration: BoxDecoration(
                      color: Color(0x11FFFFFF),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 50.h,
                        ),
                        Text(
                          updatePatch.tr,
                          style: TextStyle(
                              fontSize: 28.sp, color: Colors.white),
                        ),
                        // SizedBox(
                        //   height: 25,
                        // ),
                        Center(
                          child: Text(
                            updatePatchDes.tr,
                            style: TextStyle(
                                fontSize: 16.sp, color: Colors.white),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        // Container(
                        //   child: Column(
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     children: [
                        //       Text(
                        //         "ทางเรามีการปรับปรุงระบบฟังก์ชั่น",
                        //         style: TextStyle(
                        //             fontSize: 16, color: Colors.white),
                        //       ),
                        //       Text(
                        //         "เพื่อความสะดวกต่อการใช้งาน",
                        //         style: TextStyle(
                        //             fontSize: 16, color: Colors.white),
                        //       ),
                        //     ],
                        //   ),

                        SizedBox(
                          height: 30.h,
                        ),
                        Obx(() =>
                            Container(
                              margin: EdgeInsets.only(
                                left: 0.06.sw,
                                right: 0.06.sw,
                              ),
                              child: updatePatchCtl.restartApp.value == true
                                  ? InkWell(
                                onTap: () async {
                                  /// In Web Platform, Fill webOrigin only when your new origin is different than the app's origin
                                  Restart.restartApp();
                                },
                                child: Container(
                                  width:
                                  MediaQuery
                                      .of(context)
                                      .size
                                      .width *
                                      0.7,
                                  // height: 0.06,
                                  padding: EdgeInsets.symmetric(
                                      vertical: 10.h),
                                  decoration: BoxDecoration(
                                      color: Color(0xFF30234E),
                                      borderRadius:
                                      BorderRadius.circular(15)),
                                  child: Center(
                                    child: Text(
                                      updatePatchRestart.tr,
                                      style: TextStyle(
                                          fontSize: 15,
                                          color: Color(0xFFFFEA74),
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ),
                                ),
                              )
                                  : Container(
                                // height: 0.06.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    LinearPercentIndicator(
                                      barRadius:
                                      const Radius.circular(10),
                                      lineHeight: 8.h,
                                      percent: updatePatchCtl
                                          .downloadProgressNotifier
                                          .value /
                                          100,
                                      backgroundColor:
                                      Colors.black.withOpacity(0.2),
                                      progressColor:
                                      const Color(0xFFFFB100),
                                    ),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    Text(
                                      "${updatePatchCtl
                                          .downloadProgressNotifier.value
                                          .round()}%",
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ],
                                ),
                              ),
                            )),
                      ],
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  // print("updatePatchCtl :: ");
                  // print(updatePatchCtl.restartApp.value);
                  if(updatePatchCtl.restartApp.value){
                    Restart.restartApp();
                  }
                },
                child: Container(
                  height: MediaQuery
                      .of(context)
                      .size
                      .width * 0.155,
                  child: Icon(
                    Icons.cached_outlined,
                    size: 100.w,
                    color: Colors.white,
                  ),
                  // decoration: BoxDecoration(
                  //   image: DecorationImage(
                  //     image: AssetImage('assets/app_logo/rafco/Rafcologo_fade.png'),
                  //   ),
                  // ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

}
