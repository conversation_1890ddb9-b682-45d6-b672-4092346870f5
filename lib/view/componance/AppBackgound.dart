import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/app_config.dart';
import 'package:AAMG/controller/intro/intro.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';

class AppBackground {
  static backgroundColorApp(context) {
    return Container(
      decoration: BoxDecoration(gradient: AppColorsGradient.appBgAAMGradient),
      child: const Center(),
    );
  }

  static backgroundColorOnBoarding(context) {
    return Container(
      decoration:
          BoxDecoration(gradient: AppColorsGradient.appBgOnBroadingGradient),
    );
  }

  static backgroundColorOnBoardingFinal(context) {
    return Container(
      decoration:
      BoxDecoration(gradient: AppColorsGradient.appBgOnBroadingFinalGradient),
    );
  }

  static backgroundIntro(context) {
    if(AppConfig.of(context).countryConfigCollection.toString() ==
        'aam'){
      return Container(
        decoration: BoxDecoration(gradient: AppColorsGradient.appBgGradient),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: 46.0.h),
              width: Get.width,
              height: Get.height,
              child: SvgPicture.string(
                '<svg width="375" height="766" viewBox="0 0 375 766" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M53.6673 334.184C60.861 276.303 92.9637 224.446 141.565 192.198L230.114 133.445C260.613 113.21 288.797 89.6863 314.16 63.2982L375 0V766H0L53.6673 334.184Z" fill="#792AFF" fill-opacity="0.25"/></svg>',
                fit: BoxFit.fill,
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 34.0.h),
              width: Get.width,
              height: Get.height,
              child: SvgPicture.string(
                '<svg width="375" height="778" viewBox="0 0 375 778" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M92.9472 332.703C103.709 281.144 127.856 233.327 162.959 194.06L253.331 92.9673C282.904 59.8863 317.776 31.9565 356.516 10.322L375 0V778H0L92.9472 332.703Z" fill="#792AFF" fill-opacity="0.25"/></svg>',
                fit: BoxFit.fill,
              ),
            ),
            Center(
              child: Image.asset(
                AppImageAssets.aam_logo,
                width: 177.91.w,
                height: 32.0.h,
              ),
            ),
            AppLoading.introLoading(context),
          ],
        ),
      );
    }else if(AppConfig.of(context).countryConfigCollection.toString() ==
        'rplc'){
      return Container(
        decoration: BoxDecoration(gradient: AppColorsGradient.appBgGradient),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: 46.0.h),
              width: Get.width,
              height: Get.height,
              child: SvgPicture.string(
                '<svg width="375" height="766" viewBox="0 0 375 766" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M53.6673 334.184C60.861 276.303 92.9637 224.446 141.565 192.198L230.114 133.445C260.613 113.21 288.797 89.6863 314.16 63.2982L375 0V766H0L53.6673 334.184Z" fill="#FFC20E" fill-opacity="0.25"/></svg>',
                fit: BoxFit.fill,
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 34.0.h),
              width: Get.width,
              height: Get.height,
              child: SvgPicture.string(
                '<svg width="375" height="778" viewBox="0 0 375 778" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M92.9472 332.703C103.709 281.144 127.856 233.327 162.959 194.06L253.331 92.9673C282.904 59.8863 317.776 31.9565 356.516 10.322L375 0V778H0L92.9472 332.703Z" fill="#FFC20E" fill-opacity="0.25"/></svg>',
                fit: BoxFit.fill,
              ),
            ),
            Center(
              child: Image.asset(
                AppImageAssets.rplc_logo,

              ),
            ),
          ],
        ),
      );
    }else{
      return Container(
        decoration: BoxDecoration(gradient: AppColorsGradient.appBgGradient),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: 46.0.h),
              width: Get.width,
              height: Get.height,
              child: SvgPicture.string(
                '<svg width="375" height="766" viewBox="0 0 375 766" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M53.6673 334.184C60.861 276.303 92.9637 224.446 141.565 192.198L230.114 133.445C260.613 113.21 288.797 89.6863 314.16 63.2982L375 0V766H0L53.6673 334.184Z" fill="#22409A" fill-opacity="0.15"/></svg>',
                fit: BoxFit.fill,
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 34.0.h),
              width: Get.width,
              height: Get.height,
              child: SvgPicture.string(
                '<svg width="375" height="778" viewBox="0 0 375 778" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M92.9472 332.703C103.709 281.144 127.856 233.327 162.959 194.06L253.331 92.9673C282.904 59.8863 317.776 31.9565 356.516 10.322L375 0V778H0L92.9472 332.703Z" fill="#22409A" fill-opacity="0.15"/></svg>',
                fit: BoxFit.fill,
              ),
            ),
            Center(
              child: Image.asset(
                AppImageAssets.rafco_logo,
              ),
            ),
          ],
        ),
      );
    }

  }

  static backgroundColorHomePage(context) {
    return Container(
      decoration:
      BoxDecoration(gradient: AppColorsGradient.homePageColor),
    );
  }

  static backgroundPrimaryColor(context) {
    return Container(
      width: Get.width,
      height: Get.height,
      decoration:
      BoxDecoration(gradient: AppConfig.of(context).countryConfigCollection.toString() ==
          'aam' ? AppColorsGradient.appBgAAMGradient : AppConfig.of(context).countryConfigCollection.toString() == 'rplc'
      ?AppColorsGradient.appBgRPLCGradient: AppColorsGradient.appBgRAFCOGradient),
    );
  }
}
