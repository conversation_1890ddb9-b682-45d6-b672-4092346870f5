import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';

class CFormatter {
  static String doubleFormatCurrency(String amount) {
    try {
      if(amount.isEmpty) return amount;

      double parsedAmount = double.parse(amount);

      final NumberFormat formatter = NumberFormat.currency(
        locale: 'en_US',
        symbol: '',
        decimalDigits: 2,
      );

      return formatter.format(parsedAmount);
    } catch (e) {
      debugPrint("Error formatting currency: $e");
      return amount;
    }
  }
}