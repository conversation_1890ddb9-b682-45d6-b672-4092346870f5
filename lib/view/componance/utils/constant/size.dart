class CSizes{

  static const double space5 = 5.0;
  static const double space10 = 10.0;
  static const double space15 = 15.0;
  static const double space20 = 20.0;
  static const double space25 = 25.0;
  static const double space30 = 30.0;
  static const double space35 = 35.0;
  static const double space40 = 40.0;
  static const double space45 = 45.0;
  static const double space50 = 50.0;


  //padding and margin sizes
  static const double xs = 4.0;
  static const double sm = 10.0;
  static const double md = 30.0;
  static const double lg = 50.0;
  static const double xl = 100.0;
  static const double xxl = 130.0;

  //icon sizes
  static const double iconXs = 12.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;

  //font sizes
  static const double fontSizeSm = 14.0;
  static const double fontSizeMd = 16.0;
  static const double fontSizeLg = 18.0;

  //button sizes
  static const double buttonHeight = 18.0;
  static const double buttonRadius = 12.0;
  static const double bottomWidth = 120.0;
  static const double buttonElevation = 4.0;

  // AppBar height
  static const double appBarHeight = 56.0;

  // image sizes
  static const double imageThumbSize =80.0;

  //default spacing between sections
  static const double spaceBetweenButton = 6.0 ;
  static const double defaultPadding = 12.0 ;
  static const double spaceBtwItems = 12.0;
  static const double defaultSpace = 24.0;
  static const double spaceBtwSections = 32.0;


  //border radius
  static const double borderRadiusSm = 4.0;
  static const double borderRadiusMd = 8.0;
  static const double borderRadiusLg = 12.0;

  //divider height
  static const double dividerHeight = 1.0;

  //input field
  static const double inputFieldRadius = 12.0;
  static const double spaceBtwInputFields = 16.0;


}