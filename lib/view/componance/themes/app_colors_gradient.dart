import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/material.dart';

class AppColorsGradient {

  static Gradient appBgGradient = LinearGradient(
    begin: Alignment.topRight,
    end: Alignment.bottomLeft,
    colors: [
      configTheme().colorScheme.primary.withOpacity(0.5),
      configTheme().colorScheme.primary.withOpacity(1.0)
    ],
    stops: [0.0, 1.0],
  );


  static Gradient appBgAAMGradient = LinearGradient(
    begin: Alignment.topRight,
    end: Alignment.bottomLeft,
    colors: [
      const Color(0xAAA169FF).withOpacity(1.0),
      const Color(0x77792AFF).withOpacity(1.0)
    ],
    stops: [0.0, 1.0],
  );

  static Gradient appBgRAFCOGradient = LinearGradient(
    begin: const Alignment(0.00, -1.00),
    end: const Alignment(0, 1),
    colors: [const Color(0xFF7698FF).withOpacity(1.0), const Color(0xFF22409A).withOpacity(1.0)],
  );

  static Gradient appBgRPLCGradient = LinearGradient(
    begin: const Alignment(0.00, -1.00),
    end: const Alignment(0, 1),
    colors: [const Color(0xFFFFC20E).withOpacity(1.0), const Color(0xFFB88C0C).withOpacity(1.0)],
  );

  static Gradient buttonGradient = LinearGradient(
    begin:  const Alignment(0.00, -1.00),
    end:  const Alignment(0, 1),
    colors: [
     configTheme().buttonTheme.colorScheme?.secondary.withOpacity(1.0) ?? configTheme().colorScheme.secondary,
      configTheme().buttonTheme.colorScheme?.primary.withOpacity(1.0) ?? configTheme().colorScheme.primary,
    ],
  );

  static Gradient buttonAAMGradient = LinearGradient(
    begin:  const Alignment(0.00, -1.00),
    end:  const Alignment(0, 1),
    colors: [
       const Color(0xAAA169FF).withOpacity(1.0),
       const Color(0x77792AFF).withOpacity(1.0)
    ],
  );

  static Gradient buttonRAFCOGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFF4976FF), Color(0xFF22409A)],
  );
  static Gradient buttonRAFCOGradientLogin = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFF22409A), Color(0xFF22409A)],
  );

  static Gradient buttonRPLCGradient = LinearGradient(
    begin:  const Alignment(0.00, -1.00),
    end:  const Alignment(0, 1),
    colors: [
      const Color(0xFFFFC20E).withOpacity(1.0),
      const Color(0xFFFFC20E).withOpacity(1.0)
    ],
  );

  static Gradient appBgOnBroadingGradient =  LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [configTheme().colorScheme.onSurface, configTheme().colorScheme.surface],
  );

  static Gradient appBgOnBroadingFinalGradient =  LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [configTheme().colorScheme.onSurfaceVariant, configTheme().colorScheme.surfaceVariant],
  );

  static Gradient cardBillGradient =  LinearGradient(
    begin:  const Alignment(0.00, -1.00),
    end:  const Alignment(0, 1),
    colors: [configTheme().colorScheme.onTertiary.withOpacity(1.0), configTheme().colorScheme.tertiary.withOpacity(1.0)],
  );

  static Gradient cardMyloanGradient =  LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [configTheme().buttonTheme.colorScheme!.onSurface, configTheme().buttonTheme.colorScheme!.surface],
  );


  static Gradient appBgAAMOnBroadingGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFFFBF69), Color(0xFFFF9300)],
  );

  static Gradient appBgRAFCOOnBroadingGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFF1676C), Color(0xFFEA1B23)],
  );

  static Gradient cardbillRAFCOGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFF2484E), Color(0xFFEA1B23)],
  );
  static Gradient cardbillRPLCGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFB8C2B0), Color(0xFF6A7165)],
  );
  static Gradient cardMyloanRAFCOGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFF4976FF), Color(0xFF22409A)],
  );
  static Gradient cardMyloanRPLCGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFFFC20E), Color(0xFFD9A200)],
  );

  static Gradient appBgRPLCOnBroadingGradient = const LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFB8C2B0), Color(0xFF6A7165)],
  );

  static Gradient whiteGradient =  LinearGradient(
    begin: const Alignment(0.00, -1.00),
    end: const Alignment(0, 1),
    colors: [Colors.white.withOpacity(0.9), Colors.white.withOpacity(1)],
  );

  static Gradient homePageColor =  LinearGradient(
    begin: const Alignment(0.00, -1.00),
    end: const Alignment(0, 1),
    colors: [configTheme().colorScheme.background.withOpacity(0.8999999761581421), Colors.white.withOpacity(1)],
  );


  static Gradient myloanAAMGradient =  LinearGradient(
    begin: const Alignment(0.00, -1.00),
    end: const Alignment(0, 1),
    colors: [const Color(0x26FF9300).withOpacity(0.15), const Color(0x66FF9300).withOpacity(0.4)],
  );

  static Gradient loanPercentGradient =  LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFFFBF69), Color(0xFFFF9300)],
  );

  static Gradient AAMPAYGradient =  LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFFFF1DD).withOpacity(1.0), Color(0xFFFFE2B9).withOpacity(1.0)],
  );

  static Gradient RPLCPAYGradient =  LinearGradient(
    begin: Alignment(0.00, -1.00),
    end: Alignment(0, 1),
    colors: [Color(0xFFFFE49E).withOpacity(1.0), Color(0xFFFFDD65).withOpacity(1.0)],
  );

  static Gradient RPLCPAYGradientButton =  LinearGradient(
    // begin: Alignment(0.00, -1.00),
    // end: Alignment(0, 1),
    colors: [Colors.black.withOpacity(0.75), Colors.black.withOpacity(0.75)],
  );
}

