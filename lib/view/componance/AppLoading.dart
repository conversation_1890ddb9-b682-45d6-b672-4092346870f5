import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:AAMG/app_config.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';

import '../../controller/AppConfigService.dart';
import 'themes/theme.dart';

class AppLoading {
  static generalLoading(context) {
    print("เข้า generalLoading");
    // AppConfigService appConfigService = Get.put(AppConfigService(context: context));
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) {
          return Container(
            decoration: BoxDecoration(
                gradient:
                    AppConfig.of(context).countryConfigCollection.toString() ==
                            'aam'
                        ? AppColorsGradient.appBgAAMGradient
                        : AppConfig.of(context)
                                    .countryConfigCollection
                                    .toString() ==
                                'rafco'
                            ? AppColorsGradient.appBgRAFCOGradient
                            : AppColorsGradient.appBgRPLCGradient),
            child: Center(
              child: Stack(
                children: [
                  const Center(
                    child: SpinKitRing(
                      color: Color(0XFFFFFFFF),
                      size: 61.49,
                      lineWidth: 1.0,
                    ),
                    //  child: CircleLoader(
                    //    width: 61.49, // กำหนดความกว้าง 100.0
                    //    firstColor:  Color(0xFFD9D9D9),
                    //    secondColor: Color(0XFFFFFFFF).withOpacity(0.1),
                    //    strokeWidth: 1.w,
                    //  ),
                  ),
                  Center(
                    child: Image.asset(
                      AppConfig.of(context)
                                  .countryConfigCollection
                                  .toString() ==
                              'aam'
                          ? AppImageAssets.aam_logo_nontext
                          : AppConfig.of(context)
                                      .countryConfigCollection
                                      .toString() ==
                                  'rafco'
                              ? AppImageAssets.rafco_logo_nontext
                              : AppImageAssets.rplc_logo_nontext,
                      width: 45,
                      height: 45,
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  static introLoading(context){
    return Container(
      width: 33,
      height: 33,
      margin: EdgeInsets.only(bottom: 86.h,left: 171.w,right: 171.w,top: 647.h),
      child: CircleLoader(
        width: 33, // กำหนดความกว้าง 100.0
        firstColor: AppColors.loadingColor.withOpacity(0.5),
        secondColor: AppColors.loadingColor.withOpacity(0.5),
        strokeWidth: 1,
      ),
    );
  }

  static loadingVerify(context) {
    print("เข้า loadingVerify");
    // AppConfigService appConfigService = Get.put(AppConfigService(context: context));
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) {
          return Container(
            width: Get.width,
            height: Get.height,
            decoration: ShapeDecoration(
              gradient: LinearGradient(
                begin: const Alignment(0.00, -1.00),
                end: const Alignment(0, 1),
                colors: [
                  configTheme().colorScheme.background.withOpacity(0.8999999761581421),
                  configTheme().colorScheme.background.withOpacity(0.9)
                ],
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(0),
              ),
            ),
            child: Container(
              width: 84.w,
              height: 84.h,
              alignment: Alignment.center,
              child: Center(
                child: Stack(
                  children: [
                    Center(
                      child: CircleLoader(
                        width: 61.49, // กำหนดความกว้าง 100.0
                        firstColor: configTheme().colorScheme.primary.withOpacity(1.0),
                        secondColor: configTheme().colorScheme.secondary.withOpacity(1.0),
                        strokeWidth: 1.5.w,
                      ),
                    ),
                    Center(
                      child:
                      appConfigService.countryConfigCollection == 'aam'
                          ? Image.asset(
                        AppImageAssets.aam_loading,
                        width: 32.1.w,
                        height: 21.0.h,
                      )
                          : appConfigService.countryConfigCollection == 'rafco'
                          ? Image.asset(
                        AppImageAssets.rafco_loading,
                        width: 29.45.w,
                        height: 30.h,
                      )
                          : Image.asset(
                        AppImageAssets.rplc_loading,
                        width: 45.w,
                        height: 45.h,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  static Loaderhide(context) {
    Navigator.of(context, rootNavigator: true).pop();
  }

  static load() {
    int index = 0;
    final colors = [Colors.purple, Colors.orange];
    return Container(
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: colors[index % colors.length],
          borderRadius: BorderRadius.circular(50.0),
        ),
      ),
    );
  }

  static loading(context) {
    print("เข้า loading");
    return Container(
      decoration: BoxDecoration(gradient: AppColorsGradient.whiteGradient),
      child: Center(
        child: Stack(
          children: [
            Center(
              child: SpinKitRing(
                color: appConfigService.countryConfigCollection.toString() == 'aam' ?AppColors.AAMPurple: appConfigService.countryConfigCollection.toString() == 'rplc'?Color(0xFF6A7165).withOpacity(1.0):Color(0xFF22409A).withOpacity(1.0),
                // color: AppColors.AAMPurple,
                size: 61.49.w,
                lineWidth: 1.0.w,
              ),
            ),
            Center(
              child: Image.asset(
                appConfigService.countryConfigCollection.toString() == 'aam' ?'assets/app_logo/AAMLoading.png' : appConfigService.countryConfigCollection.toString() == 'rplc' ? AppImageAssets.rplc_loading : AppImageAssets.rafco_loading,
                width: 32.1,
                height: 21.0,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static textFieldLoading(){
    return Container(
      width: 14.w,
      height: 14.h,
      alignment: Alignment.center,
      margin: EdgeInsets.only(right: 20.w),
      child: CircleLoader(width: 14.w,  firstColor: appConfigService.countryConfigCollection == 'aam' ? const Color(0xFFFF9300).withOpacity(1.0) : appConfigService.countryConfigCollection == 'rafco' ? const Color(0xFF22409A).withOpacity(1.0): const Color(0xFF6A7165).withOpacity(1.0),
          secondColor: appConfigService.countryConfigCollection == 'aam' ? const Color(0x77792AFF).withOpacity(1.0) : appConfigService.countryConfigCollection == 'rafco' ? const Color(0x77F2484F).withOpacity(1.0) : const Color(0x77FFC20E).withOpacity(1.0),
          strokeWidth: 1.0.w)
    );
  }

  static showText(context){
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
              child: Center(
                child: Container(
                  height: 300,
                  width: 300,
                  // decoration: BoxDecoration(
                  //   borderRadius: BorderRadius.circular(10),
                  //   color: Colors.white.withOpacity(0.5),
                  // ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircleLoader(width: 14.w,  firstColor: appConfigService.countryConfigCollection == 'aam' ? const Color(0xFFFF9300).withOpacity(1.0) : appConfigService.countryConfigCollection == 'rafco' ? const Color(0xFF22409A).withOpacity(1.0): const Color(0xFF6A7165).withOpacity(1.0),
                          secondColor: appConfigService.countryConfigCollection == 'aam' ? const Color(0x77792AFF).withOpacity(1.0) : appConfigService.countryConfigCollection == 'rafco' ? const Color(0x77F2484F).withOpacity(1.0) : const Color(0x77FFC20E).withOpacity(1.0),
                          strokeWidth: 1.0.w),
                      SizedBox(
                        height: 10.h,
                      ),
                      Text(
                        chatInAppCreateGroup.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xFFFFEFD5),
                          decoration: TextDecoration.none,
                          fontSize: 16,
                        ),
                      )
                    ],
                  ),
                ),
              ),
              onWillPop: () async {
                return true;
              });
        });
  }
}

class CircleLoader extends StatelessWidget {
  final double width; // กำหนดขนาดความกว้าง
  final Color firstColor;
  final Color secondColor;
  final double strokeWidth;

  const CircleLoader({
    Key? key,
    required this.width,
    required this.firstColor,
    required this.secondColor,
    required this.strokeWidth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: width,
        height: width,
        child: CircularProgressIndicator(
          backgroundColor: firstColor,
          valueColor: AlwaysStoppedAnimation<Color>(
            secondColor,
          ),
          strokeWidth: strokeWidth,
        ),
      ),
    );
  }
}
