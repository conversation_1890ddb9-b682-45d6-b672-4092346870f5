import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../themes/theme.dart';

class HeaderGeneral extends StatelessWidget {
  final String title;
  final Widget firstIcon;
  final Widget secondIcon;
  final VoidCallback firstOnPressed;
  final VoidCallback secondOnPressed;
  final double? fistIconMarginTop;
  final double? secondIconMarginTop;
  final Color? backgroundColor;

  const HeaderGeneral(
      {Key? key,
      required this.title,
      required this.firstIcon,
      required this.secondIcon,
      required this.firstOnPressed,
      required this.secondOnPressed,
      this.fistIconMarginTop,
      this.secondIconMarginTop,
      this.backgroundColor
      })
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375.w,
      height: 106.h,
      color: backgroundColor ?? Colors.white.withOpacity(0.9),
      child: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 74.h),
            height: 24.h,
            child: Center(
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0xFF1A1818),
                  fontSize: configTheme().primaryTextTheme.titleLarge!.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.titleLarge!.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.titleLarge!.fontWeight,
                ),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: firstOnPressed,
                child: Container(
                    color: Colors.transparent,
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.only(left: 24.w, top: fistIconMarginTop ?? 67.h),
                    child: firstIcon),
              ),
              InkWell(
                onTap: secondOnPressed,
                child: Container(
                    color: Colors.transparent,
                    alignment: Alignment.centerRight,
                    margin: EdgeInsets.only(right: 24.w, top: secondIconMarginTop ?? 67.h),
                    child: secondIcon),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
