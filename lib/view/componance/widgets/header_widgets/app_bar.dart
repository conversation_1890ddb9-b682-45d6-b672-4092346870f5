
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/utils/constant/size.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../utils/device/device_utility.dart';

class CAppBar extends StatelessWidget implements PreferredSizeWidget{ ///--** implement
  const CAppBar({
    super.key,
    this.title,
    this.leadingIcon,
    this.actions,
    this.leadingOnPressed,
    this.showBackArrow = false,
  });

  final Widget? title;
  final bool showBackArrow;
  final IconData? leadingIcon;
  final List<Widget>? actions;
  final VoidCallback? leadingOnPressed;

  @override
  Widget build(BuildContext context) {
    return  Container(
      padding: const EdgeInsets.only(top: CSizes.space50, left: CSizes.defaultSpace, right: CSizes.defaultSpace, bottom: CSizes.defaultSpace),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 30,
            height: 30,
            child: showBackArrow ? IconButton(onPressed: () => Get.back(), icon: const Icon(Icons.arrow_left, color: Colors.black),)
                : leadingIcon != null ? IconButton(onPressed: leadingOnPressed, icon: Icon(leadingIcon, color:AppColors.darkGrey),)
                : null,
          ),
          title ?? const SizedBox.shrink(),
          ...?actions,
        ],
      ),
    );
  }

  @override
  // TODO: implement preferredSize
  Size get preferredSize => const Size.fromHeight(106.0); // *** if implement need this
}
