import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../utils/AppSvgImage.dart';

class HeaderIcon extends StatelessWidget {
  final String title;
  final Widget icon;
  final VoidCallback onPressed;
  const HeaderIcon({Key? key, required this.title,required this.icon, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120.h,
      width: Get.width,
      color: Colors.white.withOpacity(0.9),
      child: Container(
          margin: EdgeInsets.only(left: 24.w, right: 24.w),
          child: Stack(
            children: [
              //TODO slide page
              Container(
                height: 24.h,
                width: Get.width,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  top: 67.h,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Center(
                      child: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: configTheme().textTheme.bodyMedium?.color,
                          fontSize: configTheme().primaryTextTheme.titleMedium?.fontSize,
                          fontFamily: configTheme().primaryTextTheme.titleMedium?.fontFamily,
                          fontWeight: configTheme().primaryTextTheme.titleMedium?.fontWeight,
                          // height: 0.09,
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Container(
                height: 24.h,
                  alignment: Alignment.centerRight,
                  margin: EdgeInsets.only(top: 67.h),
                  child: icon
              ),
              GestureDetector(
                onTap: () {
                  onPressed;
                },
                child: Container(
                  color: Colors.transparent,
                  width: 24.w,
                  height: 24.h,
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(
                    top: 67.h,
                  ),
                  child: Center(
                    child: SvgPicture.string(
                      AppSvgImage.back_btn,
                    ),
                  ),
                ),
              ),
            ],
          )),
    );
  }
}
