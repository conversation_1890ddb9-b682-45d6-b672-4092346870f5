import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../themes/app_textstyle.dart';
import '../../utils/AppSvgImage.dart';

class HeaderWidget extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  const HeaderWidget({Key? key, required this.title, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 106.h,
      width: Get.width,
      color: Colors.white.withOpacity(0.9),
      child: Container(
          margin: EdgeInsets.only(left: 24.w, right: 24.w),
          child: Stack(
            children: [
              //TODO slide page
              Container(
                height: 24.h,
                width: Get.width,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  top: 67.h,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Center(
                      child: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: const Color(0xFF1A1818),
                          fontSize: 16,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w700,
                          height: 0.09,
                        ),
                      ),
                    )
                  ],
                ),
              ),
              GestureDetector(
                onTap: onPressed,
                child: Container(
                  width: 24.w,
                  height: 24.h,
                  color: Colors.transparent,
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(
                    top: 67.h,
                  ),
                  child: Center(
                    child: SvgPicture.string(
                      AppSvgImage.back_btn,
                    ),
                  ),
                ),
              ),
            ],
          )),
    );
  }
}
