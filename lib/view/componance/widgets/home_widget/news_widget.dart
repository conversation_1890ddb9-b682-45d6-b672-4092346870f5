import 'package:AAMG/controller/home/<USER>';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../controller/news_promotions/news.controller.dart';
import '../../../../controller/transalation/translation_key.dart';
import '../../../screen/news/news_detail.dart';
import '../../themes/app_textstyle.dart';
import '../../themes/theme.dart';

class HomePageNewsWidget {
  static Widget newsContent(context) {
    final HomeController homeController = Get.find<HomeController>();
    return Container(
      width: Get.width,
      height: 189.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 21.h,
            margin: EdgeInsets.only(left: 24.w, right: 24.w),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 21.h,
                      child: Image.asset(
                        'assets/home/<USER>',
                        fit: BoxFit.fill,
                        color: configTheme().colorScheme.onSecondary,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      homePromotion.tr,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontWeight,
                        height: 0,
                      ),
                    ),
                  ],
                ),
                // SizedBox(
                //     height: 18.h,
                //     child: Text(
                //       homePromotionAll.tr,
                //       textAlign: TextAlign.right,
                //       style: TextStyle(
                //         color: configTheme().textTheme.bodyMedium?.color?.withOpacity(0.5),
                //         fontSize: configTheme().primaryTextTheme.labelMedium?.fontSize,
                //         fontFamily:configTheme().primaryTextTheme.labelMedium?.fontFamily,
                //         fontWeight:configTheme().primaryTextTheme.labelMedium?.fontWeight,
                //         height: 0,
                //       ),
                //     )),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          CarouselSlider(
            items: List.generate(
              3,
              // newsController.newsLastestList.length,
              (i) {
                return GetBuilder<NewsController>(
                    init: NewsController(),
                    builder: (newsController) {
                      if (newsController.newsLastestList.isEmpty) {
                        return Container(
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      } else {
                        return GestureDetector(
                          onTap: () {
                            print(i);
                            Get.to(() => NewsDetail(
                                  index: i,
                                ));
                          },
                          child: Container(
                            width: Get.width,
                            height: 160.h,
                            margin: EdgeInsets.only(left: 24.w, right: 24.w),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: Get.width,
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            bottom: 10.h,
                                            left: 10.w,
                                            right: 10.w),
                                        decoration: ShapeDecoration(
                                          color: configTheme()
                                              .colorScheme
                                              .onPrimary,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12.r),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: 120.w,
                                              height: 120.h,
                                              decoration: ShapeDecoration(
                                                image: DecorationImage(
                                                  // image: AssetImage(newsController.newsLastestList[i].urlImgNews.toString()),
                                                  image: NetworkImage(
                                                    newsController
                                                            .newsLastestList[i]
                                                            .urlImgNews
                                                            .isNotEmpty
                                                        ? newsController
                                                            .newsLastestList[i]
                                                            .urlImgNews
                                                            .toString()
                                                        : 'https://www.thermaxglobal.com/wp-content/uploads/2020/05/image-not-found.jpg',
                                                  ),
                                                  fit: BoxFit.fill,
                                                ),
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6.r)),
                                              ),
                                            ),
                                            SizedBox(width: 10.w),
                                            Container(
                                              width: 177.w,
                                              height: 120.h,
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    alignment:
                                                        Alignment.topLeft,
                                                    child: Text(
                                                      newsController
                                                              .newsLastestList[
                                                                  i]
                                                              .headerNews ??
                                                          '',
                                                      maxLines: 2,
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 16.sp,
                                                        fontFamily:
                                                            TextStyleTheme
                                                                .text_Bold
                                                                .fontFamily,
                                                        fontWeight:
                                                            FontWeight.w700,
                                                        // height: 0.09.h,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(height: 5.h),
                                                  Container(
                                                    alignment:
                                                        Alignment.centerLeft,
                                                    child: Text(
                                                      // TODO ถ้าเป็น aam ให้ใช้ parseHtmlString แปลงข้อความจาก tag html ให้เป็น string ปกติ
                                                      appConfigService.countryConfigCollection
                                                                      .toString() ==
                                                                  'aam' ||
                                                              appConfigService
                                                                      .countryConfigCollection
                                                                      .toString() ==
                                                                  'rplc'
                                                          ? newsController
                                                              .parseHtmlString(
                                                                  newsController
                                                                      .newsLastestList[
                                                                          i]
                                                                      .bodyNews)
                                                          : newsController
                                                              .newsLastestList[
                                                                  i]
                                                              .bodyNews
                                                              .toString(),
                                                      maxLines: 2,
                                                      style: TextStyle(
                                                        color: configTheme()
                                                            .colorScheme
                                                            .background
                                                            .withOpacity(
                                                                0.8999999761581421),
                                                        fontSize: configTheme()
                                                            .primaryTextTheme
                                                            .labelSmall
                                                            ?.fontSize,
                                                        fontFamily: configTheme()
                                                            .primaryTextTheme
                                                            .labelSmall
                                                            ?.fontFamily,
                                                        fontWeight: configTheme()
                                                            .primaryTextTheme
                                                            .labelSmall
                                                            ?.fontWeight,
                                                        // height: 0.17.h,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                    });
              },
            ),
            options: CarouselOptions(
                autoPlay: true,
                initialPage: 0,
                aspectRatio: 1,
                viewportFraction: 1,
                height: 140.h,
                disableCenter: true,
                enableInfiniteScroll: false,
                onPageChanged: (index, reason) {
                  homeController.setIndexNews(index);
                }),
          ),
          SizedBox(height: 16.h),
          SizedBox(
            width: Get.width,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Opacity(
                  opacity: homeController.indexNews?.value == 0 ? 1 : 0.40,
                  child: Container(
                    width: homeController.indexNews?.value == 0 ? 15 : 4,
                    height: 4.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.primary,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
                SizedBox(width: 6.w),
                Opacity(
                  opacity: homeController.indexNews?.value == 1 ? 1 : 0.40,
                  child: Container(
                    width: homeController.indexNews?.value == 1 ? 15 : 4,
                    height: 4.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.primary,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
                SizedBox(width: 6.w),
                Opacity(
                  opacity: homeController.indexNews?.value == 2 ? 1 : 0.40,
                  child: Container(
                    width: homeController.indexNews?.value == 2 ? 15 : 4,
                    height: 4.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.primary,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
