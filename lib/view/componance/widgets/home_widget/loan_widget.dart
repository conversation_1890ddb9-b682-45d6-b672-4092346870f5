import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../../../controller/AppConfigService.dart';
import '../../../../controller/contract/contractlist.controller.dart';
import '../../../../controller/contract/myloan.controller.dart';
import '../../../../controller/transalation/transalation.controller.dart';
import '../../../../controller/transalation/translation_key.dart';
import '../../../screen/loan_screen/loan_details_screen.dart';
import '../../../screen/loan_screen/loan_main_screen.dart';
import '../../../screen/register/register_page.dart';
import '../../themes/app_colors_gradient.dart';
import '../../themes/app_textstyle.dart';
import '../../themes/theme.dart';
import '../../utils/AppImageAssets.dart';
import '../../utils/AppSvgImage.dart';
import '../../utils/formatters/formatter.dart';

class HomePageLoanWidget {
  static Widget myloanDefaultContent(context) {
    final HomeController homeController = Get.find<HomeController>();
    final ContractListController contractListController =
        Get.find<ContractListController>();
    return Container(
      margin: EdgeInsets.only(left: 24.w, right: 24.w),
      width: Get.width,
      height: 152.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 21.h,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              // mainAxisAlignment: MainAxisAlignment.start,
              // crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 21.h,
                  child: Image.asset(
                    'assets/home/<USER>',
                    fit: BoxFit.fill,
                    color: configTheme().colorScheme.onSecondary,
                  ),
                ),
                SizedBox(width: 10.w),
                Text(
                  // "สินเชื่อของฉัน",
                  homeMyLoan.tr,
                  style: TextStyle(
                    color: configTheme().textTheme.bodyMedium?.color,
                    fontSize:
                        configTheme().primaryTextTheme.bodyLarge?.fontSize,
                    fontFamily:
                        configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                    fontWeight:
                        configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                    height: 0,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          Container(
              width: Get.width,
              height: 113.h,
              decoration: ShapeDecoration(
                // color: Colors.white,
                // color: Colors.red,
                image: DecorationImage(
                  alignment: Alignment.bottomRight,
                  image: AssetImage(appConfigService.countryConfigCollection
                              .toString() ==
                          'aam'
                      ? 'assets/intro/aam/page2.png'
                      : appConfigService.countryConfigCollection.toString() ==
                              'rafco'
                          ? 'assets/intro/rafco/page2.png'
                          : AppImageAssets.rplc_loan),
                  // fit: BoxFit.contain,
                  // scale: 2
                ),
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                      width: 0.50.w,
                      strokeAlign: BorderSide.strokeAlignOutside,
                      color: configTheme().colorScheme.primary),
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                  top: 16.h,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    appConfigService.countryConfigCollection.toString() == "aam"
                        ? Text(
                            homeLoanInterested.tr,
                            style: TextStyle(
                              color: configTheme()
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withOpacity(0.5),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                            ),
                          )
                        : Text(''),
                    Text(
                      homeInterested.tr,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontWeight,
                      ),
                    ),
                    SizedBox(height: 3.h),
                    InkWell(
                        onTap: () {
                          if (homeController.isGuest!.value) {
                            showDialog(
                                context: context,
                                useSafeArea: true,
                                builder: (_) => RegisterPage());
                            // Get.to(() => const RegisterPage());
                          } else {
                            if (contractListController.contractList.isEmpty) {
                              Get.to(() => const LoanRequestDetail());
                            } else {
                              showDialog(
                                  context: context,
                                  useSafeArea: true,
                                  builder: (_) => LoanMainScreen());
                              // Get.to(() => const LoanMainScreen());
                            }
                          }
                        },
                        child: Container(
                            width: 158.w,
                            height: 36.h,
                            decoration: ShapeDecoration(
                              color: configTheme().colorScheme.primary,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6.r)),
                            ),
                            child: SizedBox(
                              height: 16.h,
                              child: Center(
                                child: Text(
                                  homeApplyLoan.tr,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14.sp,
                                    fontFamily: TextStyleTheme
                                        .text_Semi_Bold.fontFamily,
                                    fontWeight: FontWeight.w600,
                                    height: 0,
                                  ),
                                ),
                              ),
                            ))),
                  ],
                ),
              ))
        ],
      ),
    );
  }

  static Widget myloanContent(context) {
    final HomeController homeController = Get.find<HomeController>();
    final MyloanController myloanCtl = Get.put(MyloanController());
    print(Get.find<AppConfigController>().currencySymbol!.value.toString());
    return GetBuilder<ContractListController>(
      init: ContractListController(),
      builder: (contractListCtl) {
        return Container(
          width: Get.width,
          height: 261.h,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 21.h,
                margin: EdgeInsets.only(left: 24.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 21.h,
                      child: Image.asset(
                        'assets/home/<USER>',
                        fit: BoxFit.fill,
                        color: configTheme().colorScheme.onSecondary,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      homeMyLoan.tr,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontWeight,
                        height: 0,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                height: 212.h,
                child: CarouselSlider(
                  items:
                      List.generate(contractListCtl.contractList.length, (i) {
                    return GestureDetector(
                      onTap: () {
                        //TODO check ข้อมูลรายละเอียดสินเชื่อและ set ข้อมูลให้ myloanCtl
                        myloanCtl.setDataMyLoan(
                            context,
                            contractListCtl.contractList[i].ctt_code
                                .toString());
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: 24.w, right: 24.w),
                        width: Get.width,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: Get.width,
                              height: 212.h,
                              padding: EdgeInsets.only(
                                  left: 18.w,
                                  right: 18.w,
                                  top: 14.h,
                                  bottom: 14.h),
                              decoration: ShapeDecoration(
                                gradient: AppColorsGradient.cardMyloanGradient,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    height: 16.h,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          homeLoanLimit.tr,
                                          style: TextStyle(
                                            color: appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rafco'
                                                ? Colors.white.withOpacity(
                                                    0.699999988079071)
                                                : Color(0x7F1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .labelSmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .labelSmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .labelSmall
                                                ?.fontWeight,
                                            height: 0,
                                          ),
                                        ),
                                        Text(
                                          homeLoanBalance.tr,
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: appConfigService
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rafco'
                                                ? Colors.white.withOpacity(
                                                    0.699999988079071)
                                                : Color(0x7F1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .labelSmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .labelSmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .labelSmall
                                                ?.fontWeight,
                                            height: 0,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 4.h,
                                  ),
                                  SizedBox(
                                    height: 30.h,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Obx(() {
                                          return Text(
                                            '${Get.find<AppConfigController>().currencySymbol!.value.toString()}''${contractListCtl.contractList[i].loan_amount.toString().isNotEmpty ? contractListCtl.contractList[i].loan_amount.toString() : ''}',
                                            style: TextStyle(
                                              color: appConfigService
                                                          .countryConfigCollection
                                                          .toString() ==
                                                      'rafco'
                                                  ? Colors.white
                                                  : Color(0xFF1A1818),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .displaySmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .displaySmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .displaySmall
                                                  ?.fontWeight,
                                              height: 0,
                                            ),
                                          );
                                        }),
                                        Obx(() {
                                          return Text(
                                            contractListCtl
                                                    .contractList[i].remain
                                                    .toString()
                                                    .isNotEmpty
                                                ? CFormatter.doubleFormatCurrency(contractListCtl
                                                    .contractList[i].remain.toString())
                                                : '',
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color: appConfigService
                                                          .countryConfigCollection
                                                          .toString() ==
                                                      'rafco'
                                                  ? Colors.white
                                                  : Color(0xFF1A1818),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .displaySmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .displaySmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .displaySmall
                                                  ?.fontWeight,
                                              height: 0,
                                            ),
                                          );
                                        })
                                      ],
                                    ),
                                  ),
                                  //TODO : Progress Bar
                                  Container(
                                    color: Colors.transparent,
                                    width: Get.width,
                                    height: 20.h,
                                    child: Obx(() {
                                      return LinearPercentIndicator(
                                        lineHeight: 12.h,
                                        percent:
                                            contractListCtl.contactPercent![i],
                                        barRadius: const Radius.circular(10),
                                        widgetIndicator: contractListCtl
                                            .contractList[i].ctt_code
                                            .toString()
                                            .substring(6, 8) ==
                                            "DT" || contractListCtl
                                            .contractList[i].ctt_code
                                            .toString()
                                            .substring(6, 8) ==
                                            "DL"? Container():SvgPicture.string(
                                          guarunteeIcon(contractListCtl
                                              .contractList[i].guarantee_type
                                              .toString()),
                                          width: 20.w,
                                          height: 20.h,
                                        ),
                                        backgroundColor: configTheme()
                                            .colorScheme
                                            .secondary
                                            .withOpacity(0.2),
                                        linearGradient: LinearGradient(
                                          begin: Alignment(0.00, -1.00),
                                          end: Alignment(0, 1),
                                          colors: [
                                            configTheme()
                                                .colorScheme
                                                .secondary
                                                .withOpacity(0.2),
                                            configTheme()
                                                .colorScheme
                                                .secondary
                                                .withOpacity(1.0)
                                          ],
                                        ),
                                        padding: EdgeInsets.zero,
                                        maskFilter: const MaskFilter.blur(
                                            BlurStyle.solid, 3),
                                        clipLinearGradient: true,
                                        animateFromLastPercent: true,
                                        animation: true,
                                        animationDuration: 1000,
                                      );
                                    }),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  SizedBox(
                                    height: 38.h,
                                    child: Column(
                                      children: [
                                        SizedBox(
                                          height: 19.h,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                homeNoContract.tr,
                                                style: TextStyle(
                                                  color: appConfigService
                                                              .countryConfigCollection
                                                              .toString() ==
                                                          'rafco'
                                                      ? Colors.white
                                                          .withOpacity(
                                                              0.699999988079071)
                                                      : Color(0x7F1A1818),
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontWeight,
                                                  height: 0,
                                                ),
                                              ),
                                              Text(
                                                homeLoanGuarantee.tr,
                                                textAlign: TextAlign.right,
                                                style: TextStyle(
                                                  color: appConfigService
                                                              .countryConfigCollection
                                                              .toString() ==
                                                          'rafco'
                                                      ? Colors.white
                                                          .withOpacity(
                                                              0.699999988079071)
                                                      : Color(0x7F1A1818),
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontWeight,
                                                  height: 0,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 19.h,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Obx(() {
                                                return Text(
                                                  contractListCtl
                                                          .contractList[i]
                                                          .ctt_code
                                                          .toString()
                                                          .isNotEmpty
                                                      ? contractListCtl
                                                          .contractList[i]
                                                          .ctt_code
                                                          .toString()
                                                      : '',
                                                  style: TextStyle(
                                                    color: appConfigService
                                                                .countryConfigCollection
                                                                .toString() ==
                                                            'rafco'
                                                        ? Colors.white
                                                        : Color(0xFF1A1818),
                                                    fontSize: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontSize,
                                                    fontFamily: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontFamily,
                                                    fontWeight: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontWeight,
                                                    height: 0,
                                                  ),
                                                );
                                              }),
                                              Obx(() {
                                                return Text(
                                                  contractListCtl.getGuaranteeTypeName(i),
                                                  //   contractListCtl
                                                  //       .contractList[i].ctt_code
                                                  //       .toString()
                                                  //       .substring(6, 8) ==
                                                  //       "DT"? "เอเอเอ็ม เปย์"
                                                  // :contractListCtl
                                                  //         .contractList[i]
                                                  //         .guarantee_type
                                                  //         .toString()
                                                  //         .isNotEmpty
                                                  //     ? contractListCtl
                                                  //                 .contractList[
                                                  //                     i]
                                                  //                 .guarantee_type
                                                  //                 .toString() ==
                                                  //             '1'
                                                  //         ? contractListCtl
                                                  //             .guaranteeList[0]
                                                  //         : contractListCtl
                                                  //                     .contractList[
                                                  //                         i]
                                                  //                     .guarantee_type
                                                  //                     .toString() ==
                                                  //                 '2'
                                                  //             ? contractListCtl
                                                  //                     .guaranteeList[
                                                  //                 1]
                                                  //             : contractListCtl
                                                  //                         .contractList[
                                                  //                             i]
                                                  //                         .guarantee_type
                                                  //                         .toString() ==
                                                  //                     '3'
                                                  //                 ? contractListCtl
                                                  //                     .guaranteeList[2]
                                                  //                 : ''
                                                  //     : '',
                                                  textAlign: TextAlign.right,
                                                  style: TextStyle(
                                                    color: appConfigService
                                                                .countryConfigCollection
                                                                .toString() ==
                                                            'rafco'
                                                        ? Colors.white
                                                        : Color(0xFF1A1818),
                                                    fontSize: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontSize,
                                                    fontFamily: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontFamily,
                                                    fontWeight: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontWeight,
                                                    height: 0,
                                                  ),
                                                );
                                              }),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 13.5.h,
                                  ),
                                  SizedBox(
                                    height: 1.h,
                                    child: Divider(
                                      color: configTheme().dividerTheme.color,
                                      thickness:
                                          configTheme().dividerTheme.thickness,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 13.5.h,
                                  ),
                                  SizedBox(
                                    height: 38.h,
                                    child: Column(
                                      children: [
                                        SizedBox(
                                          height: 19.h,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                homeLoanMinPay.tr,
                                                style: TextStyle(
                                                  color: appConfigService
                                                              .countryConfigCollection
                                                              .toString() ==
                                                          'rafco'
                                                      ? Colors.white
                                                          .withOpacity(
                                                              0.699999988079071)
                                                      : Color(0x7F1A1818),
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontWeight,
                                                  height: 0,
                                                ),
                                              ),
                                              Text(
                                                homePaymentDate.tr,
                                                textAlign: TextAlign.right,
                                                style: TextStyle(
                                                  color: appConfigService
                                                              .countryConfigCollection
                                                              .toString() ==
                                                          'rafco'
                                                      ? Colors.white
                                                          .withOpacity(
                                                              0.699999988079071)
                                                      : Color(0x7F1A1818),
                                                  fontSize: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontSize,
                                                  fontFamily: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontFamily,
                                                  fontWeight: configTheme()
                                                      .primaryTextTheme
                                                      .labelSmall
                                                      ?.fontWeight,
                                                  height: 0,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 19.h,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Obx(() {
                                                return Text(
                                                  contractListCtl
                                                          .contractList[i]
                                                          .nextpay
                                                          .toString()
                                                          .isNotEmpty
                                                      ? CFormatter.doubleFormatCurrency(contractListCtl
                                                          .contractList[i]
                                                          .nextpay
                                                          .toString())
                                                      : '',
                                                  style: TextStyle(
                                                    color: appConfigService
                                                                .countryConfigCollection
                                                                .toString() ==
                                                            'rafco'
                                                        ? Colors.white
                                                        : Color(0xFF1A1818),
                                                    fontSize: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontSize,
                                                    fontFamily: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontFamily,
                                                    fontWeight: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontWeight,
                                                    height: 0,
                                                  ),
                                                );
                                              }),
                                              Obx(() {
                                                return Text(
                                                  contractListCtl
                                                          .contractList[i]
                                                          .due_date
                                                          .toString()
                                                          .isNotEmpty
                                                      ? contractListCtl
                                                          .setDueDateLocale(
                                                              contractListCtl
                                                                  .contractList[
                                                                      i]
                                                                  .due_date
                                                                  .toString(),
                                                      Get.find<TransalationController>().location.value == 'English'
                                                          ? 'en'
                                                          : Get.find<TransalationController>().location.value == 'Thailand' || appConfigService.countryConfigCollection == 'aam'
                                                          ? 'th'
                                                          : Get.find<TransalationController>().location.value ==
                                                          'Lao' ||
                                                          appConfigService.countryConfigCollection == 'rplc'
                                                          ? 'lo'
                                                          : Get.find<TransalationController>().location.value ==
                                                          'Cambodian' ||
                                                          appConfigService.countryConfigCollection ==
                                                              'rafco'
                                                          ? 'km'
                                                          : 'en')
                                                      : '',
                                                  textAlign: TextAlign.right,
                                                  style: TextStyle(
                                                    color: appConfigService
                                                                .countryConfigCollection
                                                                .toString() ==
                                                            'rafco'
                                                        ? Colors.white
                                                        : Color(0xFF1A1818),
                                                    fontSize: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontSize,
                                                    fontFamily: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontFamily,
                                                    fontWeight: configTheme()
                                                        .primaryTextTheme
                                                        .labelMedium
                                                        ?.fontWeight,
                                                    height: 0,
                                                  ),
                                                );
                                              }),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                  options: CarouselOptions(
                      autoPlay: false,
                      initialPage: 0,
                      aspectRatio: 1,
                      viewportFraction: 1,
                      height: 204.h,
                      disableCenter: true,
                      enableInfiniteScroll: false,
                      onPageChanged: (index, reason) {
                        homeController.setIndexMyloan(index);
                      }),
                ),
              ),
              SizedBox(height: 16.h),
              Container(
                margin: EdgeInsets.only(left: 24.w, right: 24.w),
                width: 327.w,
                height: 4.h,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                      // 3,
                      contractListCtl.contractList.length,
                      (index) => _buildIndicator(index)),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static String guarunteeIcon(String guarantee_type) {
    AppConfigService appConfig = Get.find<AppConfigService>();
    if (guarantee_type == '1') {
      //TODO car
      return appConfig.countryConfigCollection.toString() == 'aam'
          ? AppSvgImage.aam_myloan_car
          : appConfig.countryConfigCollection.toString() == 'rplc'
              ? AppSvgImage.rplc_myloan_car
              : AppSvgImage.rafco_myloan_car;
    } else if (guarantee_type == '2') {
      //TODO motocycle
      return appConfig.countryConfigCollection.toString() == 'aam'
          ? AppSvgImage.aam_myloan_motocycle
          : appConfig.countryConfigCollection.toString() == 'rplc'
              ? AppSvgImage.rplc_myloan_motocycle
              : AppSvgImage.rafco_myloan_motocycle;
    } else if (guarantee_type == '3') {
      //TODO land
      return appConfig.countryConfigCollection.toString() == 'aam'
          ? AppSvgImage.aam_myloan_land
          : appConfig.countryConfigCollection.toString() == 'rplc'
              ? AppSvgImage.rplc_myloan_land
              : AppSvgImage.rafco_myloan_land;
    } else {
      //TODO other
      return appConfig.countryConfigCollection.toString() == 'aam'
          ? AppSvgImage.aam_myloan_truck
          : appConfig.countryConfigCollection.toString() == 'rplc'
              ? AppSvgImage.rplc_myloan_truck
              : AppSvgImage.rafco_myloan_truck;
    }
  }

  static Widget _buildIndicator(int index) {
    final HomeController homeController = Get.find<HomeController>();
    return Row(
      children: [
        SizedBox(
          width: 6.w,
        ),
        Opacity(
          opacity: homeController.indexMyloan == index ? 1.0 : 0.40,
          child: Container(
            width: homeController.indexMyloan == index ? 15.0 : 4.0,
            height: 4.h,
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.tertiary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
          ),
        ),
        SizedBox(
          width: 6.w,
        ),
      ],
    );
  }
}
