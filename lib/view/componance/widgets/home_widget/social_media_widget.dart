import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
 
import '../../../../controller/branch/branch.controller.dart';
import '../../../../models/social_content_model.dart';
import '../../themes/app_colors.dart';
import '../../themes/theme.dart';
import '../../utils/AppImageAssets.dart';
import '../../utils/AppSvgImage.dart';

class SocialMediaWidget extends StatelessWidget {
   SocialMediaWidget({super.key});

  final BranchController branchController = Get.put(BranchController());

  // List<SocialContentModel> get socialContents => [
  //       SocialContentModel(
  //         appname: "AAM",
  //         branch: "Bangkok",
  //         platform: "facebook",
  //         title: "Facebook",
  //         content: "AAM จัดไฟแนนซ์",
  //         url: "https://facebook.com/aamagency",
  //         timestamp: "2024-06-17 13:00:00",
  //       ),
  //       SocialContentModel(
  //         appname: "AAM",
  //         branch: "Bangkok",
  //         platform: "tiktok",
  //         title: "Tiktok",
  //         content: "AAM จัดไฟแนนซ์",
  //         url: "https://tiktok.com/@aamagency",
  //         timestamp: "2024-06-17 13:00:00",
  //       ),
  //       SocialContentModel(
  //         appname: "AAM",
  //         branch: "Bangkok",
  //         platform: "line",
  //         title: "Line Official",
  //         content: "AAM จัดไฟแนนซ์",
  //         url: "https://line.me/aamofficial",
  //         timestamp: "2024-06-17 13:00:00",
  //       ),
  //       SocialContentModel(
  //         appname: "AAM",
  //         branch: "Bangkok",
  //         platform: "youtube",
  //         title: "YouTube",
  //         content: "AAM จัดไฟแนนซ์",
  //         url: "https://youtube.com/channel/UCxxxxx",
  //         timestamp: "2024-06-17 13:00:00",
  //       ),
  //     ];

  //map platform to asset images
  String platformImage(String platform) {
    switch (platform.toLowerCase()) {
      case "facebook":  return AppImageAssets.h_facebook;
      case "tiktok":  return AppImageAssets.h_tiktok;
      case "line": return AppImageAssets.h_line;
      case "youtube":  return AppImageAssets.h_youtube;
      case "telegram":  return AppImageAssets.h_telegram;
      case "whatsapp":  return AppImageAssets.h_whatsapp;
      default: return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    branchController.setIntialSocialMediaData();
    final List<SocialContentModel> socialMediaList = branchController.socialContents;

    return Container(
      width: Get.width,
      height: 175.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Row(
              children: [
                SizedBox(
                  height: 21.h,
                  child: Image.asset(
                    'assets/home/<USER>',
                    fit: BoxFit.fill,
                    color: configTheme().colorScheme.onSecondary,
                  ),
                ),
                SizedBox(width: 10.w),
                Text(
                  'Social media',
                  style: TextStyle(
                    fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyLarge
                                ?.fontFamily,
                    color: Colors.black,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          Expanded(
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              itemCount: socialMediaList.length,
              separatorBuilder: (_, __) => SizedBox(width: 8.w),
              itemBuilder: (context, index) {
                final item = socialMediaList[index];
                return buildSocialMediaCard(
                  imagePath: platformImage(item.platform),
                  title: item.title,
                  subTitle: item.content,
                  onPressed: () {
                   canLaunchUrl(Uri.parse(item.url)).then((value) {
                      if (value) {
                        launchUrl(Uri.parse(item.url));
                      }
                    });
                    //url
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSocialMediaCard({
    required String imagePath,
    required String title,
    required String subTitle,
    Color? subTextColor,
    required VoidCallback onPressed,
    double width = 88.0,
    double height = 130.0,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: width.w,
        height: height.h,
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 247, 247, 247),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Stack(
            children: [
              Positioned(
                bottom: 0,
                right: 0,
                child:  appConfigService.countryConfigCollection.toString() =='rafco' ?
                        SvgPicture.string(AppSvgImage.ellip_shape_rafco,  fit: BoxFit.contain,)
                        : appConfigService.countryConfigCollection .toString() ==  'rplc' ?
                        SvgPicture.string(AppSvgImage.ellip_shape_rplc,  fit: BoxFit.contain,)
                        :  SvgPicture.string(AppSvgImage.ellip_shape_aam,  fit: BoxFit.contain,) ,
              ),
              Positioned(
                bottom: 0,
                right: -5,
                width: 58.w,
                    height:62.h,
                child: Image.asset(
                  AppImageAssets.greyShape2,
                  fit: BoxFit.contain,
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: SizedBox(
                  child: Image.asset(
                    width: 58.w,
                    height:62.h,
                    AppImageAssets.greyShape1,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.only(
                        left: 10.w, top: 16.h, right: 8.w, ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: FontWeight.w700,
                            color: Colors.black,
                            fontSize: 12.sp,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          subTitle,
                          style: TextStyle(
                            color:  appConfigService.countryConfigCollection.toString() =='rafco' ?
                          AppColors.primaryRafco
                          : appConfigService.countryConfigCollection .toString() ==  'rplc' ?
                          AppColors.primaryRPLC
                          :  AppColors.primaryAAM,
                            fontWeight: FontWeight.w700,
                            fontSize: 8.sp,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall
                                ?.fontFamily,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Center(
                      child: Image.asset(
                    imagePath,
                    width: 80.w,
                    height: 90.h,
                  )),
                  SizedBox(height: 5.h),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
