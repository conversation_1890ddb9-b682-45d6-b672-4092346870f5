import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:AAMG/controller/service/AppService.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../controller/config/appConfig.controller.dart';
import '../../../../controller/likepoint/likepoint.controller.dart';
import '../../../../controller/profile/profile.controller.dart';
import '../../../../controller/transalation/transalation.controller.dart';
import '../../../../controller/transalation/translation_key.dart';
import '../../../screen/minilike/webview_minilike.dart';
import '../../../screen/profile/edit_profile.dart';
import '../../../screen/profile/profile_page.dart';
import '../../../screen/register/register_page.dart';
import '../../themes/app_textstyle.dart';
import '../../themes/theme.dart';
import '../../utils/AppImageAssets.dart';
import '../animation/shimmer_effect.dart';

class HomePageCoponentWidget {
  static Widget heading_bar(context) {
    final HomeController homeController = Get.put(HomeController());
    final chatInAppController = Get.put(ChatInAppController());
    final box = GetStorage();
    final NavigationController navigationController =
        Get.find<NavigationController>();
    final ProfileController profileController = Get.find<ProfileController>();
    return Container(
      width: Get.width,
      height: 88.h,
      padding: EdgeInsets.only(bottom: 6.h, left: 24.w, right: 24.w),
      color: Colors.white.withOpacity(0.9),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          SizedBox(
            // width: 50,
            height: 24.h,
            child: Text(
              homeMain.tr,
              style: TextStyle(
                color: const Color(0xFF1A1818),
                fontSize: 14.sp,
                fontFamily: TextStyleTheme.text_Bold.fontFamily,
                fontWeight: FontWeight.w700,
                height: 0,
              ),
            ),
          ),
          SizedBox(
            width: 125.w,
            height: 24.h,
            child: Row(
              // mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // appConfigService.countryConfigCollection.toString() == "aam"
                //     ? InkWell(
                //         onTap: () {
                //           print("home");
                //           var i = box.read('isHomePopupShown');
                //           print(i);
                //           if (i == true) {
                //             i = box.write("isHomePopupShown", null);
                //             print(i);
                //             navigationController.update();
                //             homeController.isIndexTutorial.value = 0;
                //             profileController.isShow.value = false;
                //             navigationController.showTutorial();
                //           }
                //         },
                //         child: Container(
                //             height: 22.h,
                //             width: 22.w,
                //             child: SvgPicture.string(
                //                 '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3242_15175)"> <path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 8.10001V12.89" stroke="#792AFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 16.5H12.01" stroke="#792AFF" stroke-width="2" stroke-linecap="round"/></g><defs> <clipPath id="clip0_3242_15175"> <rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                //       )
                //     : appConfigService.countryConfigCollection.toString() ==
                //             "rafco"
                //         ? InkWell(
                //             onTap: () {
                //               print("home");
                //               var i = box.read('isHomePopupShown');
                //               print(i);
                //               if (i == true) {
                //                 i = box.write("isHomePopupShown", null);
                //                 print(i);
                //                 navigationController.update();
                //                 homeController.isIndexTutorial.value = 0;
                //                 profileController.isShow.value = false;
                //                 navigationController.showTutorial();
                //               }
                //             },
                //             child: Container(
                //                 child: SvgPicture.string(
                //                     '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_67041)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.10001V12.89" stroke="#EA1B23" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#EA1B23" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3898_67041"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                //           )
                //         : InkWell(
                //             onTap: () {
                //               print("home");
                //               var i = box.read('isHomePopupShown');
                //               print(i);
                //               if (i == true) {
                //                 i = box.write("isHomePopupShown", null);
                //                 print(i);
                //                 navigationController.update();
                //                 homeController.isIndexTutorial.value = 0;
                //                 profileController.isShow.value = false;
                //                 navigationController.showTutorial();
                //               }
                //             },
                //             child: Container(
                //                 child: SvgPicture.string(
                //                     '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_32814)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.09998V12.89" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#FFC20E" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_32814"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                //           ),
                // SizedBox(
                //   width: 10.w,
                // ),

                /// chat
                GestureDetector(
                  onTap: () {
                    if (homeController.isGuest!.value) {
                      Get.to(() => const RegisterPage());
                    } else {
                      /// chat in app
                      chatInAppController.fullProcessRegister(context);
                    }
                  },
                  child: SizedBox(
                    width: 24.w,
                    height: 24.h,
                    child: Center(
                      child: SvgPicture.string(
                        fit: BoxFit.fill,
                        appConfigService.countryConfigCollection.toString() ==
                                "aam"
                            ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M19.3259 5.77772L18.827 6.11106L18.827 6.11106L19.3259 5.77772ZM19.3259 16.2223L18.827 15.8889L18.827 15.8889L19.3259 16.2223ZM18.2223 17.3259L17.8889 16.827L17.8889 16.827L18.2223 17.3259ZM14 17.9986L13.9974 17.3986C13.667 17.4001 13.4 17.6683 13.4 17.9986H14ZM14 18L14.5367 18.2683C14.5783 18.185 14.6 18.0931 14.6 18H14ZM10 18H9.4C9.4 18.0931 9.42169 18.185 9.46334 18.2683L10 18ZM10 17.9986H10.6C10.6 17.6683 10.333 17.4001 10.0026 17.3986L10 17.9986ZM5.77772 17.3259L6.11106 16.827L6.11106 16.827L5.77772 17.3259ZM4.67412 16.2223L5.173 15.8889L5.173 15.8889L4.67412 16.2223ZM4.67412 5.77772L5.173 6.11106L4.67412 5.77772ZM5.77772 4.67412L6.11106 5.173L5.77772 4.67412ZM18.2223 4.67412L17.8889 5.173L17.8889 5.173L18.2223 4.67412ZM20.6 11C20.6 9.60803 20.6007 8.51888 20.5127 7.65313C20.4236 6.77734 20.2387 6.06384 19.8248 5.44438L18.827 6.11106C19.0872 6.50049 19.2394 6.99367 19.3188 7.77457C19.3993 8.5655 19.4 9.58305 19.4 11H20.6ZM19.8248 16.5556C20.2387 15.9362 20.4236 15.2227 20.5127 14.3469C20.6007 13.4811 20.6 12.392 20.6 11H19.4C19.4 12.417 19.3993 13.4345 19.3188 14.2254C19.2394 15.0063 19.0872 15.4995 18.827 15.8889L19.8248 16.5556ZM18.5556 17.8248C19.0579 17.4892 19.4892 17.0579 19.8248 16.5556L18.827 15.8889C18.5789 16.2602 18.2602 16.5789 17.8889 16.827L18.5556 17.8248ZM14.0026 18.5986C15.0786 18.5939 15.9514 18.5735 16.6719 18.4739C17.4021 18.373 18.0146 18.1863 18.5556 17.8248L17.8889 16.827C17.5487 17.0544 17.1288 17.1993 16.5076 17.2852C15.8767 17.3724 15.0791 17.3939 13.9974 17.3986L14.0026 18.5986ZM14.6 18V17.9986H13.4V18H14.6ZM13.4311 20.4795L14.5367 18.2683L13.4633 17.7317L12.3578 19.9428L13.4311 20.4795ZM10.5689 20.4795C11.1586 21.6588 12.8414 21.6588 13.4311 20.4795L12.3578 19.9428C12.2104 20.2376 11.7896 20.2376 11.6422 19.9428L10.5689 20.4795ZM9.46334 18.2683L10.5689 20.4795L11.6422 19.9428L10.5367 17.7317L9.46334 18.2683ZM9.4 17.9986V18H10.6V17.9986H9.4ZM5.44438 17.8248C5.98544 18.1863 6.59793 18.373 7.32807 18.4739C8.04857 18.5735 8.92139 18.5939 9.99738 18.5986L10.0026 17.3986C8.92091 17.3939 8.12326 17.3724 7.49239 17.2852C6.87115 17.1993 6.45135 17.0544 6.11106 16.827L5.44438 17.8248ZM4.17524 16.5556C4.51085 17.0579 4.9421 17.4892 5.44438 17.8248L6.11106 16.827C5.73981 16.5789 5.42106 16.2602 5.173 15.8889L4.17524 16.5556ZM3.4 11C3.4 12.392 3.39927 13.4811 3.48735 14.3469C3.57644 15.2227 3.76133 15.9362 4.17524 16.5556L5.173 15.8889C4.9128 15.4995 4.76062 15.0063 4.68118 14.2254C4.60073 13.4345 4.6 12.417 4.6 11H3.4ZM4.17524 5.44438C3.76133 6.06384 3.57644 6.77734 3.48735 7.65313C3.39927 8.51888 3.4 9.60803 3.4 11H4.6C4.6 9.58305 4.60073 8.5655 4.68118 7.77457C4.76062 6.99367 4.9128 6.50049 5.173 6.11106L4.17524 5.44438ZM5.44438 4.17524C4.9421 4.51085 4.51085 4.9421 4.17524 5.44438L5.173 6.11106C5.42106 5.73981 5.73981 5.42106 6.11106 5.173L5.44438 4.17524ZM11 3.4C9.60803 3.4 8.51888 3.39927 7.65313 3.48735C6.77734 3.57644 6.06384 3.76133 5.44438 4.17524L6.11106 5.173C6.50049 4.9128 6.99367 4.76062 7.77457 4.68118C8.5655 4.60073 9.58305 4.6 11 4.6V3.4ZM13 3.4H11V4.6H13V3.4ZM18.5556 4.17524C17.9362 3.76133 17.2227 3.57644 16.3469 3.48735C15.4811 3.39927 14.392 3.4 13 3.4V4.6C14.417 4.6 15.4345 4.60073 16.2254 4.68118C17.0063 4.76062 17.4995 4.9128 17.8889 5.173L18.5556 4.17524ZM19.8248 5.44438C19.4892 4.9421 19.0579 4.51085 18.5556 4.17524L17.8889 5.173C18.2602 5.42106 18.5789 5.73981 18.827 6.11106L19.8248 5.44438Z" fill="#1A1818"/><circle cx="16" cy="11" r="1" fill="#792AFF" fill-opacity="0.5"/><circle cx="12" cy="11" r="1" fill="#792AFF" fill-opacity="0.5"/><circle cx="8" cy="11" r="1" fill="#792AFF" fill-opacity="0.5"/></svg>'
                            : appConfigService.countryConfigCollection
                                        .toString() ==
                                    "rafco"
                                ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M19.3259 5.77772L18.827 6.11106L18.827 6.11106L19.3259 5.77772ZM19.3259 16.2223L18.827 15.8889L18.827 15.8889L19.3259 16.2223ZM18.2223 17.3259L17.8889 16.827L17.8889 16.827L18.2223 17.3259ZM14 17.9986L13.9974 17.3986C13.667 17.4001 13.4 17.6683 13.4 17.9986H14ZM14 18L14.5367 18.2683C14.5783 18.185 14.6 18.0931 14.6 18H14ZM10 18H9.4C9.4 18.0931 9.42169 18.185 9.46334 18.2683L10 18ZM10 17.9986H10.6C10.6 17.6683 10.333 17.4001 10.0026 17.3986L10 17.9986ZM5.77772 17.3259L6.11106 16.827L6.11106 16.827L5.77772 17.3259ZM4.67412 16.2223L5.173 15.8889L5.173 15.8889L4.67412 16.2223ZM4.67412 5.77772L5.173 6.11106L4.67412 5.77772ZM5.77772 4.67412L6.11106 5.173L5.77772 4.67412ZM18.2223 4.67412L17.8889 5.173L17.8889 5.173L18.2223 4.67412ZM20.6 11C20.6 9.60803 20.6007 8.51888 20.5127 7.65313C20.4236 6.77734 20.2387 6.06384 19.8248 5.44438L18.827 6.11106C19.0872 6.50049 19.2394 6.99367 19.3188 7.77457C19.3993 8.5655 19.4 9.58305 19.4 11H20.6ZM19.8248 16.5556C20.2387 15.9362 20.4236 15.2227 20.5127 14.3469C20.6007 13.4811 20.6 12.392 20.6 11H19.4C19.4 12.417 19.3993 13.4345 19.3188 14.2254C19.2394 15.0063 19.0872 15.4995 18.827 15.8889L19.8248 16.5556ZM18.5556 17.8248C19.0579 17.4892 19.4892 17.0579 19.8248 16.5556L18.827 15.8889C18.5789 16.2602 18.2602 16.5789 17.8889 16.827L18.5556 17.8248ZM14.0026 18.5986C15.0786 18.5939 15.9514 18.5735 16.6719 18.4739C17.4021 18.373 18.0146 18.1863 18.5556 17.8248L17.8889 16.827C17.5487 17.0544 17.1288 17.1993 16.5076 17.2852C15.8767 17.3724 15.0791 17.3939 13.9974 17.3986L14.0026 18.5986ZM14.6 18V17.9986H13.4V18H14.6ZM13.4311 20.4795L14.5367 18.2683L13.4633 17.7317L12.3578 19.9428L13.4311 20.4795ZM10.5689 20.4795C11.1586 21.6588 12.8414 21.6588 13.4311 20.4795L12.3578 19.9428C12.2104 20.2376 11.7896 20.2376 11.6422 19.9428L10.5689 20.4795ZM9.46334 18.2683L10.5689 20.4795L11.6422 19.9428L10.5367 17.7317L9.46334 18.2683ZM9.4 17.9986V18H10.6V17.9986H9.4ZM5.44438 17.8248C5.98544 18.1863 6.59793 18.373 7.32807 18.4739C8.04857 18.5735 8.92139 18.5939 9.99738 18.5986L10.0026 17.3986C8.92091 17.3939 8.12326 17.3724 7.49239 17.2852C6.87115 17.1993 6.45135 17.0544 6.11106 16.827L5.44438 17.8248ZM4.17524 16.5556C4.51085 17.0579 4.9421 17.4892 5.44438 17.8248L6.11106 16.827C5.73981 16.5789 5.42106 16.2602 5.173 15.8889L4.17524 16.5556ZM3.4 11C3.4 12.392 3.39927 13.4811 3.48735 14.3469C3.57644 15.2227 3.76133 15.9362 4.17524 16.5556L5.173 15.8889C4.9128 15.4995 4.76062 15.0063 4.68118 14.2254C4.60073 13.4345 4.6 12.417 4.6 11H3.4ZM4.17524 5.44438C3.76133 6.06384 3.57644 6.77734 3.48735 7.65313C3.39927 8.51888 3.4 9.60803 3.4 11H4.6C4.6 9.58305 4.60073 8.5655 4.68118 7.77457C4.76062 6.99367 4.9128 6.50049 5.173 6.11106L4.17524 5.44438ZM5.44438 4.17524C4.9421 4.51085 4.51085 4.9421 4.17524 5.44438L5.173 6.11106C5.42106 5.73981 5.73981 5.42106 6.11106 5.173L5.44438 4.17524ZM11 3.4C9.60803 3.4 8.51888 3.39927 7.65313 3.48735C6.77734 3.57644 6.06384 3.76133 5.44438 4.17524L6.11106 5.173C6.50049 4.9128 6.99367 4.76062 7.77457 4.68118C8.5655 4.60073 9.58305 4.6 11 4.6V3.4ZM13 3.4H11V4.6H13V3.4ZM18.5556 4.17524C17.9362 3.76133 17.2227 3.57644 16.3469 3.48735C15.4811 3.39927 14.392 3.4 13 3.4V4.6C14.417 4.6 15.4345 4.60073 16.2254 4.68118C17.0063 4.76062 17.4995 4.9128 17.8889 5.173L18.5556 4.17524ZM19.8248 5.44438C19.4892 4.9421 19.0579 4.51085 18.5556 4.17524L17.8889 5.173C18.2602 5.42106 18.5789 5.73981 18.827 6.11106L19.8248 5.44438Z" fill="#1A1818"/><circle cx="16" cy="11" r="1" fill="#EA1B23" fill-opacity="0.5"/><circle cx="12" cy="11" r="1" fill="#EA1B23" fill-opacity="0.5"/><circle cx="8" cy="11" r="1" fill="#EA1B23" fill-opacity="0.5"/></svg>'
                                : '<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M16.3259 2.77772L15.827 3.11106L15.827 3.11106L16.3259 2.77772ZM16.3259 13.2223L15.827 12.8889L15.827 12.8889L16.3259 13.2223ZM15.2223 14.3259L14.8889 13.827L14.8889 13.827L15.2223 14.3259ZM11 14.9986L10.9974 14.3986C10.667 14.4001 10.4 14.6683 10.4 14.9986H11ZM11 15L11.5367 15.2683C11.5783 15.185 11.6 15.0931 11.6 15H11ZM7 15H6.4C6.4 15.0931 6.42169 15.185 6.46334 15.2683L7 15ZM7 14.9986H7.6C7.6 14.6683 7.33296 14.4001 7.00262 14.3986L7 14.9986ZM2.77772 14.3259L3.11106 13.827L3.11106 13.827L2.77772 14.3259ZM1.67412 13.2223L2.173 12.8889L2.173 12.8889L1.67412 13.2223ZM1.67412 2.77772L2.173 3.11106L1.67412 2.77772ZM2.77772 1.67412L3.11106 2.173L2.77772 1.67412ZM15.2223 1.67412L14.8889 2.173L14.8889 2.173L15.2223 1.67412ZM17.6 8C17.6 6.60803 17.6007 5.51888 17.5127 4.65313C17.4236 3.77734 17.2387 3.06384 16.8248 2.44438L15.827 3.11106C16.0872 3.50049 16.2394 3.99367 16.3188 4.77457C16.3993 5.5655 16.4 6.58305 16.4 8H17.6ZM16.8248 13.5556C17.2387 12.9362 17.4236 12.2227 17.5127 11.3469C17.6007 10.4811 17.6 9.39197 17.6 8H16.4C16.4 9.41695 16.3993 10.4345 16.3188 11.2254C16.2394 12.0063 16.0872 12.4995 15.827 12.8889L16.8248 13.5556ZM15.5556 14.8248C16.0579 14.4892 16.4892 14.0579 16.8248 13.5556L15.827 12.8889C15.5789 13.2602 15.2602 13.5789 14.8889 13.827L15.5556 14.8248ZM11.0026 15.5986C12.0786 15.5939 12.9514 15.5735 13.6719 15.4739C14.4021 15.373 15.0146 15.1863 15.5556 14.8248L14.8889 13.827C14.5487 14.0544 14.1288 14.1993 13.5076 14.2852C12.8767 14.3724 12.0791 14.3939 10.9974 14.3986L11.0026 15.5986ZM11.6 15V14.9986H10.4V15H11.6ZM10.4311 17.4795L11.5367 15.2683L10.4633 14.7317L9.35777 16.9428L10.4311 17.4795ZM7.56892 17.4795C8.15856 18.6588 9.84144 18.6588 10.4311 17.4795L9.35777 16.9428C9.21036 17.2376 8.78964 17.2376 8.64223 16.9428L7.56892 17.4795ZM6.46334 15.2683L7.56892 17.4795L8.64223 16.9428L7.53666 14.7317L6.46334 15.2683ZM6.4 14.9986V15H7.6V14.9986H6.4ZM2.44438 14.8248C2.98544 15.1863 3.59793 15.373 4.32807 15.4739C5.04857 15.5735 5.92139 15.5939 6.99738 15.5986L7.00262 14.3986C5.92091 14.3939 5.12326 14.3724 4.49239 14.2852C3.87115 14.1993 3.45135 14.0544 3.11106 13.827L2.44438 14.8248ZM1.17524 13.5556C1.51085 14.0579 1.9421 14.4892 2.44438 14.8248L3.11106 13.827C2.73981 13.5789 2.42106 13.2602 2.173 12.8889L1.17524 13.5556ZM0.4 8C0.4 9.39197 0.399275 10.4811 0.487346 11.3469C0.576437 12.2227 0.761326 12.9362 1.17524 13.5556L2.173 12.8889C1.9128 12.4995 1.76062 12.0063 1.68118 11.2254C1.60073 10.4345 1.6 9.41695 1.6 8H0.4ZM1.17524 2.44438C0.761326 3.06384 0.576437 3.77734 0.487346 4.65313C0.399275 5.51888 0.4 6.60803 0.4 8H1.6C1.6 6.58305 1.60073 5.5655 1.68118 4.77457C1.76062 3.99367 1.9128 3.50049 2.173 3.11106L1.17524 2.44438ZM2.44438 1.17524C1.9421 1.51085 1.51085 1.9421 1.17524 2.44438L2.173 3.11106C2.42106 2.73981 2.73981 2.42106 3.11106 2.173L2.44438 1.17524ZM8 0.4C6.60803 0.4 5.51888 0.399275 4.65313 0.487346C3.77734 0.576437 3.06384 0.761326 2.44438 1.17524L3.11106 2.173C3.50049 1.9128 3.99367 1.76062 4.77457 1.68118C5.5655 1.60073 6.58305 1.6 8 1.6V0.4ZM10 0.4H8V1.6H10V0.4ZM15.5556 1.17524C14.9362 0.761326 14.2227 0.576437 13.3469 0.487346C12.4811 0.399275 11.392 0.4 10 0.4V1.6C11.417 1.6 12.4345 1.60073 13.2254 1.68118C14.0063 1.76062 14.4995 1.9128 14.8889 2.173L15.5556 1.17524ZM16.8248 2.44438C16.4892 1.9421 16.0579 1.51085 15.5556 1.17524L14.8889 2.173C15.2602 2.42106 15.5789 2.73981 15.827 3.11106L16.8248 2.44438Z" fill="#1A1818"/><circle cx="13" cy="8" r="1" fill="#6A7165"/><circle cx="9" cy="8" r="1" fill="#6A7165"/><circle cx="5" cy="8" r="1" fill="#6A7165"/></svg>',
                        // fit: BoxFit.fill
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),

                /// setting
                GestureDetector(
                  onTap: () {
                    print("sdssdsddsdsdsdd");
                    Get.to(() => const ProfilePage());
                    // transalationCtl.showListLanguage(appConfigService.countryConfigCollection.toString() == 'aam'?"aam":appConfigService.countryConfigCollection.toString() == 'rafco'?"rafco":"rplc");
                  },
                  child: SizedBox(
                    width: 24.w,
                    height: 24.h,
                    child: SvgPicture.string(
                        appConfigService.countryConfigCollection.toString() ==
                                "rplc"
                            ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M19.6441 12.8093C19.4902 12.6837 19.4024 12.5001 19.4024 12.3068V12C19.4024 12 19.4024 11.9994 19.4024 11.9988V11.692C19.4024 11.4987 19.4908 11.3151 19.6447 11.1888L20.3621 10.6C21.0306 10.0509 21.1953 9.12563 20.7533 8.39843L19.66 6.60036C19.218 5.87316 18.2913 5.54641 17.4569 5.82122L16.5557 6.11899C16.3617 6.18301 16.1499 6.16308 15.9725 6.06523L15.416 5.75659C15.2417 5.65996 15.1202 5.49628 15.0821 5.30783L14.904 4.42178C14.7386 3.59795 13.9767 3 13.0933 3H10.9067C10.0233 3 9.26138 3.59795 9.09539 4.42239L8.91731 5.30783C8.87915 5.49628 8.75767 5.65996 8.58341 5.75659L8.02755 6.06463C7.85074 6.16308 7.63832 6.18301 7.44497 6.11899L6.54249 5.82182C5.70806 5.54641 4.78141 5.87377 4.34002 6.60036L3.24674 8.39903C2.80472 9.12563 2.96944 10.0515 3.63788 10.6006L4.35529 11.1895C4.5092 11.3157 4.5976 11.4993 4.5976 11.6926V12.0006V12.3074C4.5976 12.5007 4.50983 12.6843 4.35529 12.8105L3.63788 13.3994C2.96944 13.9485 2.80472 14.8738 3.24674 15.601L4.34002 17.399C4.78204 18.1262 5.70996 18.453 6.54312 18.1782L7.44433 17.8804C7.63768 17.8164 7.8501 17.8363 8.02755 17.9342L8.58405 18.2428C8.75831 18.3394 8.87979 18.5031 8.91795 18.6916L9.09603 19.5776C9.26202 20.4021 10.0239 21 10.9074 21H13.0939C13.9773 21 14.7392 20.4021 14.9052 19.5776L15.0833 18.6922C15.1215 18.5037 15.243 18.34 15.4172 18.2434L15.9731 17.9354C16.1493 17.8369 16.3623 17.8164 16.5557 17.881L17.4569 18.1788C18.2913 18.4542 19.218 18.1274 19.6593 17.4002L20.7526 15.6016C21.1946 14.875 21.0299 13.9491 20.3615 13.4L19.6441 12.8093ZM19.7624 15.0562L18.6685 16.8548C18.5012 17.1303 18.1488 17.2541 17.8334 17.1502L16.9322 16.8524C16.4221 16.6839 15.8618 16.7365 15.3956 16.995L14.8397 17.303C14.3799 17.5579 14.0587 17.9891 13.9589 18.4862L13.7808 19.3717C13.7172 19.6845 13.4285 19.9116 13.0933 19.9116H10.9067C10.5715 19.9116 10.2828 19.6845 10.2192 19.3717L10.0411 18.4862C9.94127 17.9891 9.62009 17.5579 9.16026 17.303L8.6044 16.9944C8.3182 16.8361 7.99702 16.7552 7.67393 16.7552C7.46978 16.7552 7.26498 16.7872 7.06782 16.8524L6.16661 17.1502C5.84925 17.2541 5.49881 17.1303 5.33091 16.8548L4.23763 15.0562C4.06972 14.7808 4.13269 14.4292 4.38581 14.2215L5.10322 13.6326C5.50962 13.2998 5.7424 12.816 5.74176 12.3056V12V11.6932C5.7424 11.1828 5.50962 10.699 5.10322 10.3662L4.38581 9.77733C4.13269 9.56956 4.06972 9.21804 4.23763 8.94262L5.33154 7.14395C5.49881 6.86853 5.85179 6.74532 6.16661 6.8486L7.06782 7.14637C7.57853 7.31488 8.13821 7.26294 8.6044 7.00383L9.16026 6.69579C9.62009 6.44091 9.94127 6.00966 10.0411 5.51258L10.2192 4.62714C10.2828 4.31427 10.5715 4.08718 10.9067 4.08718H13.0933C13.4285 4.08718 13.7172 4.31427 13.7808 4.62714L13.9589 5.51258C14.0587 6.00966 14.3799 6.44091 14.8397 6.69579L15.3956 7.00443C15.8618 7.26233 16.4215 7.31488 16.9322 7.14637L17.8334 6.8486C18.1501 6.74472 18.5012 6.86853 18.6691 7.14395L19.7624 8.94262C19.9303 9.21804 19.8673 9.56956 19.6142 9.77733L18.8968 10.3662C18.4904 10.6996 18.2576 11.1834 18.2582 11.6932V11.9994V12.3056C18.2576 12.816 18.4904 13.2998 18.8968 13.6326L19.6142 14.2215C19.8673 14.4292 19.9296 14.7808 19.7624 15.0562Z" fill="#1A1818"/><circle cx="12.0059" cy="12.005" r="3.375" fill="#6A7165"/></svg>'
                            : appConfigService.countryConfigCollection
                                        .toString() ==
                                    "rafco"
                                ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M19.6441 12.8093C19.4902 12.6837 19.4024 12.5001 19.4024 12.3068V12C19.4024 12 19.4024 11.9994 19.4024 11.9988V11.692C19.4024 11.4987 19.4908 11.3151 19.6447 11.1888L20.3621 10.6C21.0306 10.0509 21.1953 9.12563 20.7533 8.39843L19.66 6.60036C19.218 5.87316 18.2913 5.54641 17.4569 5.82122L16.5557 6.11899C16.3617 6.18301 16.1499 6.16308 15.9725 6.06523L15.416 5.75659C15.2417 5.65996 15.1202 5.49628 15.0821 5.30783L14.904 4.42178C14.7386 3.59795 13.9767 3 13.0933 3H10.9067C10.0233 3 9.26138 3.59795 9.09539 4.42239L8.91731 5.30783C8.87915 5.49628 8.75767 5.65996 8.58341 5.75659L8.02755 6.06463C7.85074 6.16308 7.63832 6.18301 7.44497 6.11899L6.54249 5.82182C5.70806 5.54641 4.78141 5.87377 4.34002 6.60036L3.24674 8.39903C2.80472 9.12563 2.96944 10.0515 3.63788 10.6006L4.35529 11.1895C4.5092 11.3157 4.5976 11.4993 4.5976 11.6926V12.0006V12.3074C4.5976 12.5007 4.50983 12.6843 4.35529 12.8105L3.63788 13.3994C2.96944 13.9485 2.80472 14.8738 3.24674 15.601L4.34002 17.399C4.78204 18.1262 5.70996 18.453 6.54312 18.1782L7.44433 17.8804C7.63768 17.8164 7.8501 17.8363 8.02755 17.9342L8.58405 18.2428C8.75831 18.3394 8.87979 18.5031 8.91795 18.6916L9.09603 19.5776C9.26202 20.4021 10.0239 21 10.9074 21H13.0939C13.9773 21 14.7392 20.4021 14.9052 19.5776L15.0833 18.6922C15.1215 18.5037 15.243 18.34 15.4172 18.2434L15.9731 17.9354C16.1493 17.8369 16.3623 17.8164 16.5557 17.881L17.4569 18.1788C18.2913 18.4542 19.218 18.1274 19.6593 17.4002L20.7526 15.6016C21.1946 14.875 21.0299 13.9491 20.3615 13.4L19.6441 12.8093ZM19.7624 15.0562L18.6685 16.8548C18.5012 17.1303 18.1488 17.2541 17.8334 17.1502L16.9322 16.8524C16.4221 16.6839 15.8618 16.7365 15.3956 16.995L14.8397 17.303C14.3799 17.5579 14.0587 17.9891 13.9589 18.4862L13.7808 19.3717C13.7172 19.6845 13.4285 19.9116 13.0933 19.9116H10.9067C10.5715 19.9116 10.2828 19.6845 10.2192 19.3717L10.0411 18.4862C9.94127 17.9891 9.62009 17.5579 9.16026 17.303L8.6044 16.9944C8.3182 16.8361 7.99702 16.7552 7.67393 16.7552C7.46978 16.7552 7.26498 16.7872 7.06782 16.8524L6.16661 17.1502C5.84925 17.2541 5.49881 17.1303 5.33091 16.8548L4.23763 15.0562C4.06972 14.7808 4.13269 14.4292 4.38581 14.2215L5.10322 13.6326C5.50962 13.2998 5.7424 12.816 5.74176 12.3056V12V11.6932C5.7424 11.1828 5.50962 10.699 5.10322 10.3662L4.38581 9.77733C4.13269 9.56956 4.06972 9.21804 4.23763 8.94262L5.33154 7.14395C5.49881 6.86853 5.85179 6.74532 6.16661 6.8486L7.06782 7.14637C7.57853 7.31488 8.13821 7.26294 8.6044 7.00383L9.16026 6.69579C9.62009 6.44091 9.94127 6.00966 10.0411 5.51258L10.2192 4.62714C10.2828 4.31427 10.5715 4.08718 10.9067 4.08718H13.0933C13.4285 4.08718 13.7172 4.31427 13.7808 4.62714L13.9589 5.51258C14.0587 6.00966 14.3799 6.44091 14.8397 6.69579L15.3956 7.00443C15.8618 7.26233 16.4215 7.31488 16.9322 7.14637L17.8334 6.8486C18.1501 6.74472 18.5012 6.86853 18.6691 7.14395L19.7624 8.94262C19.9303 9.21804 19.8673 9.56956 19.6142 9.77733L18.8968 10.3662C18.4904 10.6996 18.2576 11.1834 18.2582 11.6932V11.9994V12.3056C18.2576 12.816 18.4904 13.2998 18.8968 13.6326L19.6142 14.2215C19.8673 14.4292 19.9296 14.7808 19.7624 15.0562Z" fill="#1A1818"/><circle cx="12.0049" cy="12.005" r="3.375" fill="#EA1B23" fill-opacity="0.5"/></svg>'
                                : '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M19.6441 12.8093C19.4902 12.6837 19.4024 12.5001 19.4024 12.3068V12C19.4024 12 19.4024 11.9994 19.4024 11.9988V11.692C19.4024 11.4987 19.4908 11.3151 19.6447 11.1888L20.3621 10.6C21.0306 10.0509 21.1953 9.12563 20.7533 8.39843L19.66 6.60036C19.218 5.87316 18.2913 5.54641 17.4569 5.82122L16.5557 6.11899C16.3617 6.18301 16.1499 6.16308 15.9725 6.06523L15.416 5.75659C15.2417 5.65996 15.1202 5.49628 15.0821 5.30783L14.904 4.42178C14.7386 3.59795 13.9767 3 13.0933 3H10.9067C10.0233 3 9.26138 3.59795 9.09539 4.42239L8.91731 5.30783C8.87915 5.49628 8.75767 5.65996 8.58341 5.75659L8.02755 6.06463C7.85074 6.16308 7.63832 6.18301 7.44497 6.11899L6.54249 5.82182C5.70806 5.54641 4.78141 5.87377 4.34002 6.60036L3.24674 8.39903C2.80472 9.12563 2.96944 10.0515 3.63788 10.6006L4.35529 11.1895C4.5092 11.3157 4.5976 11.4993 4.5976 11.6926V12.0006V12.3074C4.5976 12.5007 4.50983 12.6843 4.35529 12.8105L3.63788 13.3994C2.96944 13.9485 2.80472 14.8738 3.24674 15.601L4.34002 17.399C4.78204 18.1262 5.70996 18.453 6.54312 18.1782L7.44433 17.8804C7.63768 17.8164 7.8501 17.8363 8.02755 17.9342L8.58405 18.2428C8.75831 18.3394 8.87979 18.5031 8.91795 18.6916L9.09603 19.5776C9.26202 20.4021 10.0239 21 10.9074 21H13.0939C13.9773 21 14.7392 20.4021 14.9052 19.5776L15.0833 18.6922C15.1215 18.5037 15.243 18.34 15.4172 18.2434L15.9731 17.9354C16.1493 17.8369 16.3623 17.8164 16.5557 17.881L17.4569 18.1788C18.2913 18.4542 19.218 18.1274 19.6593 17.4002L20.7526 15.6016C21.1946 14.875 21.0299 13.9491 20.3615 13.4L19.6441 12.8093ZM19.7624 15.0562L18.6685 16.8548C18.5012 17.1303 18.1488 17.2541 17.8334 17.1502L16.9322 16.8524C16.4221 16.6839 15.8618 16.7365 15.3956 16.995L14.8397 17.303C14.3799 17.5579 14.0587 17.9891 13.9589 18.4862L13.7808 19.3717C13.7172 19.6845 13.4285 19.9116 13.0933 19.9116H10.9067C10.5715 19.9116 10.2828 19.6845 10.2192 19.3717L10.0411 18.4862C9.94127 17.9891 9.62009 17.5579 9.16026 17.303L8.6044 16.9944C8.3182 16.8361 7.99702 16.7552 7.67393 16.7552C7.46978 16.7552 7.26498 16.7872 7.06782 16.8524L6.16661 17.1502C5.84925 17.2541 5.49881 17.1303 5.33091 16.8548L4.23763 15.0562C4.06972 14.7808 4.13269 14.4292 4.38581 14.2215L5.10322 13.6326C5.50962 13.2998 5.7424 12.816 5.74176 12.3056V12V11.6932C5.7424 11.1828 5.50962 10.699 5.10322 10.3662L4.38581 9.77733C4.13269 9.56956 4.06972 9.21804 4.23763 8.94262L5.33154 7.14395C5.49881 6.86853 5.85179 6.74532 6.16661 6.8486L7.06782 7.14637C7.57853 7.31488 8.13821 7.26294 8.6044 7.00383L9.16026 6.69579C9.62009 6.44091 9.94127 6.00966 10.0411 5.51258L10.2192 4.62714C10.2828 4.31427 10.5715 4.08718 10.9067 4.08718H13.0933C13.4285 4.08718 13.7172 4.31427 13.7808 4.62714L13.9589 5.51258C14.0587 6.00966 14.3799 6.44091 14.8397 6.69579L15.3956 7.00443C15.8618 7.26233 16.4215 7.31488 16.9322 7.14637L17.8334 6.8486C18.1501 6.74472 18.5012 6.86853 18.6691 7.14395L19.7624 8.94262C19.9303 9.21804 19.8673 9.56956 19.6142 9.77733L18.8968 10.3662C18.4904 10.6996 18.2576 11.1834 18.2582 11.6932V11.9994V12.3056C18.2576 12.816 18.4904 13.2998 18.8968 13.6326L19.6142 14.2215C19.8673 14.4292 19.9296 14.7808 19.7624 15.0562Z" fill="black"/><circle cx="12.0049" cy="12.005" r="3.375" fill="#792AFF" fill-opacity="0.5"/></svg>',
                        fit: BoxFit.fill),
                  ),
                ),
                SizedBox(width: 10.w),
                //TODO รูป profile
                GetBuilder<ProfileController>(builder: (profileCtl) {
                  return InkWell(
                    onTap: () {
                      if (homeController.isGuest!.value) {
                        showDialog(
                            context: context,
                            useSafeArea: true,
                            builder: (_) => const RegisterPage());
                      } else {
                        Get.to(() => const EditProfilePage());
                      }
                    },
                    child: Container(
                      width: 24.w,
                      height: 24.h,
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        image: DecorationImage(
                          image: NetworkImage(profileCtl.profile.value.avatar
                                      .toString()
                                      .isNotEmpty &&
                                  profileCtl.profile.value.avatar
                                          .toString()
                                          .toLowerCase() !=
                                      'null'
                              ? profileCtl.profile.value.avatar.obs.string
                              : 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png'),
                          fit: BoxFit.cover,
                        ),
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                              width: 1.w, color: const Color(0xFF1A1818)),
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          )
        ],
      ),
    );
  }

  static Widget welcomeAndPoint(context) {
    final HomeController homeController = Get.put(HomeController());
    final TransalationController transalationCtl =
        Get.put(TransalationController());
    final LikePointController likepointController =
        Get.put(LikePointController());
    final WebViewPointController likepointCtl =
        Get.put(WebViewPointController());
    return Container(
      margin: EdgeInsets.only(left: 24.w, right: 24.w),
      width: Get.width,
      // height: 44.h,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GetBuilder<ProfileController>(builder: (profileCtl) {
            return Container(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Obx(() {
                      return FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          homeController.isGuest!.value
                              ? '${homeHello.tr} ${onBoardingVisit.tr}'
                              : '${homeHello.tr} ${profileCtl.profile.value.firstname.obs}',
                          style: TextStyle(
                            color: configTheme().colorScheme.onPrimary,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .titleLarge
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .titleLarge
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .titleLarge
                                ?.fontWeight,
                            height: 0.09.h,
                          ),
                        ),
                      );
                    }),
                  ),
                  transalationCtl.location.value == "English"
                      ? SizedBox(
                          // height: 22.h,
                          child: Text(
                            "${homeWelcome.tr} ${appConfigService.countryConfigCollection.toString() == "aam" ? "AAM" : appConfigService.countryConfigCollection.toString() == "rafco" ? "RAFCO" : "RPLC"} ",
                            style: TextStyle(
                              color: configTheme()
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withOpacity(0.5),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                              // height: 0.15.h,
                            ),
                          ),
                        )
                      : SizedBox(
                          // height: 22.h,
                          child: Text(
                            "${appConfigService.countryConfigCollection.toString() == "aam" ? "AAM" : appConfigService.countryConfigCollection.toString() == "rafco" ? "RAFCO" : "RPLC"} ${homeWelcome.tr}  ",
                            style: TextStyle(
                              color: configTheme()
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withOpacity(0.5),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall
                                  ?.fontWeight,
                              // height: 0.15.h,
                            ),
                          ),
                        )
                ],
              ),
            );
          }),
          //TODO point
          // homeController.isGuest!.value
          //     ? Container()
          //     :
          InkWell(
            onTap: () async {
              // print('isAlreadyMigrated :: ${likepointCtl.isAlreadyMigrated.value}');
              // print('isLoading :: ${likepointCtl.isLoading.value}');
              // if (likepointCtl.isAlreadyMigrated.value == true &&
              //     likepointCtl.isLoading.value == false) {

              // print('isGuest :: ${homeController.isGuest!.value}');
              // print('isOpen :: ${likepointCtl.isOpen!.value}');
              // print('isAlreadyMigrated :: ${likepointCtl.isAlreadyMigrated!.value}');

              if(!homeController.isGuest!.value && likepointCtl.isOpen!.value && likepointCtl.isAlreadyMigrated!.value){
                var status = await Permission.camera.request();

                if (status.isGranted) {
                  // Have camera permission
                  debugPrint("Have camera permission");
                } else {
                  // Do not have permission
                  debugPrint("No camera permission");
                }
                //TODO ไปหน้า Likepoint

                    // {webViewCtl.urlLikePoint!.value}/?phone=${webViewCtl.phoneEncode!.value}&merchant=${webViewCtl.merchantID!.value}&firstName=${webViewCtl.firstName.value}&lastName=${webViewCtl.lastName.value}
                if(kIsWeb){
                  final String url = "${likepointCtl.urlLikePoint!.value}/?phone=${likepointCtl.phoneEncode!.value}&merchant=${likepointCtl.merchantID!.value}&firstName=${likepointCtl.firstName.value}&lastName=${likepointCtl.lastName.value}";
                  final Uri uri = Uri.parse(url);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri, mode: LaunchMode.externalApplication);
                  } else {
                    throw "ไม่สามารถเปิด URL ได้: $url";
                  }
                }else{
                  Get.to(() => const WebViewMinilike());
                }
              }else{
                debugPrint("Not Migrate or Not Open Status");
              }
            },
            child: SizedBox(
              height: 44.h,
              child: Row(
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: 22.h,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Stack(
                            //   alignment: Alignment.center,
                            //   children: [
                            //     Container(
                            //       width: 22.w,
                            //       height: 22.h,
                            //       decoration: BoxDecoration(
                            //         color: Color(0xFFFF9300).withOpacity(0.4),
                            //         borderRadius: BorderRadius.circular(22),
                            //       ),
                            //       child: Center(
                            //         child: Container(
                            //           height: 15.h,
                            //           width: 15.w,
                            //           decoration: BoxDecoration(
                            //             color: Color(0xFFFF9300).withOpacity(0.2),
                            //             borderRadius: BorderRadius.circular(22),
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //     Center(
                            //       child: Text(
                            //         appConfigService.countryConfigCollection == 'aam'?'A':'R',
                            //         textAlign: TextAlign.center,
                            //         style: TextStyle(
                            //           color: appConfigService.countryConfigCollection == 'aam'? AppColors.AAMPurpleSecondary:appConfigService.countryConfigCollection == 'rafco'?AppColors.onBoradingRAFCO2:Colors.yellowAccent,
                            //           fontSize: 12,
                            //           fontWeight: FontWeight.w500,
                            //         ),
                            //       ),
                            //     ),
                            //   ],
                            // ),
                            SizedBox(
                              width: 22.w,
                              height: 22.h,
                              // child: Image.asset('assets/home/<USER>',
                              //     fit: BoxFit.fill),
                              child: SvgPicture.string(likepointCtl.point_icon.value,
                                  ),
                            ),
                            SizedBox(width: 4.w),
                            SizedBox(
                              height: 22.h,
                              child: Center(child: Obx(() {
                                return Text(
                                  //TODO AAM แสดงจำนวน AAMP, RPLP, RAFP ( Likepoint 2.0)
                                  // appConfigService.countryConfigCollection
                                  //             .toString() ==
                                  //         "aam" || appConfigService
                                  //                 .countryConfigCollection
                                  //                 .toString() ==
                                  //             "rplc"
                                  //     ?
                                  likepointCtl.balanceLikePoint.value
                                      .toString(),
                                  // : likepointController.likePoint!.value,
                                  textAlign: TextAlign.right,
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .titleLarge
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .titleLarge
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .titleLarge
                                        ?.fontWeight,
                                    height: 0,
                                  ),
                                );
                              })),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                          height: 22.h,
                          child: Center(child: Obx(() {
                            return Text(
                              //TODO AAM แสดงจำนวน AAMP, RPLP, RAFP ( Likepoint 2.0)
                              '= ${Get.find<AppConfigController>().currencySymbol!.value.toString()}${likepointCtl.balanceLikePointTH.value.toString()}',
                              // appConfigService.countryConfigCollection.toString() == "aam" || appConfigService
                              //     .countryConfigCollection
                              //     .toString() ==
                              //     "rplc"
                              //     ?
                              // : likepointController.likePointBath!.value}',
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .labelSmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .labelSmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .labelSmall
                                    ?.fontWeight,
                                height: 0,
                              ),
                            );
                          }))),
                    ],
                  ),
                  SizedBox(width: 6.w),
                  SizedBox(
                      width: 24.w,
                      height: 44.h,
                      child: SvgPicture.string(
                        appConfigService.countryConfigCollection == 'aam'
                            ? AppSvgImage.icon_open_AAMP
                            : appConfigService.countryConfigCollection ==
                                    'rafco'
                                ? AppSvgImage.icon_open_RAFP
                                : AppSvgImage.icon_open_RPLP,
                        fit: BoxFit.fill,
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget appDetail(context) {
    final TransalationController transalationCtl =
        Get.put(TransalationController());
    final HomeController homeController = Get.find<HomeController>();
    return Container(
      width: 327.w,
      height: 56.h,
      decoration: ShapeDecoration(
        color: const Color(0xFFF9F9F9),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1.w,
            strokeAlign: BorderSide.strokeAlignCenter,
            color: configTheme().colorScheme.primary.withOpacity(0.1),
          ),
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      child: Row(
        // mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            // margin: EdgeInsets.only(left: 12.w),
            width: 259.w,
            // color: Colors.teal,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text:
                        "${appConfigService.countryConfigCollection.toString() == "aam" ? "AAM" : appConfigService.countryConfigCollection.toString() == "rafco" ? "RAFCO" : "RPLC"}",
                    style: TextStyle(
                      color: configTheme().colorScheme.onSecondary,
                      fontSize:
                          configTheme().primaryTextTheme.labelLarge?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.labelLarge?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.labelLarge?.fontWeight,
                      // height: 0,
                    ),
                  ),
                  // TextSpan(
                  //   text: homeSubTitle.tr,
                  //   style: TextStyle(
                  //     color: configTheme().textTheme.bodyMedium?.color,
                  //     fontSize:
                  //         configTheme().primaryTextTheme.labelLarge?.fontSize,
                  //     fontFamily:
                  //         configTheme().primaryTextTheme.labelLarge?.fontFamily,
                  //     fontWeight:
                  //         configTheme().primaryTextTheme.labelLarge?.fontWeight,
                  //     height: 0,
                  //   ),
                  // ),
                  // TextSpan(text: " "),
                  transalationCtl.location.value == ""
                      ? TextSpan(
                          text: " ${homeTitleDescription.tr}",
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: 12.sp,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelLarge
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelLarge
                                ?.fontWeight,
                            // height: 0,
                          ),
                        )
                      : transalationCtl.location.value == "English"
                          ? TextSpan(
                              text:
                                  " ${homeTitleDescription.tr} ${appConfigService.countryConfigCollection == "aam" ? "Thailand" : appConfigService.countryConfigCollection == "rafco" ? "Cambodia" : appConfigService.countryConfigCollection == "rplc" ? "Laos" : ""}",
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: 11.sp,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .labelLarge
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .labelLarge
                                    ?.fontWeight,
                                // height: 0,
                              ),
                            )
                          : transalationCtl.location.value == "Thailand"
                              ? TextSpan(
                                  text:
                                      " ${appConfigService.countryConfigCollection == "aam" ? "เป็นผู้ประกอบธุรกิจสินเชื่อ ที่อยู่ภายใต้การกำกับของ ธนาคารแห่งประเทศไทย" : appConfigService.countryConfigCollection == "rafco" ? "เป็นผู้ประกอบธุรกิจสินเชื่อ ที่อยู่ภายใต้การกำกับของ ธนาคารแห่งประเทศกัมพูชา" : appConfigService.countryConfigCollection == "rplc" ? "เป็นผู้ประกอบธุรกิจสินเชื่อ ที่อยู่ภายใต้การกำกับของ ธนาคารแห่งประเทศลาว" : ""}",
                                  style: TextStyle(
                                    color: configTheme()
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                    fontSize: 12.sp,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelLarge
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelLarge
                                        ?.fontWeight,
                                    // height: 0,
                                  ),
                                )
                              : transalationCtl.location.value == "Cambodian"
                                  ? TextSpan(
                                      text: " ${homeTitleDescription.tr}",
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color,
                                        fontSize: 12.sp,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelLarge
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelLarge
                                            ?.fontWeight,
                                        // height: 0,
                                      ),
                                    )
                                  : transalationCtl.location.value == "Lao"
                                      ? TextSpan(
                                          text: " ${homeTitleDescription.tr}",
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color,
                                            fontSize: 12.sp,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .labelLarge
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .labelLarge
                                                ?.fontWeight,
                                            // height: 0,
                                          ),
                                        )
                                      : const TextSpan(text: ""),
                ],
              ),
            ),
          ),
          // Text.rich(
          //   TextSpan(
          //     children: [
          //       TextSpan(
          //         text: homeTitle.tr,
          //         style: TextStyle(
          //           color: configTheme().colorScheme.onSecondary,
          //           fontSize: configTheme().primaryTextTheme.labelLarge?.fontSize,
          //           fontFamily:  configTheme().primaryTextTheme.labelLarge?.fontFamily,
          //           fontWeight:  configTheme().primaryTextTheme.labelLarge?.fontWeight,
          //           height: 0,
          //         ),
          //       ),
          //       TextSpan(
          //         text: homeSubTitle.tr,
          //         style: TextStyle(
          //           color: configTheme().textTheme.bodyMedium?.color,
          //           fontSize: configTheme().primaryTextTheme.labelLarge?.fontSize,
          //           fontFamily:  configTheme().primaryTextTheme.labelLarge?.fontFamily,
          //           fontWeight:  configTheme().primaryTextTheme.labelLarge?.fontWeight,
          //           height: 0,
          //         ),
          //       ),
          //       TextSpan(
          //         text: homeTitleDescription.tr,
          //         style: TextStyle(
          //           color: configTheme().textTheme.bodyMedium?.color,
          //           fontSize: 10.sp,
          //           fontFamily:  configTheme().primaryTextTheme.labelLarge?.fontFamily,
          //           fontWeight:  configTheme().primaryTextTheme.labelLarge?.fontWeight,
          //           height: 0,
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          SizedBox(width: 20.w),
          GestureDetector(
            onTap: () {
              homeController.closeAAMNote();
            },
            child: SizedBox(
              width: 24.w,
              height: 24.h,
              child:
                  Image.asset('assets/home/<USER>', fit: BoxFit.fill),
              // child: Stack(children: [
              //     ,
              //     ]),
            ),
          ),
        ],
      ),
    );
  }

  static Widget serviceMenuCard(context, Svg, title) {
    return
        // GestureDetector(
        // onTap: () {
        //   print(title);
        //   if (homeController.isGuest!.value) {
        //     Get.to(() => const RegisterPage());
        //   } else if (title == homeApply.tr) {
        //     if (Get.find<ContractListController>().contractList.isEmpty) {
        //       Get.to(() => const LoanRequestDetail());
        //     } else {
        //       Get.to(() => const LoanMainScreen());
        //     }
        //   }
        // },
        // child:
        Container(
      width: 75.w,
      // height: 90.h,
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            height: 46.h,
            decoration: ShapeDecoration(
              color:  configTheme().colorScheme.onSecondary.withOpacity(0.05),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Center(
              child: Container(
                height: 25.h,
                width: 25.w,
                child: SvgPicture.string(
                  Svg,
                ),
              ),
            ),
          ),
          SizedBox(height: 9.h),
          Text(
            title,
            style: TextStyle(
              color: configTheme().textTheme.bodyMedium?.color,
              fontSize: configTheme().primaryTextTheme.labelSmall?.fontSize,
              fontFamily: configTheme().primaryTextTheme.labelSmall?.fontFamily,
              fontWeight: FontWeight.w500,
              // height: 0,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
    // );
  }
}
