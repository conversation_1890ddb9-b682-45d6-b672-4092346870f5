import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../controller/bill_payment/billPayment.controller.dart';
import '../../../../controller/contract/contractlist.controller.dart';
import '../../../../controller/contract/myloan.controller.dart';
import '../../../../controller/transalation/transalation.controller.dart';
import '../../../../controller/transalation/translation_key.dart';
import '../../../screen/bill_payment/select_payment_screen.dart';
import '../../themes/app_colors_gradient.dart';
import '../../themes/theme.dart';
import '../../utils/formatters/formatter.dart';
import '../animation/shimmer_effect.dart';
import '../contract/contract_pay_widget.dart';

class HomePageBillWidget {
  static Widget billContent(context) {
    final MyloanController myloanCtl = Get.put(MyloanController());
    Get.lazyPut(() => BillPaymentController(), fenix: true);

    return GetBuilder<ContractListController>(
      // init: ContractListController(),
      builder: (contractListCtl) {
        return Container(
          // margin: EdgeInsets.only(left: 24.w, right: 24.w),
          width: Get.width,
          // height: 205.h,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.only(left: 24.w),
                height: 21.h,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 21.h,
                      child: Image.asset(
                        'assets/home/<USER>',
                        fit: BoxFit.fill,
                        color: configTheme().colorScheme.onSecondary,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      homeInstallment.tr,
                      // 'บิลที่ต้องชำระ',
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyLarge
                            ?.fontWeight,
                        // height: 0,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 8.h),
              SizedBox(
                height: 176.h,
                child: CarouselSlider(
                  items: List.generate(
                    // 3,
                      contractListCtl.contractList.length, (i) {
                    return Padding(
                      padding: EdgeInsets.only(left: 24.w, right: 24.w),
                      child: Container(
                        width: Get.width,
                        height: 176.h,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: ShapeDecoration(
                          gradient: AppColorsGradient.cardBillGradient,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        child: Container(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 24.h,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment
                                      .spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      child: Text(
                                        homePaymentAmount.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .colorScheme
                                              .background
                                              .withOpacity(0.699999988079071),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                          // height: 0,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      child: Text(
                                        homeNoContract.tr,
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: configTheme()
                                              .colorScheme
                                              .background
                                              .withOpacity(0.699999988079071),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                          // height: 0,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 30.h,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment
                                      .spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Obx(() {
                                      if (contractListCtl.contractPeriod
                                          .isEmpty) {
                                        return AnimatedShimmer(width: 100.w);
                                      } else {
                                        return Text(
                                          contractListCtl.contractPeriod[i]
                                              .nextpay
                                              .toString()
                                              .isNotEmpty
                                              ? CFormatter.doubleFormatCurrency(
                                              contractListCtl
                                                  .contractPeriod[i].nextpay
                                                  .toString())
                                              : "",
                                          style: TextStyle(
                                            color: configTheme()
                                                .colorScheme
                                                .background
                                                .withOpacity(0.8999999761581421),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .displayMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .displayMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .displayMedium
                                                ?.fontWeight,
                                            // height: 0,
                                          ),
                                        );
                                      }
                                    }),
                                    Obx(() {
                                      if (contractListCtl.contractPeriod
                                          .isEmpty) {
                                        return AnimatedShimmer(width: 180.w);
                                      }
                                      return Text(
                                        contractListCtl.contractPeriod[i].ctt_code
                                            .toString()
                                            .isNotEmpty
                                            ? contractListCtl
                                            .contractPeriod[i].ctt_code
                                            .toString()
                                            : "",
                                        style: TextStyle(
                                          color: configTheme()
                                              .colorScheme
                                              .background
                                              .withOpacity(0.8999999761581421),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontWeight,
                                          // height: 0,
                                        ),
                                      );
                                    }),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 2.h,
                              ),
                              Container(
                                alignment: Alignment.center,
                                height: 20.h,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment
                                      .spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      homePaymentDate.tr,
                                      style: TextStyle(
                                        color: configTheme()
                                            .colorScheme
                                            .background
                                            .withOpacity(0.699999988079071),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                        // height: 0,
                                      ),
                                    ),
                                    Text(
                                      '',
                                      // homeBeforePaymentDate.tr,
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                        color: configTheme()
                                            .colorScheme
                                            .background
                                            .withOpacity(0.699999988079071),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                        // height: 0,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 2.h,
                              ),
                              Container(
                                height: 20.h,
                                alignment: Alignment.center,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment
                                      .spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Obx(() {
                                      if (contractListCtl.contractPeriod
                                          .isEmpty) {
                                        return AnimatedShimmer(width: 100.w);
                                      } else {
                                        return Text(
                                          contractListCtl.contractPeriod[i]
                                              .due_date
                                              .toString()
                                              .isNotEmpty
                                              ? contractListCtl.setDueDateLocale(
                                              contractListCtl
                                                  .contractPeriod[i].due_date
                                                  .toString(),
                                              Get
                                                  .find<TransalationController>()
                                                  .location
                                                  .value ==
                                                  'English'
                                                  ? 'en'
                                                  : Get
                                                  .find<TransalationController>()
                                                  .location
                                                  .value ==
                                                  'Thailand' ||
                                                  appConfigService
                                                      .countryConfigCollection ==
                                                      'aam'
                                                  ? 'th'
                                                  : Get
                                                  .find<TransalationController>()
                                                  .location
                                                  .value ==
                                                  'Lao' ||
                                                  appConfigService
                                                      .countryConfigCollection ==
                                                      'rplc'
                                                  ? 'lo'
                                                  : Get
                                                  .find<TransalationController>()
                                                  .location
                                                  .value ==
                                                  'Cambodian' ||
                                                  appConfigService
                                                      .countryConfigCollection ==
                                                      'rafco'
                                                  ? 'km'
                                                  : 'en')
                                              : "",
                                          style: TextStyle(
                                            color: configTheme()
                                                .colorScheme
                                                .background
                                                .withOpacity(0.8999999761581421),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontWeight,
                                          ),
                                        );
                                      }
                                    }),
                                    Text(
                                      '',
                                      // '+ 1,000 ${point.tr}',
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                        color: configTheme().colorScheme
                                            .background,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                        // height: 0,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              SizedBox(
                                height: 14.h,
                              ),

                              //TODO: Button Pay Bill
                              GestureDetector(
                                onTap: () async {
                                  print('Pay Bill');
                                  // await myloanCtl.setDataPayment(
                                  //     0,
                                  //     contractListCtl.contractList[0].ctt_code
                                  //         .toString(),
                                  //     contractListCtl.contractList[0].nextpay
                                  //         .toString());
                                  // ContractPayWidget.alertQRPayment(context); //TODO : แก้ไขเป็นหน้าใหม่

                                  if (Get
                                      .find<ContractListController>()
                                      .contractList
                                      .isNotEmpty) {
                                    await Get.find<BillPaymentController>()
                                        .setInitailContract(
                                        i,
                                        contractListCtl.contractList[i].ctt_code
                                            .toString());
                                    Get.to(() => SelectPaymentScreen());
                                  }
                                },
                                child: Container(
                                  width: Get.width,
                                  height: 36.h,
                                  // padding: const EdgeInsets.symmetric(
                                  //     horizontal: 12, vertical: 6),
                                  clipBehavior: Clip.antiAlias,
                                  decoration: ShapeDecoration(
                                    color: const Color(0xBF1A1818),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6)),
                                  ),
                                  child: Row(
                                    // mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 18.w,
                                        height: 15.h,
                                        child: Image.asset(
                                          'assets/home/<USER>',
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      SizedBox(
                                        // height: 16.h,
                                        child: Center(
                                          child: Text(
                                            homePayment.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .colorScheme
                                                  .background
                                                  .withOpacity(
                                                  0.8999999761581421),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodyLarge
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyLarge
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyLarge
                                                  ?.fontWeight,
                                              // height: 0,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
                  options: CarouselOptions(
                    autoPlay: false,
                    initialPage: 0,
                    aspectRatio: 1,
                    viewportFraction: 1,
                    height: 454.h,
                    disableCenter: true,
                    enableInfiniteScroll: false,
                    onPageChanged: (index, reason) {
                      print('index: $index');
                      contractListCtl.setIndexMyBill(index);
                    },
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              Container(
                margin: EdgeInsets.only(left: 24.w, right: 24.w),
                width: 327.w,
                height: 4.h,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    // 3,
                      contractListCtl.contractList.length,
                          (index) => _buildIndicator(index)),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Widget _buildIndicator(int index) {
    final ContractListController contractListCtl = Get.find<
        ContractListController>();
    return Row(
      children: [
        SizedBox(
          width: 6.w,
        ),
        Obx(() {
          return Opacity(
            opacity: contractListCtl.indexMyBill == index ? 1.0 : 0.40,
            child: Container(
              width: contractListCtl.indexMyBill == index ? 15.0 : 4.0,
              height: 4.h,
              decoration: ShapeDecoration(
                color: configTheme().colorScheme.tertiary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
            ),
          );
        }),
        SizedBox(
          width: 6.w,
        ),
      ],
    );
  }

}
