import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class KYCStatusWidget extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? textColor;

  const KYCStatusWidget({Key? key,required this.title,required this.onPressed,this
  .backgroundColor,this.textColor}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 67.w,
      height: 20.h,
      decoration: ShapeDecoration(
        color: backgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6.r)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 20.h,
            child: Text(
              title,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: textColor,
                fontSize: 12.sp,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
