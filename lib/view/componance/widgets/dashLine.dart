import 'package:flutter/material.dart';

class DashedLine extends StatelessWidget {
  final double dashWidth;
  final double dashHeight;
  final double dashSpace;
  final Color color;

  // Constructor ของ DashedLine ซึ่ง require ทุก parameter เสมอ
  const DashedLine({
    Key? key,
    required this.dashWidth,
    required this.dashHeight,
    required this.dashSpace,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final totalWidth = constraints.maxWidth;
      final dashCount = (totalWidth / (dashWidth + dashSpace)).floor();

      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(dashCount, (index) {
          return Container(
            width: dashWidth,
            height: dashHeight,
            color: color,
          );
        }),
      );
    });
  }
}