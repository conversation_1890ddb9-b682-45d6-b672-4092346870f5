import 'package:flutter/cupertino.dart';

class CardNotiWidget extends StatelessWidget {
  const CardNotiWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 327,
      height: 146,
      padding: const EdgeInsets.only(bottom: 12),
      decoration: ShapeDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.00, -1.00),
          end: Alignment(0, 1),
          colors: [],
        ),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            strokeAlign: BorderSide.strokeAlignOutside,
            color: Color(0x0C792AFF),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        shadows: [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 18,
            offset: Offset(3, 6),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 42,
            padding: const EdgeInsets.only(top: 8, left: 14, right: 8, bottom: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 279,
                  height: 26,
                  child: Text(
                    'แนะนำจัดสินเชื่อ',
                    style: TextStyle(
                      color: Color(0xFF792AFF),
                      fontSize: 12,
                      fontFamily: 'Noto Sans Thai',
                      fontWeight: FontWeight.w600,
                      height: 0,
                    ),
                  ),
                ),
                Container(
                  width: 26,
                  height: 26,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        decoration: ShapeDecoration(
                          color: Color(0xFFFF9300),
                          shape: OvalBorder(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 327,
            height: 92,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 299,
                  height: 20,
                  child: Text(
                    'รับ 4,000 พอยท์',
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: 14,
                      fontFamily: 'Noto Sans Thai',
                      fontWeight: FontWeight.w700,
                      height: 0,
                    ),
                  ),
                ),
                SizedBox(
                  width: 299,
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: 'ยินดีด้วย คุณแนะนำจัดสินเชื่อได้สำเร็จ จำนวน 1 ราย รับคะแนน 4,000 พอยท',
                          style: TextStyle(
                            color: Color(0xBF1A1818),
                            fontSize: 14,
                            fontFamily: 'Noto Sans Thai',
                            fontWeight: FontWeight.w400,
                            height: 0.11,
                          ),
                        ),
                        TextSpan(
                          text: '์ ',
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14,
                            fontFamily: 'Noto Sans Thai',
                            fontWeight: FontWeight.w400,
                            height: 0.11,
                          ),
                        ),
                        TextSpan(
                          text: 'ไปเลย!',
                          style: TextStyle(
                            color: Color(0xBF1A1818),
                            fontSize: 14,
                            fontFamily: 'Noto Sans Thai',
                            fontWeight: FontWeight.w400,
                            height: 0.11,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  width: 299,
                  height: 20,
                  child: Text(
                    '20 มี.ค. 2567 - 09:31',
                    style: TextStyle(
                      color: Color(0x7F1A1818),
                      fontSize: 12,
                      fontFamily: 'Noto Sans Thai',
                      fontWeight: FontWeight.w400,
                      height: 0,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
