import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';


class CImageWidget extends StatelessWidget {
  const CImageWidget({
    super.key,
    this.fit,
    required this.image,
    this.overlayColor,
    this.backgroundColor,
    required this.width,
    required this.height,
    this.padding,
    this.margin,
    this.borderRadius ,
  });

  final BoxFit? fit;
  final String image;
  final Color? overlayColor;
  final Color? backgroundColor;
  final double? borderRadius;
  final double width, height;
  final EdgeInsets? padding, margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(color: backgroundColor ?? AppColors.white, borderRadius: BorderRadius.circular(borderRadius ?? 0.0)),
      child: Image(fit: fit, image: AssetImage(image), color: overlayColor,),
    );
  }
}