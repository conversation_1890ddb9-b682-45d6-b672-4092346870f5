import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/theme.dart';
import '../../utils/constant/size.dart';

class CChoiceChip extends StatelessWidget {
  const CChoiceChip({
    super.key, required this.text, required this.selected, this.onSelected,
  });

  final String text;
  final bool selected;
  final void Function(bool)? onSelected;

  @override
  Widget build(BuildContext context) {

    final aamApp =(appConfigService.countryConfigCollection == 'aam');
    final rafcoApp =(appConfigService.countryConfigCollection == 'rafco');
    final rplcApp =(appConfigService.countryConfigCollection == 'rplc');

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: CSizes.spaceBetweenButton),
      child: Theme(
        data: Theme.of(context).copyWith(canvasColor: Colors.transparent),
        child: ChoiceChip(
          label: Text(text),
          selected: selected,
          onSelected: onSelected,
          labelStyle: TextStyle(color: selected ? ( rplcApp ? AppColors.darkGrey : Colors.white ) :  AppColors.darkGrey),
          labelPadding: const EdgeInsets.all(0),
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          backgroundColor: Colors.white,
          selectedColor:  aamApp ? AppColors.AAMPurple : rafcoApp ? AppColors.primaryRafco : AppColors.RPLCYellow,
          elevation: 0,
          pressElevation: 0,
          showCheckmark: false,
          side: BorderSide(
            color: selected
                ? (aamApp ? AppColors.AAMPurple : rafcoApp ? AppColors.primaryRafco : AppColors.RPLCYellow)
                : Colors.grey.shade100,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
