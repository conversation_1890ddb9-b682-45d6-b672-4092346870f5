import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../themes/app_colors.dart';
import '../../utils/constant/size.dart';

class CIconTextButtonWidget extends StatelessWidget {
  const CIconTextButtonWidget({
    super.key,
    required this.svgIcon,
    this.iconColor ,
    this.title,
    this.textColor = AppColors.white,
    this.backgroundColor = AppColors.white,
    this.borderRadius = CSizes.sm,
    this.btnWidth ,
    this.btnHeight ,
    this.iconWidth ,
    this.iconHeight ,
    this.borderColor ,
    this.onTap,

  });

  final String svgIcon;
  final Color? iconColor;
  final String? title;
  final Color? textColor;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderRadius;
  final double? btnWidth, btnHeight, iconWidth, iconHeight;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
       // mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Icon Container
          Container(
            width: btnWidth ?? 56,
            height: btnHeight ?? 56,
            decoration: BoxDecoration(
              color: backgroundColor ?? AppColors.white,
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: borderColor ?? AppColors.transparent,
                width: 1,
              ),
            ),
            child: Center(
              child: SvgPicture.asset(
                svgIcon,
                fit: BoxFit.cover,
                width: iconWidth ?? 27,
                height: iconHeight ?? 27,
                color: iconColor ?? AppColors.grey,
              ),
            ),
          ),

          if (title != null)
          const SizedBox(height: CSizes.space10 ,),

          if (title != null)
            SizedBox(
              width: 75,
              child: Text(title!,
              style: Theme.of(context).textTheme.titleSmall!.apply(color: textColor ?? AppColors.darkGrey,),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }
}
