
import 'package:flutter/material.dart';
import '../../../componance/utils/constant/size.dart';
import '../../themes/app_colors.dart';

class CustomBottomSheet extends StatelessWidget {
  const CustomBottomSheet({
    super.key,
    required this.child,
    required this.height,
    this.borderRadius,
    this.showBorderTop = false,
    this.padding  ,
  });
  final Widget child;
  final double height;
  final double? borderRadius;
  final bool showBorderTop;
  final EdgeInsets? padding;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(borderRadius ?? CSizes.borderRadiusLg),
          topRight: Radius.circular(borderRadius ?? CSizes.borderRadiusLg),
        ),
        border: showBorderTop ? const Border(top: BorderSide(color: AppColors.lightGrey, width: 1.0,),) : null,
      ),
      child: Padding(
        padding:padding ?? const EdgeInsets.only(top: CSizes.space40, bottom: CSizes.space25, left : CSizes.defaultSpace, right: CSizes.defaultSpace),
        child: child,
      ),
    );
  }
}

