import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';

class TextAreaWidget extends StatelessWidget {
  const TextAreaWidget({
    super.key, this.hintText,  this.minLine, required this.controller,
  });

  final String? hintText;
  final int?  minLine;
  final TextEditingController controller;

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      cursorColor: AppColors.grey,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: const TextStyle(color: AppColors.darkGrey),
        contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Colors.grey.shade400,
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Colors.grey.shade400,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Colors.grey.shade400,
            width: 1.5,
          ),
        ),
      ),
      style: const TextStyle(
        color: AppColors.darkGrey,
        fontSize: 16.0,
      ),
      minLines: minLine,
      maxLines: null,
    );
  }
}