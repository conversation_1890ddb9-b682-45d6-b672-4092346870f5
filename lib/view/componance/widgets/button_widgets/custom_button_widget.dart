import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';
import '../../utils/constant/size.dart';

class CCustomButton extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;
  final Color backgroundColor;
  final Color textColor;
  final bool showBorder;
  final Color borderColor;
  final double? width;

  const CCustomButton({
    Key? key,
    required this.label,
    required this.onPressed,
    this.backgroundColor = Colors.grey,
    this.textColor = Colors.white,
    this.showBorder = false,
    this.borderColor = Colors.transparent,
    this.width = double.infinity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: 45,
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: CSizes.defaultPadding),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(CSizes.buttonRadius),
            border: !showBorder
                ? Border.all(color: borderColor, width: 1.0)
                : Border.all(color: Colors.transparent, width: 0.0),
          ),
          alignment: Alignment.center,
          child: Text(
            label,style: Theme.of(context).textTheme.titleSmall!.copyWith(color: Colors.white)
          ),
        ),
      ),
    );
  }
}
