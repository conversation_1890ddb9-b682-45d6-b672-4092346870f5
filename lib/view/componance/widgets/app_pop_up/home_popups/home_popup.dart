import 'dart:ui';

import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../../controller/transalation/translation_key.dart';

class HomePopup {
  static ADsHomePopup(BuildContext context) {

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
          child: Container(
            margin: EdgeInsets.only(
              left: kIsWeb && ScreenUtil().screenWidth > 500 ? 22 : 22.w,
              right: kIsWeb && ScreenUtil().screenWidth > 500 ? 22 : 22.w,
              top: kIsWeb && ScreenUtil().screenWidth > 500 ? 157.h : 157.h,
              bottom: kIsWeb && ScreenUtil().screenWidth > 500 ? 155.h : 0.h,
            ),
            width: kIsWeb && ScreenUtil().screenWidth > 500
                ? 330.w
                : ScreenUtil().screenWidth,
            height: kIsWeb && ScreenUtil().screenWidth > 500
                ? 502.h
                : ScreenUtil().screenHeight,
            child: Stack(
              children: [
                Container(
                  width: kIsWeb && ScreenUtil().screenWidth > 500
                      ? 330.w
                      : ScreenUtil().screenWidth,
                  height: kIsWeb && ScreenUtil().screenWidth > 500
                      ? 502.h
                      : ScreenUtil().screenHeight,
                  alignment: Alignment.topCenter,
                  child: Column(
                    children: [
                      CarouselSlider(
                        items: List.generate(Get.find<AppConfigController>().popupList!.length, (i) {
                          return Container(
                            width: kIsWeb && ScreenUtil().screenWidth > 500
                                ? 330.w
                                : ScreenUtil().screenWidth,
                            height: kIsWeb && ScreenUtil().screenWidth > 500
                                ? 434.h
                                : ScreenUtil().screenHeight,
                            child: Image.network(
                              Get.find<AppConfigController>().popupList![i],
                              fit: BoxFit.fill,
                            ),
                          );
                        }),
                        options: CarouselOptions(
                          autoPlay: false,
                          initialPage: 0,
                          aspectRatio: 1,
                          viewportFraction: 1,
                          disableCenter: true,
                          enableInfiniteScroll: false,
                          onPageChanged: (index, reason) {},
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Container(
                        alignment: Alignment.bottomCenter,
                        child: PrimaryButtonPopUp(
                          title: accept_popup.tr,
                          onPressed: () {
                            Navigator.pop(context, true);
                          },
                          buttonWidth: 120.w,
                          backgroundColor: Colors.white,
                          isActive: true,
                          borderSize: 1.w,
                          borderColor: Colors.black.withOpacity(0.2),
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: InkWell(
                    onTap: () => Navigator.pop(context, false),
                    borderRadius: BorderRadius.circular(8),
                    child: SizedBox(
                      child: Padding(
                        padding: EdgeInsets.all(8),
                        child: SvgPicture.string(AppSvgImage.icon_close_white),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
