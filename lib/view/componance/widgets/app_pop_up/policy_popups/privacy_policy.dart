import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../themes/app_textstyle.dart';
import '../../../utils/AppSvgImage.dart';
import '../../componance_widget.dart';

class PrivacyPolicyWidget {
  static buildPrivacyPolicyAAM(context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              children: [
                                SvgPicture.string(
                                  AppSvgImage.privacy_policy,
                                  color:  AppColors.AAMPurple
                                ),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    "นโนบายความเป็นส่วนตัว",
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      // height: 0.10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text: '1.ขอบเขตและวัตถุประสงค์',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        "\n\nบริษัท เอเอเอ็ม แคปปิตอล เซอร์วิส จำกัด (“บริษัท”) ตระหนักถึงความสำคัญยึดมั่นในการดำเนินธุรกิจอย่างมีจรรยาบรรณและเคารพในสิทธิความเป็นส่วนตัวในการคุ้มครองข้อมูลส่วนบุคคลของบุคคลธรรมดา ที่มีอยู่กับบริษัทตามพระราชบัญญัติคุ้มครองข้อมูลส่วนบุคคล พ.ศ.2562 บริษัทให้ความสำคัญกับการคุ้มครองข้อมูลส่วนบุคคลและการรักษาความมั่นคง ปลอดภัยของข้อมูลส่วนบุคคลเพื่อให้ท่านมั่นใจว่า จากข้อมูลส่วนบุคคลของท่านที่บริษัทได้รับ จะถูกนำไปใช้ตรงตามวัตถุประสงค์ และเป็นไปอย่างถูกต้องตามกฎหมาย บริษัทจึงได้กำหนดนโยบายการคุ้มครองข้อมูลส่วนบุคคล (“นโยบาย”) ฉบับนี้ขึ้น เพื่อแจ้งให้ท่านในฐานะเจ้าของข้อมูลส่วนบุคคลทราบถึงวัตถุประสงค์และรายละเอียดของการเก็บรวบรวม ใช้ และ / หรือเปิดเผยข้อมูลส่วนบุคคล ตลอดจนสิทธิต่างๆ ของท่านตามกฎหมาย",
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n2. ข้อมูลส่วนบุคคลอะไรบ้างที่บริษัทเก็บรวบรวม ใช้ และ/หรือเปิดเผย',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\n2.1 นโยบายฉบับนี้ใช้กับใครบ้าง\nนโยบายฉบับนี้ใช้สำหรับท่าน หากท่านเป็นบุคคลประเภทใดประเภทหนึ่งหรือหลายประเภท ดังนี้\nลูกค้าประเภทบุคคล ที่อยู่ภายใต้นโยบาย\n● ลูกค้าบุคคลธรรมดาของบริษัท (ลูกค้าบุคคลธรรมดา)\nลูกค้าบุคคลธรรมดาของบริษัท เช่น\n - ผู้ซึ่งได้รับ หรือเคยได้รับการติดต่อจากบริษัท\n - ผู้ซึ่งใช้ หรือเคยใช้ผลิตภัณฑ์ และ/หรือ บริการ ของบริษัท\n - ผู้ซึ่งติดต่อสอบถามข้อมูลผลิตภัณฑ์ และ/หรือ บริการ ของบริษัท\n - ผู้ที่ได้รับการเสนอหรือชักชวนจากบริษัท ให้ใช้หรือรับผลิตภัณฑ์และ /หรือ บริการ',
                                  ),
                                  const TextSpan(
                                      text:
                                          '\n● บุคคลธรรมดาที่มีการเกี่ยวข้องกับลูกค้าประเภทนิติบุคคลของบริษัท หรือนิติบุคคลที่มีการทำธุรกรรมกับริษัท\nบุคคลธรรมดา ที่มีความเกี่ยวข้องกับลูกค้านิติบุคคลของบริษัท หรือนิติบุคคลที่มีการทำธุรกรรมกับบริษัท เช่น\n - ผู้ถือหุ้น\n - กรรมการ\n - ผู้มีอำนาจกระทำการแทน\n - หุ้นส่วนตัวแทน\n - พนักงาน เจ้าหน้าที่ และ / หรือ ผู้ที่ได้รับมอบหมาย'),
                                  const TextSpan(
                                      text:
                                          '\n● บุคคลธรรมดาที่มีความเกี่ยวข้องกับการทำธุรกรรมของบริษัท หรือลูกค้าของบริษัท\nบุคคลธรรมดาที่เกี่ยวข้องกับการทำธุรกรรมของบริษัทหรือลูกค้าของบริษัท เช่น\n  - ผู้ติดต่อ\n - ลูกจ้าง พนักงาน เจ้าหน้าที่ บุคลากร\n - บุคคลในครอบครัว เพื่อน เพื่อนบ้าน\n - บุคคลที่ลูกค้าของบริษัทแนะนำหรืออ้างอิง\n - คู่ค้า เจ้าหนี้ ลูกหนี้ ผู้ให้เช่า ผู้เช่าย\n - บุคคลที่ได้ชำระเงินให้แก่หรือรับเงินจากลูกค้าของบริษัท\n - บุคคลอื่นใดที่บริษัทอาจได้รับข้อมูลส่วนบุคคลจากการทำธุรกรรมของลูกค้า(เช่น ประเมินทรัพย์สิน หรือการให้สินเชื่อ)\n - บุคคลที่ได้เข้าชมเว็บไซค์หรือแอปพลิเคชันหรือบัญชีสื่อสังคมออนไลน์ของบริษัท หรือเข้าใช้บริการที่สาขาหรือสำนักงานใหญ่ของบริษัท\n - ที่ปรึกษาด้านวิชาชีพ\n - บุคคลธรรมดาอื่นในทำนองเดียวกัน'),
                                  const TextSpan(
                                      text:
                                          '\n● บุคคลทั่วไป\nบบุคคลธรรมดาทั่วไป เช่น\n- บุคคลที่บริษัทมีความสัมพันธ์ ปฎิสัมพันธ์ ติดต่อกันโดยประการอื่น หรือให้ข้อมูลส่วนบุคคลกับบริษัท หรือที่บริษัทได้รับข้อมูลส่วนบุคคลมาทั้งทางตรงและทางอ้อมไม่ว่าผ่านช่องทางใด\n\n2.2 ข้อมูลส่วนบุคคลอะไรบ้างที่บริษัทเก็บรวบรวม ใช้ และ/หรือเปิดเผย\nข้อมูลส่วนบุคคล หมายถึง ข้อมูลที่ทำให้สามารถระบุตัวตนของท่านได้ ไม่ว่าทางตรงหรือทางอ้อม ดังนี้\n 2.2.1 บุคคลธรรมดา คือ ลูกค้าบุคคลธรรมดา บุคคลธรรมดาที่มีความเกี่ยวข้องกับการทำธุรกรรมของบริษัท หรือลูกค้าของบริษัท และบุคคลธรรมดาทั่วไป\nประเภทข้อมูลที่บริษัทเก็บรวบรวม ใช้และ /หรือ เปิดเผย\n ● ข้อมูลส่วนตัว\n  - คำนำหน้าชื่อ ชื่อ ชื่อกลาง นามสกุล นามแฝง (หากมี)\n  - เพศ วันเดือนปีเกิด อายุ\n  - สถานภาพทางการสมรส สถานภาพครอบครัว จำนวนสมาชิกในครอบครัวและจำนวนบุตร\n  - ข้อมูลความสัมพันธ์ (เช่น ท่านและผู้รับสิทธิประโยชน์)\n  - สัญชาติ ประเทศที่พำนัก\n  - ลายมือชื่อ\n  - ข้อมูลบนเอกสารที่ออกโดยหน่วยงานราชการ (เช่น สำเนาบัตรประจำตัวประชาชน สำเนาหนังสือเดินทาง สำเนาวีซ่า สำเนาใบต่างด้าว สำเนาใบอนุญาตทำงาน สำเนาบัตรประจำตัวข้าราชการ /รัฐวิสาหกิจ สำเนาทะเบียนบ้าน สำเนาสูติบัตร สำเนาใบเปลี่ยนชื่อ สำเนาทะเบียนสมรส สำเนาใบสำคัญหย่า สำเนาใบมรณบัตร สำเนาใบอนุญาตขับขี่รถยนต์ หรือเอกสารที่ใช้ในการระบุและยืนยันตัวตนที่มีลักษณะเดียวกัน) ข้อมูล KYC และ CDD อื่นๆ เป็นต้น'),
                                  const TextSpan(
                                      text:
                                          '\n ● ข้อมูลเพื่อการติดต่อ\n  - ที่อยู่ตามเอกสารสำคัญ ที่อยู่อาศัยปัจจุบัน และที่อยู่ในประเทศตามสัญชาติ สถานที่ทำงาน\n  - หมายเลขโทรศัพท์ หมายเลขโทรศัพท์เคลื่อนที่ หมายเลขโทรสาร อีเมล\n  - ชื่อหรือบัญชีเข้าใช้งานสำหรับการติดต่อสื่อสารทางอิเล็กทรอนิกส์หรือสื่อสังคมออนไลน์ต่างๆ (เช่น ไอดีไลน์ (LINE ID) , ชื่อเฟสบุ๊ค (FACEBOOK Login) , ชื่อบัญชีกูเกิล (GOOGLE Login) , เทเลแกรม (TELEGRAM) )\n  - หลักฐานการมีถิ่นที่อยู่ในประเทศไทย (สำหรับกรณีชาวต่างชาติ)\n ● ข้อมูลการศึกษาและการทำงาน\n  - ข้อมูลบนสำเนาบัตรนักศึกษา ระดับการศึกษาสูงสุด\n  - อาชีพและสาขาอาชีพ\n  - ตำแหน่ง อายุงานปัจจุบัน\n  - รายละเอียดงาน ประเภทธุรกิจ\n ● ข้อมูลความเป็นเจ้าของกิจการ\n  - สัดส่วนการถือหุ้น และ / หรือ ข้อมูลบนเอกสารอื่นใดเพื่อยืนยันการประกอบธุรกิจ (เช่น สัญญาเช่าสถานประกอบกิจการ)'),
                                  const TextSpan(
                                      text:
                                          '\n ● ข้อมูลทางการเงินและการทำธุรกรรม\n  - เลขที่บัญชีงินฝาก จำนวนเงินฝาก ดอกเบี้ย\n   - หมายเลขบัตรเครดิต / เดบิต\n  - ข้อมูลรายได้ แหล่งที่มาของรายได้และรายจ่าย\n  - ข้อมูลบนหนังสือรับรองเงินเดือน สลิปเงินเดือน / โบนัส หรือหลักฐานแสดงรายได้อื่น ๆ เอกสารการเดินบัญชีของธนาคารอื่นราคาประเมินทรัพย์สิน\n  - เลขประจำตัวผู้เสียภาษีอากรและข้อมูลการเสียภาษีของบุคคล\n  - ข้อมูลการสมัครใช้ช่องทาง ผลิตภัณฑ์ และ / หรือ บริการ\n  - ข้อมูลคะแนนเครดิต (Credit Score) วงเงินบัตร คะแนนสะสมวงเงินสินเชื่ออนุมัติ ข้อมูลการกู้ยืมเงิน ยอดหนี้ ข้อมูลหลักประกันและเอกสารแสดงความเป็นเจ้าของหลักประกัน รายละเอียดและประวัติการชำระเงิน\n  - ประวัติการทำธุรกรรม รายละเอียดธุรกรรมและวัตถุประสงค์ในการทำธุรกรรม ข้อมูลในบันทึกช่วยจำการทำธุรกรรม หมายเลขอ้างอิงการทำธุรกรรม ช่องทางการทำธุรกรรม\n  - บัญชีชื่อผู้ใช้งานแอปพลิเคชันและรหัสผ่าน\n  - ข้อมูลการเอาประกันภัย (เช่น เอกสารคำขอเอาประกันภัย ประเภทการเอาประกันภัย คำขอเอาประกันภัย รายละเอียดข้อมูลกรมธรรม์ประกันภัย วันที่เริ่มและวันที่สิ้นสุดการคุ้มครอง รายละเอียดทรัพย์สินที่เอาประกันภัย เบี้ยประกันภัย ทุนประกันภัย และรายละเอียดการใช้สิทธิเรียกร้องตามกรมธรรม์ประกันภัย)\n  - ข้อมูลอื่น ๆ ประกอบการใช้ผลิตภัณฑ์ / บริการ (เช่น รหัสลูกค้า /หมายเลขประจำตัวลูกค้า ข้อมูลเช็ค ข้อมูลตั๋วแลกเงิน วงเงินสินเชื่อ อัตราดอกเบี้ย สกุลเงินที่เกี่ยวข้อง ข้อมูลประกอบการขอสินเชื่อ ข้อมูลทางการค้า คำสั่งซื้อสินค้า สัญญาจะซื้อจะขาย และ / หรือ สัญญาอื่น ๆ รายละเอียดการค้ำประกัน แบบแจ้งสถานะความเป็นบุคคลอเมริกัน / ไม่เป็นบุคคลอเมริกัน (FATCA)'),
                                  const TextSpan(
                                      text:
                                          '\n ● ข้อมูลทางเทคนิค อุปกรณ์หรือเครื่องมือ\n  - ข้อมูลการใช้งานแอปพลิเคชัน\n   - หมายเลขประจำเครื่องคอมพิวเตอร์ (IP address หรือ Macaddress)\n  - คุกกี้(Cookies ID)\n  - เว็บบีคอน (Web beacon) พิกเซลแท็ก (Pixel Tag) หรือ Software Development Kit (SDK)\n  - รหัสประจำอุปกรณ์ (Device ID)\n  - รุ่นและประเภทของอุปกรณ์ เครือข่าย ข้อมูลการเชื่อมต่อ\n  - ข้อมูลการเข้าถึง ข้อมูลการเข้าใช้งานแบบ single sign - on (SSO)ล็อก (Log)\n  - ข้อมูลการเข้าสู่ระบบ (Log - in) ระยะเวลาที่เข้าถึง การใช้งานและระยะเวลาการใช้งานแอปพลิเคชันและเว็บไซต๊ ประวัติการค้นหาข้อมูลการเรียกดู\n  - ค่าเขตเวลา (Time zone) และสถานที่ตั้ง (Location Data)\n ● ข้อมูลอื่นๆ\n  - บันทึกการสื่อสารหรือการโต้ตอบระหว่างท่านกับบริษัทรายละเอียดเรื่องร้องเรียนหรือการออกความเห็น คำขอใช้สิทธิต่างๆ ผลประเมินการสำรวจความคิดเห็น บันทึกเสียง ภาพถ่ายภาพเคลื่อนไหว คลิปบันทึกเสียง บันทึกการสื่อสารผ่าน Log / Chat-Bot ภาพนิ่งหรือภาพเคลื่อนไหวจากกล้องโทรทัศน์วงจรปิด(CCTV) ข้อมูลบนคำสั่งศาล / ราชกิจจานุเบกษาที่เกี่ยวกับการทำธุรกรรมของลูกค้าของบริษัท หรือเกี่ยวกับการปฏิบัติตามกฎหมายของบริษัท (เช่น คำสั่งพิทักษ์ทรัพย์ คำสั่งแต่งตั้งผู้จัดการมรดกคำสั่งให้เป็นคนไร้ความสามารถ หรือคนสมือนไร้ความสามารถคำสั่งเรียกพยานเอกสารหรือพยานวัตถุ) และข้อมูลอื่นใดที่ถือว่าเป็นข้อมูลส่วนบุคคลภายใต้กฎหมายคุ้มครองข้อมูลสวนบุคคล\n  - ข้อมูลการลงทะเบียนเข้าร่วมกิจกรรมของบริษัท'),
                                  const TextSpan(
                                      text:
                                          '\n 2.2.2 บุคลากรของนิติบุคคล คือ บุคคลธรรมดาที่มีความเกี่ยวข้องกับลูกค้านิติบุคคลของบริษัท หรือนิติบุคคลที่มีการทำธุรกรรมกับบริษัท\n ประเภทข้อมูลที่บริษัทเก็บรวบรวม ใช้และ /หรือ เปิดเผย\n ● ข้อมูลส่วนตัว\n  - คำนำหน้าชื่อ ชื่อ ชื่อกลาง นามสกุล นามแฝง (หากมี)\n  - เพศ วันเดือนปีเกิด อายุ\n  - สถานภาพทางการสมรส ลายมือชื่อ\n  - ข้อมูลบนเอกสารที่ออกโดยหน่วยงานราชการ (เช่น สำเนาบัตรประจำตัวประชาชน สำเนาหนังสือเดินทาง สำเนาวีซ่า สำเนาใบต่างด้าว สำเนาใบอนุญาตทำงาน สำเนาทะเบียนบ้าน หรือเอกสารที่ใช้ในการระบุและยืนยันตัวตนที่มีลักษณะเดียวกัน) ข้อมูล KYCและ CDD อื่นๆ เป็นต้น\n ● ข้อมูลเพื่อการติดต่อ\n  - ที่อยู่ตามเอกสารสำคัญ ที่อยู่อาศัยปัจจุบัน และที่อยู่ในประเทศตามสัญชาติ สถานที่ทำงาน\n  - หมายเลขโทรศัพท์ หมายเลขโทรศัพท์เคลื่อนที่ หมายเลขโทรสาร อีเมล์\n ● ข้อมูลการทำงาน\n  - อาชีพและสาขาอาชีพ\n  - ตำแหน่ง อายุงานปัจจุบัน\n  - รายละเอียดงาน ประเภทธุรกิจ\n ● ข้อมูลที่ปรากฏในเอกสารประกอบการทำธุรกรรม\n  - หนังสือรับรองบริษัท\n  - บัญชีรายชื่อผู้ถือหุ้น\n  - หนังสือมอบอำนาจ\n  - หนังสือจดทะเบียนพาณิชย์\n ● ข้อมูลอื่นๆ\n  - ข้อมูลที่มีการเก็บรวบรวม ใช้ และ / หรือเปิดเผยโดยเกี่ยวข้องกับความสัมพันธ์กับบริษัท เช่น ข้อมูลที่นิติบุคคลให้แก่บริษัท หรือนิติบุคคลอื่นที่เกี่ยวข้องในสัญญา รายละเอียดเรื่องร้องเรียนหรือการออกความเห็น ผลประเมินการสำรวจความคิดเห็น ข้อมูลการลงทะเบียนเข้าร่วมกิจกรรมของบริษัท'),
                                  const TextSpan(
                                    text:
                                        '\n\n2.3ข้อมูลส่วนบุคคลที่มีความอ่อนไหว(Sensitive Data)\n   ข้อมูลส่วนบุคคลที่ละเอียดอ่อน หมายถึง ข้อมูลส่วนบุคคลที่กฎหมายกำหนดเป็นการเฉพาะ โดยบริษัทไม่มีเจตนาในการเก็บรวบรวมข้อมูลส่วนบุคคลที่ละเอียดอ่อนจากท่าน หากแต่ในบางกรณี บริษัทอาจจำเป็นต้องเก็บรวบรวมข้อมูลส่วนบุคคลที่ละเอียดอ่อนจากท่านเพื่อประกอบการให้บริการหรือผลิตภัณฑ์แก่ท่าน เช่น ศาสนาตามสำเนาบัตรประจำตัวประชาชน หรือเชื้อชาติตามสำเนาหนังสือเดินทางของบางประเทศ ข้อมูลชีวภาพ (Biometic) (เช่น ข้อมูลภาพจำลองใบหน้า ข้อมูลจำลองลายนิ้วมือข้อมูลลายมือชื่ออิเล็กทรอนิกส์ที่มีการใช้ทคโนโลยีที่นำลักษณะเด่นทางพฤติกรรมของการลงลายมือชื่อดังกล่าวมาใช้สำหรับการพิสูจน์และยืนยันตัวบุคคลที่เป็นผู้ลงลายมือชื่อนั้น) ข้อมูลประวัติอาชญากรรม ข้อมูลสุขภาพ ข้อมูลความพิการ พฤติกรรมทางเพศ เป็นต้น ทั้งนี้ บริษัทจะเก็บรวบรวม ใช้ และ / หรือ เปีดเผยข้อมูลส่วนบุคคลที่ละเอียดอ่อนต่อเมื่อบริษัทได้รับความยินยอมโดยชัดแจ้งจากท่าน หรือในกรณีที่บริษัทมีความจำเป็นตามกรณีที่กฎหมายอนุญาต โดยจะดำเนินการเป็นคราวๆ ไปเมื่อต้องเก็บรวบรวมข้อมูลส่วนบุคคลที่ละเอียดอ่อนจากท่าน (ต่อไปในนโยบายฉบับนี้หากไม่กล่าวโดยเฉพาะเจาะจงจะเรียกข้อมูลส่วนบุคคลและข้อมูลส่วนบุคคลที่ละเอียดอ่อนข้างตัน รวมกันว่า "ข้อมูลส่วนบุคคล")',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\n2.4 ข้อมูลส่วนบุคคลของผู้เยาว์ คนไร้ความสามารถ หรือคนเสมือนไร้ความสามารถ\n   บริษัทไม่มีเจตนาที่จะเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลของผู้เยาว์ คนไร้ความสามารถ หรือคนเสมือนไร้ความสามารถ เว้นแต่บริษัทจะได้รับความยินยอมจากผู้ใช้อำนาจปกครอง ผู้อนุบาล หรือผู้พิทักษ์ (แล้วแต่กรณี) หากบริษัททราบว่าบริษัทได้เก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลของผู้เยาว์ คนไร้ความสามารถ หรือคนเสมือนไร้ความสามารถ โดยปราศจากความยินยอมของผู้ใช้อำนาจปกครอง ผู้อนุบาล หรือผู้พิทักษ์ (แล้วแต่กรณี บริษัทจะดำเนินการลบหรือทำลายข้อมูลส่วนบุคคลนั้น หรือเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลสวนบุคคลเหล่านั้นเฉพาะกรณีที่บริษัทสามารถอาศัยฐานทางกฎหมายอื่นโดยไม่ต้องอาศัยความยินยอม',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\n2.5 ข้อมูลส่วนบุคคลของบุคคลที่สามอื่นใด\n   หากท่านเป็นผู้ให้ข้อมูลส่วนบุคคลของบุคคลที่สามอื่นใด ซึ่งเป็นบุคลากรของนิติบุคคล และ / หรือ ที่เกี่ยวซ้องกับท่านแก่บริษัท เช่น ผู้ถือหุ้น กรรมการ ผู้มีอำนาจกระทำการแทน บุคคลในครอบครัว บุคคลอ้างอิง คู่ค้า ผู้จำนอง ผู้ให้หลักประกัน ผู้รับผลประโยชน์ ผู้จัดการมรดก ผู้ติดต่อฉุกเฉิน และ / หรือ บุคคลอื่นใดตามเอกสารการทำธุรกรรมของท่าน เป็นต้นขอให้ท่านโปรดแจ้งให้บุคคลเหล่านั้นทราบเกี่ยวกับรายละเอียดตามนโยบายฉบับนี้ และขอความยินยอมจากบุคคลเหล่านั้นหากจำเป็น หรือกำหนดฐานทางกฎหมายอื่นเพื่อให้แนใจว่าบริษัทสามารถเก็บรวบรวม ใช้ และ / หรือเปิดเผยชัอมูลสวนบุคคลของบุคคลที่สามเหล่านี้ได้',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n3. บริษัทเก็บรวบรวม ใช้ และ/เหรือเปิดเผยข้อมูลส่วนบุคคลของลูกค้า เพื่อวัตถุประสงค์ใดบ้าง',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทจะเก็บรวบรวม ใช้ และ / หรือเปิดเผยข้อมูลส่วนบุคคลของท่านเท่าที่จำเป็นภายใต้วัตถุประสงค์อันชอบด้วยกฎหมายของบริษัท ซึ่งรวมถึงการเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลเพื่อการปฏิบัติตามสัญญาซึ่งท่านเป็นคู่สัญญา เพื่อปฏิบัติหน้าที่ตามกฎหมาย เพื่อประโยชน์โดยชอบด้วยกฎหมาย เพื่อดำเนินการตามความยินยอมของท่น และ / หรือเพื่อดำเนินการภายใต้ฐานทางกฎหมายอื่น ๆ โดยวัตถุประสงค์ในการเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลตามนโยบายฉบับนี้ มีดังนี้\n\n3.1 วัตถุประสงค์ที่อาศัยความยินยอม\nบริษัทจะเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลของท่านโดยอาศัยฐานความยินยอม สำหรับวัตถุประสงค์ดังต่อไปนี้\n  3.1.1 การเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลที่ละเอียดอ่อนที่บริษัทไม่สามารถอาศัยฐานทางกฎหมายอื่น นอกเหนือจากการขอความยินยอมโดยชัดแจ้งได้ วัตถุประสงค์ดังกล่าว ได้แก่\n   (1) ข้อมูลศาสนา และเชื้อชาติ (ข้อมูลดังกล่าวได้มาจากการเก็บสำเนาบัตรประจำตัวประชาชนหรือสำเนาหนังสือเดินทางของบางประเทศที่ธนาคารจำเป็นต้องใช้เป็นหลักฐานในการพิสูจน์และยืนยันตัวบุคคลเท่านั้น)\n   (2) ข้อมูลชีวภาพ เพื่อการลงลายมือชื่อ การพิสูจน์และยืนยันตัวบุคคล การให้บริการยืนยันตัวตนทางอิเล็กทรอนิกส์(electronic Know Your Customer) ของบริษัทและเพื่อสนับสนุนพันธมิตรทางธุรกิจของบริษัท\n   (3) ข้อมูลประวัติสุขภาพ ข้อมูลความพิการ ประวัติอาชญากรรม พฤติกรรมทางเพศ ซึ่งธนาคารจะเก็บรวบรวม ใช้ และ /หรือเปิดเผยข้อมูลส่วนบุคคลดังกล่าว เฉพาะกรณีที่มีความจำเป็นเพื่อการใช้ผลิตภัณฑ์ และ / หรือ บริการบางประเภทของบริษัทเท่านั้นและสำหรับประวัติอาชญากรรมเพื่อการตรวจสอบและการดำเนินการอายัดทรัพย์สินที่เกี่ยวข้องอีกด้วย',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n  3.1.2 การวิเคราะห์ วิจัย และ / หรือ จัดทำข้อมูลทางสถิติ รวมถึง เพื่อการพัฒนา ปรับปรุงผลิตภัณฑ์ และ / หรือ บริการของบริษัท และเหรือบริษัทอื่นในกลุ่มธุรกิจทางการเงินของบริษัท พันธมิตรทางธุรกิจ และ / หรือนิติบุคคลอื่น ที่ต้องอาศัยความยินยอมจากท่านตามกฎหมาย\n  3.1.3 การดำเนินการทางการตลาด การนำสงข้อเสนอผลิตภัณฑ์ และ / หรือ บริการ สิทธิพิเศษในการเข้าร่วมกิจกรรมที่บริษัท และเหรือ บริษัทอื่นในกลุ่มธุรกิจทางการเงินของบริษัท พันธมิตรทางธุรกิจ และ / หรือนิติบุคตลอื่นจัดขึ้นรวมถึงข่าวสาร คำแนะนำที่เบ็นประโยชน์และโปรโมชั่นที่คัดสรรอย่างเหมาะสม และการออกกลยุทธ์ทางการตลาดที่ต้องอาศัยความยินยอมจากท่านตามกฎหมาย\n  ทั้งนี้ บริษัทอาจขอความยินยอมโดยตรงจากท่านหรือผ่านบริษัทอื่นในกลุ่มธุรกิจทางการเงินของบริษัท และหรือพันธมิตทางธุรกิจ และ / หรือ นิติบุคคลอื่น เป็นคราวๆ ไป\n\n3.2 วัตถุประสงค์ที่อาศัยฐานทางกฎหมายอื่น นอกเหนือจากความยินยอม\nบริษัทจะเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลของท่นโดยอาศัยฐานทางกฎหมายอื่นๆ เท่าที่จำเป็นภายใต้วัตถุประสงค์อันชอบด้วยกฎหมายของบริษัท เช่น เพื่อการปฏิบัติตามสัญญาซึ่งท่านเป็นคู่สัญญาหรือตามที่ท่านร้องขอ เพื่อปฏิบัติหน้าที่ตามกฎหมาย เพื่อประโยชน์โดยชอบด้วยกฎหมาย และ / หรือ เพื่อดำเนินการภายใต้ฐานทางกฎหมายอื่นๆ สำหรับวัตถุประสงค์ ดังต่อไปนี้\n  3.2.1 การดำเนินการก่อนเข้าทำสัญญากับบริษัท เช่น การให้คำปรึกษา คำแนะนำ และ / หรือ\n  ข้อมูลอื่นใดที่เกี่ยวข้องกับผลิตภัณฑ์ และ / หรือ บริการ การวิเคราะห์และประเมินความต้องการของลูกค้า การตรวจสอบคุณสมบัติการตรวจสอบสถานะของลูกค้านิติบุคคล การตรวจสอบความถูกต้องของข้อมูลหรือเอกสาร การพิสูจน์และยืนยันตัวบุคคล รวมถึงกระบวนการทำความรู้จักลูกค้า (know - your - customer (KYC) และตรวจสอบสถานะลูกค้า (customer due diligence (CDD)การประเมินทรัพย์สิน การตรวจสอบข้อมูลเครดิต และ / หรือ การขอแก้ไขข้อมูลเครดิต การตรวจสอบกับรายชื่อบุคคลที่ถูกกำหนด(Sanction List) ของหน่วยงานบังคับใช้กฎหมายของรัฐและ / หรือหน่วยงานทางการซึ่งเปิดเผยเป็นการทั่วไปตามที่กฎหมายกำหนดการตรวจสอบการถูกพิทักษ์ทรัพย์หรือความเป็นบุคคลล้มละลาย การจัดระดับความเสี่ยงลูกค้า การ pre - fll ข้อมูลส่วนตัว / ข้อมูลเพื่อการติดต่อของลูกค้าเพื่อการอำนวยความสะดวกในการสมัครผลิตภัณฑ์ และ / หรือ บริการของบริษัท',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n  3.2.2 การดำเนินการใด ๆ ที่เกี่ยวข้องกับการพิจารณาให้ผลิตภัณฑ์ และ / หรือบริการต่าง ๆ\n  เช่น การติดต่อสื่อสาร การประมวลผลคำขอและการดำเนินการตามกระบวนการพิจารณาอนุมัติคำขอซึ่งรวมถึงแต่ไม่จำกัดเพียง การประเมินรัพย์สินลูกค้า การตรวจสอบและประเมินราคาทรัพย์สิน การทบทวนราคาทรัพย์สิน) การกำหนดวงเงิน สินเชื่อ การเข้าทำสัญญา ข้อตกลง และ / หรือ นิติกรรมอื่นใดที่เกี่ยวข้อง การลงทะเบียนใช้ผลิตภัณฑ์ บริการและ / หรือ เพื่อเข้าร่วมกิจกรรมต่าง ๆ ของบริษัท\n  3.2.3 การส่งมอบผลิตภัณฑ์ และ / หรือ บริการต่าง ๆ ตามสัญญาที่ท่านได้ทำไว้กับริษัท เช่น\n  - การขอสินเชื่อ การทำสัญญากู้ยืมเงิน\n  - การดำเนินการใด ๆ ที่เกี่ยวข้องกับการให้ใช้ผลิตภัณฑ์ และ / หรือ บริการ (เช่น การเปลี่ยนแปลงข้อมูลการตั้ง ใช้ เปลี่ยนแปลงวงเงิน หรือปรับปรุงบัญชี การจ่ายดอกเบี้ย การคืนเงินต้น การรับชำระราคา ตรวจสอบยอดเงินในบัญชีลูกหนี้การทำรายงานธุรกรรม การดำเนินงานที่เกี่ยวข้องกับความสัมพันธ์ระหว่างวงเงินสินเชื่อกับหลักประกันการดำเนินการที่เกี่ยวข้องกับหลักประกัน การเอาประกันภัยทรัพย์สิน การเปลี่ยนแปลงหรือเพิ่มวงเงินสินเชื่อเงินกู้เบิกเงินเกินบัญชี การจัดเตรียมเอกสารของข้อมูลลูกค้าเพื่อประกอบการทำธุรกรรมของลูกค้า (เช่น การรับรองเอกสาร)\n  - การตรวจสอบ ยืนยัน และปรับปรุงรายการธุรกรรม (รวมถึงกรณี Know Your Customer (KYC)กระบวนการทำความรู้จักลูกค้าที่สามารถระบุตัวตน และพิสูจน์ตัวตนได้อย่างถูกต้อง และ CustomerDue Diligence (CDD) กรตรวจสอบด้วยการประเมินและบริหารความเสี่ยงก่อนอนุมัติรับลูกค้า)\n  - การมอบสิทธิประโยชน์ และการดำเนินการให้เป็นไปตามสิทธิประโยชน์ของลูกค้า\n  - การจัดการความสัมพันธ์ลูกค้า การดำเนินธุรกรรมหลังการขาย การอำนวยความสะดวกลูกค้า และหรือ การจัดการสินค้าสมนาคุณให้กับลูกค้า\n  - การให้คำปรึกษาหรือแนวทางการจัดการความเสี่ยง\n  - การจัดการข้อร้องเรียน แก้ไขปัญหา การดำเนินการตามคำขอลูกค้า\n  - การรับชำระเงินหรือทรัพย์สินใดๆ\n  - การติดตามการปฏิบัติตามเงื่อนไขการใช้ผลิตภัณฑ์ และ / หรือ บริการ การยกเลิกการบริการ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n  3.2.4 การดำเนินการทางการตลาดที่ไม่ต้องอาศัยความยินยอมจากท่านตามกฎหมาย เช่น\n  - การพิจารณากลุ่มลูกค้าเพื่อการนำส่งคำเชิญเข้าร่วมกิจกรรม หรือการส่งเสริมการขายตามความเหมาะสม\n  - การนำส่งข้อเสนอผลิตภัณฑ์ และ / หรือ บริการ สิทธิพิเศษในการเข้าร่วมกิจกรรม งานอีเว้นท์ หรือการประชุมที่บริษัทจัดขึ้น รวมถึงการอำนวยความสะดวกในการเข้าร่วมกิจกรรม (เช่น การลงทะเบียนเข้างาน)\n  - การนำเสนอผลิตภัณฑ์ บริการ และ / หรือ สิทธิพิเศษที่ท่านร้องขอ หรือการแจ้งสิทธิประโยชน์ของท่าน\n  - การนำเสนอผลิตภัณฑ์ และ / หรือ บริการประเภทเดียวกัน / ใกล้เคียงกับที่ท่านมีอยู่กับบริษัท หรือบริษัทอื่นในกลุ่มธุรกิจทางการเงินของบริษัท\n  - การติดต่อในกรณีที่ท่านยังสมัครใช้ผลิตภัณฑ์ และ / หรือ บริการไม่สำเร็จ (drop - off) เพื่ออำนวยความสะดวกให้แก่ท่านในกรณีที่ท่านต้องการสมัครใช้ผลิตภัณฑ์ และ / หรือ บริการประเภทเดียวกันกับบริษัทอีกครั้ง หรือนำเสนอผลิตภัณฑ์ และ / หรือบริการอื่นที่น่าจะอยู่ในความสนใจของท่าน\n  - การจัดกิจกรรมส่งเสริมการขาย (เช่น การให้สิทธิประโยชน์และของรางวัล)\n  3.2.5 การวิเคราะห์ วิจัย และ / หรือ จัดทำข้อมูลทางสถิติที่ไม่ต้องอาศัยความยินยอมจากท่านตามกฎหมาย เพื่อใช้ในการพัฒนา ปรับปรุงผลิตภัณฑ์ และ / หรือ บริการภายในบริษัทเอง เช่น\n  - การวิเคราะห์ วิจัย วิจัยการตลาด จัดทำข้อมูลทางสถิติ การวิเคราะห์ข้อมูลทางการเงินของท่าน และ /หรือ การจัดทำรายงานสำหรับการใช้ภายในบริษัท\n  - การวิเคราะห์ จัดทำแบบจำลอง (เช่น การทำ Credit Scoring)\n  - การศึกษา วิเคราะห์ และติดตามสัดส่วนเครดิตพอร์ตโฟลิโอ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n  3.2.6 การดำเนินงานอื่น ๆ ของบริษัท เช่น\n  - การรักษาประโยชน์อันชอบด้วยกฎหมาย\n  - การจัดทำฐานช้อมูลลูกค้า หรือ การเก็บบันทึกข้อมูลลงระบบหรือฐานข้อมูล\n  - การบริหารจัดการ การบริหารความเสี่ยง การกำกับตรวจสอบภายในบริษัท\n  - การพิจารณาทบทวนคุณภาพเครดิตลูกค้า\n  - การแจ้งเตือนชำระหนี้หรือต่ออายุผลิตภัณฑ์ และ / หรือ บริการต่าง ๆ\n  - การติดตามทวงถามหนี้\n  - การสำรวจและประเมินความพึงพอใจภายหลังใช้ผลิตภัณฑ์ และ / หรือ บริการ\n  - การดำเนินคดีหรือกระบวนการทางกฎหมายที่เกี่ยวข้อง\n  - การร่วมงาน การประสานงาน และ / หรือ การมอบหมายงานให้ผู้อื่นดำเนินการแทนหรือร่วมกับบริษัท(เช่น เพื่อการออกแบบผลิตภัณฑ์หรือบริการ ออกแบบประสบการณ์การให้บริการลูกค้า ออกแบบกระบวนการ หรือการสนับสนุนการส่งมอบผลิตภัณฑ์ และ / หรือ บริการ)\n  - การโอนสิทธิ และ / หรือ หน้าที่ การบริหารกิจการของบริษัท และบริษัทอื่นในกลุ่มธุรกิจทางการเงินของบริษัท\n  - การจัดการเรื่องร้องเรียน หรือการจัดการเหตุการณ์กระทำผิดต่อกฎหมาย หรือเหตุการณ์ต้องสงสัย(เช่น การทุจริต การฟอกเงิน การก่อการร้ายและแพร่ขยายอาวุธที่มีอานุภาพทำลายล้างสูง การก่ออาชญากรรม การละเมิดทรัพย์สินทางปัญญา ซึ่งรวมถึง การวางแผนการจัดการ การตรวจสอบ การเฝ้าระวัง การเก็บหลักฐาน การรายงาน และ / หรือ การดำเนินการตรวจจับ)\n  - การป้องกันและประเมินความเสี่ยงที่อาจเกิดขึ้นจากการให้สินเชื่อของระบบสถาบันการเงิน\n  - การทำฐานข้อมูลด้านความเสี่ยงทางธุรกิจต่อบริษัท\n  - การดำเนินการด้านเทคโนโลยีสารสนเทศ การจัดการระบบสื่อสาร และการป้องกัน รับมือ ลดความเสี่ยงด้านเทคในโลยีสารสนเทศและภัยคุกคามทางไซเบอร์',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n  3.2.7 การปฏิบัติตามคำสั่งของผู้มีอำนาจตามกฎหมาย และ / หรือ การปฏิบัติตามกฎหมาย\n  - การปฏิบัติตามคำสั่งศาล คำสั่งของหน่วยงานรัฐ หน่วยงานที่มีอำนาจกำกับดูแลธนาคาร เจ้าพนักงานของรัฐที่มีอำนาจตาม กฎหมายคุ้มครองข้อมูลส่วนบุคคล กฎหมายธุรกิจสถาบันการเงิน กฎหมายหลักทรัพย์และตลาดหลักทรัพย์ กฎหมายประกันชีวิต กฎหมายประกันวินาศภัย กฎหมายระบบการชำระเงิน กฎหมายควบคุมการแลกเปลี่ยนเงิน กฎหมายสถาบันคุ้มครองเงินฝาก กฎหมายภาษีอากรกฎหมายป้องกันและปราบปรามการฟอกเงินกฎหมายการป้องกันและปราบปรามการสนับสนุนทางการเงินแก่การก่อการร้ายและแพร่ขยายอาวุธที่มีอานุภาพทำลายล้างสูง กฎหมายว่าด้วยการกระทำความผิดเกี่ยวกับคอมพิวเตอร์ กฎหมายล้มละลาย และกฎหมายอื่นๆที่ธนาคารจำเป็นต้องปฏิบัติตามทั้งในประเทศไทยและต่งประเทศ รวมถึงประกาศและระเบียบที่ออกตามกฎหมายดังกล่าว ทั้งที่ใช้บังคับอยู่แล้วในขณะนี้ ที่จะแก้ไขเพิ่มเติม หรือที่จะมีขึ้นต่อไปในอนาคต\n  - การจัดทำฐานช้อมูลลูกค้า หรือ การเก็บบันทึกข้อมูลลงระบบหรือฐานข้อมูล\n  - การบริหารจัดการ การบริหารความเสี่ยง การกำกับตรวจสอบภายในบริษัท\n  3.2.8 การป้องกันหรือระงับอันตรายต่อชีวิต ร่างกาย หรือสุขภาพของบุคคล\n  3.2.9 การจัดทำเอกสารประวัติศาสตร์หรือจดหมายเหตุเพื่อประโยชน์สาธารณะ หรือที่เกี่ยวกับการศึกษาวิจัยหรือสถิติ\n  3.2.10 การดำเนินภารกิจเพื่อประโยชน์สาธารณะของบริษัท หรือการปฏิบัติหน้าที่ในการใช้อำนาจรัฐที่ได้มอบให้แก่บริษัท\n  ทั้งนี้ หากบริษัทจำเป็นต้องเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลจากท่านสำหรับการเข้าทำหรือการปฏิบัติตามสัญญาที่ท่นได้ทำไว้กับบริษัท และ / หรือ การปฏิบัติหน้าที่ตามกฎหมายของบริษัท และท่านไม่ให้ข้อมูลส่วนบุคคลที่จำเป็นเหล่านั้นแก่บริษัทเมื่อมีการร้องขอ หรือกรณีที่ท่านเลือกที่จะลบบัญชีผู้ใช้งานของท่านออกจากแอปพลิเคชันที่ให้บริการของบริษัท บริษัทอาจจะไม่สามารถพิจารณานุมัติหรือส่งมอบ / จัดหา ผลิตภัณฑ์ และ / หรือ บริการบางส่วนหรือทั้งหมดให้แก่ท่านได้ ตลอดจนอาจส่งผลกระทบต่อการปฏิบัติหน้าที่ตามกุฎหมายของบริษัท หรือความสัมพันธ์ระหว่างท่านและบริษัทได้',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n4.บริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านให้ใครบ้าง',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านให้แก่ผู้อื่นภายใต้ความยินยอมของท่านหรือภายใต้หลักเกณฑ์ที่กฎหมายอนุญาตให้เปิดเผยได้ โดยบุคคลหรือหน่วยงานที่เป็นผู้รับข้อมูลส่วนบุคคลดังกล่าวจะเก็บรวบรวม ใช้ และ / หรือ เปิดเผยข้อมูลส่วนบุคคลของท่านตามขอบเขตที่ท่านได้ให้ความยินยอมหรือขอบเขตที่เกี่ยวข้องในนโยบายฉบับนี้ หรือในบางกรณี ท่านอาจอยู่ภายใต้นโยบายการคุ้มครองข้อมูลสวนบุคคลของผู้รับข้อมูลส่วนบุคคลของท่านเหล่านั้นอีกด้วย โดยที่ผู้รับข้อมูลสวนบุคคลของท่านอาจอยู่ในประเทศไทยหรือต่างประเทศ โดยบริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านให้แก่บุคคลหรือหน่วยงานต่างๆ ตามแต่ความสัมพันธ์และการทำธุรกรรมของท่าน ดังต่อไปนี้\nประเภทผู้รับข้อมูลส่วนบุคคล\n ● บริษัทอื่นในกลุ่มธุรกิจของบริษัท\n  - บริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านให้แก่บริษัทอื่นในกลุ่มธุรกิจอื่นของบริษัท เพื่อวัตถุประสงค์ที่กำหนด หรือตามความยินยอมของท่าน ภายใต้นโยบายฉบับนี้ โดยการนี้ บริษัทอื่นในกลุ่มธุรกิจของบริษัทสามารถยึดถือความยินยอมที่บริษัทได้มา\n ● ผู้ให้บริการของบริษัท\n  บริษัทอาจใช้บริษัทอื่น คู่ค้า ตัวแทนของบริษัท ผู้รับจ้างช่วงงานต่อ หรือผู้ให้บริการภายนอก เพื่อประกอบธุรกิจแทนบริษัท หรือเพื่อช่วยสนับสนุนการให้ผลิตภัณฑ์และหรือบริการของบริษัท แก่ท่าน ด้วยเหตุนี้ บริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านให้แก่ผู้ให้บริการของบริษัท ซึ่งรวมถึงแต่ไม่จำกัดเพียงฃื  - ตัวแทน\n   - ผู้ให้บริการยืนยันตัวตนบนโลกดิจิทัล (National Digital ID)\n   - ผู้ให้บริการระบบโครงสร้างพื้นฐานด้านการชำระเงิน และการโอนเงิน\n  - ผู้ให้บริการระบบใครงสร้างพื้นฐานทางดิจิทัล และผู้ให้บริการระบบฐานข้อมูลเพื่อการแลกเปลี่ยนข้อมูลระหว่างสถาบันการเงิน\n  - ผู้ให้บริการประเมินราคาทรัพย์สิน\n  - ศูนย์รับฝากทรัพย์\n  - ผู้ให้บริการด้านเทคโนโลยีสารสนเทศ การสนับสนุนด้านเทคโนโลยี และการรักษาความมั่นคงปลอดภัยด้านเทคโนโลยี\n  - ผู้ให้บริการ Cloud Computing\n  - ผู้ให้บริการเพื่อการทำการตลาด\n  - ผู้ให้บริการคลังเก็บเอกสาร\n  - ผู้ให้บริการสื่อสังคมออนไลน์\n  - ผู้ให้บริการช่องทางการรับชำระเงิน\n  - ผู้ให้บริการติดตามทวงถามหนี้\n  - โรงพิมพ์หรือผู้ให้บริการสิ่งพิมพ์\n  - ผู้ให้บริการจัดส่งเอกสารหรือพัสดุ\n  - ผู้ให้บริการสำหรับการให้บริการอำนวยความสะดวก (Concierge Services)',
                                  ),
                                  const TextSpan(
                                      text:
                                          '\n ● พันธมิตรทางธุรกิจของบริษัท\n  บริษัทอาจเปิดเผยข้อมูลสวนบุคคลของท่านให้\n  - พันธมิตรทางธุรกิจของธนาคาร ทั้งที่ร่วมมือกันในการจัดหาผลิตภัณฑ์ และ/หรือบริการ (เช่นบริษัทประภันภัย หรือบริษัทประกันวินาศภัย หรือบริษัทประกันชีวิต)\n  - พันธมิตรทางธุรกิจในการสะสมคะแนนหรือการทำ Tokenization\n  - พันธมิตรทางธุรกิจที่ออกผลิตภัณฑ์ร่วมกันในลักษณะ co-brand\n  - ธนาคาร หรือสถานบันการเงินอื่น\n  - พันธมิตรทางธุรกิจที่เป็นผู้ห็บริการเทคโนโลยีด้านการเงิน (FinTech)\n  - ผู้จัดจำหน่ายหลักทรัพย์ (Underwriter) ทั้งนี้ กรณีเปิดเผยข้อมูลส่วนบุคคลของท่านให้พันธมิตรทางธุรกิจเพื่อวัตถุประสงค์ทางการตลาดของพันธมิตรทางธุรกิจ เช่น เพื่อการส่งเสริมการขาย การประชาสัมพันธ์ หรือการเสนอผลิตภัณฑ์ และ/หรือ บริการจากพันธมิตรทางธุรกิจให้แก่ท่าน บริษัทจะแจ้งรายชื่อพันธมิตรทางธุรกิจให้ท่านทราบเพื่อประกอบการตัดสินใจให้ความยินยอม โดยการนี้ พันธมิตรทางธุรกิจสามารถยึดถือตามความยินยอมที่บริษัทได้มา\n ● บุคคลตามที่กฎหมายกำหนด\n  ในบางกรณี บริษัทมีความจำเป็นในการเปิดเผยข้อมูลส่วนบุคคลของท่านเพื่อการปฏิบัติตามคำสั่งของผู้มีอำนาจ หรือมีสิทธิตามกฎหมาย และ/หรือ การปฏิบัติตามกฎหมาย ซึ่งผู้รับข้อมูลส่วนบุคคลของท่าน รวมถึง\n  - หน่วยงานที่มีหน้าที่บังคับใช้กฎหมาย\n  - หน่วยงานที่มีอำนาจกำกับดูแลสถาบันการเงิน\n  - หน่วยงานที่มีอำนาจกำกับดูแลตลาดหลักทรัพย์\n  - หน่วยงานราชการ\n  - สมาคม หน่วยงาน หรือบุคคลอื่นใด ตามความจำเป็นในการปฏิบัติตามภาระหน้าที่ตามกฎหมาย หรือภาระหน้าที่ตามข้อบังคับ หรือเพื่อคุ้มครองสิทธิของบริษัท สิทธิบุคคลภายนอก ซึ่งอาจรวมถึงการดำเนินกระบวนการทางกฎหมายใด ๆ ที่เกี่ยวข้อง เช่น สมาคมตลาดตราสารหนี้ไทย สมาคมบริษัทบริหารสินทรัพย์ไทย\n ● ที่ปรึกษา/ผู้เชี่ยวชาญ\n  เพื่อประโยชน์ในการดำเนินธุรกิจของบริษัท บริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านไปยัง\n  - ผู้สอบบัญชี\n  - ผู้ตรวจสอบภายนอก\n  - ที่ปรึกษาด้านกฎหมาย\n  - ที่ปรึกษาด้านภาษี\n  - บริษัทจัดอันดับความน่าเชื่อถือ\n  - ที่ปรึกษาหรือผู้เชี่ยวชาญอื่น ๆ ตามแต่กรณี\n ● ผู้สนใจจะเข้ารับโอนสิทธิ และ/หรือ ผู้รับโอนสิทธิในธุรกรรมหรือการควบรวมกิจการต่างๆ ของบริษัท\n   ในกรณีที่บริษัทมีการปรับปรุงโครงสร้างองค์กร ปรับโครงสร้างหนี้ การควบรวมกิจการ การได้มาซึ่งกิจการ การโอนสิทธิ การเลิกกิจการ หรือเหตุการณ์อื่นใด ในลักษณะเดียวกันนั้น บริษัทอาจมีความจำเป็นในการเปิดเผยข้อมูลส่วนบุคคลของท่านไปยัง\n  - คู่ค้า ผู้สนใจ\n  - บริษัทบริหารสินทรัพย์ และเหรือ ผู้รับโอนสิทธิดังกล่าว'),
                                  const TextSpan(
                                    text:
                                        '\n ● บุคคลที่สามอื่นใด\n   บริษัทอาจเปิดเผยข้อมูลส่วนบุคคลของท่านไปยังบุคคลที่สามอื่นใด เพื่อให้เป็นไปตามวัตถุประสงค์ที่ระบุในนโยบายฉบับนี้ บุคคลที่สามอื่นใดที่รับข้อมูลสวนบุคคลของท่าน อาจรวมถึงแต่ไม่จำกัดเพียง\n  - บุคคลที่ท่านมีสัญญาหรือความสัมพันธ์ทางธุรกรรมร่วม (เช่น บุคคลอ้างอิง ผู้ให้หลักประกัน ผู้รับผลประโยชน์)\n  - บริษัทข้อมูลเครดิต\n  - ผู้พัฒนาเทคโนโลยีโครงสร้างพื้นฐาน และเหรือ ระบบงานของบริษัท\n  - ธนาคารหรือสถาบันการเงินอื่น ๆ ที่ท่านมีธุรกรรมร่วม\n  - ผู้ออกหลักทรัพย์ นายทะเบียนหลักทรัพย์ ผู้จัดการทองทุน ผู้รับฝากหลักทรัพย์ หรือบริษัทหลักทรัพย์จัดการกองทุนอื่น ๆ ที่เกี่ยวข้องกับธุรกรรมของท่าน\n  - องค์กรกลางเพื่อการโอนเงินไปยังธนาคารต่างประเทศ\n  - สมาชิกของผู้ให้บริการยืนยันตัวตนบนโลกดิจิทัล (National Digital ID)\n  - ผู้ให้บริหารเครือข่ายบัตร (เช่น VISA, Mastercard, JCB, UPI)\n  - มหาวิทยาลัยหรือสถานศึกษา\n  - ผู้ให้บริการสื่อสังคมออนไลน์\n  - สาธารณะหรือบุคคลทั่วไป',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n5.บริษัทส่งหรือโอนข้อมูลส่วนบุคคลของท่านไปยังต่างประเทศหรือไม่',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทอาจมีความจำเป็นต้องส่งหรือโอนข้อมูลส่วนบุคคลของท่านไปยังบริษัทในเครือกิจการ/ธุรกิจเดียวกันที่อยู่ต่างประเทศ หรือไปยังผู้รับข้อมูลอื่นซึ่งเป็นส่วนหนึ่งของการดำเนินธุรกิจตามปกติของบริษัท เช่น การส่งหรือโอนข้อมูลส่วนบุคคลไปเก็บไว้บน server/cloud ในประเทศต่างๆ\nกรณีที่ประเทศปลายทางมีมาตรฐานไม่เพียงพอ บริษัทจะดูแลการส่งหรือโอนข้อมูลส่วนบุคคลให้เป็นไปตามที่กฎหมายกำหนด และจะดำเนินการให้มีมาตรการคุ้มครองข้อมูลส่วนบุคคลที่เห็นว่าจำเป็นและเหมาะสมสอดคล้องกับมาตรฐานการรักษาความลับ เช่น มีข้อตกลงรักษาความลับกับผู้รับข้อมูลในประเทศดังกล่าว หรือในกรณีที่ผู้รับข้อมูลเป็นบริษัทในเครือกิจการ/ธุรกิจเดียวกัน บริษัทอาจเลือกใช้วิธีการดำเนินการให้มีนโยบายการคุ้มครองข้อมูลส่วนบุคคลที่ได้รับการตรวจสอบและรับรองจากผู้มีอำนาจตามกฎหมายที่เกี่ยวข้องและจะดำเนินการให้การส่งหรือโอนข้อมูลส่วนบุคคลไปยังบริษัทในเครือกิจการ/ ธุรกิจเดียวกันที่อยู่ต่างประเทศเป็นไปตามนโยบายการคุ้มครองข้อมูลส่วนบุคคลดังกล่าวแทนการดำเนินการตามที่กฎหมายกำหนดไว้ก็ได้',
                                  ),
                                  TextSpan(
                                    text: '\n\n6.การใช้งานคุกกี้',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทอาจเก็บรวบรวมและใช้คุกกี้และเทคโนโลยีในลักษณะเดียวกัน เมื่อท่านใช้ผลิตภัณฑ์ และ/หรือบริการของบริษัท รวมถึงการใช้เว็บไซต์ การทำธุรกรรมทางการเงินผ่านเครือข่ายอินเทอร์เน็ต และแอปพลิเคชันของบริษัท คุกกี้ คือ ไฟล์เล็ก ๆ เพื่อจัดเก็บข้อมูลการเข้าใช้งานเว็บไซต์ เช่น วันเวลา ลิงค์ที่คลิก หน้าที่เข้าชม เงื่อนไขการตั้งค่าต่าง ๆ โดยจะบันทึกลงไปในอุปกรณ์คอมพิวเตอร์ และ/หรือ เครื่องมือสื่อสารที่เข้าใช้งานของท่าน เช่น โน๊ตบุ๊ค แท็บเล็ต หรือ สมาร์ทโฟน ผ่านทางเว็บเบราว์เซอร์ในขณะที่ท่านเข้าสู่เว็บไซต์ โดยคุกกี้จะไม่ก่อให้เกิดอันตรายต่ออุปกรณ์คอมพิวเตอร์ และ/หรือ เครื่องมือสื่อสารของลูกค้า ในกรณีดังต่อไปนี้ ข้อมูลส่วนบุคคลของลูกค้าอาจถูกจัดเก็บเพื่อใช้เพิ่มประสบการณ์การใช้งานบริการทางออนไลน์ โดยจะจำเอกลักษณ์ของภาษาและปรับแต่งข้อมูลการใช้งานตามความต้องการของลูกค้าเป็นการยืนยันคุณลักษณะเฉพาะตัว ข้อมูลความปลอดภัยของท่าน รวมถึงบริการที่ท่านสนใจ นอกจากนี้คุกกี้ยังถูกใช้เพื่อวัดปริมาณการเข้าใช้งานบริการทางออนไลน์ การปรับเปลี่ยนเนื้อหาตามการใช้งานของท่านโดยพิจารณาจากพฤติกรรมการเข้าใช้งานครั้งก่อน ๆ และ ณ ปัจจุบัน และอาจมีวัตถุประสงค์เพื่อการโฆษณาประชาสัมพันธ์\nท่านสามารถเข้าใช้และเข้าสู่เว็บไซต์ได้โดยไม่ต้องเปิดเผยข้อมูลส่วนบุคคลของท่าน การให้ข้อมูลส่วนบุคคลของท่านเป็นไปตามความสมัครใจ อย่างไรก็ตาม หากท่านไม่ให้ข้อมูลส่วนบุคคลของท่านแก่บริษัท บริษัทอาจไม่สามารถให้บริการตามที่ท่านต้องการได้ และอาจทำให้ประสบการณ์การใช้เว็บไซต์ของท่านเป็นไปอย่างไม่ต่อเนื่อง และท่านจะสูญเสียโอกาสในการรับทราบข่าวสาร โฆษณาและประชาสัมพันธ์ เกี่ยวกับผลิตภัณฑ์ และบริการที่ดีจากบริษัท',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nนอกจากนี้ บุคคลภายนอกอาจออกคุกกี้ผ่านเว็บไซต์ของเราเพื่อมอบโฆษณาที่เกี่ยวข้องกับความสนใจของท่านตามกิจกรรมการเบราวซ์ของท่าน บุคคลภายนอกเหล่านี้อาจเก็บรวบรวมประวัติการเบราวซ์ของท่านหรือข้อมูลอื่นเพื่อให้ทราบว่าท่านเข้าถึงเว็บไซต์อย่างไร และเพจที่ท่านเยี่ยมชมหลังจากที่ออกจากเว็บไซต์ของเราข้อมูลที่รวบรวมผ่านตัวกลางอัตโนมัติเหล่านี้อาจมีความเกี่ยวข้องกับข้อมูลส่วนบุคคลที่ท่านได้ให้ไว้ก่อนหน้านี้บนเว็บไซต์ของเรา',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 12.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n7.บริษัทจัดเก็บข้อมูลส่วนบุคคลของท่านไว้นานเท่าใด',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทจะเก็บรักษาข้อมูลส่วนบุคคลของท่านในระหว่างที่ท่านเป็นลูกค้า หรือมีความสัมพันธ์อยู่กับบริษัท หรือตามระยะเวลาที่จำเป็นเพื่อให้บรรลุวัตถุประสงค์ที่เกี่ยวข้องในนโยบายฉบับนี้ และเมื่อท่านสิ้นสุดความสัมพันธ์กับบริษัท บริษัทจะเก็บรักษาข้อมูลส่วนบุคคลของท่านไว้ต่อไปภายหลังจากนั้นตามระยะเวลาที่จำเป็นตามอายุความ หรือระยะเวลาที่กฎหมายกำหนดหรืออนุญาตไว้ เช่น\n   - จัดเก็บไว้ตามกฎหมายป้องกันและปราบปรามการพอกเงิน 5 - 10 ปีนับแต่ยุติความสัมพันธ์ตามแต่กรณี\n   - จัดเก็บไว้ตามกฎหมายธุรกิจสถาบันการเงิน กฎหมายหลักทรัพย์และตลาดหลักทรัพย์ กฎหมายการบัญชีกฎหมายภาษีอากร 10 ปีนับแต่ยุติความสัมพันธ์\n  ทั้งนี้ บริษัทจะมีการดำเนินการในขั้นตอนที่เหมาะสม เพื่อทำการลบหรือทำลายข้อมูลส่วนบุคคล หรือทำให้เป็นข้อมูลที่ไม่สามารถระบุถึงตัวตนของท่นได้เมื่อหมดความจำเป็นหรือสิ้นสุดระยะเวลาดังกล่าว',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n8.บริษัทคุ้มครองข้อมูลส่วนบุคคลของท่านอย่างไร',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทจะเก็บรักษาข้อมูลสวนบุคคลของท่านไว้เป็นอย่างดีตามมาตรการเชิงเทคนิค (Technical Safeguard) มาตรการเชิงบริหารจัดการ (Administrative Safeguard) และมาตรการป้องกันทางกายภาพ (Physical Safeguard) เพื่อธำรงไว้ซึ่งความลับความถูกต้องครบถ้วน สภาพความพร้อมใช้งานของข้อมูลส่วนบุคคล เพื่อป้องกันการเข้าถึง เก็บรวบรวม เปลี่ยนแปลง แก้ไข ใช้ และ/ หรือ เปิดเผยช้อมูลส่วนบุคคลโดยปราศจากอำนาจหรือโดยมิชอบ ทั้งนี้เป็นไปตามที่กฎหมายที่ใช้บังคับกำหนดบริษัทได้จัดให้มีมาตรการที่เหมาะสมเพื่อป้องกันการละเมิดข้อมูลส่วนบุคคล โดยบริษัทได้กำหนดนโยบาย ระเบียบ และหลักเกณฑ์ในการคุ้มครองข้อมูลส่วนบุคคล เช่น มาตรการควบคุมการเข้าถึงข้อมูลสวนบุคคลและการใช้งานอุปกรณ์สำหรับการจัดเก็บและประมวลผลข้อมูลส่วนบุคคลที่ปลอดภัยและเหมาะสม การจำกัดการเข้าถึงข้อมูลส่วนบุคคล การกำหนดสิทธิการเข้าถึงข้อมูลส่วนบุคคลของผู้ใช้งาน สิทธิในการอนุญาตให้พนักงานที่ได้รับมอบหมายให้เข้าถึงข้อมูล และหน้าที่ความรับผิดชอบของผู้ใช้งาน เพื่อป้องกันการเข้าถึงข้อมูลส่วนบุคคล การเปีดเผย การล่วงรู้ หรือการลักลอบทำสำเนาข้อมูลส่วนบุคคล หรือการลักขโมยอุปกรณ์จัดเก็บหรือประมวลผลข้อมูลส่วนบุคคลโดยไม่ได้รับอนุญาต โดยจัดให้มีมาตรการสำหรับการตรวจสอบย้อนหลังเกี่ยวกับการเข้าถึง เปลี่ยนแปลง ลบ หรือถ่ายโอนข้อมูลสวนบุคคลที่สอดคล้องเหมาะสมกับวิธีการและเครื่องมือที่ใช้ในการเก็บรวบรวม ใช้หรือเปิดเผยข้อมูลสวนบุคคล รวมถึงการตรวจสอบเพื่อประเมินประสิทธิผลของการปฏิบัติตามนโยบาย ระเบียบ และหลักเกณฑ์ในการคุ้มครองข้อมูลสวนบุคคลนอกจากนี้ ผู้บริหาร พนักงาน ลูกจ้าง ผู้รับจ้าง ตัวแทน ที่ปรึกษา และผู้รับข้อมูลจากบริษัทมีหน้าที่ต้องรักษาความลับข้อมูลส่วนบุคคลตามมาตรการรักษาความลับที่บริษัทกำหนดขึ้น',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n9.สิทธิของท่านเกี่ยวกับข้อมูลส่วนบุคคลมีอะไรบ้าง',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nสิทธิของท่านในข้อนี้เป็นสิทธิตามกฎหมายที่ท่านควรทราบ โดยท่านสามารถขอใช้สิทธิต่างๆ ได้ภายใต้ข้อกำหนดของกฎหมาย และนโยบายที่กำหนดไว้ในขณะนี้หรือที่จะมีการแก้ไขเพิ่มเติมในอนาคต ตลอดจนหลักเกณฑ์ตามที่บริษัทกำหนดขึ้น และในกรณีท่านมีอายุไม่ครบ 20 ปีบริบูรณ์ หรือถูกจำกัดความสามารถในการทำนิติกรรมตามกฎหมาย ท่านสามารถขอใช้สิทธิโดยให้บิดาและมารดา ผู้ใช้อำนาจปกครอง หรือมีผู้อำนาจกระทำการแทนเป็นผู้แจ้งความประสงค์\n 9.1 สิทธิขอถอนความยินยอม หากท่านได้ให้ความยินยอมให้บริษัทเก็บรวบรวม ใช้ และ/หรือเปิดเผยข้อมูลส่วนบุคคลของท่าน (ไม่ว่าจะเป็นความยินยอมที่ท่านให้ไว้ก่อนวันที่กฎหมายคุ้มครองข้อมูลส่วนบุคคลใช้บังคับหรือหลังจากนั้น) ท่านมีสิทธิที่จะถอนความยินยอมเมื่อใดก็ได้ตลอดระยะเวลาที่ข้อมูลส่วนบุคคลของท่านอยู่กับบริษัท เว้นแต่มีข้อจำกัดสิทธินั้นโดยกฎหมายหรือมีสัญญาที่ให้ประโยชน์แก่ท่านอยู่ ทั้งนี้ การถอนความยินยอมของท่านอาจส่งผลกระทบต่อท่านจากการใช้ผลิตภัณฑ์ และ/หรือบริการต่างๆ เช่น ท่านจะไม่ได้รับสิทธิประโยชน์ โปรโมชั่นหรือข้อเสนอใหม่ๆ ไม่ได้รับผลิตภัณฑ์หรือบริการที่ดียิ่งขึ้นและสอดคล้องกับความต้องการของท่าน หรือไม่ได้รับข้อมูลข่าวสารอันเป็นประโยชน์แก่ท่าน เป็นต้น เพื่อประโยชน์ของท่าน จึงควรศึกษาและสอบถามถึงผลกระทบก่อนเพิกถอนความยินยอม\n 9.2 สิทธิขอเข้าถึงข้อมูล ท่านมีสิทธิขอเข้าถึงข้อมูลส่วนบุคคลของท่านที่อยู่ในความรับผิดชอบของบริษัท และขอให้บริษัททำสำเนาข้อมูลดังกล่าวให้แก่ท่าน รวมถึงขอให้บริษัทเปิดเผยว่าบริษัทได้ข้อมูลส่วนบุคคลของท่านมาได้อย่างไร\n 9.3 สิทธิขอถ่ายโอนข้อมูล ท่านมีสิทธิขอรับข้อมูลส่วนบุคคลของท่านในกรณีที่บริษัทได้จัดทำข้อมูลส่วนบุคคลนั้นอยู่ในรูปแบบให้สามารถอ่านหรือใช้งานได้ด้วยเครื่องมือหรืออุปกรณ์ที่ทำงานได้โดยอัตโนมัติ และสามารถใช้หรือเปิดเผยข้อมูลส่วนบุคคลได้ด้วยวิธีการอัตโนมัติ รวมทั้งมีสิทธิขอให้บริษัทส่งหรือโอนข้อมูลส่วนบุคคลในรูปแบบดังกล่าวไปยังผู้ควบคุมข้อมูลส่วนบุคคลอื่นเมื่อสามารถทำได้ด้วยวิธีการอัตโนมัติ และมีสิทธิขอรับข้อมูลส่วนบุคคลที่บริษัทส่งหรือโอนข้อมูลส่วนบุคคลในรูปแบบดังกล่าวไปยังผู้ควบคุมข้อมูลส่วนบุคคลอื่นโดยตรง เว้นแต่ไม่สามารถดำเนินการได้เพราะเหตุทางเทคนิค\n ทั้งนี้ ข้อมูลส่วนบุคคลของท่านข้างต้นต้องเป็นข้อมูลส่วนบุคคลที่ท่านได้ให้ความยินยอมแก่บริษัทในการเก็บรวบรวม ใช้ และ/หรือเปิดเผย หรือเป็นข้อมูลส่วนบุคคลที่บริษัทจำเป็นต้องเก็บรวบรวม ใช้ และ/หรือเปิดเผยเพื่อให้ท่านสามารถใช้ผลิตภัณฑ์และ/หรือบริการของบริษัทได้ตามความประสงค์ซึ่งท่านเป็นคู่สัญญาอยู่กับบริษัท หรือเพื่อใช้ในการดำเนินการตามคำขอของท่านก่อนใช้ผลิตภัณฑ์ และ/หรือบริการของบริษัท หรือเป็นข้อมูลส่วนบุคคลอื่นตามที่ผู้มีอำนาจตามกฎหมายกำหนด',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n 9.4 สิทธิขอคัดค้าน\n ท่านมีสิทธิขอคัดค้านการเก็บรวบรวม ใช้ และ/หรือเปิดเผยข้อมูลส่วนบุคคลของท่านในเวลาใดก็ได้ หากการเก็บรวบรวม ใช้ และ/หรือเปิดเผยข้อมูลส่วนบุคคลของท่านที่ทำขึ้นเพื่อการดำเนินงานที่จำเป็นภายใต้ประโยชน์โดยชอบด้วยกฎหมายของบริษัท หรือของบุคคลหรือนิติบุคคลอื่น โดยไม่เกินขอบเขตที่ท่านสามารถคาดหมายได้อย่างสมเหตุสมผล หรือเพื่อดำเนินการตามภารกิจเพื่อสาธารณประโยชน์ หากท่านยื่นคัดค้าน บริษัทจะยังคงดำเนินการเก็บรวบรวม ใช้ และ/หรือเปิดเผยข้อมูลส่วนบุคคลของท่านต่อไปเฉพาะที่บริษัทสามารถแสดงเหตุผลตามกฎหมายได้ว่ามีความสำคัญยิ่งกว่าสิทธิขั้นพื้นฐานของท่าน หรือเป็นไปเพื่อการยืนยันสิทธิตามกฎหมาย การปฏิบัติตามกฎหมาย หรือการต่อสู้ในการฟ้องร้องตามกฎหมาย ตามแต่ละกรณีนอกจากนี้ ท่านยังมีสิทธิขอคัดค้านการเก็บรวบรวม ใช้ และ/หรือเปิดเผยข้อมูลส่วนบุคคลของท่านที่ทำขึ้นเพื่อวัตถุประสงค์เกี่ยวกับการตลาด หรือเพื่อวัตถุประสงค์เกี่ยวกับการศึกษาวิจัยทางวิทยาศาสตร์ ประวัติศาสตร์ หรือสถิติได้อีกด้วย\n 9.5 สิทธิขอให้ลบหรือทำลายข้อมูล\n ท่านมีสิทธิขอลบหรือทำลายข้อมูลส่วนบุคคลของท่าน หรือทำให้ข้อมูลส่วนบุคคลเป็นข้อมูลที่ไม่สามารถระบุตัวท่านได้ หากท่านเชื่อว่าข้อมูลส่วนบุคคลของท่านถูกเก็บรวบรวม ใช้ และ/หรือเปิดเผยโดยไม่ชอบด้วยกฎหมายที่เกี่ยวข้อง หรือเห็นว่าบริษัทหมดความจำเป็นในการเก็บรักษาไว้ตามวัตถุประสงค์ที่เกี่ยวข้องในนโยบายฉบับนี้ หรือเมื่อท่านได้ใช้สิทธิขอถอนความยินยอมหรือใช้สิทธิขอคัดค้านตามที่แจ้งไว้ข้างต้นแล้ว\n 9.6 สิทธิขอให้ระงับการใช้ข้อมูล\n ท่านมีสิทธิขอให้ระงับการใช้ข้อมูลส่วนบุคคลชั่วคราวในกรณีที่บริษัทอยู่ระหว่างตรวจสอบตามคำร้องขอใช้สิทธิขอแก้ไขข้อมูลส่วนบุคคลหรือขอคัดค้านของท่าน หรือกรณีอื่นใดที่บริษัทหมดความจำเป็นและต้องลบหรือทำลายข้อมูลส่วนบุคคลของท่านตามกฎหมายที่เกี่ยวข้องแต่ท่านขอให้บริษัทระงับการใช้แทน\n 9.7 สิทธิขอให้แก้ไขข้อมูล\n ท่านมีสิทธิขอแก้ไขข้อมูลส่วนบุคคลของท่านให้ถูกต้อง เป็นปัจจุบัน สมบูรณ์ และไม่ก่อให้เกิดความเข้าใจผิด\n 9.8 สิทธิร้องเรียน\n ท่านมีสิทธิร้องเรียนต่อผู้มีอำนาจตามกฎหมายที่เกี่ยวข้อง หากท่านเชื่อว่าการเก็บรวบรวม ใช้ และ/หรือเปิดเผยข้อมูลส่วนบุคคลของท่านเป็นการกระทำในลักษณะที่ฝ่าฝืนหรือไม่ปฏิบัติตามกฎหมายที่เกี่ยวข้อง\n 9.9 การใช้สิทธิของท่านดังกล่าวข้างต้นอาจถูกจำกัดภายใต้กฎหมายที่เกี่ยวข้อง\n และมีบางกรณีที่มีเหตุจำเป็นที่บริษัทอาจปฏิเสธหรือไม่สามารถดำเนินการตามคำขอใช้สิทธิข้างต้นของท่านได้ เช่น ต้องปฏิบัติตามกฎหมายหรือคำสั่งศาล เพื่อประโยชน์สาธารณะ การใช้สิทธิอาจละเมิดต่อสิทธิหรือเสรีภาพของบุคคลอื่น เป็นต้น หากบริษัทปฏิเสธคำขอข้างต้น บริษัทจะแจ้งเหตุผลของการปฏิเสธให้ท่านทราบด้วย',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n10.การใช้ข้อมูลส่วนบุคคลตามวัตถุประสงค์เดิม',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทมีสิทธิในการเก็บรวบรวมและใช้ข้อมูลส่วนบุคคลของท่านที่บริษัทได้เก็บรวบรวมไว้ก่อนวันที่ พรบ. คุ้มครองข้อมูลส่วนบุคคลในส่วนที่เกี่ยวข้องกับการเก็บรวบรวม การใช้ และการเปิดเผยข้อมูลส่วนบุคคลมีผลใช้บังคับ ต่อไปตามวัตถุประสงค์เดิม หากท่านไม่ประสงค์ที่จะให้บริษัทเก็บรวมรวมและใช้ข้อมูลส่วนบุคคลดังกล่าวต่อไป ท่านสามารถแจ้งบริษัทเพื่อขอถอนความยินยอมของท่านเมื่อใดก็ได้',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n11.ท่านจะติดต่อบริษัท และเจ้าหน้าที่คุ้มครองข้อมูลส่วนบุคคลได้อย่างไร',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nหากท่านมีความประสงค์ที่จะใช้สิทธิของท่านที่เกี่ยวข้องกับข้อมูลส่วนบุคคลของท่าน หรือหากท่านมีข้อสงสัยเกี่ยวกับข้อมูลส่วนบุคคลของท่านภายใต้นโยบายความเป็นส่วนตัวฉบับนี้ โปรดติดต่อเราหรือเจ้าหน้าที่คุ้มครองข้อมูลส่วนบุคคลของเราที่\n  เจ้าหน้าที่คุ้มครองข้อมูลส่วนบุคคล : (Data Protection Officer)\n  ฝ่ายการตลาดและบริหารลูกค้า AAM\n  50/11 ม.2 ท่าช้าง เมืองจันทบุรี จันทบุรี 22000\n  อีเมล <EMAIL>\n  หมายเลขโทรศัพท์ 0819455508',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\n12.การเปลี่ยนแปลงประกาศนโยบายการคุ้มครองข้อมูลส่วนบุคคล',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nบริษัทอาจเปลี่ยนแปลงหรือแก้ไขเพิ่มเติมประกาศนโยบายการคุ้มครองข้อมูลส่วนบุคคลนี้เป็นครั้งคราว โดยบริษัทจะแจ้งประกาศนโยบายการคุ้มครองข้อมูลส่วนบุคคลฉบับปัจจุบันไว้ที่เว็บไซต์ของบริษัท https://www.aamfinancegroup.com/',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  static buildPrivacyPolicyRAFCO(context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.string(
                                    AppSvgImage.icon_privacy_policy,
                                  color: AppColors.primaryRafco
                                ),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    "គោលការណ៍ឯកជនភាពសម្រាប់អតិថិជន",
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      // height: 0.10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  const TextSpan(
                                    text:
                                        "RPTN Alliance Financial Leasing PLC ទទួលយកការការពារព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកយ៉ាងយកចិត្តទុកដាក់ គោលការណ៍ឯកជនភាពនេះពន្យល់ពីគោលការណ៍ណែនាំសម្រាប់ការប្រមូល ការប្រើប្រាស់ ឬការបង្ហាញព័ត៌មានផ្ទាល់ខ្លួន។ រួមទាំងសិទ្ធិផ្សេងៗរបស់ម្ចាស់ទិន្នន័យផ្ទាល់ខ្លួន យោងតាមច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន\n",
                                  ),
                                  TextSpan(
                                    text: '\nការប្រមូលព័ត៌មានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        "\n\nបយើងប្រមូលព័ត៌មានផ្ទាល់ខ្លួនដែលទទួលបានដោយផ្ទាល់ពីអ្នកតាមរយៈបណ្តាញខាងក្រោម។",
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\n● ការចុះឈ្មោះជាសមាជិក\n● លេខទូរស័ព្ទ\n● អ៊ីមែល\n● ចូល Facebook\n● ចូល Google\n● ចូល LINE\n● Telegram Web\nយើងអាចប្រមូលព័ត៌មានផ្ទាល់ខ្លួនអំពីអ្នក ដែលយើងទទួលបានពីប្រភពផ្សេងទៀត ដូចជាម៉ាស៊ីនស្វែងរក ប្រព័ន្ធផ្សព្វផ្សាយសង្គមជាដើម ទីភ្នាក់ងាររដ្ឋាភិបាល អ្នកក្រៅប្រទេសផ្សេងទៀត។ល។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nប្រភេទនៃព័ត៌មានផ្ទាល់ខ្លួនដែលប្រមូលបាន។',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nព័ត៌មានផ្ទាល់ខ្លួនដូចជា នាមខ្លួន នាមត្រកូល អាយុ ថ្ងៃខែឆ្នាំកំណើត សញ្ជាតិ លេខអត្តសញ្ញាណជាតិ លិខិតឆ្លងដែនជាដើម។\nព័ត៌មានទំនាក់ទំនងដូចជា អាស័យដ្ឋាន លេខទូរស័ព្ទ អ៊ីមែល។ល។\n"ព័ត៌មានគណនីដូចជាគណនីអ្នកប្រើប្រាស់ ប្រវត្តិប្រើប្រាស់។ល។\nភស្តុតាងនៃអត្តសញ្ញាណ ដូចជាច្បាប់ចម្លងអត្តសញ្ញាណប័ណ្ណ  ច្បាប់ចម្លងលិខិតឆ្លងដែន។ល។',
                                  ),
                                  const TextSpan(
                                      text:
                                          '\nព័ត៌មានអំពីប្រតិបត្តិការ និងហិរញ្ញវត្ថុ ដូចជាប្រវត្តិការបញ្ជាទិញ ព័ត៌មានលម្អិតអំពីប័ណ្ណឥណទាន ព័ត៌មានលម្អិតអំពីគណនីធនាគារ។ល។\nព័ត៌មានបច្ចេកទេសដូចជាអាសយដ្ឋាន IP លេខសម្គាល់ខូគី ID  ប្រវត្តិប្រើប្រាស់គេហទំព័រ (កំណត់ហេតុសកម្មភាព) ។ល។\nព័ត៌មានផ្សេងទៀតដូចជារូបភាព ចលនា និងព័ត៌មានផ្សេងទៀតដែលត្រូវបានចាត់ទុកថាជាព័ត៌មានផ្ទាល់ខ្លួន យោងទៅតាមច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន។\nយើងនឹងប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនដ៏រសើបដូចខាងក្រោម នៅពេលដែលយើងមានការយល់ព្រមច្បាស់លាស់របស់អ្នក។ លុះត្រាតែច្បាប់តម្រូវឱ្យធ្វើដូច្នេះ'),
                                  const TextSpan(
                                      text:
                                          '\n● ជាតិសាសន\n● សាសនា ឬទស្សនវិជ្ជា។\n● ប្រវត្តិឧក្រិដ្ឋកម្ម\n● ទិន្នន័យជីវសាស្រ្ត ដូចជាទិន្នន័យរូបភាពមុខដែលបានក្លែងធ្វើ។ ទិន្នន័យក្លែងធ្វើ Iris ទិន្នន័យក្លែងធ្វើស្នាមម្រាមដៃ\nព័ត៌មានផ្សេងទៀតដែលប៉ះពាល់ដល់ព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក ដូចដែលបានបញ្ជាក់ដោយក្រុមប្រឹក្សាការពារទិន្នន័យផ្ទាល់ខ្លួន។'),
                                  TextSpan(
                                    text: '\n\nអនីតិជន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nប្រសិនបើអ្នកមានអាយុក្រោម 20 ឆ្នាំ ឬមានសមត្ថភាពផ្នែកច្បាប់មានកំណត់។ យើងអាចប្រមូល ប្រើ ឬបញ្ចេញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។ យើងអាចទាមទារការយល់ព្រមពីឪពុកម្តាយ ឬអាណាព្យាបាលរបស់អ្នក ឬដែលច្បាប់អនុញ្ញាតឱ្យធ្វើបាន ប្រសិនបើយើងដឹងថាព័ត៌មានផ្ទាល់ខ្លួនត្រូវបានប្រមូលពីអនីតិជនដោយមិនមានការយល់ព្រមពីឪពុកម្តាយ ឬអាណាព្យាបាល។ យើងនឹងបន្តលុបព័ត៌មានទាំងនោះចេញពីប្រព័ន្ធទន្ន័យរបស់យើង។',
                                  ),
                                  TextSpan(
                                    text: '\n\nបៀបរក្សាទុកព័ត៌មានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយើងនឹងរក្សាព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកជាទម្រង់ក្រដាស និងអេឡិចត្រូនិក។\nយើងរក្សាទុកព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកដូចខាងក្រោម។\n● ប្រព័ន្ធទន្ន័យរបស់ក្រុមហ៊ុនរបស់យើងនៅក្នុងប្រទេសកម្ពុជា។\n● ក្រុមហ៊ុនរបស់យើងបម្រើនៅក្រៅប្រទេស។\n● ក្រុមហ៊ុនរបស់យើងបម្រើនៅក្រៅប្រទេស។\n● អ្នកផ្តល់សេវាប្រព័ន្ធទន្ន័យនៅបរទេស។',
                                  ),
                                  TextSpan(
                                    text: '\n\nការដំណើរការទិន្នន័យផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយើងនឹងប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកសម្រាប់គោលបំណងដូចខាងក្រោម។\n● ដើម្បីបង្កើត និងគ្រប់គ្រងគណនីអ្នកប្រើប្រាស់។\n● ដើម្បីចែកចាយទំនិញ ឬសេវាកម្ម។\n● ដើម្បីកែលម្អផលិតផល សេវាកម្ម ឬបទពិសោធន៍អ្នកប្រើប្រាស់។\n● សម្រាប់ការគ្រប់គ្រងផ្ទៃក្នុងរបស់ក្រុមហ៊ុន។\n● សម្រាប់គោលបំណងទីផ្សារ និងផ្សព្វផ្សាយ។',
                                  ),
                                  const TextSpan(
                                      text:
                                          '\n● សម្រាប់សេវាកម្មក្រោយពេលលក់។\n● ដើម្បីប្រមូលមតិកែលម្អ។\n● បង់ថ្លៃទំនិញ ឬសេវាកម្ម។\n● ដើម្បីអនុលោមតាមល័ក្ខខ័ណ្ឌនិងលក្ខខណ្ឌ (លក្ខខណ្ឌ) ។\n● អនុវត្តតាមច្បាប់ និងបទប្បញ្ញត្តិរបស់ភ្នាក់ងាររដ្ឋាភិបាល។'),
                                  TextSpan(
                                    text: '\n\nការបង្ហាញព័ត៌មានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយើងអាចបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកទៅកាន់អ្នកដ៏ទៃដោយមានការយល់ព្រមរបស់អ្នក ឬកន្លែងដែលច្បាប់អនុញ្ញាត ដូចខាងក្រោម៖\nយើងអាចបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកទៅកាន់អ្នកដ៏ទៃដោយមានការយល់ព្រមរបស់អ្នក ឬកន្លែងដែលច្បាប់អនុញ្ញាត ដូចខាងក្រោម៖ យើងអាចបញ្ចេញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកនៅក្នុងក្រុមហ៊ុនតាមការចាំបាច់ ដើម្បីកែលម្អ និងអភិវឌ្ឍផលិតផល ឬសេវាកម្មរបស់យើង។ យើងអាចប្រមូលព័ត៌មានផ្ទៃក្នុងសម្រាប់ផលិតផល ឬសេវាកម្មផ្សេងៗនៅក្រោមគោលការណ៍នេះ ដើម្បីផ្តល់អត្ថប្រយោជន៍កាន់តែប្រសើរឡើងដល់អ្នក និងអ្នកដទៃ។ ',
                                  ),
                                  TextSpan(
                                    text: '\n\nអ្នកផ្តល់សេវា',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយយយើងអាចបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកមួយចំនួនទៅកាន់អ្នកផ្តល់សេវារបស់យើងតាមការចាំបាច់ ដើម្បីអនុវត្តការងារដូចជាការបង់ប្រាក់ ទីផ្សារ ការអភិវឌ្ឍន៍ផលិតផល ឬសេវាកម្មជាដើម។ ទោះយ៉ាងណាក៏ដោយ អ្នកផ្តល់សេវាមានគោលការណ៍ឯកជនភាពផ្ទាល់ខ្លួនរបស់ពួកគេ។ ',
                                  ),
                                  TextSpan(
                                    text: '\n\nដៃគូអាជីវកម្ម',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយើងអាចចែករំលែកព័ត៌មានមួយចំនួនជាមួយដៃគូអាជីវកម្មរបស់យើង ដើម្បីទាក់ទង និងសម្របសម្រួលការផ្តល់ផលិតផល ឬសេវាកម្ម។ និងផ្តល់ព័ត៌មានតាមការចាំបាច់ទាក់ទងនឹងភាពអាចរកបាននៃផលិតផល ឬសេវាកម្ម។',
                                  ),
                                  TextSpan(
                                    text: '\n\nការផ្ទេរអាជីវកម្ម',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយើងអាចបញ្ចេញព័ត៌មាន រួមទាំងព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។ សម្រាប់ការរៀបចំរចនាសម្ព័ន្ធឡើងវិញ ការរួមបញ្ចូលគ្នាឬការដកខ្លួនចេញ ឬការផ្ទេរទ្រព្យសម្បត្តិផ្សេងទៀត។ ភាគីទទួលត្រូវតែចាត់ចែងព័ត៌មានរបស់អ្នកក្នុងលក្ខណៈមួយដែលស្របតាមគោលការណ៍នេះ រួមទាំងច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន។ ',
                                  ),
                                  TextSpan(
                                    text: '\n\nការអនុវត្តន៏ច្បាប់',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nក្នុងករណីមានច្បាប់ ឬទីភ្នាក់ងាររដ្ឋាភិបាលស្នើសុំ យើងនឹងបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកតាមការចាំបាច់ដល់ភ្នាក់ងាររដ្ឋាភិបាល ដូចជាតុលាការ ទីភ្នាក់ងាររដ្ឋាភិបាលជាដើម។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nការផ្ទេរព័ត៌មានផ្ទាល់ខ្លួនទៅក្រៅប្រទេស',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nយើងអាចបញ្ចេញ ឬផ្ទេរព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកទៅកាន់បុគ្គល អង្គការ ឬប្រព័ន្ធទិន្ន័យមេ(Server)ដែលមានទីតាំងនៅក្រៅប្រទេស យើងនឹងអនុវត្តវិធានការផ្សេងៗ។ ដើម្បីធានាថាការផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នកទៅប្រទេសគោលដៅមានស្តង់ដារគ្រប់គ្រាន់នៃការការពារទិន្នន័យផ្ទាល់ខ្លួន ឬករណីផ្សេងទៀតតាមតម្រូវការដោយច្បាប់។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nសិទ្ធិរបស់ម្ចាស់ទិន្នន័យផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nនៅក្រោមច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន អ្នកមានសិទ្ធិធ្វើដូចខាងក្រោម៖\nនសិទ្ធិដកការយល់ព្រម (right to withdraw consent)ប្រសិនបើអ្នកបានផ្តល់ការយល់ព្រមរបស់អ្នក។ យើងនឹងប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។ មិនថាការយល់ព្រមរបស់អ្នកត្រូវបានផ្តល់ឱ្យមុនកាលបរិច្ឆេទមានប្រសិទ្ធភាពនៃច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន ឬបន្ទាប់ពី។ អ្នកមានសិទ្ធិដកការយល់ព្រមរបស់អ្នកនៅពេលណាក៏បាន។ \nសិទ្ធិចូលប្រើ(right to access)  អ្នកមានសិទ្ធិស្នើសុំការចូលប្រើព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក ដែលស្ថិតនៅក្រោមការទទួលខុសត្រូវរបស់យើង និងស្នើសុំឱ្យយើងធ្វើច្បាប់ចម្លងព័ត៌មាននោះសម្រាប់អ្នក រួមទាំងការស្នើសុំឱ្យយើងបង្ហាញពីរបៀបដែលយើងទទួលបានព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nសិទ្ធិក្នុងការចល័តទិន្នន័យ៖ អ្នកមានសិទ្ធិស្នើសុំទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក ក្នុងករណីដែលយើងបានធ្វើឱ្យវាមានជាទម្រង់ដែលអាចអាន ឬប្រើដោយឧបករណ៍ ឬឧបករណ៍ដែលអាចដំណើរការដោយស្វ័យប្រវត្តិ។ និងអាចប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនដោយមធ្យោបាយស្វ័យប្រវត្តិ រួមទាំងសិទ្ធិក្នុងការស្នើសុំឱ្យយើងផ្ញើ ឬផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនក្នុងទម្រង់បែបនោះទៅឧបករណ៍បញ្ជាទិន្នន័យផ្សេងទៀត នៅពេលដែលវាអាចត្រូវបានធ្វើដោយមធ្យោបាយស្វ័យប្រវត្តិ។ និងមានសិទ្ធិស្នើសុំទិន្នន័យផ្ទាល់ខ្លួនដែលយើងផ្ញើ ឬផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនក្នុងទម្រង់បែបនេះដោយផ្ទាល់ទៅឧបករណ៍បញ្ជាទិន្នន័យផ្សេងទៀត។ លុះត្រាតែមិនអាចដំណើរការបានដោយសារហេតុផលបច្ចេកទេស។\nសិទ្ធិជំទាស់ អ្នកមានសិទ្ធិជំទាស់ចំពោះការប្រមូល ប្រើប្រាស់ ឬការបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកគ្រប់ពេល។ ប្រសិនបើការប្រមូល ការប្រើប្រាស់ ឬការបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកគឺសម្រាប់ប្រតិបត្តិការចាំបាច់។ នៅក្រោមផលប្រយោជន៍ស្របច្បាប់របស់យើង ឬរបស់បុគ្គលផ្សេងទៀត ឬនីតិបុគ្គល ក្នុងកម្រិតដែលអ្នកអាចរំពឹងដោយសមហេតុផល ឬដើម្បីបំពេញបេសកកម្មរបស់យើងជាប្រយោជន៍សាធារណៈ។ \nសិទ្ធិក្នុងការលុប/បំផ្លិចបំផ្លាញ អ្នកមានសិទ្ធិស្នើសុំការលុប/បំផ្លាញទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក ឬធ្វើឱ្យទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នកមិនអាចកំណត់អត្តសញ្ញាណបាន។ ប្រសិនបើអ្នកជឿថាព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកត្រូវបានប្រមូល ប្រើប្រាស់ ឬបង្ហាញដោយបំពានច្បាប់ដែលពាក់ព័ន្ធ ឬថាយើងលែងត្រូវការរក្សាទុកវាសម្រាប់គោលបំណងពាក់ព័ន្ធនឹងគោលការណ៍នេះទៀតហើយ។ ឬនៅពេលដែលអ្នកបានប្រើសិទ្ធិដកការយល់ព្រម ឬប្រើសិទ្ធិជំទាស់ដូចមានចែងខាងលើ។',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nសិទ្ធិក្នុងការរឹតបន្តឹងដំណើរការ អ្នកមានសិទ្ធិស្នើសុំការផ្អាកជាបណ្តោះអាសន្ននៃការប្រើប្រាស់ទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក ក្នុងករណីដែលយើងកំពុងស៊ើបអង្កេតសំណើរបស់អ្នកសម្រាប់ការកែតម្រូវទិន្នន័យផ្ទាល់ខ្លួន ឬការជំទាស់របស់អ្នក។ ឬករណីផ្សេងទៀតដែលយើងលែងត្រូវការ ហើយត្រូវតែលុប ឬបំផ្លាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក ស្របតាមច្បាប់ដែលពាក់ព័ន្ធ ប៉ុន្តែអ្នកស្នើសុំឱ្យយើងផ្អាកការប្រើប្រាស់ជំនួសវិញ។ \nសិទ្ធិក្នុងការរឹតបន្តឹងដំណើរការ អ្នកមានសិទ្ធិស្នើសុំការផ្អាកជាបណ្តោះអាសន្ននៃការប្រើប្រាស់ទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក ក្នុងករណីដែលយើងកំពុងស៊ើបអង្កេតសំណើរបស់អ្នកសម្រាប់ការកែតម្រូវទិន្នន័យផ្ទាល់ខ្លួន ឬការជំទាស់របស់អ្នក។ ឬករណីផ្សេងទៀតដែលយើងលែងត្រូវការ ហើយត្រូវតែលុប ឬបំផ្លាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក ស្របតាមច្បាប់ដែលពាក់ព័ន្ធ ប៉ុន្តែអ្នកស្នើសុំឱ្យយើងផ្អាកការប្រើប្រាស់ជំនួសវិញ។ \nសិទ្ធិដាក់ពាក្យបណ្តឹង អ្នកមានសិទ្ធិដាក់ពាក្យបណ្តឹងជាមួយអាជ្ញាធរផ្លូវច្បាប់ដែលពាក់ព័ន្ធ។ ប្រសិនបើអ្នកជឿថាការប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។ វាជាទង្វើមួយដែលបំពាន ឬមិនគោរពតាមច្បាប់ដែលពាក់ព័ន្ធ។',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nអ្នកអាចអនុវត្តសិទ្ធិរបស់អ្នកក្នុងនាមជាម្ចាស់ទិន្នន័យផ្ទាល់ខ្លួនខាងលើ។ ដោយទាក់ទងមន្ត្រីការពារទិន្នន័យផ្ទាល់ខ្លួនរបស់យើង យោងទៅតាមព័ត៌មានលម្អិតនៅចុងបញ្ចប់នៃគោលការណ៍នេះ។ \nយើងនឹងជូនដំណឹងដល់អ្នកអំពីលទ្ធផលក្នុងរយៈពេល 30 ថ្ងៃចាប់ពីថ្ងៃដែលយើងទទួលបានសំណើរបស់អ្នក ដើម្បីអនុវត្តសិទ្ធិរបស់អ្នក។ តាមទម្រង់ ឬវិធីសាស្រ្តដែលយើងបញ្ជាក់ ទោះយ៉ាងណាក៏ដោយ ប្រសិនបើយើងបដិសេធសំណើនេះ យើងនឹងប្រាប់អ្នកអំពីហេតុផលសម្រាប់ការបដិសេធតាមរយៈបណ្តាញផ្សេងៗដូចជា សារជាអក្សរ (SMS) អ៊ីមែល ទូរស័ព្ទ សំបុត្រជាដើម។',
                                  ),
                                  TextSpan(
                                    text: '\n\nការផ្សាយពាណិជ្ជកម្ម និងទីផ្សារ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nសម្រាប់អត្ថប្រយោជន៍នៃការទទួលបានផលិតផល ឬសេវាកម្មរបស់យើង។ យើងប្រើប្រាស់ព័ត៌មានរបស់អ្នកដើម្បីវិភាគ និងកែលម្អផលិតផល ឬសេវាកម្មរបស់យើង។ និងទីផ្សារតាមរយៈ Google, Facebook, កូដតាមដានភីកសែល និងផ្សេងៗទៀត។ យើងប្រើប្រាស់ព័ត៌មាននោះដើម្បីកែសម្រួលផលិតផល ឬសេវាកម្មសម្រាប់អ្នក។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nបច្ចេកវិទ្យាតាមដានផ្ទាល់ខ្លួន (Cookies)',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nដើម្បីធ្វើឱ្យបទពិសោធន៍អ្នកប្រើប្រាស់របស់អ្នកកាន់តែពេញលេញ និងមានប្រសិទ្ធភាព យើងប្រើខូឃី (Cookies)ឬបច្ចេកវិទ្យាស្រដៀងគ្នា។ ដើម្បីអភិវឌ្ឍលទ្ធភាពទទួលបានផលិតផល ឬសេវាកម្ម ការផ្សាយពាណិជ្ជកម្មសមរម្យ និងតាមដានការប្រើប្រាស់របស់អ្នក។ យើងប្រើខូឃីដើម្បីកំណត់អត្តសញ្ញាណ និងតាមដានអ្នកប្រើប្រាស់គេហទំព័រ និងការចូលប្រើប្រាស់គេហទំព័ររបស់យើង។ ប្រសិនបើអ្នកមិនចង់ឱ្យខូគីត្រូវបានដាក់នៅលើកុំព្យូទ័ររបស់អ្នក។ អ្នកអាចកំណត់កម្មវិធីរុករករបស់អ្នកឱ្យបដិសេធខូឃី មុនពេលប្រើប្រាស់គេហទំព័ររបស់យើង។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nរក្សាសុវត្ថិភាពព័ត៌មានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nដយើងនឹងរក្សាសុវត្ថិភាពនៃព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកស្របតាមគោលការណ៍។ ការសម្ងាត់(confidentiality)  ភាពត្រឹមត្រូវ ភាពពេញលេញ (integrity) និងលក្ខខណ្ឌរួចរាល់ក្នុងការប្រើប្រាស់(availability) នេះគឺដើម្បីការពារការបាត់បង់ ការចូលប្រើ ការផ្លាស់ប្តូរ ការកែប្រែ ឬការបង្ហាញ។ លើសពីនេះ យើងនឹងផ្តល់វិធានការដើម្បីរក្សាសុវត្ថិភាពនៃព័ត៌មានផ្ទាល់ខ្លួន។ ដែលគ្របដណ្តប់វិធានការបង្ការរដ្ឋបាល (administrative safeguard) ការការពារបច្ចេកទេស (technical safeguard) និងវិធានការការពាររាងកាយទាក់ទង(physical safeguard)នឹងការចូលប្រើ ឬការគ្រប់គ្រងការប្រើប្រាស់ព័ត៌មានផ្ទាល់ខ្លួន (access control)។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nការជូនដំណឹងអំពីការបំពានទិន្នន័យផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nក្នុងករណីមានការរំលោភលើព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។ យើងនឹងជូនដំណឹងទៅការិយាល័យនៃគណៈកម្មការការពារទិន្នន័យផ្ទាល់ខ្លួនដោយមិនពន្យារពេលក្នុងរយៈពេល 72 ម៉ោង។ ដោយដឹងពីមូលហេតុតាមដែលអាចធ្វើទៅបាន។ កន្លែងដែលការបំពានមានហានិភ័យខ្ពស់ក្នុងការប៉ះពាល់ដល់សិទ្ធិ និងសេរីភាពរបស់អ្នក។ យើងនឹងជូនដំណឹងអ្នកអំពីការរំលោភបំពាន និងផ្តល់ការដោះស្រាយដោយមិនពន្យារពេលតាមរយៈបណ្តាញផ្សេងៗ។ ដូចជា គេហទំព័រ សារជាអក្សរ (SMS) អ៊ីមែល ការហៅទូរសព្ទ សំបុត្រជាដើម។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nការផ្លាស់ប្តូរចំពោះគោលការណ៍ឯកជនភាព',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nកយើងអាចផ្លាស់ប្តូរគោលការណ៍នេះពីពេលមួយទៅពេលមួយ។ អ្នកអាចដឹងពីលក្ខខណ្ឌដែលបានកែសម្រួលនៃគោលការណ៍នេះតាមរយៈគេហទំព័ររបស់យើង។ \nគោលការណ៍នេះត្រូវបានកែសម្រួលចុងក្រោយ និងមានប្រសិទ្ធភាពត្រឹមថ្ងៃទី 31 ខែសីហា ឆ្នាំ 2022។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nគោលការណ៍ឯកជនភាពនៃគេហទំព័រផ្សេងទៀត។',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nគគោលការណ៍ឯកជនភាពនេះអនុវត្តតែចំពោះការផ្តល់ផលិតផល សេវាកម្ម និងការប្រើប្រាស់គេហទំព័រដល់អតិថិជនរបស់យើង។ ប្រសិនបើអ្នកចូលមើលគេហទំព័រផ្សេងទៀតសូម្បីតែតាមរយៈគេហទំព័ររបស់យើង។ ការការពារព័ត៌មានផ្ទាល់ខ្លួននឹងអនុលោមតាមគោលការណ៍ឯកជនភាពនៃគេហទំព័រនោះ ដែលយើងមិនពាក់ព័ន្ធជាមួយ',
                                  ),
                                  TextSpan(
                                    text: '\n\nព័ត៌មានលម្អិតទំនាក់ទំនង',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nប្រសិនបើអ្នកចង់សាកសួរអំពីគោលការណ៍ឯកជនភាពនេះ រួមទាំងការស្នើសុំប្រើប្រាស់សិទ្ធិផ្សេងៗ អ្នកអាចទាក់ទងមកយើងខ្ញុំ ឬមន្ត្រីការពារទិន្នន័យផ្ទាល់ខ្លួនរបស់យើងដូចខាងក្រោម។',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nអ្នកគ្រប់គ្រងទិន្នន័យផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nRPTN Alliance Financial Leasing PLC\nអ៊ីមែល <EMAIL>\nគេហទំព័រ https://rafcocambodia.com/\nគលេខទូរស័ព្ទ (+855) 010 225 044',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nអ្នកគ្រប់គ្រងការពារទិន្នន័យផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nផ្នែកទីផ្សារ RAFCO\nអ៊ីមែល <EMAIL>\nអ៊ីមែល <EMAIL>\nលេខទូរស័ព្ទ (+855) 081 793 502',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  static buildPrivacyPolicyRPLC(context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.string(
                                    AppSvgImage.icon_privacy_policy,
                                    color: AppColors.primaryRPLC_Yellow
                                ),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    "ນະໂຍບາຍຄວາມເປັນສ່ວນຕົວ",
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      // height: 0.10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text:
                                        "ນະໂຍບາຍຄວາມເປັນສ່ວນຕົວສຳຫຼັບລູກຄ້າ\n",
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        '\nບໍລິສັດຮ່ວມພັດທະນາເຊົ່າສິນເຊື່ອ ຈຳກັດ ໃຫ້ຄວາມສຳຄັນກັບການຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ໂດຍນະໂຍບາຍຄວາມເປັນສ່ວນຕົວສະບັບນີ້ໄດ້ອະທິບາຍແນວປະຕິບັດກ່ຽວກັບການເກັບລວບລວມ ໃຊ້ ຫຼື ເປີດເຜີຍຂໍ້ມູນສ່ວນບຸກຄົນລວມເຖີງສິດຕ່າງໆ ຂອງເຈົ້າຂອງຂໍ້ມູນຕາມກົດໝາຍຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວ',
                                  ),
                                  TextSpan(
                                    text: '\n\nການເກັບກຳຂໍ້ມູນສ່ວນບູກຄົນ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n່● ການສະໝັກສະມາຊີກ\n● ໂທລະສັບ\n● ອີເມວ\n● Facebook Login\n● Google Login\n● LINE Login\nເຮົາອາດເກັບກຳຂໍ້ມູນສ່ວນຕົວຂອງທ່ານທີ່ເຮົາເຂົ້າເຖີງໄດ້ຈາກແຫຼ່ງອື່ນ ເຊົ່ນ ເສີດເອັນຈິ້ນ ໂຊຊ່ຽວມີເດຍ ໜ່ວຍງານລັດ ບຸກຄົນພາຍນອກອື່ນໆ ເປັນຕົ້ນ ',
                                  ),
                                  TextSpan(
                                    text: '\n\nປະເພດຂໍ້ມູນສ່ວນບູກຄົນທີ່ເກັບກຳ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nຂໍ້ມູນສ່ວນຕົວ ເຊັ່ນ ຊື່ ນາມສະກຸນ ອາຍຸ ວັນເດືອນປີເກິດ ສັນຊາດ ເລກປະຈຳຕົວ ໜັງສືເດິນທາງ ເປັນຕົ້ນ ເຊັ່ນ ຊື່ ນາມສະກຸນ ອາຍຸ ວັນເດືອນປີເກິດ ສັນຊາດ ເລກປະຈຳຕົວ ໜັງສືເດິນທາງ ເປັນຕົ້ນ\nເຊັ່ນ ຊື່ ນາມສະກຸນ ອາຍຸ ວັນເດືອນປີເກິດ ສັນຊາດ ເລກປະຈຳຕົວ ໜັງສືເດິນທາງ ເປັນຕົ້ນ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nຂໍ້ມູນການຕິດຕໍ່ :ເຊັ່ນ ທີ່ຢູ່ ,ເບີໂທລະສັບ, ທີ່ຢູ່ອີເມລ, ແລະອື່ນໆ.\nຂໍ້ມູນບັນຊີ ເຊັ່ນ ບັນຊີຜູ້ໃຊ້ງານ ປະຫວັດການນໍາໃຊ້ງານ, ເປັນຕົ້ນ.\nເຊັ່ນ ບັນຊີຜູ້ໃຊ້ງານ ປະຫວັດການນໍາໃຊ້ງານ, ເປັນຕົ້ນ.\nຫຼັກຖານສະແດງຕົວຕົນ ເຊັ່ນ ສໍາເນົາບັດປະຈໍາຕົວ ສຳເນົາໜັງສືຜ່ານແດນ ເປັນຕົ້ນ\nເຊັ່ນ ສໍາເນົາບັດປະຈໍາຕົວ ສຳເນົາໜັງສືຜ່ານແດນ ເປັນຕົ້ນ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nຂໍ້ມູນທຸລະກໍາແລະການເງິນ ເຊັ່ນ ປະຫວັດການສັ່ງຊື້ ລາຍລະອຽດບັດເຄຣດິດ ບັນຊີທະນາຄານ ເປັນຕົ້ນ\nເຊັ່ນ ປະຫວັດການສັ່ງຊື້ ລາຍລະອຽດບັດເຄຣດິດ ບັນຊີທະນາຄານ ເປັນຕົ້ນ\nຂໍ້ມູນທາງເຕັກນິກ ເຊັ່ນ  ທີ່ຢູ່ IP, ID Cookie, ປະຫວັດການນໍາໃຊ້ເວັບໄຊທ໌ (ບັນທຶກກິດຈະກໍາ) ເປັນຕົ້ນ\nຂໍ້ມູນອື່ນໆ ເຊັ່ນ ຮູບພາບ, ພາບເຄື່ອນໄຫວ ແລະຂໍ້ມູນອື່ນໆ ທີ່ຖືວ່າເປັນຂໍ້ມູນສ່ວນຕົວຕາມກົດໝາຍຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວກຳໜດໄວ້\nເຊັ່ນ ຮູບພາບ, ພາບເຄື່ອນໄຫວ ແລະຂໍ້ມູນອື່ນໆ ທີ່ຖືວ່າເປັນຂໍ້ມູນສ່ວນຕົວຕາມກົດໝາຍຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວກຳໜດໄວ້\nພວກເຮົາຈະເກັບກຳ, ໃຊ້ ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວທີ່ລະອຽດອ່ອນຕໍ່ໄປນີ້ ເມື່ອພວກເຮົາໄດ້ຮັບການຍິນຍອມຢ່າງຈະແຈ້ງຈາກທ່ານ ເວັ້ນເສຍແຕ່ໄດ້ກໍານົດໄວ້ໃນກົດຫມາຍ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n● ສັນຊາດ\n● ສາດສະຫນາ ຫຼືປັດຊະຍາ\n● ປະຫວັດອາຊະຍາກໍາ\n● ຂໍ້ມູນທາງຊີວະພາບ ເຊັ່ນ ຂໍ້ມູນຮູບໃບໜ້າ. ຂໍ້ມູນຈໍາລອງມ່ານຕາ ຂໍ້ມູນຮູບແບບລາຍນິ້ວມື\nຂໍ້ມູນອື່ນໆ ທີ່ມີຜົນກະທົບຕໍ່ຂໍ້ມູນສ່ວນຕົວຂອງທ່ານຕາມທີ່ຄະນະກໍາມະການຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວປະກາດກຳໜດໄວ້',
                                  ),
                                  TextSpan(
                                    text: '\n\nເຍົາວະຊົນ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nຖ້າຫາກເຈົ້າມີອາຍຸຕ່ຳກວ່າ 20 ປີ ຫຼືມີຂໍ້ຈຳກັດທາງກົດໝາຍ. ພວກເຮົາອາດຈະເກັບກໍາ, ນໍາໃຊ້ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ.ພວກເຮົາອາດຈຳເປັນຕ້ອງໃຫ້ພໍ່ແມ່ ຫຼືຜູ້ປົກຄອງຂອງທ່ານໃຫ້ຄວາມຍິນຍອມ ຫຼື ໃຫ້ກົດໝາຍອະນຸຍາດໃຫ້ເຮັດໄດ້. ຫາກພວກເຮົາຮັບຮູ້ວ່າມີການເກັບກຳຂໍ້ມູນສ່ວນຕົວຈາກຜູ້ເຍົາໂດຍບໍ່ໄດ້ຮັບຄວາມຍິນຍອມຈາກພໍ່ແມ່ຫຼືຜູ້ປົກຄອງ ພວກເຮົາຈະລຶບຂໍ້ມູນນັ້ນອອກຈາກເຊີບເວີຂອງພວກເຮົາ',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nວິທີການເກັບຮັກສາຂໍ້ມູນສ່ວນບຸກຄົນ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nພວກເຮົາຈະເກັບຮັກສາຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໃນຮູບແບບເອກກະສານ ແລະຮູບແບບເອເລັກໂຕຣນິກ.\nພວກເຮົາເກັບຮັກສາຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ດັ່ງຕໍ່ໄປນີ້: ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n● ເຊີບເວີຂອງບໍລິສັດຂອງພວກເຮົາໃນປະເທດໄທ.\n● ເຊີບເວີຂອງບໍລິສັດຂອງພວກເຮົາໃນຕ່າງປະເທດ.\n● ຜູ້ໃຫ້ບໍລິການເຊີບເວີໃນປະເທດໄທ\n● ຜູ້ໃຫ້ບໍລິການເຊີບເວີໃນຕ່າງປະເທດ',
                                  ),
                                  TextSpan(
                                    text: '\n\nການປະມວນຜົນຂໍ້ມູນສ່ວນບຸກຄົນ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nພວກເຮົາຈະເກັບກຳ, ໃຊ້ ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານເພື່ອຈຸດປະສົງດັງຕໍ່ໄປນີ້:\n● ເພື່ອສ້າງ ແລະຈັດການບັນຊີຜູ້ໃຊ້ງານ\n● ເພື່ອຈັດສົ່ງສິນຄ້າ ຫຼືການບໍລິການ.\n● ເພື່ອປັບປຸງສິນຄ້າ, ບໍລິການ ຫຼືປະສົບການຂອງຜູ້ໃຊ້ງານ\n● ເພື່ອການບໍລິຫານຈັດການພາຍໃນຂອງບໍລິສັດ.\n● ເພື່ອການຕະຫຼາດ ແລະການສົ່ງເສີມການຂາຍ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n● ເພື່ອການບໍລິການຫຼັງການຂາຍ\n● ເພື່ອເກັບກໍາຂໍ້ສະເໜີແນະຄວາມຄິດເຫັນ.\n● ເພື່ອຊໍາລະຄ່າສິນຄ້າ ຫຼືບໍລິການ\n● ເພື່ອປະຕິບັດຕາມຂໍ້ຕົກລົງ ແລະເງື່ອນໄຂ.(Terms and Conditions)\n● ເພື່ອປະຕິບັດຕາມກົດໝາຍ ແລະ ລະບຽບການຂອງອົງການລັດ.',
                                  ),
                                  TextSpan(
                                    text: '\n\nການເປີດເຜີຍຂໍ້ມູນສ່ວນບຸກຄົນ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\nພວກເຮົາອາດຈະເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໃຫ້ແກ່ຜູ້ອື່ນພາຍໃຕ້ຄວາມຍິນຍອມຂອງທ່ານຫຼືຕາມທີ່ກົດຫມາຍອະນຸຍາດໃຫ້ເປີດເຜີຍ ດັ່ງຕໍ່ໄປນີ້:',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການບໍລິຫານຈັດການພາຍໃນອົງການຈັດຕັ້ງ\nພວກເຮົາອາດຈະເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານພາຍໃນບໍລິສັດເທົ່າທີ່ຈໍາເປັນເພື່ອປັບປຸງ ແລະພັດທະນາສິນຄ້າຫຼືບໍລິການຂອງພວກເຮົາ.\nພວກເຮົາອາດຈະເກັບກໍາຂໍ້ມູນພາຍໃນສໍາລັບສິນຄ້າຫຼືບໍລິການຕ່າງໆ ພາຍໃຕ້ນະໂຍບາຍນີ້ເພື່ອຜົນປະໂຫຍດຂອງທ່ານແລະຜູ້ອື່ນຫຼາຍຂຶ້ນ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nຜູ້ໃຫ້ບໍລິການ\nພວກເຮົາອາດຈະເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໃຫ້ກັບຜູ້ໃຫ້ບໍລິການຂອງພວກເຮົາເທົ່າທີ່ຈໍາເປັນເພື່ອດຳເນີນການໃນດ້ານຕ່າງເຊັ່ນ: ການຊຳລະເງິນ, ການຕະຫຼາດ, ການພັດທະນາສິນຄ້າຫຼືບໍລິການ, ເປັນຕົ້ນ.\n"ທັ່ງນີ້ ຜູ້ໃຫ້ບໍລິການມີນະໂຍບາຍຄວາມເປັນສ່ວນຕົວຂອງຕົນເອງ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nພັນທະມິດທາງທຸລະກິດ\nພວກເຮົາອາດເປີດເຜີຍຂໍ້ມູນບາງຢ່າງກັບພັນທະມິດທາງທຸລະກິດເພື່ອຕິດຕໍ່ ແລະປະສານງານໃນການໃຫ້ບໍລິການສິນຄ້າ ຫຼືບໍລິການ. ແລະໃຫ້ຂໍ້ມູນທີ່ຈໍາເປັນກ່ຽວກັບຄວາມພ້ອມໃຊ້ງານຂອງສິນຄ້າຫຼືບໍລິການ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການໂອນທຸລະກິດ\nພວກເຮົາອາດຈະເປີດເຜີຍຂໍ້ມູນ ລວມເຖີງຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ສໍາລັບການປັບໂຄງສ້າງຂອງບໍລິສັດ ການຄວບລວມຫຼືການຂາຍກິດຈະການ ຫຼືການໂອນຊັບສິນອື່ນໆ\nໂດຍຝ່າຍທີ່ຮັບໂອນຕ້ອງປະຕິບັດຕໍ່ຂໍ້ມູນຂອງທ່ານໃນລັກສະນະທີ່ສອດຄ່ອງກັບນະໂຍບາຍນີ້. ລວມທັງກົດໝາຍຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວພ້ອມ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການບັງຄັບໃຊ້ກົດໝາຍ\nໃນກໍລະນີທີ່ມີກົດຫມາຍຫຼືອົງການຂອງລັດຮ້ອງຂໍ ພວກເຮົາຈະເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວວຂອງທ່ານຕາມຄວາມຈໍາເປັນຕໍ່ອົງການລັດ ເຊັ່ນ: ສານ, ອົງການລັດ, ເປັນຕົ້ນ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການໂອນຂໍ້ມູນສ່ວນຕົວໄປຕ່າງປະເທດ\nພວກເຮົາອາດຈະເປີດເຜີຍ ຫຼືໂອນຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໃຫ້ກັບບຸກຄົນ, ອົງການຈັດຕັ້ງ ຫຼືເຊີບເວີທີ່ຕັ້ງຢູ່ໃນຕ່າງປະເທດ. ໂດຍພວກເຮົາຈະດຳເນີນການຕາມມາດຕະການຕ່າງໆ. ເພື່ອໃຫ້ມັ່ນໃຈວ່າການໂອນຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໄປຍັງປະເທດປາຍທາງນັ້ນ ມີມາດຕະຖານການຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວທີ່ພຽງພໍ ຫຼືກໍລະນີອື່ນໆຕາມທີ່ກົດໝາຍກຳນົດໄວ້.\nເພື່ອໃຫ້ມັ່ນໃຈວ່າການໂອນຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໄປຍັງປະເທດປາຍທາງນັ້ນ ມີມາດຕະຖານການຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວທີ່ພຽງພໍ ຫຼືກໍລະນີອື່ນໆຕາມທີ່ກົດໝາຍກຳນົດໄວ້.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nໄລຍະເວລາເກັບຮັກສາຂໍ້ມູນສ່ວນບຸກຄົນ\nພວກເຮົາຈະເກັບຮັກສາຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໄວ້ຕາມໄລຍະເວລາທີ່ຈໍາເປັນໃນລະຫວ່າງທີ່ທ່ານເປັນລູກຄ້າຫຼືມີຄວາມສໍາພັນຢູ່ກັບພວກເຮົາຫຼືຕະຫຼອດໄລຍະເວລາທີ່ຈໍາເປັນເພື່ອໃຫ້ບັນລຸຈຸດປະສົງທີ່ກ່ຽວຂ້ອງກັບນະໂຍບາຍນີ້.\nເຊິ່ງອາດຈຳເປັນຕ້ອງເກັບຮັກສາໄວ້ຕໍ່ໄປພາຍຫຼັງຈາກນັ້ນ ຫາກວ່າມີກົດໝາຍກຳນົດໄວ້, ພວກເຮົາຈະລຶບ, ທຳລາຍ ຫຼື ເຮັດໃຫ້ເປັນຂໍ້ມູນທີ່ບໍ່ສາມາດລະບຸຕົວຕົນຂອງທ່ານໄດ້ ໃນເວລາທີ່ບໍ່ຈໍາເປັນຫຼືສິ້ນສຸດໄລຍະເວລາດັ່ງກ່າວ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິຂອງເຈົ້າຂອງຂໍ້ຂໍ້ມູນສ່ວນບຸກຄົນ\nພາຍໃຕ້ກົດໝາຍຄູ້ມຄອງຂໍ້ມູນສ່ວນຕົວ ທ່ານມີສິດທິໃນການດຳເນີນການ ດັງຕໍ່ໄປນີ້ ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທີ່ຈະຖອນການຍິນຍອມເຫັນດີ (right to withdraw consent) ຖ້າຫາກວ່າທ່ານໄດ້ໃຫ້ການຍິນຍອມເຫັນດີຂອງທ່ານ ພວກເຮົາຈະເກັບກໍາ, ນໍາໃຊ້ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ. ບໍ່ວ່າຈະເປັນການຍິນຍອມເຫັນດີທີ່ທ່ານໄດ້ໃຫ້ກ່ອນວັນທີທີ່ກົດໝາຍຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວມີຜົນບັງຄັບໃຊ້ ຫຼືຫຼັງຈາກນັ້ນ. ທ່ານມີສິດທີ່ຈະຖອນການຍິນຍອມເຫັນດີຂອງທ່ານໄດ້ທຸກເວລາ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິໃນການຮ້ອງຂໍການເຂົ້າເຖິງຂໍ້ມູນ (right to access) ທ່ານມີສິດຮ້ອງຂໍການເຂົ້າເຖິງຂໍ້ມູນສ່ວນຕົວຂອງທ່ານທີ່ຢູ່ພາຍໃຕ້ຄວາມຮັບຜິດຊອບຂອງພວກເຮົາ ແລະຂໍໃຫ້ພວກເຮົາເຮັດສຳເນົາຂໍ້ມູນດັ່ງກ່າວໃຫ້ທ່ານ. ລວມທັງຂໍໃຫ້ພວກເຮົາເປີດເຜີຍວ່າ ພວກເຮົາໄດ້ຮັບຂໍ້ມູນສ່ວນຕົວຂອງທ່ານມາໄດ້ແບບໃດ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິໃນການຮ້ອງຂໍໂອນຂໍ້ມູນ (right to data portability) ທ່ານມີສິດທິຂໍຮັບຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໃນກໍລະນີທີ່ພວກເຮົາເຮັດໃຫ້ຂໍ້ມູນສ່ວນບຸກຄົນນັ້ນຢູ່ໃນຮູບແບບໃຫ້ສາມາດອ່ານຫຼືໃຊ້ງານໄດ້ໂດຍເຄື່ອງມືຫຼືອຸປະກອນທີ່ເຮັດວຽກໄດ້ໂດຍອັດຕະໂນມັດ. ແລະສາມາດນໍາໃຊ້ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນບຸກຄົນໄດ້ໂດຍວິທີການອັດຕະໂນມັດ. ລວມທັງມີສິດທີ່ຈະຮ້ອງຂໍໃຫ້ພວກເຮົາສົ່ງຫຼືໂອນຂໍ້ມູນສ່ວນບຸກຄົນໃນຮູບແບບດັ່ງກ່າວໄປຍັງຜູ້ຄວບຄຸມຂໍ້ມູນສ່ວນບຸກຄົນອື່ນ ເມື່ອສາມາດເຮັດໄດ້ໂດຍວິທີການອັດຕະໂນມັດ. ແລະມີສິດທິຈະຂໍຮັບຂໍ້ມູນສ່ວນບຸກຄົນທີ່ພວກເຮົາສົ່ງຫຼືໂອນຂໍ້ມູນສ່ວນບຸກຄົນໃນຮູບແບບດັ່ງກ່າວໄປຍັງຜູ້ຄວບຄຸມຂໍ້ມູນສ່ວນບຸກຄົນອື່ນໂດຍກົງ ເວັ້ນເສຍແຕ່ບໍ່ສາມາດດໍາເນີນການໄດ້ເນື່ອງຈາກເຫດຜົນດ້ານເຕັກນິກ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິໃນການຂໍຄັດຄ້ານ (right to object) ທ່ານມີສິດທິຂໍຄັດຄ້ານໃນການເກັບກຳ ນຳໃຊ້ ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງເຈົ້າໄດ້ທຸກເວລາ ຖ້າການເກັບກໍາ, ນໍາໃຊ້ ຫຼືການເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ທີ່ຖືກສ້າງຂື້ນເພື່ອການດຳເນີນງານທີ່ຈໍາເປັນ ພາຍໃຕ້ຜົນປະໂຫຍດອັນຊອບທໍາຕາມກົດໝາຍຂອງພວກເຮົາ ຫຼືຂອງບຸກຄົນ ຫຼືນິຕິບຸກຄົນອື່ນ. ໂດຍບໍ່ເກີນຂອບເຂດທີ່ທ່ານສາມາດຄາດໝາຍໄດ້ຢ່າງສົມເຫດສົມຜົນຫຼືເພື່ອດຳເນີນການຕາມພາລະກິດເພື່ອຜົນປະໂຫຍດສາທາລະນະ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິຮ້ອງຂໍໃຫ້ລຶບຫຼືທຳລາຍຂໍ້ມູນ (right to erasure/destruction) ທ່ານມີສິດຮ້ອງຂໍໃຫ້ລຶບ ຫຼືທໍາລາຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ຫຼືເຮັດໃຫ້ຂໍ້ມູນສ່ວນຕົວຂອງທ່ານບໍ່ສາມາດລະບຸຕົວຕົນໄດ້.ຖ້າຫາກທ່ານເຊື່ອວ່າຂໍ້ມູນສ່ວນຕົວຂອງທ່ານຖືກເກັບກຳ, ນຳໃຊ້ ຫຼື ເປີດເຜີຍຢ່າງຜິດກົດໝາຍທີ່ກ່ຽວຂ້ອງ, ຫຼືທ່ານເຫັນວ່າພວກເຮົາບໍ່ຈໍາເປັນຕ້ອງເກັບຮັກສາໄວ້ເພື່ອຈຸດປະສົງທີ່ກ່ຽວຂ້ອງໃນນະໂຍບາຍນີ້.ຫຼືເມື່ອທ່ານໄດ້ໃຊ້ສິດທິຂໍຖອນການຍິນຍອມ ຫຼືໃຊ້ສິດທິຂໍຄັດຄ້ານຕາມທີ່ໄດ້ແຈ້ງໄວ້ຂ້າງເທິງແລ້ວ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິໃນການຮ້ອງຂໍໃຫ້ໂຈະການນໍາໃຊ້ຂໍ້ມູນ (right to restriction of processing) ທ່ານມີສິດຮ້ອງຂໍໃຫ້ຢຸດເຊົາການນໍາໃຊ້ຂໍ້ມູນສ່ວນຕົວຂອງທ່ານເປັນການຊົ່ວຄາວໃນກໍລະນີທີ່ພວກເຮົາຢູ່ໃນລະຫວ່າງການກວດສອບຕາມການຮ້ອງຂໍໃຊ້ສິດຂໍແກ້ໄຂຂໍ້ມູນສ່ວນຕົວຂອງທ່ານຫຼືການຄັດຄ້ານຂອງທ່ານ. ຫຼືໃນກໍລະນີອື່ນໆທີ່ພວກເຮົາບໍ່ມີຄວາມຈຳເປັນ  ແລະຕ້ອງລຶບ ຫຼືທໍາລາຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານຕາມກົດໝາຍທີ່ກ່ຽວຂ້ອງ ແຕ່ທ່່ານຂໍໃຫ້ພວກເຮົາລະງັບການນຳໃຊ້ແທນ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nສິດທິໃນການຮ້ອງຂໍ່ໃຫ້ແກ້ໄຂຂໍ້ມູນ (right to rectification) ທ່ານມີສິດຮ້ອງຂໍໃຫ້ແກ້ໄຂຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໃຫ້ຖືກຕ້ອງ, ເປັນປະຈຸບັນ, ຄົບຖ້ວນສົມບູນ ແລະບໍ່ເຮັດໃຫ້ເກີດການເຂົ້າໃຈຜິດ\n\nສິດທິໃນການຮ້ອງຮຽນອຸທອນ(right to lodge a complaint)\nທ່ານມີສິດຮ້ອງຮຽນຕໍ່ຜູ້ມີອຳນາດຕາມກົດໝາຍທີ່ກ່ຽວຂ້ອງ. ຖ້າຫາກທ່ານເຊື່ອວ່າການເກັບກໍາ, ນຳໃຊ້ ຫຼືເປີດເຜີຍຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ເປັນການກະທຳໃນລັກສະນະທີ່ລະເມີດ ຫຼື ບໍ່ປະຕິບັດຕາມກົດໝາຍທີ່ກ່ຽວຂ້ອງ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nທ່ານສາມາດໃຊ້ສິດຂອງທ່ານໃນການເປັນເຈົ້າຂອງຂໍ້ຂໍ້ມູນສ່ວນບຸກຄົນຂ້າງເທິງ. ໂດຍການຕິດຕໍ່ເຈົ້າໜ້າທີ່ຄູ້ມຄອງຂໍ້ມູນສ່ວນຕົວຂອງພວກເຮົາຕາມລາຍລະອຽດໃນຕອນທ້າຍຂອງນະໂຍບາຍນີ້.ພວກເຮົາຈະແຈ້ງຜົນການດຳເນີນການໃຫ້ທ່ານຊາບພາຍໃນ 30 ມື້ນັບຈາກວັນທີທີ່ພວກເຮົາໄດ້ຮັບການຮ້ອງຂໍຂອງທ່ານ ອີງຕາມຮູບແບບຫຼືວິທີການທີ່ພວກເຮົາກໍານົດໄວ້ ຢ່າງໃດກໍຕາມ, ຖ້າຫາກພວກເຮົາປະຕິເສດຄໍາຮ້ອງຂໍ, ພວກເຮົາຈະແຈ້ງເຫດຜົນຂອງການປະຕິເສດເຫດຜົນໃຫ້ທ່ານຮູ້ໂດຍຜ່ານຊ່ອງທາງຕ່າງໆ ເຊັ່ນ: ຂໍ້ຄວາມ (SMS), ອີເມວ, ໂທລະສັບ, ຈົດຫມາຍ, ແລະອື່ນໆ.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການໂຄສະນາແລະການຕະຫຼາດ\nເພື່ອຜົນປະໂຫຍດໃນການໄດ້ຮັບສິນຄ້າຫຼືການບໍລິການຂອງພວກເຮົາ ພວກເຮົາໃຊ້ຂໍ້ມູນຂອງທ່ານເພື່ອວິເຄາະ ແລະປັບປຸງສິນຄ້າ ຫຼືການບໍລິການຂອງພວກເຮົາ. ແລະເຮັດການຕະຫຼາດຜ່ານ Google, Facebook, ລະຫັດຕິດຕາມ pixel, ແລະອື່ນໆ. ພວກເຮົາໃຊ້ຂໍ້ມູນດັ່ງກ່າວເພື່ອໃຫ້ສິນຄ້າ ຫຼືການບໍລິການຂອງພວກເຮົາເໝາະສົມກັບທ່ານ.\n\nເຕັກໂນໂລຊີການຕິດຕາມສ່ວນບຸກຄົນ (Cookies) ເພື່ອເພີ່ມປະສົບການການໃຊ້ງານຂອງທ່ານ ໃຫ້ສົມບູນ ແລະມີປະສິດທິພາບຫຼາຍຂຶ້ນ, ພວກເຮົາໃຊ້ຄຸກກີ້ (Cookies)ຫຼືເທັກໂນໂລຍີທີ່ຄ້າຍຄືກັນ. ເພື່ອປັບປຸງພັດທະນາການເຂົ້າເຖິງສິນຄ້າຫຼືບໍລິການ ການໂຄສະນາທີ່ເຫມາະສົມ ແລະຕິດຕາມການນໍາໃຊ້ຂອງທ່ານ ພວກເຮົາໃຊ້ຄຸກກີ້ ເພື່ອລະບຸແລະຕິດຕາມຜູ້ໃຊ້ຂອງເວັບໄຊທ໌ແລະການເຂົ້າເຖິງເວັບໄຊທ໌ຂອງພວກເຮົາ.ຖ້າຫາກວ່າທ່ານບໍ່ຕ້ອງການຄຸກກີ້ໄວ້ໃນຄອມພິວເຕີຂອງທ່ານ ທ່ານສາມາດຕັ້ງຄ່າກໍານົດຕົວທ່ອງເວັບຂອງທ່ານເພື່ອປະຕິເສດຄຸກກີ້ກ່ອນທີ່ຈະນໍາໃຊ້ເວັບໄຊທ໌ຂອງພວກເຮົາໄດ້.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການຮັກສາຄວາມປອດໄພຂອງຂໍ້ມູນສ່ວນບຸກຄົນ ພວກເຮົາຈະຮັກສາຄວາມປອດໄພຂອງຂໍ້ມູນສ່ວນຕົວຂອງທ່ານໄວ້ຕາມຫຼັກການຂອງ ການຮັກສາຄວາມລັບ (confidentiality), ຄວາມຖືກຕ້ອງຄົບຖ້ວນ (integrity) ແລະສະພາບພ້ອມໃຊ້ງານ (availability) ທັງນີ້ ເພື່ອປ້ອງກັນການສູນຫາຍ, ການເຂົ້າເຖິງ, ການນໍາໃຊ້, ການປ່ຽນແປງ, ການແກ້ໄຂຫຼືການເປີດເຜີຍ, ນອກຈາກນີ້ພວກເຮົາຈະຈັດໃຫ້ມີມາດຕະການການຮັກສາຄວາມມັ່ນຄົງປອດໄພຂອງຂໍ້ມູນສ່ວນບຸກຄົນ.ເຊິ່ງກວມເອົາມາດຕະການປ້ອງກັນດ້ານການບໍລິຫານຈັດການ (administrative safeguard)  ມາດຕະການປ້ອງກັນດ້ານເຕັກນິກ(technical safeguard) ແລະມາດຕະການການປ້ອງກັນທາງດ້ານຮ່າງກາຍ (physical safeguard) ໃນການເຂົ້າເຖິງຫຼືການຄວບຄຸມການໃຊ້ງານຂໍ້ມູນສ່ວນບຸກຄົນ (access control)',
                                  ),
                                  TextSpan(
                                    text:
                                        '\n\nການແຈ້ງການລະເມີດຂໍ້ມູນສ່ວນບຸກຄົນ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nໃນກໍລະນີທີ່ມີການລະເມີດຂໍ້ມູນສ່ວນຕົວຂອງທ່ານ ພວກເຮົາຈະແຈ້ງໃຫ້ຫ້ອງການຂອງຄະນະກໍາມະການຄຸ້ມຄອງຂໍ້ມູນສ່ວນບຸກຄົນຮັບຮູ້ ໂດຍບໍ່ມີການຊັກຊ້າພາຍໃນ 72 ຊົ່ວໂມງ.ນັບຕັ້ງແຕ່ຮູ້ສາເຫດເທົ່າທີ່ເປັນໄປໄດ້ ໃນກໍລະນີທີການລະເມີດມີຄວາມສ່ຽງສູງທີ່ຈະມີຜົນກະທົບຕໍ່ສິດທິແລະເສລີພາບຂອງທ່ານ ພວກເຮົາຈະແຈ້ງໃຫ້ທ່ານຊາບກ່ຽວກັບການລະເມີດ ພ້ອມກັບວິທີແກ້ໄຂໂດຍບໍ່ມີການຊັກຊ້າໂດຍຜ່ານຊ່ອງທາງຕ່າງໆ.ເຊັ່ນ ເວັບໄຊທ໌, ຂໍ້ຄວາມ (SMS), ອີເມວ (e-mail), ໂທໂທລະສັບ, ຈົດຫມາຍ, ແລະອື່ນໆ',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nການແກ້ໄຂປ່ຽນແປງນະໂຍບາຍຄວາມເປັນສ່ວນຕົວ ພວກເຮົາອາດຈະແກ້ໄຂປ່ຽນແປງປັບປູງນະໂຍບາຍນີ້ໃນບາງຄັ້ງຄາວ. ໂດຍທ່ານສາມາດຮັບຮູ້ຂໍ້ກໍານົດແລະເງື່ອນໄຂນະໂຍບາຍທີ່ມີການແກ້ໄຂປ່ຽນແປງປັບປຸງນີ້ໄດ້ຜ່ານທາງເວັບໄຊທ໌ຂອງພວກເຮົາ.ນະໂຍບາຍນີ້ໄດ້ຮັບການແກ້ໄຂປັບປຸງຄັ້ງລ່າສຸດແລະມີຜົນບັງຄັບໃຊ້ໃນວັນທີ 15 ກັນຍາ 2022.',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nນະໂຍບາຍຄວາມເປັນສ່ວນຕົວຂອງເວັບໄຊທ໌ອື່ນໆ ນະໂຍບາຍຄວາມເປັນສ່ວນຕົວສະບັບນີ້ໃຊ້ສຳຫຼັບການນຳສະເໜີສິນຄ້າ, ການບໍລິການ ແລະການນໍາໃຊ້ເວັບໄຊທ໌ສໍາລັບລູກຄ້າຂອງພວກເຮົາເທົ່ານັ້ນ. ຫາກທ່ານເຂົົ້າຊົມໃຊ້ເວັບໄຊທ໌ອື່ນ, ເຖິງແມ່ນວ່າຜ່ານຊ່ອງທາງເວັບໄຊທ໌ຂອງພວກເຮົາ ການຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວຕ່າງໆ  ແມ່ນຈະເປັນໄປຕາມນະໂຍບາຍຄວາມເປັນສ່ວນຕົວຂອງເວັບໄຊທ໌ນັ້ນ. ເຊິ່ງພວກເຮົາຈະບໍ່ໄດ້ມີສ່ວນກ່ຽວຂ້ອງໃດໆ\n\nລາຍລະອຽດການຕິດຕໍ່\nຖ້າຫາກທ່ານຕ້ອງການສອບຖາມກ່ຽວກັບນະໂຍບາຍຄວາມເປັນສ່ວນຕົວສະບັບນີ້ ລວມທັງການຮ້ອງຂໍການນໍາໃຊ້ສິດທິຕ່າງໆ, ທ່ານສາມາດຕິດຕໍ່ພວກເຮົາຫຼືເຈົ້າຫນ້າທີ່ຄຸ້ມຄອງຂໍ້ມູນສ່ວນຕົວນຂອງພວກເຮົາດັ່ງນີ້:\n\nຜູ້ຄວບຄູມຂໍ້ມູນສ່ວນຕົວ\nບໍລິສັດ ຮ່ວມພັດທະນາ ເຊົ່າສິນເຊື່ອ ຈຳກັດ\nອີເມວ <EMAIL>\nเว็บไซต์ https://www.ruampattanaleasing.com/\nໝາຍເລກໂທລະສັບ (+856) 020-29880022',
                                  ),
                                  const TextSpan(
                                    text:
                                        '\n\nເຈົ້າຫນ້າທີ່ຄູ້ມຄອງຂໍ້ມູນສ່ວນຕົວ\nຝ່າຍການຕະຫຼາດ RPLC\nອີເມວ <EMAIL>\nໝາຍເລກໂທລະສັບ (+856) 020-29880022\n\nບໍລິສັດ ຮ່ວມພັດທະນາ ເຊົ່າສິນເຊື່ອ ຈຳກັດ 577/6 ຖະໜົນ 13 ເໜືອ ໜ່ວຍ 34 ບ້ານນາເລົ່າ ເມືີອງສີໂຄດຕະບອງ ນະຄອນຫຼວງວຽງຈັນ',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }
}
