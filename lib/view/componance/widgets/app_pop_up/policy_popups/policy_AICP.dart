import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/componance/widgets/componance_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class AICPPolicy {
  static buildTermsAICP(context){
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              children: [
                                SvgPicture.string('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#EA1B23"/> <path d="M9.993 12.5H10.0005" stroke="#EA1B23" stroke-linecap="round" stroke-linejoin="round"/> <path d="M10 10L10 6.66667" stroke="#EA1B23" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    "កិច្ចព្រមព្រៀងនិងលក្ខខណ្ឌ",
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      // height: 0.10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  const TextSpan(
                                    text:
                                    "\nកិច្ចព្រមព្រៀងនិងលក្ខខណ្ឌ\nកិច្ចព្រមព្រៀងខាងក្រោមនេះ (“កិច្ចព្រមព្រៀងការប្រើប្រាស់”) គឺជាកិច្ចព្រមព្រៀងរវាងអ្នកនិងគេហទំព័រ https://aicpmotor.com និងកម្មវិធី AICP Motor ដែលរួមបញ្ចូលទាំងសេចក្ដីលម្អិត និងការបម្រើសេវាកម្មទាំងអស់នៅលើគេហទំព័រ https://aicpmotor.com និងកម្មវិធី AICP Motor លើប្រព័ន្ទប្រតិបត្តិការ Android និង IOS ដែលអ្នកត្រូវអនុវត្តតាម ដោយការភ្ជាប់ឬចូលប្រើប្រាស់សេវាកម្មគេហទំព័រឬកម្មវិធី AICP Motor ឬ https://aicpmotor.com (\"\"គេហទំព័រឬកម្មវិធី”) ស្ថិតនៅក្រោមការគ្រប់គ្រងរបស់ហាង AICP Motor សម្ដៅទៅលើ អ្នកយល់ព្រមនូវកិច្ចព្រមព្រៀង លក្ខខណ្ឌនៃការបម្រើសេវាកម្ម ដែលបានបង្ហាញក្នុងគេហទំព័រឬកម្មវិធីដោយផ្អែកតាមគោលការណ៏ច្បាប់ (\"\"លក្ខខណ្ឌ”) និងអ្នកយល់ព្រមទទួលយក និងអនុវត្ត នយោបាយភាពជាឯកជន របស់គេហទំព័រឬកម្មវិធី និងយល់ព្រមអនុវត្តតាមលក្ខន្តិកផ្សេងៗ នៅលើគេហទំព័រឬកម្មវិធី ដូចជាវិធីប្រើប្រាស់ វិធីដែលត្រូវអនុត្តនិងជំហានឬលក្ខខណ្ឌសម្រាប់ការភ្ជាប់ឬការប្រើប្រាស់របស់គេហទំព័រឬកម្មវិធី AICP MOTOR ក្នុងការចូលប្រើប្រាស់ឬសេវាកម្ម នៅលើទម្រង់ការប្រើប្រាស់សេវាកម្ម ក្នុងបែបបទផ្សេងៗ តាមលក្ខខណ្ឌដែល AICP MOTOR បានកំណត់។",
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nនិយមន័យ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n\nAICP MOTOR\n\"សមាជិក” សំដៅលើ សមាជិ https://aicpmotor.com និងកម្មវិធីរបស់ AICP MOTOR របស់ហាង AICP MOTOR\n“ហាង” សំដៅលើ ហាង AICP MOTOR  ដែលជាអ្នកដំណើរការនិងបម្រើសេវាកម្មការប្រើប្រាស់ចំពោះសមាជិក\n“ព័ត៌មានផ្ទាល់ខ្លួន” សំដៅលើ ព័ត៌មានដែលទាក់ទងទៅនឹងបុគ្គលណាម្នាក់ ដែលអាចកំណត់អត្តសញ្ញាណរបស់បុគ្គលម្នាក់នោះបាន មិនថាដោយផ្ទាល់ឬដោយប្រយោល\n“LIKEPOINT” សំដៅលើ ការសន្សំពិន្ទុដែលសមាជិកនឹងទទួលបានពេលបានចូលរួមសកម្មភាពផ្សេងៗជាមួយក្រុមហ៊ុន ដែលនឹងទទួលបានអត្ថប្រយោជន៍ផ្សេងៗ ដែលលក្ខខណ្ឌនៃការផ្ដល់ជូននូវសេវាកម្មអនុវត្តតាមក្រុមហ៊ុនបានកំណត់',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nការផ្ដល់សេវាកម្មនិងលក្ខខណ្ឌក្នុងការប្រើប្រាស់',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n\nអ្នកប្រើប្រាស់ត្រូវតែយល់ព្រម និងគោរពតាមលក្ខខណ្ឌ ដែលបង្ហាញនៅលើលក្ខខណ្ឌជាសមាជិកភាព សេវាកម្ម និងលក្ខខណ្ឌនៃការប្រើប្រាស់សេវាកម្មនេះ ឬនៅក្នុងកំណែទម្រង់ដែលនឹងកែប្រែនាពេលអនាគត ហើយប្រសិនបើអ្នកបាន ប្រើប្រាស់សេវាកម្មរបស់ AICP Motor ត្រូវបានចាត់ទុកថា អ្នកយល់ព្រមតាមលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្មនេះ\n1. លក្ខខណ្ឌនៃសេវាកម្មសមាជិកភាព យោងតាមលក្ខខណ្ឌសមាជិកភាព និងលក្ខខណ្ឌនៃសេវាកម្មនៃកិច្ចសន្យាសេវាកម្មកម្មវិធីទូរស័ព្ទ ប្រសិនបើសមាជិកបានទទួលយករួចហើយ វាត្រូវបានចាត់ទុកថាសមាជិកព្រមទទួលយកលក្ខខណ្ឌនៃសេវាកម្មសមាជិកភាព  ហើយលក្ខខណ្ឌនៃសេវាកម្មសមាជិកភាពនៃកម្មវិធីនេះត្រូវបានអនុវត្ត។\n2. សមាជិកអាចចូលរួមក្នុងសកម្មភាពតាមរយៈគេហទំព័រ។ https://aicpmotor.com (\"\"\"\"គេហទំព័រ\"\"\"\") និងកម្មវិធី AICP MOTOR បានគ្រប់ទីកន្លែងទាំងអស់ ដែលឧបករណ៍អេឡិចត្រូវរបសល់លោកអ្នកត្រូវបានភ្ជាប់អ៊ីនធឺណិត\n3. AICP MOTOR អាចផ្លាស់ប្តូរទៅតាមលក្ខខណ្ឌសមាជិកភាព និងលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្មតាមការគួរ ប្រើសិនបើមានការផ្លាស់ប្តូរ លក្ខខណ្ឌផ្សេងៗនឹងត្រូវបានប្រកាសនៅលើកម្មវិធី AICP MOTOR \n4. សមាជិកមានតូនាទីចូលមើលគេហទំព័រ ឬកម្មវិធី ហើយពិនិត្យមើលខ្លឹមសារនៃកិច្ចព្រមព្រៀងសម្រាប់ការផ្លាស់ប្តូរណាមួយដែលបានប្រកាស ប្រសិនបើសមាជិកមានសិទ្ធិចូលប្រើសេវាកម្មបន្ទាប់ពីការផ្លាស់ប្តូរដែលបានប្រកាស អ្នកត្រូវបានចាត់ទុកថាបានទទួលយក និងយល់ព្រមចំពោះការផ្លាស់ប្តូរណាមួយដូចដែលបានរៀបរាប់ខាងលើ\n5. ការសន្សំ ការប្រើប្រាស់ និងការដូរនៃពិន្ទុរង្វាន់ តម្រូវទៅតាមលក្ខខណ្ឌដែលក្រុមហ៊ុនបានកំណត់ ក្នុងករណីដែលមានការផ្លាស់ប្តូរ ក្រុមហ៊ុននឹងជូនដំណឹងដល់សមាជិកអោយបានជ្រាប។\n6. ឧបករណ៏អេឡិចត្រូនិចដែលអាចប្រើដើម្បីចូលរួមសកម្មភាពបាន ត្រូវតែអាចភ្ជាប់អ៊ីនធឺណិតបាន និងអាចធ្វើប្រតិបត្តិការបានកបណ្ដាញ (ឬ Web Browser) បាន។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nកម្មសិទ្ធិបញ្ញា',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n\nសមាជិកយល់ព្រម និងទទួលស្គាល់ថាព័ត៌មានគេហទំព័រ និងកម្មវិធី ទាំងអស់គឺជាកម្មសិទ្ធបញ្ញា ដែលជាកម្មសិទ្ធដែលត្រូវបានការពារដោយច្បាប់ ហាមប្រាមមិនអនុញ្ញាតអោយបុគ្គលណាម្នាក់អាចចម្លង កែប្រែ ក្លែងបន្លំ ផលិតឡើងវិញ យកតម្រាប់តាម ឬអនុញ្ញាតឱ្យបុគ្គលណាម្នាក់អនុវត្តសិទ្ធិបែបនេះតាមមធ្យោបាយផ្សេងឡើយ។ តាមរបៀបដែលបំពាន ដោយមធ្យោបាយណាមួយ ពីកម្មសិទ្ធិបញ្ញាដែលបានរៀបរាប់ខាងលើ ដោយគ្មានការអនុញ្ញាតជាលាយលក្ខណ៍អក្សរ',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nការផ្លាស់ប្ដូរនិងកែតម្រូវ้',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n\n1. ក្រុមហ៊ុនអាចមានការកែសម្រួល នូវលក្ខខណ្ឌ និងការប្រើប្រាស់គេហទំព័រ https://aicpmotor.com (\"\"\"\"គេហទំព័រ\"\"\"\") និងកម្មវិធី AICP MOTOR និងកិច្ចព្រមព្រៀងការប្រើប្រាស់ក្នុងពេលណាមួយក៏បាន ទោះជាយ៉ាងណាក៏ដោយ ក្រុមហ៊ុនពុំមានការចាំបាច់ជូនដំណឹងដល់សមាជិកអំពីការកែប្រែណាមួយចំពោះលក្ខខណ្ឌនិងការប្រើប្រាស់ណាមួយនោះទេ របស់គេហទំព័រ https://aicpmotor.com (“គេហទំព័រ”) និងកម្មវិធី AICP MOTOR ។\n2. សមាជិកត្រូវតែទទួលយកការប្រកាសលក្ខខណ្ឌនៃការប្រើប្រាស់ថ្មីនៅលើគេហទំព័រhttps://aicpmotor.com (\"\"\"\"គេហទំព័រ\"\"\"\") និងកម្មវិធី AICP MOTOR ដោយក្រុមហ៊ុនពុំមានការចាំបាច់ជូនដំណឹងដល់សមាជិកពីការប្រើប្រាស់គេហទំព័រ ឬកម្មវិធីបន្ទាប់ពីមានការកែតម្រូវណាមួយនោះទេ ចាត់ទុកថាជាសមាជិក យល់ព្រម និងទទួលយកលក្ខខណ្ឌនៃការប្រើប្រាស់ថ្មី។ ក្រុមហ៊ុន លើកណែនាំសមាជិកឱ្យពិនិត្យឡើងវិញនូវលក្ខខណ្ឌនៃការប្រើប្រាស់រាល់ពេលដែលសមាជិកចូលប្រើប្រាស់វា។',
                                  ),
                                  const TextSpan(
                                    text: '\n3 សមាជិកទទួលយកលក្ខខណ្ឌនៃការប្រើប្រាស់ដោយការចូលប្រើ និង/ឬធ្វើប្រតិបត្តិការនៅលើគេហទំព័រ https://aicpmotor.com (\"\"\"\"គេហទំព័រ\"\"\"\") និងកម្មវិធី AICP MOTOR ។\n4 ប្រសិនបើលក្ខខណ្ឌណាមួយ នៅក្នុងលក្ខខណ្ឌនៃការប្រើប្រាស់នេះគឺខុសច្បាប់ ចាត់ទុកជាមោឃៈ ឬមិនអាចអនុវត្តបានដោយហេតុផលណាមួយ នោះលក្ខខណ្ឌនឹងត្រូវចាត់ទុកថាត្រូវបានលុបចោល និងមិនប៉ះពាល់ដល់ភាពត្រឹមត្រូវ ឬការអនុវត្តនៃលក្ខខណ្ឌការប្រើប្រាស់ដែលនៅសល់។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nអត្ថប្រយោជន៍',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n\n1. សមាជិកអាចប្រើប្រាស់សេវាកម្មផ្សេងៗតាមលក្ខខណ្ឌ និងស្តង់ដារ។\n2. សមាជិកអាចសន្សំពិន្ទុបានដោយការទាញយកកម្មវិធី និងចួលរួមសកម្មភាពតាមការគ្រោងទុក\n3. សមាជិកអាចប្រើប្រាស់ពិន្ទុរង្វាន់បានតាមដែលក្រុមហ៊ុនបានកំណត់',
                                  ),
                                  const TextSpan(
                                    text: '\n\n\"ការបញ្ចប់សេវាកម្ម ការលុបចោល និងការផ្អាកសេវាកម្ម\n ក្នុងករណីដែលអ្នកប្រើប្រាស់ចង់លុបចោលសេវាកម្ម អ្នកប្រើប្រាស់អាចធ្វើ តាមវិធីខាងក្រោមដូចនេះ\nអ្នកប្រើប្រាស់សេវាកម្មលុបចោលគណនីដោយខ្លួនឯងតាមរយៈ កម្មវិធី ឬតាមការស្នើសុំតាមរយៈ Callcenter : 088-5267800 \nTelegram  : 088-5267800  Facebook : AICP Motor និងដំណើរការតាមវិធីសាស្រ្តដែលបានបញ្ជាក់ដោយក្រុមហ៊ុន។\nក្រុមហ៊ុនរក្សាសិទ្ធិក្នុងការលុបចោលការផ្ដល់សេវាកម្ម ឬផ្អាកផ្ដល់សេវាកម្ម ដោយមិនចាំបាច់ជូនដំណឹងដល់អ្នកប្រើប្រាស់អោយបានជ្រាបជាមុនឡើយ ក្នុងករណីដូចខាងក្រោមនេះ',
                                  ),
                                  const TextSpan(
                                    text: '\n1. អ្នកប្រើប្រាស់ បានប្រើឯកសារក្លែងក្លាយ ឬបន្លំបង្ហាញរបាយការណ៍ ក្នុងការចុះឈ្មោះគណនី រួមទាំងការជូនដំណឹង ឬការក្លែងបន្លំភស្តុតាង\nឬបង្ហាញភស្តុតាង ឯកសារមិនពេញលេញតាមការស្នើសុំរបស់ក្រុមហ៊ុន។\n2. អ្នកប្រើប្រាស់សេវាបានបាត់បងជីវិត\n3. អ្នកប្រើប្រាស់សេវាគឺជាបុគ្គលដែលត្រូវបាន រដ្ឋាភិបាល ឬភ្នាក់ងាររដ្ឋាភិបាលបញ្ជាចាប់ខ្លួន ឬឱ្យរឹបអូសទ្រព្យសម្បត្តិ។\nឬជាបុគ្គលដែលបានកំណត់ថាជា ជនជាប់ចោទ ឬត្រូវបានសង្ស័យថាបានប្រព្រឹត្តបទល្មើសខុសច្បាប់\n4. ក្រុមហ៊ុនមានហេតុផលសង្ស័យថាអ្នកប្រើប្រាស់សេវាកម្មបានប្រព្រឹត្តបទល្មើស ប៉ុនប៉ងប្រព្រឹត្តឬធ្វើសកម្មភាពណាមួយដែលខុសនឹងលក្ខខណ្ឌដូចបានកំណត់ ដែលអាចបណ្តាលឱ្យខូចខាតដល់ក្រុមហ៊ុន ឬបុគ្គលដទៃទៀត\n5. ក្នុងករណីដែលអតិថិជនលុបចោលសេវាកម្មនៃលេខទូរស័ព្ទដែលប្រើដើម្បីបើកគណនី នៅពេលដែលក្រុមហ៊ុនដឹងអំពីព័ត៌មានអំពីការលុបចោលសេវាកម្មរបស់លេខទូរស័ព្ទខាងលើ\n6. អ្នកប្រើប្រាស់សេវាកម្មបានប្រព្រឹត្តខុសជាមួយនឹងលក្ខខណ្ឌណាមួយដែលមានចែងក្នុងលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្មនេះ\n7. ក្រុមហ៊ុនបានដឹងថាអ្នកប្រើប្រាស់សេវាកម្មនេះ បានក្លាយជាបុគ្គលអសមត្ថភាព។ឬអ្នកដែលគ្មានសមត្ថភាពតាមច្បាប់\nនិងដោយគ្មានការយល់ព្រម ឬប្រើប្រាស់ជំនួសពី អាណាព្យាបាលស្របច្បាប់របស់អ្នកប្រើប្រាស់ និងប្រើប្រាស់សេវាកម្មក្រោមលក្ខខណ្ឌនៃសេវាកម្មទាំងនេះ។\nដូចនេះ ក្រុមហ៊ុនមិនពាក់ព័ន្ធ ឬទទួលខុសត្រូវចំពោះការខូចខាតទាំងអស់ដែលអាចកើតឡើងដោយសារតែការផ្អាក ឬលុបចោលការប្រើប្រាស់សេវាកម្មនេះឡើយ\n\nលើសពីនេះទៅទៀត ក្នុងករណីដែលក្រុមហ៊ុនមានហេតុសង្ស័យ ឬក្រុមហ៊ុនចាំត្រូវអនុវត្តតាមច្បាប់ណាមួយ ក្រុមហ៊ុនអាចនឹងទាក់ទងដើម្បីស្នើសុំឱ្យអ្នកប្រើប្រាស់ផ្តល់ព័ត៌មាន ដាក់ឯកសារភស្តុតាង ឬឯកសារបន្ថែមផ្សេង ដើម្បីឱ្យក្រុមហ៊ុនពិចារណា ប្រសិនបើអ្នកប្រើសេវាកម្មមិនអនុវត្តតាមពេលវេលាកំណត់ដែលក្រុមហ៊ុនបានស្នើសុំនោះទេ\nឬក្រុមហ៊ុនទទួលបានព័ត៌មាន ឯកសារ ភស្តុតាង ឬឯកសារបន្ថែមណាមួយពីអ្នកប្រើប្រាស់ ហើយក្រុមហ៊ុនចាត់ទុកថាអ្នកប្រើប្រាស់សេវាកម្ម ចូលនូវលក្ខខណ្ឌណាមួយ ដែលបានរៀបរាប់ខាងលើ ឬក្រុមហ៊ុនចាំបាច់ត្រូវបញ្ចប់ទំនាក់ទំនងជាមួយអ្នកប្រើប្រាស់ ដោយផ្អែកលើហេតុផលផ្លូវច្បាប់ ក្រុមហ៊ុននឹងរក្សាសិទ្ធិក្នុងការលុបចោលសេវាកម្ម ឬផ្អាកសេវាកម្មភ្លាមៗ។',
                                  ),
                                  TextSpan(
                                    text:
                                    ' \n\nសក្ខីកម្មរបស់អ្នកប្រើសេវាកម្ម',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n1. អ្នកប្រើប្រាស់សេវាកម្មត្រូវធានាថា អ្នកប្រើប្រាស់សេវាកម្មមានសមត្ថកិច្ចស្របច្បាប់ ឬទទួលបានការយល់ព្រមផ្លូវច្បាប់ដើម្បីដាក់ពាក្យ និងប្រើប្រាស់សេវាកម្ម រួមទាំងការយល់ព្រមនឹងត្រូវបានកំណត់ដោយលក្ខខណ្ឌនៃសេវាកម្មនេះ និង/ឬចុះកិច្ចសន្យាណាមួយ ទាក់ទងនឹងការប្រើប្រាស់សេវាកម្ម ប្រសិនបើអ្នកជាអាណាព្យាបាលស្របច្បាប់របស់អ្នកប្រើប្រាស់ដែលជាម្ចាស់គណនី អ្នកយល់ព្រមដាក់ពាក្យសុំសេវាកម្ម ហើយយល់ព្រមតាមលក្ខខណ្ឌនៃសេវាកម្មនេះ និង/ឬចុះកិច្ចសន្យាណាមួយ ទាក់ទងនឹងការប្រើប្រាស់សេវាកម្មក្នុងនាមអ្នកប្រើប្រាស់ដែលជាម្ចាស់គណនី។\nដូចនេះ អ្នកប្រើប្រាស់ និង/ឬអាណាព្យបាលស្របច្បាប់របស់អ្នកប្រើប្រាស់ ត្រូវយល់ថា អ្នកប្រើប្រាស់ត្រូវទទួលខុសត្រូវចំពោះលទ្ធផលផ្នែកហិរញ្ញវត្ថុ ឬផលវិបាកផ្សេងទៀតដែល អាចកើតឡើងជាលទ្ធផលនៃការប្រើប្រាស់សេវាកម្ម បង្ករឡើងដោយអ្នកប្រើប្រាស់សេវាកម្ម ឬអ្នកប្រើប្រាស់សេវាកម្ម និង/ឬអាណាព្យាបាលស្របច្បាប់របស់អ្នកប្រើប្រាស់ អនុញ្ញាតឲ្យភាគីទីបីប្រើប្រាស់ទិន្នន័យចុះឈ្មោះ ពាក្យសម្ងាត់ ឬព័ត៌មានផ្សេងទៀតរបស់អ្នកប្រើប្រាស់បាន។',
                                  ),
                                  const TextSpan(
                                    text: '\n2. អ្នកប្រើប្រាស់សេវាកម្មត្រូវ ធានាថាគាត់នឹងមិនប្រើប្រាស់កម្មវិធី ក្នុងការធ្វើប្រតិបត្តិការហិរញ្ញវត្ថុក្នុងគោលបំណងខុសច្បាប់ណាមួយនោះឡើយ ដូចជាការភ្នាល់ ការជួញដូរផ្លូវភេទ ការលាងលុយកខ្វក់ ឬការកេងប្រវ័ញ្ចគណនី មិនថាសម្រាប់ផលប្រយោជន៍របស់អ្នកប្រើប្រាស់ផ្ទាល់ និង/ឬបុគ្គលផ្សេងទៀត។ ធ្វើអាជីវកម្មក្នុងទម្រង់ផ្សេងៗ រួមទាំងសកម្មភាពដែលបំពានដល់សណ្តាប់ធ្នាប់សាធារណៈ និងសីលធម៌សង្គម រំលោភលើកម្មសិទ្ធិបញ្ញារបស់អ្នកដទៃ ឬអំពើខុសច្បាប់ផ្សេងទៀតៗ រួមទាំងនឹងមិនបណ្តាលឱ្យកម្មវិធី ឬបណ្តាញដែលបានភ្ជាប់ទៅកម្មវិធីត្រូខូចខាត ឬនឹងមិនបង្កការរំខានដល់ការប្រើប្រាស់កម្មវិធីរបស់បុគ្គលដទៃឡើយ។ មិនត្រូវព្យាយាមចូលទៅក្នុងកម្មវិធី ឬបណ្តាញភ្ជាប់ទៅកម្មវិធី ដោយគ្មានការអនុញ្ញាត តាមរយៈការលួចចូល (hacking) លួចចម្លង ឬបំផ្លាញមូលដ្ឋានទិន្នន័យ បំផ្លាញពាក្យសម្ងាត់ឯកជន (password mining) ឬមធ្យោបាយផ្សេងទៀត ប្រសិនបើ\nមានការខូចខាត ណាមួយចំពោះក្រុមហ៊ុន ឬក្រុមហ៊ុនត្រូវទទួលខុសត្រូវចំពោះការទទួលខុសត្រូវណាមួយ ដែលកើតឡើងដោយអ្នកប្រើប្រាស់បានធ្វើប្រតិបត្តិការហិរញ្ញវត្ថុតាមរយៈសេវាកម្មរបស់ក្រុមហ៊ុន សម្រាប់គោលបំណងដែលបានរៀបរាប់ខាងលើ ឬសកម្មភាពរបស់អ្នកប្រើដែលបានប្រព្រឹត្តទៅលើកម្មវិធីដួចដែលបានរៀបរាប់ ក្រុមហ៊ុនមានសិទ្ធិផ្អាក ឬបញ្ឈប់សេវាកម្មភ្លាមៗ ហើយអ្នកប្រើប្រាស់ត្រូវទទួលខុសត្រូវដោយការសងសំណងដល់ក្រុមហ៊ុនក្នុងគ្រប់ប្រការទាំងអស់ ក្រុមហ៊ុនមិនពាក់ព័ន្ធ ឬទទួលខុសត្រូវចំពោះការខូចខាតទាំងអស់ដែលអាចកើតឡើងនោះទេ។',
                                  ),
                                  const TextSpan(
                                    text: '\n2. អ្នកប្រើប្រាស់សេវាកម្មត្រូវ ធានាថាគាត់នឹងមិនប្រើប្រាស់កម្មវិធី ក្នុងការធ្វើប្រតិបត្តិការហិរញ្ញវត្ថុក្នុងគោលបំណងខុសច្បាប់ណាមួយនោះឡើយ ដូចជាការភ្នាល់ ការជួញដូរផ្លូវភេទ ការលាងលុយកខ្វក់ ឬការកេងប្រវ័ញ្ចគណនី មិនថាសម្រាប់ផលប្រយោជន៍របស់អ្នកប្រើប្រាស់ផ្ទាល់ និង/ឬបុគ្គលផ្សេងទៀត។ ធ្វើអាជីវកម្មក្នុងទម្រង់ផ្សេងៗ រួមទាំងសកម្មភាពដែលបំពានដល់សណ្តាប់ធ្នាប់សាធារណៈ និងសីលធម៌សង្គម រំលោភលើកម្មសិទ្ធិបញ្ញារបស់អ្នកដទៃ ឬអំពើខុសច្បាប់ផ្សេងទៀតៗ រួមទាំងនឹងមិនបណ្តាលឱ្យកម្មវិធី ឬបណ្តាញដែលបានភ្ជាប់ទៅកម្មវិធីត្រូខូចខាត ឬនឹងមិនបង្កការរំខានដល់ការប្រើប្រាស់កម្មវិធីរបស់បុគ្គលដទៃឡើយ។ មិនត្រូវព្យាយាមចូលទៅក្នុងកម្មវិធី ឬបណ្តាញភ្ជាប់ទៅកម្មវិធី ដោយគ្មានការអនុញ្ញាត តាមរយៈការលួចចូល (hacking) លួចចម្លង ឬបំផ្លាញមូលដ្ឋានទិន្នន័យ បំផ្លាញពាក្យសម្ងាត់ឯកជន (password mining) ឬមធ្យោបាយផ្សេងទៀត ប្រសិនបើ\nមានការខូចខាត ណាមួយចំពោះក្រុមហ៊ុន ឬក្រុមហ៊ុនត្រូវទទួលខុសត្រូវចំពោះការទទួលខុសត្រូវណាមួយ ដែលកើតឡើងដោយអ្នកប្រើប្រាស់បានធ្វើប្រតិបត្តិការហិរញ្ញវត្ថុតាមរយៈសេវាកម្មរបស់ក្រុមហ៊ុន សម្រាប់គោលបំណងដែលបានរៀបរាប់ខាងលើ ឬសកម្មភាពរបស់អ្នកប្រើដែលបានប្រព្រឹត្តទៅលើកម្មវិធីដួចដែលបានរៀបរាប់ ក្រុមហ៊ុនមានសិទ្ធិផ្អាក ឬបញ្ឈប់សេវាកម្មភ្លាមៗ ហើយអ្នកប្រើប្រាស់ត្រូវទទួលខុសត្រូវដោយការសងសំណងដល់ក្រុមហ៊ុនក្នុងគ្រប់ប្រការទាំងអស់ ក្រុមហ៊ុនមិនពាក់ព័ន្ធ ឬទទួលខុសត្រូវចំពោះការខូចខាតទាំងអស់ដែលអាចកើតឡើងនោះទេ។',
                                  ),
                                  const TextSpan(
                                    text: '\n3. អ្នកប្រើធានាថាព័ត៌មានដែលបានផ្តល់ឱ្យក្រុមហ៊ុនដោយអនុលោមតាមប្រការ 2 នៃលក្ខខណ្ឌប្រើប្រាស់ច្បាប់នេះ និងព័ត៌មានផ្សេងទៀតដែលអ្នកប្រើប្រាស់ផ្តល់ជូនក្រុមហ៊ុន វាជាព័ត៌មានត្រឹមត្រូវគ្រប់ផ្នែក ប្រសិនបើក្រុមហ៊ុនរកឃើញថាអ្នកប្រើប្រាស់សេវាកម្មបានក្លែងបន្លំព័ត៌មានឯកសារ និង/ឬបន្លំខ្លួនជាបុគ្គលណាមួយ ទោះបីជាបុគ្គលនោះ យល់ព្រមឬមិនយល់ព្រមក៏ដោយ  ក្រុមហ៊ុនរក្សាសិទ្ធិក្នុងការផ្អាក ឬលុបចោលសេវាកម្មភ្លាមៗ ហើយអ្នកប្រើប្រាស់សេវាកម្មត្រូវទទួលខុសត្រូវចំពោះក្រមហ៊ុនក្នុងគ្រប់ប្រការទាំងអស់ ដោយមិនគិតថាអ្នកប្រើប្រាស់មានចេតនាឬមិនមានចេតនាក្លែងបន្លំនោះទេ។\n4. អ្នកប្រើប្រាស់ត្រូវធានាថា ខណៈពេលដែលអ្នកប្រើប្រាស់បានផ្ទៀងផ្ទាត់ខ្លួនឯង តាមរយៈដំណើរការនៃការផ្ទៀងផ្ទាត់អត្តសញ្ញាណជាមួយ OTP ឬ (One Time Password) គឺជាសំណុំនៃពាក្យសម្ងាត់ដែលអាចប្រើបានតែម្តងប៉ុណ្ណោះ។ វាមានគោលបំណងធានាការចូលប្រើកម្មវិធី ផ្ញើតាមរយៈសារជាអក្សរនៃលេខទូរស័ព្ទ ដើម្បីអនុញ្ញាតឱ្យអ្នកប្រើប្រាស់ប្រើប្រាស់ OTP ដើម្បីផ្ទៀងផ្ទាត់អត្តសញ្ញាណរបស់ពួកគេ ថាអ្នកជាអ្នកចូលប្រើកម្មវិធីនេះតាមដំណើរការកំណត់អត្តសញ្ញាណ ផ្ទៀងផ្ទាត់និងផ្ទៀងផ្ទាត់អត្តសញ្ញាណរបស់ក្រុមហ៊ុន អ្នកប្រើសេវាកម្មបានធ្វើដោយខ្លួនឯង ហើយមិនមែនកូនភ្លោះ ឬអ្នកណាផ្សេងទៀតដែលមានរូបរាងស្រដៀងនឹងអ្នកប្រើប្រាស់នោះទេ។ អ្នកប្រើត្រូវធានាថា ប្រតិបត្តិការណាមួយដោយគណនីអ្នកប្រើប្រាស់ តាមរយៈដំណើរការផ្ទៀងផ្ទាត់ OTP ឬ One Time Password ជាប្រតិបត្តិការរបស់អ្នកប្រើប្រាស់ដោយផ្ទាល់ ហើយគណនីយនេះគឺពេញលេញ និងត្រឹមត្រូវ។ ប្រសិនបើមានការខូចខាតណាមួយដែលកើតឡើងពីប្រតិបត្តិការដែលបានដំណើរការនោះ អ្នកប្រើប្រាស់ត្រូវទទួលស្គាល់ថាក្រុមហ៊ុនមិនទទួលខុសត្រូវចំពោះការខូចខាតណាមួយឡើយ។\n5. អ្នកប្រើប្រាស់ ត្រូវធានាថានឹង ទទួលខុសត្រូវ ដោយការអនុលោមតាមនិងសហការ រួមទាំងការយល់ព្រមផ្តល់ព័ត៌មាន និង/ឬផ្ដល់នូវភស្តុតាងឯកសារ ឬឯកសារបន្ថែមណាមួយដែលក្រុមហ៊ុនអាចស្នើសុំឱ្យដាក់ជូន ក្នុងគោលបំណងពិចារណាលើការបម្រើសេវាកម្ម ប្រតិបត្តិការហិរញ្ញវត្ថុផ្សេងៗ ឬអនុលោមតាមតម្រូវការ ការិយាល័យប្រឆាំងការលាងលុយកខ្វក់ ឬបទប្បញ្ញត្តិផ្លូវច្បាប់ណាមួយដែលទាក់ទងនឹងការអនុវត្តលក្ខខណ្ឌនៃសេវាកម្មច្បាប់នេះ ដែលមានស្រាប់ និង/ឬត្រូវធ្វើឡើងនាពេលអនាគតយ៉ាងតឹងរ៉ឹង។',
                                  ),
                                  TextSpan(
                                    text:
                                    ' \n\nដែនកំណត់នៃការទទួលខុសត្រូវរបស់ក្រុមហ៊ុន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n1. ក្រុមហ៊ុននឹងផ្តល់សេវាកម្មល្អបំផុត ដើម្បីធានាថាអ្នកប្រើប្រាស់ ទទួលបានសេវាកម្មជាទីគាប់ចិត្ត ដូច្នេះហើយប្រសិនបើមានសេវាកម្មណាមួយរបស់ក្រុមហ៊ុនអាចមានកំហុសឆ្គង ការយឺតយ៉ាវ មិនថាបណ្តាលមកពីប្រព័ន្ធកុំព្យូទ័រ ប្រព័ន្ធអ៊ីនធឺណិត ឧបករណ៍អេឡិចត្រូនិកផ្សេងៗ ឬប្រព័ន្ធផ្សេងទៀតដែលទាក់ទងនឹងការផ្តល់សេវា រួមទាំងបណ្តាញទូរស័ព្ទចល័ត ឬមិនថាដោយហេតុផលផ្សេងៗណាមួយក៏ដោយ ហើយដោយមិនគិតពីហេតុផលណាមួយ អ្នកប្រើប្រាស់ត្រូវយល់ព្រមថាមិនលើកយកហេតុផលកំហុសឆ្គងខាងលើ មកជារហេតុផលនៃការទាមទារសំណង់ ការខូចខាតណាមួយពីក្រុមហ៊ុននោះទេ។\n2. ក្រុមហ៊ុនរក្សាសិទ្ធិមិនទទួលខុសត្រូវចំពោះការបាត់បង់ ឬការខូចខាតមិនថាដោយផ្ទាល់ ឬដោយប្រយោល ឬដោយប្រយោល ដែលបានកើតឡើងចំពោះទិន្នន័យនៅក្នុងទូរស័ព្ទដៃ ឬទូរស័ព្ទចល័ត ឬឧបករណ៍ផ្សេងទៀត ដោយសារតែការប្រើប្រាស់សេវាកម្មរបស់អ្នកប្រើប្រាស់នោះឡើយ។\n3. ក្រុមហ៊ុនគ្រាន់តែជាអ្នកផ្តល់សេវា ដែលផ្តល់ជូនតាមរយៈកម្មវិធី និងបណ្តាញផ្សេងទៀត (ប្រសិនបើមាន) ដូចដែលក្រុមហ៊ុនបានបញ្ជាក់តែប៉ុណ្ណោះ ក្រុមហ៊ុនពុំបានទទួលដឹងលឺ ឬចូលរួម ឬគាំទ្រក្នុងសកម្មភាពណាមួយ នៃអ្នកប្រើប្រាស់ ទាក់ទងនឹងការប្រើប្រាស់សេវាកម្ម និង/ឬការប្រើប្រាស់កម្មវិធី រួមទាំងបណ្តាញផ្សេងទៀតនោះទេ។\nប្រសិនបើអ្នកប្រើសេវាកម្មបានចាត់វិធានការណាមួយ។ ថាតើចេតនាឬអត់ ដែលជាទង្វើខុសច្បាប់ និង/ឬបង្កការខូចខាតដល់អ្នកដទៃ ដោយផ្ទាល់ ឬដោយប្រយោល។ អ្នកប្រើប្រាស់យល់ព្រមទទួលខុសត្រូវចំពោះសកម្មភាពនេះ។ និង/ឬការខូចខាតដល់ភាគីទីបី ឬអ្នកដែលបានទទួលការខូចខាតដោយផ្ទាល់\n4. ក្រុមហ៊ុនរក្សាសិទ្ធិក្នុងការទទួលខុសត្រូវចំពោះណាមួយ ដែលទាក់ទងនឹងច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន ឬច្បាប់ផ្សេងទៀត ដោយសារតែអ្នកប្រើប្រាស់លាក់បាំងព័ត៌មាន ឬផ្តល់ព័ត៌មានមិនក្លែងក្លយដល់ក្រុមហ៊ុន ប្រសិនបើអ្នកប្រើប្រាស់លាក់បាំងព័ត៌មាន ឬក្លែងបន្លំព័តមាន អ្នកប្រើប្រាស់ត្រូវយល់ព្រមទទួលខុសត្រូវចំពោះការខូចខាតណាមួយ ដែលបានកើតឡើងចំពោះក្រុមហ៊ុន និង/ឬបុគ្គលផ្សេងទៀតទាំងអស់។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nព័តមានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nអ្នកប្រើប្រាស់យល់ព្រម និងទទួលស្គាល់វាថា ក្រុមហ៊ុនអាចប្រមូល ប្រើប្រាស់ បង្ហាញ ឬស្នើសុំ ព័ត៌មានផ្ទាល់ខ្លួន ប៉ុន្តែមិនកំណត់ចំពោះ ព័ត៌មានអំពី នាមត្រកូល-ឈ្មោះ ត្រូវការព័ត៌មានចាំបាច់ដូចជា ព័តមានទំនាក់ទំនង ទីតាំង (location) សៀវភៅទូរស័ព្ទ (phonebook) របស់អ្នកប្រើប្រាស់ និង/ដែលអ្នកប្រើប្រាស់បានផ្តល់ឱ្យក្រុមហ៊ុន និង/ឬ ជាកម្មសិទ្ធិរបស់ក្រុមហ៊ុន និង/ឬដែលក្រុមហ៊ុនទទួលបាន ឬប្រមូលពី\nប្រភពផ្សេងទៀត ឬបុគ្គលដទៃដោយ ស្របច្បាប់ សម្រាប់គោលបំណងពិចារណាលើការស្នើសុំ ចុះឈ្មោះគណនី ការផ្ទៀងផ្ទាត់/ការផ្ទៀងផ្ទាត់អត្តសញ្ញាណ ការប្រើប្រាស់សេវាកម្ម ការសុំជំនួយ ការទាក់ទងសាកសួរ ធ្វើស្ថិតិ វិភាគទិន្នន័យ ដើម្បីបង្កើតសេវាកម្ម ឬផ្តល់អត្ថប្រយោជន៍ដែលអាចផ្តល់អត្ថប្រយោជន៍ដល់អ្នកប្រើប្រាស់ ឬផ្តោតលើការផ្តល់ជូននូវសេវាកម្មដ៏ល្អដល់អ្នកប្រើប្រាស់\nការការពារការក្លែងបន្លំ និងរក្សាសុវត្ថិភាពគណនី ការបញ្ជូនព័ត៌មាន រួមទាំងប្រតិបត្តការណាមួយ សម្រាប់គោលបំណងណាមួយ។ ទាក់ទងនឹងសេវាកម្ម ដែលអ្នកប្រើប្រាស់អាចចាប់អារម្មណ៍ ឬមានប្រយោជន៍ចំពោះអ្នកប្រើប្រាស់ តាមរយៈវិធីសាស្ត្រការទំនាក់ទំនង ឬបណ្តាញទំនាក់ទំនងដែលអ្នកប្រើប្រាស់បានផ្ដល់ជូន ក្រុមហ៊ុនមានសិទ្ធិស្របច្បាប់ ទាក់ទងនឹងការផ្តល់សេវា\nហើយអ្នកប្រើប្រាស់ត្រូវយល់ព្រមថាក្រុមហ៊ុនអាចបញ្ជូន ឬផ្ទេរព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកប្រើប្រាស់ទៅកាន់អង្គភាពដែលមានការគ្រប់គ្រងលើក្រុមហ៊ុន ឬស្ថិតនៅក្រោមការគ្រប់គ្រងរបស់ក្រុមហ៊ុន ឬនីតិបុគ្គលដែលស្ថិតក្រោមការគ្រប់គ្រងរបស់ក្រុមហ៊ុន រួមទាំងនីតិបុគ្គល ឬបុគ្គលផ្សេងទៀតដែលក្រុមហ៊ុនជាភាគីកិច្ចសន្យា ឬមានទំនាក់ទំនងផ្លូវច្បាប់ ឬជាបុគ្គលខាងក្រៅ ឬនីតិបុគ្គលដែលទទួលខុសត្រូវក្នុងដំណើរការទិន្នន័យផ្ទាល់ខ្លួនទាំងខាងក្នុង និងខាងក្រៅ\nដូចនេះ ក្រុមហ៊ុនមានភាពចាំបាច់ក្នុងការបញ្ជូន ឬផ្ទេរព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកប្រើប្រាស់ទៅឱ្យបុគ្គលដទៃ ឬនីតិបុគ្គលនៅក្រៅប្រទេស តាមស្តង់ដារការពារទិន្នន័យផ្ទាល់ខ្លួនមិនគ្រប់គ្រាន់តាមតម្រូវការរបស់ច្បាប់។ ក្រុមហ៊ុនធានាថាក្រុមហ៊ុននឹងចាត់វិធានការដើម្បីការពារទិន្នន័យផ្ទាល់ខ្លួនដែលសមរម្យ និងស្របតាមស្តង់ដារដែលច្បាប់បានកំណត់។\nអ្នកប្រើប្រាស់អាចត្រួតពិនិត្យ និងសិក្សាព័ត៌មានលម្អិតផ្សេងៗ តាមរយៈគោលការណ៍ឯកជនភាពរបស់ក្រុមហ៊ុន បាននៅទីនេះ',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nភាសា',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nការកែសម្រួលលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្មច្បាប់នេះ មិនថាមានប្ដូរជាភាសាណាមួយក៍ដោយ គ្រាន់តែសម្រាប់សម្រួលភាពងាយស្រួលដល់អ្នកប្រើប្រាស់តែប៉ុណ្ណោះ ពុំមានចេតនាអ្វីផ្សេងឡើយ ដើម្បីកែសម្រួលលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្ម នៅក្នុងករណីនៃជម្លោះរវាងកំណែទម្រង់ភាសាខ្មែរនិងភាសាដទៃទៀត ត្រូវយោងតាមច្បាប់ជាភាសាខ្មែរ។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nសំណួរនិងការស្នើរសុំ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n1. ក្រុមហ៊ុននឹងពិចារណាលើវិធីសាស្រ្តនៃការទំនាក់ទំនង ផ្តល់ព័ត៌មានដល់សមាជិកដោយមធ្យោបាយដែលក្រុមហ៊ុនយល់ឃើញថាសមរម្យ ដូចជាការបង្ហោះនៅលើគេហទំព័រ ឬបណ្តាញទំនាក់ទំនងណាមួយដែលសមាជិកបានផ្តល់ព័ត៌មានដល់ក្រុមហ៊ុន។\n2. សមាជិកអាចទាក់ទងក្រុមហ៊ុនតាមរយៈបណ្តាញទាំងនេះ ដូចជា\nសម្រាប់ការសាកសួរ ការផ្ដល់យោបល់អំពីគំរូសេវាកម្ម អត្ថប្រយោជន៍ និងព័ត៌មានអំពីសកម្មភាព រាយការណ៍ពីបញ្ហាក្នុងការប្រើប្រាស់កម្មវិធី សូមទាក់ទង <EMAIL>',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }
  static buildPrivacyPolicyAICP(context){
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.string('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 2.50001V2.50001C16.3807 2.50001 17.5 3.61929 17.5 5.00001L17.5 6.78572C17.5 6.98521 17.5 7.08495 17.4719 7.16514C17.4217 7.30876 17.3088 7.42169 17.1651 7.47194C17.085 7.5 16.9852 7.5 16.7857 7.5L12.5 7.5M15 2.50001V2.50001C13.6193 2.50001 12.5 3.61929 12.5 5.00001L12.5 7.5M15 2.50001L6.5 2.50001C4.61438 2.50001 3.67157 2.50001 3.08579 3.08579C2.5 3.67158 2.5 4.61439 2.5 6.50001L2.5 17.5L5 16.6667L7.5 17.5L10 16.6667L12.5 17.5L12.5 7.5" stroke="#EA1B23"/> <path d="M5.83301 5.83325L9.16634 5.83325" stroke="#EA1B23" stroke-linecap="round"/> <path d="M6.66699 9.16675H5.83366" stroke="#EA1B23" stroke-linecap="round"/> <path d="M5.83301 12.5L8.33301 12.5" stroke="#EA1B23" stroke-linecap="round"/></svg>'),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    "នៃការការពារព័ត៌មានផ្ទាល់ខ្លួន",
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      // height: 0.10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  const TextSpan(
                                    text:
                                    "\nAICP Motor ផ្ដោតសំខាន់លើឯកជនភាព និងការការពារព័ត៌មានផ្ទាល់ខ្លួនរបស់អតិថិជន អ្នកប្រើប្រាស់ ទើបក្រុមហ៊ុនបានបង្កើតគោលការណ៍ការពារទិន្នន័យផ្ទាល់ខ្លួននេះឡើង ដើម្បីជូនដំណឹងដល់អតិថិជន អំពីការអនុវត្តរបស់ក្រុមហ៊ុនសម្រាប់ការប្រមូល ការប្រើប្រាស់ និងការបង្ហាញព័ត៌មានផ្ទាល់ខ្លួន ដូច្នេះអ្នកអាចប្រើប្រាស់សេវាកម្មបានតាមរយៈកម្មវិធី AICP Motor\n\nដែលបានចែង និងអាចនឹងកើតឡើងនាពេលអនាគត រួមទាំងមិនកំណត់ចំពោះសេវាកម្មណាមួយនោះឡើយ ពាក់ព័ន្ធនឹងការប្រើប្រាស់កម្មវិធី គេហទំព័រ និងបណ្តាញនានា\nដែលផ្តល់សេវាកម្មដោយក្រុមហ៊ុន (ជាសមូហភាព \"សេវាកម្ម\") ដោយមានទំនុកចិត្ត។",
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nនិយមន័យ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nព័ត៌មានផ្ទាល់ខ្លួនរបស់សមាជិកដែលត្រូវប្រមូលមាន ដូចខាងក្រោម៖\n\n(1) ឈ្មោះនិងនាមត្រកូល\n(2) ថ្ងៃខែឆ្នាំកំណើត\n(3) ទីក្រុង\n(4) ព័ត៌មានផ្ទាល់ខ្លួនរបស់បុគ្គលផ្សេងទៀត ដែលអ្នកបានផ្តល់ឱ្យក្រុមហ៊ុន ឬក្រុមហ៊ុនបានទទួល\n(5) ទិន្នន័យរបស់ឧបករណ៍ ដែលចាំបាច់ត្រូវប្រមូលមានដូចជា ដូចជាម៉ូដែល Hardware របស់អ្នក កំណែប្រព័ន្ធប្រតិបត្តិការ ទិន្នន័យបណ្តាញទូរស័ព្ទចល័ត រួមទាំងលេខទូរស័ព្ទពេលចុច Report ដើម្បីរាយការណ៍បញ្ហាតែប៉ុណ្ណោះ\n(6) ទិន្នន័យទីតាំង ដំណើរការព័ត៌មានអំពីទីតាំងរបស់អ្នក នឹងប្រើដោយបច្ចេកវិទ្យាកំណត់ទីតាំងដូចជាអាសយដ្ឋាន IP, GPS',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nវិធីសាស្រ្តប្រមូលព័ត៌មានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n\nការប្រមូលព័ត៌មានផ្ទាល់ខ្លួនតាមរយៈតាមរយៈវិធីសាស្រ្ដផ្សេងៗ នឹងទទួលបានព័ត៌មានសមាជិកពីហាងដែលពាក់ព័ន្ធ ហើយអាចប្រមូលព័ត៌មានផ្ទាល់ខ្លួនពីសកម្មភាពផ្សេងៗទៀត សមាជិកនឹងផ្តល់ព័ត៌មានដោយផ្ទាល់ទៅក្រុមហ៊ុន ឬតាមរយៈគេហទំព័រ https://aicpmotor.com និងកម្មវិធី AICP របស់ក្រុមហ៊ុន ឬពីប្រភពផ្សេងពីសមាជិកដោយផ្ទាល់ ដូចជាពីអតិថិជនផ្សេងទៀតនៃហាង សមាគមពាណិជ្ជកម្ម ឬអង្គការផ្សេងទៀតជាដើម\n\n\"មូលដ្ឋានសម្រាប់ការប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក និងគោលបំណងនៃដំណើរការ\nក្រុមហ៊ុននឹងប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក ក្រោមមូលដ្ឋានច្បាប់ និងអនុវត្តគោលបំណងដូចខាងក្រោម\nមូលដ្ឋានសម្រាប់ការអនុវត្តកិច្ចសន្យាដែលអ្នកជាភាគី ឬដើម្បីដំណើរការសំណើរបស់អ្នកមុនពេលចុះកិច្ចសន្យា រួមទាំង ការដែលមិនកំណត់ចំពោះ',
                                  ),

                                  const TextSpan(
                                    text: '\n◦ ការពិចារណាលើការស្នើរសុំចុះឈ្មោះចូលប្រាស់កម្មវិធី។\n◦ អ្វីផ្សេងទៀតដែលចាំបាច់ដើម្បីផ្តល់សេវាកម្មល្អបំផុតពីក្រុមហ៊ុន។\n◦ការផ្ដល់សេវាកម្មពីសុំជំនួយ ទំនាក់ទំនងសាកសួរព័ត៌មាន ទទួលយកការស្នើសុំ ទទួលព័ត៌មាន ឬសម្រាប់ការសាកសួរអំពីសេវាកម្មផ្សេងៗ\n◦ ការអភិវឌ្ឍន៍ បង្កើនគុណភាពនៃការផ្ដល់សេវាកម្ម បង្កើនប្រសិទ្ធភាពសេវាកម្ម និងការធ្វើអោយមានភាពងាយស្រួលសម្រាប់លោកអ្នកដែលចូលមកប្រើប្រាស់សេវាកម្ម។\n◦ ការប្រូមូលព័ត៌មានដើម្បីផ្តល់សេវាកម្ម។\n◦ ក្នុងគោលបំណងណាមួយ ដែលទាក់ទងនឹងសេវាកម្ម ដែលអ្នកអាចចាប់អារម្មណ៍ ឬមាន ប្រយោជន៍ដល់អ្នក តាមរយៈបណ្តាញជូនដំណឹងរបស់ក្រុមហ៊ុន និងបណ្ដាយទំនាក់ទំនងដែលអ្នកបានផ្ដល់អោយ ក្រុមហ៊ុន ដែលស្របច្បាប់ទាក់ទងនឹងការផ្តល់សេវាកម្ម។\n◦ គ្រប់ដំណើរការផ្សេងៗទៀតទាក់ទងនឹងសេវាកម្ម ដែលអាចប្រយោជន៍សម្រាប់អ្នក\nមូលដ្ឋានសម្រាប់ការអនុវត្តកាតព្វកិច្ចតាមផ្លូវច្បាប់ទាំងអស់ដោយគ្មានការលើកលែង។\n◦ អនុលោមតាមច្បាប់ដែលពាក់ព័ន្ធជាមួយក្រុមហ៊ុន ដែលក្រុមហ៊ុនមានកាតព្វកិច្ចគោរពតាម ដើម្បីអនុវត្តតាមច្បាប់ បទប្បញ្ញត្តិ គោលការណ៍ណែនាំ ដែលបានចែងដោយអាជ្ញាធរអនុវត្តច្បាប់ ឬអាជ្ញាធរគ្រប់គ្រង ដែលពាក់ព័ន្ធទាំង ដោយគ្មានការលើកលែង\n◦គោលបំណងដើម្បីវិភាគ ឬប៉ានស្មានអំពីចំណូលចិត្ត ឬអាកប្បកិរិយារបស់អ្នក រួមទាំងការស្រាវជ្រាវ ការអភិវឌ្ឍន៍ ការកែសម្រួលផលិតផល និងផែនការទីផ្សារ សម្រាប់សាខារបស់ក្រុមហ៊ុន បានផ្សព្វផ្សាយនូវផលិតផល និងសេវាកម្ម អត្ថប្រយោជន៍ ផ្សព្វផ្សាយពាណិជ្ជកម្មនិងការស្នើសុំផ្សេងៗ របស់ក្រុមហ៊ុនដែលពាក់ព័ន្ធ ឬដៃគូអាជីវកម្មរបស់ក្រុមហ៊ុនដែលពាក់ព័ន្ធ ដែលបានវិភាគ និងជ្រើសរើសយ៉ាងសមរម្យសម្រាប់អ្នក។ \n◦ គោលបំណងផ្សេងៗទៀតស្របច្បាប់ដែលក្រុមហ៊ុនអាចស្វែងរកការនូវចំណូលចិត្តរបស់អ្នកបានជាខណៈពេល ណាមួយ ដូចជាដើម្បីជួយសម្រួលអ្នកក្នុងការស្នើសុំប្រើប្រាស់សេវាកម្មផ្សេងទៀតតាមរយៈកម្មវិធីនេះ។ផលប្រយោជន៍ស្របច្បាប់របស់ក្រុមហ៊ុន ឬរបស់នីតិបុគ្គលក្រៅពីក្រុមហ៊ុន លុះត្រាតែអត្ថប្រយោជន៍បែបនេះមិនសំខាន់ជាងសិទ្ធិជាមូលដ្ឋានរបស់អ្នកចំពោះទិន្នន័យផ្ទាល់ខ្លួន ដោយគ្មានការលើកលែង។\n◦ ការទប់ស្កាត់អំពើទុច្ចរិត និងធានាសុវត្ថិភាពគណនីរបស់លោកអ្នក មូលដ្ឋានផលប្រយោជន៍សាធារណៈសំខាន់ៗ ក្រុមហ៊ុនបានដាក់ចាត់វិធានការសមស្របដើម្បីការពារសិទ្ធិ និងផលប្រយោជន៍ជាមូលដ្ឋានរបស់អ្នកទាំងអស់ដោយគ្មានការលើកលែង។\n◦ ការផ្ទៀងផ្ទាត់/ផ្ទៀងផ្ទាត់អត្តសញ្ញាណ ដើម្បីការពារឧក្រិដ្ឋកម្ម និងការក្លែងបន្លំ\n◦ ការសង្ស័យថាមានការគាំទ្រផ្នែកហិរញ្ញវត្ថុសម្រាប់អំពើភេរវកម្ម ឬការលាងលុយកខ្វក់។',
                                  ),
                                  const TextSpan(
                                    text: '\n\nបន្ថែមលើមូលដ្ឋានច្បាប់ និងគោលបំណងខាងលើ ក្រុមហ៊ុនអាចប្រមូល ប្រើប្រាស់ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកក្រោមមូលដ្ឋានច្បាប់ផ្សេងទៀត ដូចជាដើម្បីការពារ ឬទប់ស្កាត់គ្រោះថ្នាក់ដល់អាយុជីវិត ឬសុខភាពរបស់អ្នក ឬដើម្បីសម្រេចបាននូវគោលបំណង ដែលទាក់ទងនឹងការរៀបចំឯកសារប្រវត្តិសាស្ត្រ ឬអត្ថបងសម្រាប់ជាប្រយោជន៍សាធារណៈ។ល។ ប្រសិនបើទិន្នន័យផ្ទាល់ខ្លួនត្រូវបានប្រមូល ប្រើប្រាស់ ឬបង្ហាញក្រោមគោលបំណងផ្សេងទៀតដោយផ្អែកលើមូលដ្ឋានទាំងនេះ ក្រុមហ៊ុននឹងជូនដំណឹងនៅពេលក្រោយ។ \n\nការបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកទៅកាន់ភាគីទីបី និងការប្រមូលព័ត៌មានផ្ទាល់ខ្លួនពីប្រភពផ្សេងៗ សម្រាប់ការអនុវត្តនូវគោលបំណងដែលច្បាប់របស់ក្រុមហ៊ុនដូចដែលបានរៀបរាប់ខាងលើ ក្រុមហ៊ុនអាចប្រមូលពី និង/ឬប្រើប្រាស់ បញ្ជូន ផ្ទេរ ប្រូមូល និង/ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនទៅកាន់មនុស្សដូចខាងក្រោម៖ នីតិបុគ្គលដែលគ្រប់គ្រងក្រុមហ៊ុន ឬស្ថិតក្រោមការគ្រប់គ្រងរបស់ក្រុមហ៊ុន រួមទាំងក្រុមហ៊ុនដែលស្ថិតនៅក្រោមការគ្រប់គ្រងក្រុមហ៊ុនផងដែរ នីតិបុគ្គល ឬបុគ្គលផ្សេងទៀតដែលក្រុមហ៊ុនជាភាគីកិច្ចសន្យា ឬមានទំនាក់ទំនងស្របច្បាប់ជាមួយក្រុមហ៊ុន រួមទាំងអ្នកផ្តល់សេវាផ្សេងទៀត ដែលមានសិទ្ធិក្នុងការប្រើប្រាស់ទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក ទាំងខាងក្នុង និងខាងក្រៅ ដូចជាអ្នកផ្តល់សេវាកម្មខាងក្រៅ អ្នកផ្តល់សេវាកម្ម ឬដៃគូសហការផ្សេងទៀតរបស់ក្រុមហ៊ុន។ ក្រុមហ៊ុនធានាថា អ្នកផ្តល់សេវាកម្មទាំងនោះ នឹងប្រើប្រាស់ព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកស្របតាមគោលការណ៍ការពារទិន្នន័យផ្ទាល់ខ្លួន និងច្បាប់ពាក់ព័ន្ធ។\nអ្នកត្រួតពិនិត្យគណនី, អ្នកត្រួតពិនិត្យខាងក្រៅនៃក្រុមហ៊ុន ភ្នាក់ងាររដ្ឋាភិបាល ភ្នាក់ងារផ្ទេរសិទ្ធិ និងបុគ្គលផ្សេងទៀត ឬនីតិបុគ្គល ដែលក្រុមហ៊ុនមានការចាំបាច់ត្រូវផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក សម្រាប់គោលបំណងនៃការអនុវត្តតាមច្បាប់\nអ្នកផ្ទេរអាជីវកម្ម/ពាណិជ្ជកម្ម ក្នុងករណីដែលក្រុមហ៊ុនមានការរួមបញ្ចូល ការផ្ទេរ ការលក់ទ្រព្យសម្បត្តិ និង/ឬអាជីវកម្មទាំងអស់ ឬតែផ្នែកក៏ដោយ។\n\"',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nគោលបំណងនៃការប្រមូលព័ត៌មានផ្ទាល់ខ្លួន',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nនឹងប្រមូល ប្រើប្រាស់ និង/ឬបង្ហាញព័ត៌មានផ្សេងៗ រួមទាំងព័ត៌មានផ្ទាល់ខ្លួនរបស់សមាជិក ព្រោះវាជាការចាំបាច់សម្រាប់ផលប្រយោជន៍ស្របច្បាប់ និង/ឬអ្នកលក់ ឬដោយមានការយល់ព្រមពីសមាជិក ឬដោយសារហេតុផលស្របច្បាប់ផ្សេងទៀត តាមការចាំបាច់សម្រាប់គោលបំណងដូចខាងក្រោមៈ\n (1) ដើម្បីប្រើប្រាស់ រួមទាំងការគ្រប់គ្រង និងរៀបចំព័ត៌មានរបស់អ្នកនៅក្នុងសកម្មភាពទីផ្សារ ឬយោងទៅតាមលក្ខខណ្ឌនៃការចូលរួមក្នុងសកម្មភាពនីមួយៗ\n (2) ដើម្បីផ្តល់ព័ត៌មាន ផ្ដល់ដំបូន្មាន ទំនាក់ទំនងសាធារណៈអំពីផលិតផល សេវាកម្ម ឬសកម្មភាពទីផ្សារតាមរយៈទិន្នន័យដែលអ្នកបានផ្តល់អោយ រួមទាំងការណែនាំពីផលិតផល និងសេវាកម្ម\n (៣) ដើម្បីអភិវឌ្ឍផលិតផល និងសេវាកម្ម ក៏ដូចជាសកម្មភាពផ្សព្វផ្សាយ និងសកម្មភាពទីផ្សារ\n (4) ដើម្បីទំនាក់ទំនង ឆ្លើយតប និងផ្តល់ជំនួយដល់សមាជិក រួមទាំងការផ្ដល់សេវាកម្មក្រោយការមានទិញលក់រួចរាល់។\n (5) ដើម្បីផ្ទៀងផ្ទាត់អត្តសញ្ញាណមុនពេលផ្តល់សេវាកម្មដល់សមាជិក\n (៦) សម្រាប់ការវិភាគ និងអភិវឌ្ឍន៍ទំនិញ និងសេវាកម្ម\n (7) ដើម្បីផ្តល់សេវា និង/ឬសម្រាប់ការទំនាក់ទំនង ការផ្ញើឥវ៉ាន់ឬសំបុត្រតាមប្រៃសណីយ៍  ដូចជាការទំនាក់ទំនងសាកសួរទាក់ទងនឹងការពេញចិត្តចំពោះទំនិញ និងសេវាកម្ម។ សេចក្តីជូនដំណឹងអំពីសកម្មភាពផ្សព្វផ្សាយ ឬការបញ្ចុះតម្លៃពិសេស\n (8) សម្រាប់គោលបំណងនៃការធានារថយន្ត\n (៩) ដើម្បីអនុវត្តតាមកិច្ចសន្យា\n (១០) អនុលោមតាមច្បាប់ និង/ឬ ដើម្បីផលប្រយោជន៍សាធារណៈ\n(១១) ការពារ ឬទប់ស្កាត់គ្រោះថ្នាក់ដល់អាយុជីវិត រាងកាយ ឬសុខភាពរបស់បុគ្គល។\nប្រសិនបើសមាជិកមិនផ្តល់ព័ត៌មានផ្ទាល់ខ្លួនរបស់សមាជិក ប្រហែលជាមិនអាចផ្តល់សេវាកម្មក្នុងកាលៈទេសៈជាក់លាក់ណាមួយបានទេ។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nរយៈពេលក្នុងការរក្សាទុកនូវព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nក្រុមហ៊ុនមានការចាំបាច់ក្នុងការរក្សារព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកទុក ក្នុងរយៈពេលមួយដែល អ្នកនៅតែបន្តប្រើប្រាស់សេវាកម្ម ឬទំនាក់ទំនងជាមួយក្រុមហ៊ុន\nឬនៅតែរក្សាទុក ដរាបណានៅតែចាំបាច់ ដើម្បីសម្រេចតាមគោលបំណងដែលទាក់ទងនឹងការផ្តល់សេវាកម្មរបស់ក្រុមហ៊ុន ដោយអាចនឹងត្រូវប្រមូលតាមក្រោយ ប្រសិនបើស្របច្បាប់បានចែង\nដូចជា ច្បាប់ស្តីពីការពារនិងប្រឆាំងការលាងលុយកខ្វក់ ឬសម្រាប់គោលបំណងផ្ទៀងផ្ទាត់ វិវាទដែលអាចកើតមាននៅក្នុងលក្ខន្តិកៈនៃការកំណត់ តាមដែលច្បាប់កំណត់ក្នុងរយៈពេលមិនលើសពី 10 ឆ្នាំ ក្រុមហ៊ុននឹងលុប ឬបំផ្លាញព័ត៌មានផ្ទាល់ខ្លួន ឬធ្វើអោយព័ត៌មានដែលមិនអាចកំណត់អត្តសញ្ញាណផ្ទាល់ខ្លួនអំពីអ្នក ឬនៅពេលដែលលែងមានភាពចាំបាច់ ឬផុតកំណត់តាមពេលវាលាដែលបានរៀបរាបខាងលើ។\nសិទ្ធិរបស់អ្នកតាមច្បាប់ការការពារព័ត៌មានផ្ទាល់ខ្លួន\nក្នុងនាមអ្នកជាម្ចាស់ទិន្នន័យផ្ទាល់ខ្លួន ស្របតាមច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន អ្នកមានសិទ្ធិស្របច្បាប់ក្នុងការដំណើរការដូចខាងក្រោម៖\n1.  សិទ្ធិក្នុងការដកកិច្ចព្រមព្រៀងរបស់អ្នក\nអ្នកមានសិទ្ធិដកចេញនូវកិច្ចព្រមព្រៀងរបស់អ្នកបាន ដែលអ្នកបានផ្តល់ឱ្យក្រុមហ៊ុន ដែលអ្នកបានយល់ព្រមរបស់អ្នកជាមួយក្រុមហ៊ុនបានគ្រប់ពេលវេលា ក្នុងអំឡុងពេលដែលព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកនៅជាមួយក្រុមហ៊ុន។\n2. សិទ្ធិក្នុងការស្នើសុំចូលប្រើ និងទទួលបានច្បាប់ចម្លងនៃទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នក អ្នកមានសិទ្ធិស្នើសុំការចូលប្រើទិន្នន័យផ្ទាល់ខ្លួនដែលអ្នកផ្តល់ឱ្យក្រុមហ៊ុន ឬស្នើសុំឱ្យក្រុមហ៊ុនធ្វើច្បាប់ចម្លងទិន្នន័យផ្ទាល់ខ្លួនបែបនេះ ជាទម្រង់ដែលអាចត្រូវបានអាន ឬប្រើជាទូទៅដោយឧបករណ៍ ឬឧបករណ៍ស្វ័យប្រវត្តិឬប្រើ ឬបង្ហាញព័ត៌មានផ្ទាល់ខ្លួនបែបនេះដោយមធ្យោបាយស្វ័យប្រវត្តិ (ក្នុងករណីក្រុមហ៊ុនបានរៀបចំ)។ ឬស្នើសុំឱ្យក្រុមហ៊ុនបញ្ជូន ឬផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនបែបនេះទៅកាន់អ្នកត្រួតពិនិត្យទិន្នន័យផ្ទាល់ខ្លួនផ្សេងទៀតតាមតម្រូវការដោយច្បាប់។ ឬទទួលបានព័ត៌មានផ្ទាល់ខ្លួនដែលក្រុមហ៊ុនផ្ញើ ឬផ្ទេរទៅឱ្យអ្នកត្រួតពិនិត្យទិន្នន័យផ្ទាល់ខ្លួនផ្សេងទៀត (លើកលែងតែបច្ចេកទេសមិនអាចធ្វើទៅបាន) រួមទាំងការស្នើសុំឱ្យក្រុមហ៊ុនបង្ហាញការទទួលបានទិន្នន័យផ្ទាល់ខ្លួនដែលអ្នកមិនបានផ្តល់ការយល់ព្រមពីក្រុមហ៊ុនដល់អ្នក។',
                                  ),
                                  const TextSpan(
                                    text: '\n2. អ្នកប្រើប្រាស់សេវាកម្មត្រូវ ធានាថាគាត់នឹងមិនប្រើប្រាស់កម្មវិធី ក្នុងការធ្វើប្រតិបត្តិការហិរញ្ញវត្ថុក្នុងគោលបំណងខុសច្បាប់ណាមួយនោះឡើយ ដូចជាការភ្នាល់ ការជួញដូរផ្លូវភេទ ការលាងលុយកខ្វក់ ឬការកេងប្រវ័ញ្ចគណនី មិនថាសម្រាប់ផលប្រយោជន៍របស់អ្នកប្រើប្រាស់ផ្ទាល់ និង/ឬបុគ្គលផ្សេងទៀត។ ធ្វើអាជីវកម្មក្នុងទម្រង់ផ្សេងៗ រួមទាំងសកម្មភាពដែលបំពានដល់សណ្តាប់ធ្នាប់សាធារណៈ និងសីលធម៌សង្គម រំលោភលើកម្មសិទ្ធិបញ្ញារបស់អ្នកដទៃ ឬអំពើខុសច្បាប់ផ្សេងទៀតៗ រួមទាំងនឹងមិនបណ្តាលឱ្យកម្មវិធី ឬបណ្តាញដែលបានភ្ជាប់ទៅកម្មវិធីត្រូខូចខាត ឬនឹងមិនបង្កការរំខានដល់ការប្រើប្រាស់កម្មវិធីរបស់បុគ្គលដទៃឡើយ។ មិនត្រូវព្យាយាមចូលទៅក្នុងកម្មវិធី ឬបណ្តាញភ្ជាប់ទៅកម្មវិធី ដោយគ្មានការអនុញ្ញាត តាមរយៈការលួចចូល (hacking) លួចចម្លង ឬបំផ្លាញមូលដ្ឋានទិន្នន័យ បំផ្លាញពាក្យសម្ងាត់ឯកជន (password mining) ឬមធ្យោបាយផ្សេងទៀត ប្រសិនបើ\nមានការខូចខាត ណាមួយចំពោះក្រុមហ៊ុន ឬក្រុមហ៊ុនត្រូវទទួលខុសត្រូវចំពោះការទទួលខុសត្រូវណាមួយ ដែលកើតឡើងដោយអ្នកប្រើប្រាស់បានធ្វើប្រតិបត្តិការហិរញ្ញវត្ថុតាមរយៈសេវាកម្មរបស់ក្រុមហ៊ុន សម្រាប់គោលបំណងដែលបានរៀបរាប់ខាងលើ ឬសកម្មភាពរបស់អ្នកប្រើដែលបានប្រព្រឹត្តទៅលើកម្មវិធីដួចដែលបានរៀបរាប់ ក្រុមហ៊ុនមានសិទ្ធិផ្អាក ឬបញ្ឈប់សេវាកម្មភ្លាមៗ ហើយអ្នកប្រើប្រាស់ត្រូវទទួលខុសត្រូវដោយការសងសំណងដល់ក្រុមហ៊ុនក្នុងគ្រប់ប្រការទាំងអស់ ក្រុមហ៊ុនមិនពាក់ព័ន្ធ ឬទទួលខុសត្រូវចំពោះការខូចខាតទាំងអស់ដែលអាចកើតឡើងនោះទេ។',
                                  ),
                                  const TextSpan(
                                    text: '\n\nដូចនេះ អ្នកអាចទាក់ទងមន្ត្រីការពារទិន្នន័យផ្ទាល់ខ្លួនរបស់ក្រុមហ៊ុន ដើម្បីដាក់ស្នើសុំឱ្យក្រុមហ៊ុនអនុវត្តសិទ្ធិខាងលើ តាមរយៈ Callcenter : 088-5267800 Facebook : https://www.facebook.com/AICPmotor2 ក្រុមហ៊ុននឹងបំពេញសំណើរបស់អ្នកក្នុងរយៈពេលដែលបានបញ្ជាក់ដោយច្បាប់ការពារទិន្នន័យផ្ទាល់ខ្លួន ហើយក្រុមហ៊ុននឹងជូនដំណឹងដល់អ្នកអំពីផលវិបាកដែលនឹងកើតឡើងក្នុងការអនុវត្តសំណើនោះ។ ទោះបីជាយ៉ាងណាក៏ដោយ ក្រុមហ៊ុនមានសិទ្ធិបដិសេធមិនដំណើរការតាមសំណើរបស់អ្នក ក្នុងករណីដែលវាត្រូវបានទាមទារដោយច្បាប់ លើសពីនេះ ក្រុមហ៊ុនអាចគិតថ្លៃសេវាសម្រាប់ដំណើរការស្នើរសុំរបស់អ្នក ដោយផ្អែកគោលការណ៍ថ្លៃសេវាកម្ម និងគោលនយោបាយផ្ទៃក្នុងរបស់ក្រុមហ៊ុន។\n\"',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nការរក្សាសុវត្ថិភាព',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nក្រុមហ៊ុនទទួលស្គាល់ និងផ្ដល់សារៈសំខាន់នៃព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក ដូច្នេះហើយ ទើបក្រុមហ៊ុនកែលម្អ និងអភិវឌ្ឍប្រព័ន្ធសុវត្ថិភាពសម្រាប់ព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកស្របតាមច្បាប់ ទាន់សម័យ និងសុវត្ថិភាពស្របតាមស្តង់ដារអន្តរជាតិគ្រប់ពេលវេលា។\n\nក្រុមហ៊ុននឹងប្រឹងប្រែងឱ្យអស់ពីសមត្ថភាពដើម្បីអនុវត្តតាមគោលការណ៍ឯកជនភាពនេះ និងបានសង្កត់ធ្ងន់ទៅកាន់មន្ត្រី រួមទាំងអ្នកដំណើរការទិន្នន័យផ្ទាល់ខ្លួន នៃក្រុមហ៊ុនដែលមានសិទ្ធិចូលប្រើទិន្នន័យផ្ទាល់ខ្លួន ឬមានតួនាទីស្របច្បាប់ រក្សា និងគោរពសុវត្ថិភាពនៃព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នក។\nក្នុងករណីដែលក្រុមហ៊ុនមានតម្រូវការ ឬត្រូវការផ្ទេរព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកទៅបរទេស ក្រុមហ៊ុននឹងប្រើប្រាស់ស្តង់ដារដូចដែលច្បាប់តម្រូវលើការការពារទិន្នន័យផ្ទាល់ខ្លួនបានកំណត់ ហើយក្រុមហ៊ុននឹងមិនផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នកទៅកាន់ប្រទេសណាមួយ ដែលមិនមានស្តង់ដារការពារទិន្នន័យផ្ទាល់ខ្លួនគ្រប់គ្រាន់ស្របតាមច្បាប់ដែលតម្រូវដោយច្បាប់នោះទេ លុះត្រាតែអ្នកយល់ព្រមឱ្យក្រុមហ៊ុនផ្ញើ ឬផ្ទេរទិន្នន័យផ្ទាល់ខ្លួនរបស់អ្នកទៅកាន់ប្រទេសគោលដៅនោះ។\nនៅក្នុងព្រឹត្តិការណ៍ដែលព័ត៌មានផ្ទាល់ខ្លួនរបស់អ្នកប្រើប្រាស់កំពុងធ្វើចារកម្មនៅលើកុំព្យូទ័រ ទាំងដោយការលួចចូល(hacking)  លួចចម្លង ឬបំផ្លាញមូលដ្ឋានទិន្នន័យ បំផ្លាញពាក្យសម្ងាត់ឯកជន ((password mining)) ឬវិធីសាស្រ្តផ្សេងទៀត ដែលមិនមែនជាកំហុសរបស់ក្រុមហ៊ុន ក្រុមហ៊ុនមានសិទ្ធិបដិសេធចំពោះរការទទួលខុសត្រូវណាមួយបានដែលកើតឡើងដោយសកម្មភាពដែលបានរៀបរាប់ខាងលើ។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nការកែតម្រូវគោលនយោបាយ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nលើសពីនេះ ការប្រើប្រាស់សេវាកម្មរបស់ក្រុមហ៊ុនណាមួយ ត្រូវបានចាត់ទុកថា ជាការទទួលយកគោលការណ៍ឯកជនភាពច្បាប់នេះ ក្រុមហ៊ុនអាចកែប្រែ ឬបន្ថែមគោលការណ៍ឯកជនភាពនេះនៅពេលណាមួយ ហើយនឹងជូនដំណឹងដល់អ្នកប្រើប្រាស់បានជ្រាបការប្រើប្រាស់កម្មវិធីរបស់អ្នក ឬសេវាកម្មណាមួយបន្ទាប់ពីការកែប្រែ ឬការបន្ថែមនោះត្រូវបានបង្ហោះ ចាត់ទុកថាជាការយល់ព្រម ការកែប្រែ ឬបន្ថែមនីមួយៗតែម្ដង។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nភាសា',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\nការកែសម្រួលលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្មច្បាប់នេះ មិនថាមានប្ដូរជាភាសាណាមួយក៍ដោយ គ្រាន់តែសម្រាប់សម្រួលភាពងាយស្រួលដល់អ្នកប្រើប្រាស់តែប៉ុណ្ណោះ ពុំមានចេតនាអ្វីផ្សេងឡើយ ដើម្បីកែសម្រួលលក្ខខណ្ឌនៃការផ្ដល់សេវាកម្ម នៅក្នុងករណីនៃជម្លោះរវាងកំណែទម្រង់ភាសាខ្មែរនិងភាសាដទៃទៀត ត្រូវយោងតាមច្បាប់ជាភាសាខ្មែរ។',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\nសំណួរនិងការស្នើរសុំ',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '\n1. ក្រុមហ៊ុននឹងពិចារណាលើវិធីសាស្រ្តនៃការទំនាក់ទំនង ផ្តល់ព័ត៌មានដល់សមាជិកដោយមធ្យោបាយដែលក្រុមហ៊ុនយល់ឃើញថាសមរម្យ ដូចជាការបង្ហោះនៅលើគេហទំព័រ ឬបណ្តាញទំនាក់ទំនងណាមួយដែលសមាជិកបានផ្តល់ព័ត៌មានដល់ក្រុមហ៊ុន។\n2. សមាជិកអាចទាក់ទងក្រុមហ៊ុនតាមរយៈបណ្តាញទាំងនេះ ដូចជា\nសម្រាប់ការសាកសួរ ការផ្ដល់យោបល់អំពីគំរូសេវាកម្ម អត្ថប្រយោជន៍ និងព័ត៌មានអំពីសកម្មភាព រាយការណ៍ពីបញ្ហាក្នុងការប្រើប្រាស់កម្មវិធី សូមទាក់ទង <EMAIL>',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }
}