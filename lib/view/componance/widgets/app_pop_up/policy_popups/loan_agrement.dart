import 'package:AAMG/controller/request_loan/loan.controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';

import '../../../themes/app_colors.dart';
import '../../../themes/app_textstyle.dart';
import '../../../themes/theme.dart';
import '../../../utils/AppSvgImage.dart';
import 'terms_condition.dart';
import '../../button_widgets/primary_button.dart';

class LoanAgrementWidget {
  static acceptAAMPAYPolicy(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        barrierColor: Colors.black.withOpacity(0.2),
        builder: (context) {
          return GetBuilder<LoanController>(
              init: Get.find<LoanController>(),
              builder: (loanCtl) {
                return WillPopScope(
                  onWillPop: () async {
                    // Prevents the back button from closing the bottom sheet
                    return true;
                  },
                  child: Container(
                      width: Get.width,
                      height: 326.h,
                      decoration: ShapeDecoration(
                        color: configTheme().colorScheme.background,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                      ),
                      child: Column(children: [
                        Container(
                            width: 335.w,
                            margin:
                            EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: 50.h,
                                  padding: const EdgeInsets.only(left: 2, right: 2),
                                  decoration: const BoxDecoration(
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(0x0A000000),
                                        blurRadius: 18,
                                        offset: Offset(3, 6),
                                        spreadRadius: 0,
                                      )
                                    ],
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 2.w,
                                        height: 50.h,
                                        decoration: BoxDecoration(
                                          color: AppColors.AAMPurple,
                                          borderRadius: BorderRadius.circular(50),
                                        ),
                                      ),
                                      SizedBox(width: 12.w),
                                      Container(
                                        child: Text(
                                          "ข้อตกลงและเงื่อนไขการใช้บริการ\nสินเชื่อเอเอเอ็ม",
                                          style: TextStyle(
                                            color: const Color(0xFF1A1818),
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                SizedBox(
                                  child: Text(
                                    "เพื่อรักษาประโยชน์ของตัวท่านสำหรับการให้บริการนี้\nกรุณากดยอมรับข้อตกลงด้านล่าง เพื่อดำเนินการต่อ",
                                    style: TextStyle(
                                      color: const Color(0x7F1A1818),
                                      fontSize: 14,
                                      fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  height: 34.h,
                                ),
                                SizedBox(
                                  height: 20.h,
                                  child: Row(
                                    mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          loanCtl.acceptTermPolicy();
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                            child: Row(
                                              children: [
                                                Container(
                                                  width: 20.w,
                                                  height: 20.h,
                                                  decoration: ShapeDecoration(
                                                    color: loanCtl
                                                        .isAcceptedTermPolicy!.value
                                                        ? AppColors.AAMPurple
                                                        : Colors.transparent,
                                                    shape: OvalBorder(
                                                      side: BorderSide(
                                                          width: 1.w,
                                                          color: AppColors.AAMPurple
                                                              .withOpacity(0.25)),
                                                    ),
                                                  ),
                                                  child: SvgPicture.string(
                                                      AppSvgImage.check),
                                                ),
                                                const SizedBox(width: 10),
                                                SizedBox(
                                                  height: 20,
                                                  child: Text(
                                                    "ยอมรับข้อตกลงการใช้บริการ",
                                                    style: TextStyle(
                                                      color: const Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            )),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          TermsAndConditionWidget.buildTermsAndConditionsAAM_Loan(context);
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                          width: 100.w,
                                          alignment: Alignment.centerRight,
                                          child: SvgPicture.string(
                                              AppSvgImage.icon_more_info),
                                        ),
                                      )
                                    ],
                                  ),
                                )
                              ],
                            )),
                        SizedBox(
                          height: 34.h,
                        ),
                        PrimaryButton(
                            title: "ยอมรับ",
                            onPressed: () async {
                              if (loanCtl.isAcceptedTermPolicy!.value) {
                                // var chk = await loanCtl.updateDigitalAgreement(
                                //     context); //TODO เปิดถ้าปล่อยใช้จริง ใช้สำหรับเก็บข้อมูล appcept policy & set script สำหรับโอนเงินอัตโนมัติ
                                // if (chk) {
                                //   Navigator.pop(context, true);
                                // }
                                loanCtl.AcceptLoanAgreement();
                              }
                            },
                            buttonWidth: 327.w,
                            backgroundColor: AppColors.AAMPurple,
                            backgroundInactiveColor: AppColors.inActiveButtonColor,
                            textColor: Colors.white,
                            isActive: loanCtl.isAcceptedTermPolicy!.value),
                      ])),
                );
              });
        });
  }
}