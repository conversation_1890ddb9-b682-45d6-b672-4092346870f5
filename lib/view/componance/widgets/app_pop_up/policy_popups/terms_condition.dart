import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/privacy_policy.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';

import '../../../../../controller/transalation/km.dart';
import '../../../../../controller/transalation/lo.dart';
import '../../../../../controller/transalation/th.dart';
import '../../../themes/app_textstyle.dart';
import '../../../themes/theme.dart';
import '../../../utils/AppSvgImage.dart';
import '../../componance_widget.dart';

class TermsAndConditionWidget {
  static alertTermAndPolicy(BuildContext context) {
    final country = appConfigService.countryConfigCollection.toString();

    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<PolicyController>(
              init: PolicyController(),
              builder: (policyCtl) {
                return Container(
                    width: Get.width,
                    height: 444.h,
                    decoration: const ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(18),
                          topRight: Radius.circular(18),
                        ),
                      ),
                    ),
                    child: Column(children: [
                      Container(
                          width: 335.w,
                          // height: 206.h,
                          margin: EdgeInsets.only(
                              left: 20.w, right: 20.w, top: 30.h),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 50.h,
                                padding:
                                const EdgeInsets.only(left: 2, right: 2),
                                decoration: const BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x0A000000),
                                      blurRadius: 18,
                                      offset: Offset(3, 6),
                                      spreadRadius: 0,
                                    )
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 2.w,
                                      height: 50.h,
                                      decoration: BoxDecoration(
                                        color: appConfigService
                                            .countryConfigCollection ==
                                            "aam"
                                            ? AppColors.AAMPurple
                                            : appConfigService
                                            .countryConfigCollection ==
                                            "rafco"
                                            ? AppColors.primaryRafco
                                            : AppColors.RPLCMyloanCard2,
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                    ),
                                    SizedBox(width: 12.w),
                                    Container(
                                      // margin: EdgeInsets.only(left: 8.w),
                                      // height: 50.h,
                                      child: Text(
                                        "${policyHeader.tr}",
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818),
                                          // fontSize: 16.sp,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              SizedBox(
                                child: Text(
                                  policyDes.tr,
                                  style: TextStyle(
                                    color: const Color(0x7F1A1818),
                                    fontSize: 14,
                                    fontFamily:
                                    TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 34.h,
                              ),
                              Container(
                                height: 68.h,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      // width: 335,
                                      height: 20,
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                              child: Row(
                                                children: [
                                                  InkWell(
                                                    onTap: () {
                                                      policyCtl
                                                          .acceptTermPolicy();
                                                    },
                                                    child: Container(
                                                      width: 20.w,
                                                      height: 20.h,
                                                      decoration: ShapeDecoration(
                                                        color: policyCtl
                                                            .isAcceptedTermPolicy!
                                                            .value
                                                            ? appConfigService
                                                            .countryConfigCollection ==
                                                            "aam"
                                                            ? AppColors
                                                            .AAMPurple
                                                            : appConfigService
                                                            .countryConfigCollection ==
                                                            "rafco"
                                                            ? AppColors
                                                            .primaryRafco
                                                            : AppColors
                                                            .RPLCMyloanCard2
                                                            : Colors
                                                            .transparent,
                                                        shape: OvalBorder(
                                                          side: BorderSide(
                                                              width: 1.w,
                                                              color: appConfigService
                                                                  .countryConfigCollection ==
                                                                  "aam"
                                                                  ? AppColors
                                                                  .AAMPurple
                                                                  .withOpacity(
                                                                  0.25)
                                                                  : appConfigService
                                                                  .countryConfigCollection ==
                                                                  "rafco"
                                                                  ? AppColors
                                                                  .primaryRafco
                                                                  .withOpacity(
                                                                  0.25)
                                                                  : AppColors
                                                                  .RPLCMyloanCard2
                                                                  .withOpacity(
                                                                  0.25)),
                                                        ),
                                                      ),
                                                      child: SvgPicture.string(
                                                          AppSvgImage.check),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  SizedBox(
                                                    // width: 158,
                                                    height: 20,
                                                    child: Text(
                                                      policyClickTerms.tr,
                                                      style: TextStyle(
                                                        color:
                                                        const Color(0xFF1A1818),
                                                        fontSize: 14,
                                                        fontFamily: TextStyleTheme
                                                            .text_Regular
                                                            .fontFamily,
                                                        fontWeight: FontWeight
                                                            .w400,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              )),
                                          InkWell(
                                            onTap: () {
                                              country == "aam"
                                                  ? buildTermsAndConditionsAAM(
                                                  context)
                                                  : country == "rafco"
                                                  ? buildTermsAndConditionsRAFCO(
                                                  context)
                                                  : country == "rplc"
                                                  ? buildTermsAndConditionsRPLC(
                                                  context)
                                                  : Container();
                                              // : RAFCOPolicy.buildTermsAndConditionsRafco(context);
                                            },
                                            child: Container(
                                              color: Colors.transparent,
                                              child: SvgPicture.string(
                                                  AppSvgImage.icon_more_info),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                    Container(
                                      // width: 335,
                                      height: 20,
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                              child: Row(
                                                children: [
                                                  InkWell(
                                                    onTap: () {
                                                      policyCtl
                                                          .acceptPrivacyPolicy();
                                                    },
                                                    child: Container(
                                                      width: 20.w,
                                                      height: 20.h,
                                                      decoration: ShapeDecoration(
                                                        color: policyCtl
                                                            .isAcceptedPrivacyPolicy!
                                                            .value
                                                            ? appConfigService
                                                            .countryConfigCollection ==
                                                            "aam"
                                                            ? AppColors
                                                            .AAMPurple
                                                            : appConfigService
                                                            .countryConfigCollection ==
                                                            "rafco"
                                                            ? AppColors
                                                            .primaryRafco
                                                            : AppColors
                                                            .RPLCMyloanCard2
                                                            : Colors
                                                            .transparent,
                                                        shape: OvalBorder(
                                                          side: BorderSide(
                                                              width: 1.w,
                                                              color: appConfigService
                                                                  .countryConfigCollection ==
                                                                  "aam"
                                                                  ? AppColors
                                                                  .AAMPurple
                                                                  .withOpacity(
                                                                  0.25)
                                                                  : appConfigService
                                                                  .countryConfigCollection ==
                                                                  "rafco"
                                                                  ? AppColors
                                                                  .primaryRafco
                                                                  .withOpacity(
                                                                  0.25)
                                                                  : AppColors
                                                                  .RPLCMyloanCard2
                                                                  .withOpacity(
                                                                  0.25)),
                                                        ),
                                                      ),
                                                      child: SvgPicture.string(
                                                          AppSvgImage.check),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  SizedBox(
                                                    // width: 192,
                                                    height: 20.h,
                                                    child: Text(
                                                      policyClickPrivacy.tr,
                                                      style: TextStyle(
                                                        color:
                                                        const Color(0xFF1A1818),
                                                        fontSize: 14,
                                                        fontFamily: TextStyleTheme
                                                            .text_Regular
                                                            .fontFamily,
                                                        fontWeight: FontWeight
                                                            .w400,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              )),
                                          InkWell(
                                            onTap: () {
                                              country == "aam"
                                                  ? PrivacyPolicyWidget
                                                  .buildPrivacyPolicyAAM(
                                                  context)
                                                  : country == "rafco"
                                                  ? PrivacyPolicyWidget
                                                  .buildPrivacyPolicyRAFCO(
                                                  context)
                                                  : country == "rplc"
                                                  ? PrivacyPolicyWidget
                                                  .buildPrivacyPolicyRPLC(
                                                  context)
                                                  : Container();
                                            },
                                            child: Container(
                                              color: Colors.transparent,
                                              child: SvgPicture.string(
                                                  AppSvgImage.icon_more_info),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                      SizedBox(
                        height: 116.h,
                      ),
                      PrimaryButton(
                          title: policyClickAll.tr,
                          onPressed: () async {
                            var chk = await policyCtl.acceptAllPolicy();
                            if (chk) {
                              Navigator.pop(context, true);
                            }
                          },
                          buttonWidth: 327.w,
                          backgroundColor:
                          appConfigService.countryConfigCollection == "aam"
                              ? AppColors.AAMPurple
                              : AppColors.textBlackColor,
                          backgroundInactiveColor:
                          AppColors.inActiveButtonColor,
                          textColor: Colors.white,
                          isActive: policyCtl.isAcceptedPrivacyPolicy!.value &&
                              policyCtl.isAcceptedTermPolicy!.value
                              ? true
                              : false),
                    ]));
              });
        });
  }

  static buildTermsAndConditionsAAM(context) {
    String language = Get.locale?.languageCode ?? 'th';
    print(language);
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.string(
                                    '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#792AFF"/><path d="M9.993 12.5H10.0005" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 10L10 6.66667" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    language == 'en'
                                        ? Th()
                                        .messages['termAndCondition_aam']
                                        .toString()
                                        : termAndCondition_aam.tr,
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                      // height: 0.10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages[
                                    'termAndConditionDesc_aam']
                                        .toString()
                                        : termAndConditionDesc_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['definition_aam']
                                        .toString()
                                        : definition_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['definitionDesc_aam']
                                        .toString()
                                        : definitionDesc_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['disclosure_aam']
                                        .toString()
                                        : disclosure_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['disclosureDesc1_aam']
                                        .toString() : disclosureDesc1_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['disclosureDesc2_aam']
                                        .toString() : disclosureDesc2_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['disclosureDesc3_aam']
                                        .toString() : disclosureDesc3_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['disclosureDesc4_aam']
                                        .toString() : disclosureDesc4_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['disclosureDesc4_1_aam']
                                        .toString() : disclosureDesc4_1_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['disclosureDesc4_2_aam']
                                        .toString() : disclosureDesc4_2_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['disclosureDesc4_3_aam']
                                        .toString() : disclosureDesc4_3_aam
                                        .tr}',
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' ? Th()
                                        .messages['Intellectual_assets_aam']
                                        .toString() : Intellectual_assets_aam
                                        .tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages[
                                    'Intellectual_assetsDesc_aam']
                                        .toString()
                                        : Intellectual_assetsDesc_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['benefits_aam']
                                        .toString()
                                        : benefits_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['benefits_aamDesc']
                                        .toString()
                                        : benefits_aamDesc.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['cookies_aam']
                                        .toString()
                                        : cookies_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['cookies_aamDesc']
                                        .toString()
                                        : cookies_aamDesc.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th()
                                        .messages['service_and_usage_restrictions_aam']
                                        .toString()
                                        : service_and_usage_restrictions_aam
                                        .tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en'
                                        ? Th()
                                        .messages['service_and_usage_restrictionsDesc1_aam']
                                        .toString()
                                        : service_and_usage_restrictionsDesc1_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en'
                                        ? Th()
                                        .messages['service_and_usage_restrictionsDesc2_aam']
                                        .toString()
                                        : service_and_usage_restrictionsDesc2_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages[
                                    'service_and_usage_restrictionsDesc3_aam']
                                        .toString()
                                        : service_and_usage_restrictionsDesc3_aam
                                        .tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' ? Th()
                                        .messages['accumulated_points_aam']
                                        .toString() : accumulated_points_aam
                                        .tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages[
                                    'accumulated_pointsDesc_aam']
                                        .toString()
                                        : accumulated_pointsDesc_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['amendments_aam']
                                        .toString()
                                        : amendments_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['amendmentsDesc1_aam']
                                        .toString() : amendmentsDesc1_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['amendmentsDesc2_aam']
                                        .toString()
                                        : amendmentsDesc2_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['suggestions_aam']
                                        .toString()
                                        : suggestions_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['suggestionsDesc1_aam']
                                        .toString() : suggestionsDesc1_aam
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['suggestionsDesc2_aam']
                                        .toString()
                                        : suggestionsDesc2_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['contact_aam']
                                        .toString()
                                        : contact_aam.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['contactDesc_aam']
                                        .toString()
                                        : contactDesc_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' ? Th()
                                        .messages['privacy_policy_notice_aam']
                                        .toString() : privacy_policy_notice_aam
                                        .tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages[
                                    'privacy_policy_noticeDesc_aam']
                                        .toString()
                                        : privacy_policy_noticeDesc_aam.tr,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  static buildTermsAndConditionsRPLC(context) {
    String language = Get.locale?.languageCode ?? 'lo';
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.string(
                                    '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#792AFF"/><path d="M9.993 12.5H10.0005" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 10L10 6.66667" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                                    color: AppColors.primaryRPLC_Yellow),
                                SizedBox(width: 9.w),
                                SizedBox(
                                  width: 290.w,
                                  child: FittedBox(
                                    alignment: Alignment.centerLeft,
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      language == 'en' || language == 'th'
                                          ? LO()
                                          .messages['termAndCondition']
                                          .toString()
                                          : termAndCondition.tr,
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 14.sp,
                                        fontFamily: TextStyleTheme
                                            .text_Regular.fontFamily,
                                        fontWeight: FontWeight.w700,
                                        // height: 0.10,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['termAndConditionDesc']
                                        .toString()
                                        : termAndConditionDesc.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO().messages['definition'].toString()
                                        : definition.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['definitionDesc']
                                        .toString()
                                        : definitionDesc.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO().messages['disclosure'].toString()
                                        : disclosure.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc1']
                                        .toString()
                                        : disclosureDesc1.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc2']
                                        .toString()
                                        : disclosureDesc2.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc3']
                                        .toString()
                                        : disclosureDesc3.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc4']
                                        .toString()
                                        : disclosureDesc4.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc5']
                                        .toString()
                                        : disclosureDesc5.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc6']
                                        .toString()
                                        : disclosureDesc6.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['disclosureDesc7']
                                        .toString()
                                        : disclosureDesc7.tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['disclosureDesc8']
                                        .toString()
                                        : disclosureDesc8.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO().messages['Intellectual_assets']
                                        .toString()
                                        : Intellectual_assets.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                      text: language == 'en' || language == 'th'
                                          ? LO()
                                          .messages[
                                      'Intellectual_assetsDesc']
                                          .toString()
                                          : Intellectual_assetsDesc.tr),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['service_and_usage_restrictions']
                                        .toString()
                                        : service_and_usage_restrictions
                                        .tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['service_and_usage_restrictionsDesc1']
                                        .toString()
                                        : service_and_usage_restrictionsDesc1
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['service_and_usage_restrictionsDesc2']
                                        .toString()
                                        : service_and_usage_restrictionsDesc2
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages[
                                    'service_and_usage_restrictionsDesc3']
                                        .toString()
                                        : service_and_usage_restrictionsDesc3
                                        .tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO().messages['amendments'].toString()
                                        : amendments.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['amendmentsDesc']
                                        .toString()
                                        : amendmentsDesc.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO().messages['suggestions']
                                        .toString()
                                        : suggestions.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['suggestionsDesc1']
                                        .toString()
                                        : suggestionsDesc1.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? LO().messages['contact'].toString()
                                        : contact.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? LO().messages['contactDesc1']
                                        .toString()
                                        : contactDesc1.tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? LO()
                                        .messages['contactDesc2']
                                        .toString()
                                        : contactDesc2.tr,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  static buildTermsAndConditionsRAFCO(context) {
    String language = Get.locale?.languageCode ?? 'km';
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          // color: Colors.white,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    // color: Colors.white,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              // mainAxisAlignment: MainAxisAlignment.start,
                              // crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.string(
                                    '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#792AFF"/><path d="M9.993 12.5H10.0005" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 10L10 6.66667" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                                    color: AppColors.primaryRafco),
                                SizedBox(width: 9.w),
                                SizedBox(
                                  width: 280.w,
                                  child: FittedBox(
                                    alignment: Alignment.centerLeft,
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      language == 'en' || language == 'th'
                                          ? Km()
                                          .messages[
                                      'termAndCondition_rafco']
                                          .toString()
                                          : termAndCondition_rafco.tr,
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 14.sp,
                                        fontFamily: TextStyleTheme
                                            .text_Regular.fontFamily,
                                        fontWeight: FontWeight.w700,
                                        // height: 0.10,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? Km()
                                        .messages[
                                    'termAndConditionDesc_rafco']
                                        .toString()
                                        : termAndConditionDesc_rafco.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? Km().messages['definition_rafco']
                                        .toString()
                                        : definition_rafco.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['definitionDesc_rafco']
                                        .toString()
                                        : definitionDesc_rafco.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? Km().messages['disclosure_rafco']
                                        .toString()
                                        : disclosure_rafco.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc1_rafco']
                                        .toString()
                                        : disclosureDesc1_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc2_rafco']
                                        .toString()
                                        : disclosureDesc2_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc3_rafco']
                                        .toString()
                                        : disclosureDesc3_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc4_rafco']
                                        .toString()
                                        : disclosureDesc4_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc5_rafco']
                                        .toString()
                                        : disclosureDesc5_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc6_rafco']
                                        .toString()
                                        : disclosureDesc6_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['disclosureDesc7_rafco']
                                        .toString()
                                        : disclosureDesc7_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['disclosureDesc8_rafco']
                                        .toString()
                                        : disclosureDesc8_rafco.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['Intellectual_assets_rafco']
                                        .toString()
                                        : Intellectual_assets_rafco.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                      text: language == 'en' || language == 'th'
                                          ? Km()
                                          .messages[
                                      'Intellectual_assetsDesc_rafco']
                                          .toString()
                                          : Intellectual_assetsDesc_rafco.tr),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['service_and_usage_restrictions_rafco']
                                        .toString()
                                        : service_and_usage_restrictions_rafco
                                        .tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['service_and_usage_restrictionsDesc1_rafco']
                                        .toString()
                                        : service_and_usage_restrictionsDesc1_rafco
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['service_and_usage_restrictionsDesc2_rafco']
                                        .toString()
                                        : service_and_usage_restrictionsDesc2_rafco
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? Km()
                                        .messages[
                                    'service_and_usage_restrictionsDesc3_rafco']
                                        .toString()
                                        : service_and_usage_restrictionsDesc3_rafco
                                        .tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? Km().messages['suggestions_rafco']
                                        .toString()
                                        : suggestions_rafco.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['suggestionsDesc1_rafco']
                                        .toString()
                                        : suggestionsDesc1_rafco.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en' || language == 'th'
                                        ? Km().messages['contact_rafco']
                                        .toString()
                                        : contact_rafco.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' || language == 'th'
                                        ? Km().messages['contactDesc1_rafco']
                                        .toString()
                                        : contactDesc1_rafco.tr}\n',
                                  ),
                                  TextSpan(
                                    text: language == 'en' || language == 'th'
                                        ? Km()
                                        .messages['contactDesc2_rafco']
                                        .toString()
                                        : contactDesc2_rafco.tr,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  static buildTermsAndConditionsAAM_Pay(context) {
    String language = Get.locale?.languageCode ?? 'th';
    print(language);
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return GetBuilder<AAMPayController>(
            init: Get.find<AAMPayController>(),
            builder: (_aamPayController) {
          return Container(
            height: Get.height,
            width: Get.width,
            // color: Colors.white,
            margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
            child: NotificationListener<ScrollNotification>(
              onNotification: (scrollNotification) {
                // เช็คว่าเลื่อนถึงจุดล่างสุดหรือยัง
                if (scrollNotification is ScrollEndNotification) {
                  final maxScroll =
                      scrollNotification.metrics.maxScrollExtent;
                  final currentScroll = scrollNotification.metrics.pixels;

                  if (currentScroll == maxScroll) {
                    debugPrint("ถึงจุดล่างสุดแล้ว");
                    _aamPayController.setReadAgreement().then((value) {
                      Future.delayed(const Duration(milliseconds: 10000),
                              () {
                            debugPrint("## ครบ 10 วิ");
                            if (!_aamPayController
                                .isCloseDigitalAgreement.value) {
                              Navigator.pop(Get.context!);
                            }
                          });
                    });
                  }
                }
                return true; // ต้อง return true เพื่อให้ event ไม่ถูกส่งต่อไปยัง widget อื่น
              },
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _aamPayController.isReadDigitalAgreement.value
                        ? Container(
                      height: 24.h,
                      width: 24.w,
                      child: Align(
                        alignment: Alignment.topRight,
                        child: GestureDetector(
                            onTap: () async {
                              await _aamPayController
                                  .setCloseAgreement();
                              Navigator.pop(context);
                            },
                            child: SvgPicture.string(
                                AppSvgImage.close_btn)),
                      ),
                    )
                        : Container(),
                    Container(
                        height: Get.height,
                        width: Get.width,
                        // color: Colors.white,
                        margin: EdgeInsets.only(top: 25.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12.0.r),
                            topRight: Radius.circular(12.0.r),
                          ),
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 14.w, top: 26.h, bottom: 14.h),
                                child: Row(
                                  // mainAxisSize: MainAxisSize.min,
                                  // mainAxisAlignment: MainAxisAlignment.start,
                                  // crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SvgPicture.string(
                                        '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#792AFF"/><path d="M9.993 12.5H10.0005" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 10L10 6.66667" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
                                    SizedBox(width: 9.w),
                                    Center(
                                      child: Text(
                                        language == 'en'
                                            ? Th()
                                            .messages[
                                        'termAndCondition_aam_loan']
                                            .toString()
                                            : termAndCondition_aam_loan.tr,
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818),
                                          fontSize: 14.sp,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w700,
                                          // height: 0.10,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ComponanceWidget.buildDivider(),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 14.w, top: 14.h, right: 14.w),
                                child: Text.rich(
                                  TextSpan(
                                    style: TextStyle(
                                      color: const Color(0xff707070),
                                      fontSize: 12.sp,
                                      fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    children: [
                                      TextSpan(
                                        text: language == 'en'
                                            ? Th()
                                            .messages[
                                        'termAndConditionDesc_aam_loan']
                                            .toString()
                                            : termAndConditionDesc_aam_loan.tr,
                                      ),
                                      TextSpan(
                                        text:
                                        '\n\n${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_1']
                                            .toString()
                                            : termAndCondition_aam_loan_1
                                            .tr}\n\n',
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818),
                                          fontSize: 14.sp,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: language == 'en'
                                            ? Th()
                                            .messages[
                                        'termAndConditionDesc_aam_loan_1']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_1
                                            .tr,
                                      ),
                                      TextSpan(
                                        text:
                                        '\n\n${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_2']
                                            .toString()
                                            : termAndCondition_aam_loan_2
                                            .tr}\n\n',
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818),
                                          fontSize: 14.sp,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                        '${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_2']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_2
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_3']
                                            .toString()
                                            : termAndCondition_aam_loan_3
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_3']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_3
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_4']
                                            .toString()
                                            : termAndCondition_aam_loan_4
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_4']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_4
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_5']
                                            .toString()
                                            : termAndCondition_aam_loan_5
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_5']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_5
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_6']
                                            .toString()
                                            : termAndCondition_aam_loan_6
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_6']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_6
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_7']
                                            .toString()
                                            : termAndCondition_aam_loan_7
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_7']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_7
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_8']
                                            .toString()
                                            : termAndCondition_aam_loan_8
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_8']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_8
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_9']
                                            .toString()
                                            : termAndCondition_aam_loan_9
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_9']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_9
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_10']
                                            .toString()
                                            : termAndCondition_aam_loan_10
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_10']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_10
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_11']
                                            .toString()
                                            : termAndCondition_aam_loan_11
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_11']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_11
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_12']
                                            .toString()
                                            : termAndCondition_aam_loan_12
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_12']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_12
                                            .tr}',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndCondition_aam_loan_13']
                                            .toString()
                                            : termAndCondition_aam_loan_13
                                            .tr}\n',
                                      ),
                                      TextSpan(
                                        text:
                                        '   ${language == 'en'
                                            ? Th()
                                            .messages['termAndConditionDesc_aam_loan_13']
                                            .toString()
                                            : termAndConditionDesc_aam_loan_13
                                            .tr}',
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 220.h,
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  static buildTermsAndConditionsRPLC_Pay(context) {
    String language = Get.locale?.languageCode ?? 'th';
    print(language);

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return GetBuilder<AAMPayController>(
            init: Get.find<AAMPayController>(),
            builder: (_aamPayController) {
              return Container(
                height: Get.height,
                width: Get.width,
                margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
                child: NotificationListener<ScrollNotification>(
                  onNotification: (scrollNotification) {
                    // เช็คว่าเลื่อนถึงจุดล่างสุดหรือยัง
                    if (scrollNotification is ScrollEndNotification) {
                      final maxScroll =
                          scrollNotification.metrics.maxScrollExtent;
                      final currentScroll = scrollNotification.metrics.pixels;

                      if (currentScroll == maxScroll) {
                        debugPrint("ถึงจุดล่างสุดแล้ว");
                        _aamPayController.setReadAgreement().then((value) {
                          Future.delayed(const Duration(milliseconds: 10000),
                                  () {
                                debugPrint("## ครบ 10 วิ");
                                if (!_aamPayController
                                    .isCloseDigitalAgreement.value) {
                                  Navigator.pop(Get.context!);
                                }
                              });
                        });
                      }
                    }
                    return true; // ต้อง return true เพื่อให้ event ไม่ถูกส่งต่อไปยัง widget อื่น
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _aamPayController.isReadDigitalAgreement.value
                            ? Container(
                          height: 24.h,
                          width: 24.w,
                          child: Align(
                            alignment: Alignment.topRight,
                            child: GestureDetector(
                                onTap: () async {
                                  await _aamPayController
                                      .setCloseAgreement();
                                  Navigator.pop(context);
                                },
                                child: SvgPicture.string(
                                    AppSvgImage.close_btn)),
                          ),
                        )
                            : Container(),
                        Container(
                            height: Get.height,
                            width: Get.width,
                            margin: EdgeInsets.only(top: 25.h),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(12.0.r),
                                topRight: Radius.circular(12.0.r),
                              ),
                            ),
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: 14.w, top: 26.h, bottom: 14.h),
                                    child: Row(
                                      children: [
                                        SvgPicture.string(
                                          '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#792AFF"/><path d="M9.993 12.5H10.0005" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 10L10 6.66667" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                                          color: AppColors.primaryRPLC_Yellow,
                                        ),
                                        SizedBox(width: 9.w),
                                        Center(
                                          child: Text(
                                            language == 'en'
                                                ? Th()
                                                .messages[
                                            'termAndCondition_rplc_loan']
                                                .toString()
                                                : termAndCondition_rplc_loan.tr,
                                            style: TextStyle(
                                              color: const Color(0xFF1A1818),
                                              fontSize: 14.sp,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  ComponanceWidget.buildDivider(),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: 14.w, top: 14.h, right: 14.w),
                                    child: Text.rich(
                                      TextSpan(
                                        style: TextStyle(
                                          color: const Color(0xff707070),
                                          fontSize: 12.sp,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: language == 'en'
                                                ? Th()
                                                .messages[
                                            'termAndConditionDesc_rplc_loan']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan
                                                .tr,
                                          ),
                                          TextSpan(
                                            text:
                                            '\n\n${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_1']
                                                .toString()
                                                : termAndCondition_rplc_loan_1
                                                .tr}\n\n',
                                            style: TextStyle(
                                              color: const Color(0xFF1A1818),
                                              fontSize: 14.sp,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          TextSpan(
                                            text: language == 'en'
                                                ? Th()
                                                .messages[
                                            'termAndConditionDesc_rplc_loan_1']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_1
                                                .tr,
                                          ),
                                          TextSpan(
                                            text:
                                            '\n\n${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_2']
                                                .toString()
                                                : termAndCondition_rplc_loan_2
                                                .tr}\n\n',
                                            style: TextStyle(
                                              color: const Color(0xFF1A1818),
                                              fontSize: 14.sp,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                            '${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_2']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_2
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_3']
                                                .toString()
                                                : termAndCondition_rplc_loan_3
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_3']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_3
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_4']
                                                .toString()
                                                : termAndCondition_rplc_loan_4
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_4']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_4
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_5']
                                                .toString()
                                                : termAndCondition_rplc_loan_5
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_5']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_5
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_6']
                                                .toString()
                                                : termAndCondition_rplc_loan_6
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_6']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_6
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_7']
                                                .toString()
                                                : termAndCondition_rplc_loan_7
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_7']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_7
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_8']
                                                .toString()
                                                : termAndCondition_rplc_loan_8
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_8']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_8
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_9']
                                                .toString()
                                                : termAndCondition_rplc_loan_9
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_9']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_9
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_10']
                                                .toString()
                                                : termAndCondition_rplc_loan_10
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_10']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_10
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_11']
                                                .toString()
                                                : termAndCondition_rplc_loan_11
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_11']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_11
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_12']
                                                .toString()
                                                : termAndCondition_rplc_loan_12
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_12']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_12
                                                .tr}',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndCondition_rplc_loan_13']
                                                .toString()
                                                : termAndCondition_rplc_loan_13
                                                .tr}\n',
                                          ),
                                          TextSpan(
                                            text:
                                            '   ${language == 'en'
                                                ? Th()
                                                .messages['termAndConditionDesc_rplc_loan_13']
                                                .toString()
                                                : termAndConditionDesc_rplc_loan_13
                                                .tr}',
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 220.h,
                                  ),

                                  // PrimaryButton(
                                  //     title: policyClickAll.tr,
                                  //     onPressed: () async {
                                  //       if (_aamPayController.isReadDigitalAgreement.value) {
                                  //
                                  //           Navigator.pop(context, true);
                                  //
                                  //       }
                                  //     },
                                  //     height: 48.h,
                                  //     buttonWidth: 327.w,
                                  //     backgroundColor: appConfigService.countryConfigCollection.toString() == 'aam' ?AppColors.AAMPurple : appConfigService.countryConfigCollection.toString() == 'rplc' ? AppColors.primaryRPLC_Grey : AppColors.primaryRafco,
                                  //     backgroundInactiveColor:
                                  //     AppColors.inActiveButtonColor,
                                  //     textColor: Colors.white,
                                  //     isActive: _aamPayController.isReadDigitalAgreement.value),
                                ],
                              ),
                            )),
                      ],
                    ),
                  ),
                ),
              );
            });
      },
    );
  }

  static buildTermsAndConditionsAAM_Loan(context) {
    String language = Get.locale?.languageCode ?? 'th';
    print(language);
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.white.withOpacity(0),
      builder: (context) {
        return Container(
          height: Get.height,
          width: Get.width,
          margin: EdgeInsets.only(top: 67.h, left: 12.w, right: 12.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  height: 24.h,
                  width: 24.w,
                  child: Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.string(AppSvgImage.close_btn)),
                  ),
                ),
                Container(
                    height: Get.height,
                    width: Get.width,
                    margin: EdgeInsets.only(top: 25.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.0.r),
                        topRight: Radius.circular(12.0.r),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 26.h, bottom: 14.h),
                            child: Row(
                              children: [
                                SvgPicture.string(
                                    '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <circle cx="10.0003" cy="10.0001" r="8.33333" stroke="#792AFF"/><path d="M9.993 12.5H10.0005" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 10L10 6.66667" stroke="#792AFF" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
                                SizedBox(width: 9.w),
                                Center(
                                  child: Text(
                                    language == 'en'
                                        ? Th()
                                        .messages['loanPolicy_aam']
                                        .toString()
                                        : loanPolicy_aam.tr,
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ComponanceWidget.buildDivider(),
                          Padding(
                            padding: EdgeInsets.only(
                                left: 14.w, top: 14.h, right: 14.w),
                            child: Text.rich(
                              TextSpan(
                                style: TextStyle(
                                  color: const Color(0xff707070),
                                  fontSize: 12.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w500,
                                ),
                                children: [
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['loanPolicyDesc_aam']
                                        .toString()
                                        : loanPolicyDesc_aam.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['loanPolicy_aam_1']
                                        .toString()
                                        : loanPolicy_aam_1.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text: language == 'en'
                                        ? Th()
                                        .messages['loanPolicyDesc_aam_1']
                                        .toString()
                                        : loanPolicyDesc_aam_1.tr,
                                  ),
                                  TextSpan(
                                    text:
                                    '\n\n${language == 'en'
                                        ? Th().messages['loanPolicy_aam_2']
                                        .toString()
                                        : loanPolicy_aam_2.tr}\n\n',
                                    style: TextStyle(
                                      color: const Color(0xFF1A1818),
                                      fontSize: 14.sp,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_2']
                                        .toString() : loanPolicyDesc_aam_2
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en'
                                        ? Th().messages['loanPolicy_aam_3']
                                        .toString()
                                        : loanPolicy_aam_3.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_3']
                                        .toString() : loanPolicyDesc_aam_3
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '${language == 'en'
                                        ? Th().messages['loanPolicy_aam_4']
                                        .toString()
                                        : loanPolicy_aam_4.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_4']
                                        .toString() : loanPolicyDesc_aam_4
                                        .tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en'
                                        ? Th().messages['loanPolicy_aam_5']
                                        .toString()
                                        : loanPolicy_aam_5.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_5']
                                        .toString() : loanPolicyDesc_aam_5.tr}',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en'
                                        ? Th().messages['loanPolicy_aam_6']
                                        .toString()
                                        : loanPolicy_aam_6.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_6']
                                        .toString() : loanPolicyDesc_aam_6.tr}',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en'
                                        ? Th().messages['loanPolicy_aam_7']
                                        .toString()
                                        : loanPolicy_aam_7.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_7']
                                        .toString() : loanPolicyDesc_aam_7.tr}',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en'
                                        ? Th().messages['loanPolicy_aam_8']
                                        .toString()
                                        : loanPolicy_aam_8.tr}\n',
                                  ),
                                  TextSpan(
                                    text:
                                    '   ${language == 'en' ? Th()
                                        .messages['loanPolicyDesc_aam_8']
                                        .toString() : loanPolicyDesc_aam_8.tr}',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 220.h,
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }
}
