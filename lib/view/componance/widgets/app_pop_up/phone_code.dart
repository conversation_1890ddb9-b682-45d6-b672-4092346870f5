import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../controller/login/login.controller.dart';
import '../../../../controller/register/register.controller.dart';

class PhoneCodeWidget {
  static PhoneCodePopUp(BuildContext context, String controller) {
    print('controller: $controller');
    return showCupertinoModalPopup(
      // isDismissible: true,
      context: context,
      // isScrollControlled: true,
      // enableDrag: false,
      // backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
            width: 72.w,
            height: 144.h,
            margin: EdgeInsets.only(
                top: 127.h, bottom: 510.h, left: 24.w, right: 279.w),
            child: Container(
              padding: EdgeInsets.only(
                top: 14.5.h,
                bottom: 14.5.h,
                left: 9.w,
                right: 9.w,
              ),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x0A000000),
                    blurRadius: 18,
                    offset: Offset(3, 6),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Container(
                height: 115.h,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (controller == 'login') {
                          Get.find<LoginController>().setPhoneCode('+66');
                        } else {
                          Get.find<RegisterController>().setPhoneCode('+66');
                        }
                        Navigator.pop(context);
                      },
                      child: SizedBox(
                        height: 25.h,
                        child: DefaultTextStyle(
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14.sp,
                            fontFamily:
                            TextStyleTheme.IBMPlexSansThai.fontFamily,
                            fontWeight: FontWeight.w700,
                          ),
                          child: Text(
                            '+66',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.IBMPlexSansThai.fontFamily,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 9.h),
                    Container(
                      width: 54.w,
                      height: 1.h,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                            width: 0.20.w,
                            color: Color(0xFF1A1818),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 9.h),
                    GestureDetector(
                      onTap: () {
                        if (controller == 'login') {
                          Get.find<LoginController>().setPhoneCode('+855');
                        } else {
                          Get.find<RegisterController>().setPhoneCode('+855');
                        }
                        Navigator.pop(context);
                      },
                      child: SizedBox(
                        height: 25.h,
                        child: DefaultTextStyle(
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14.sp,
                            fontFamily:
                            TextStyleTheme.IBMPlexSansThai.fontFamily,
                            fontWeight: FontWeight.w700,
                          ),
                          child: Text(
                            '+855',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.IBMPlexSansThai.fontFamily,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 9.h),
                    Container(
                      width: 54.w,
                      height: 1.h,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                            width: 0.20.w,
                            color: Color(0xFF1A1818),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 9.h),
                    GestureDetector(
                      onTap: () {
                        if (controller == 'login') {
                          Get.find<LoginController>().setPhoneCode('+856');
                        } else {
                          Get.find<RegisterController>().setPhoneCode('+856');
                        }
                        Navigator.pop(context);
                      },
                      child: SizedBox(
                        height: 25.h,
                        child: DefaultTextStyle(
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14.sp,
                            fontFamily:
                            TextStyleTheme.IBMPlexSansThai.fontFamily,
                            fontWeight: FontWeight.w700,
                          ),
                          child: Text(
                            '+856',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.IBMPlexSansThai.fontFamily,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
      },
    );
  }
}
