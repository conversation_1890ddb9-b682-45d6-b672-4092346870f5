
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class Popup{
  static popUpChatInApp(context){
    showDialog(
      context: context,
      useSafeArea: false,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: Text(chatInAppWait.tr,style: TextStyle(
            fontSize: 12.sp,
            color: Color(0xFFE9967A)
        ),),
        content: Text(chatInAppContent.tr,style: TextStyle(
            fontSize: 12.sp,
            color: Color(0xFFE9967A)
        ),),
        actions: [
          CupertinoDialogAction(
            isDefaultAction: true,
            child: Text(accountOk.tr,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}