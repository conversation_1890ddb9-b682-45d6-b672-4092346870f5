
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:restart_app/restart_app.dart';

import '../../../../../controller/shorebird/updatePatch.controller.dart';

class PatchPopUp extends StatefulWidget {
  const PatchPopUp({super.key});

  @override
  State<PatchPopUp> createState() => _PatchPopUpState();
}

class _PatchPopUpState extends State<PatchPopUp> {
  final updatePatchCtl = Get.put(UpdatePatchController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
          child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.4,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.8,
                        height: MediaQuery.of(context).size.height * 0.35,
                        decoration: BoxDecoration(
                          color: Color(0x11FFFFFF),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "กำลังอัปเดตแพทซ์",
                              style:
                                  TextStyle(fontSize: 28, color: Colors.white),
                            ),
                            SizedBox(
                              height: 25,
                            ),
                            Container(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "ทางเรามีการปรับปรุงระบบฟังก์ชั่น",
                                    style: TextStyle(
                                        fontSize: 16, color: Colors.white),
                                  ),
                                  Text(
                                    "เพื่อความสะดวกต่อการใช้งาน",
                                    style: TextStyle(
                                        fontSize: 16, color: Colors.white),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 60,
                            ),
                            Obx(() => Container(
                                  margin: EdgeInsets.only(
                                    left: 0.06.sw,
                                    right: 0.06.sw,
                                  ),
                                  child: updatePatchCtl.restartApp.value == true
                                      ? InkWell(
                                          onTap: () async {
                                            /// In Web Platform, Fill webOrigin only when your new origin is different than the app's origin
                                            Restart.restartApp();
                                          },
                                          child: Container(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.7,
                                            height: 0.06.sh,
                                            decoration: BoxDecoration(
                                                color: Color(0xFF30234E),
                                                borderRadius:
                                                    BorderRadius.circular(15)),
                                            child: Center(
                                              child: Text(
                                                "รีสตาร์ท",
                                                style: TextStyle(
                                                    fontSize: 15,
                                                    color: Color(0xFFFFEA74),
                                                    fontWeight:
                                                        FontWeight.w400),
                                              ),
                                            ),
                                          ),
                                        )
                                      : Container(
                                          height: 0.06.sh,
                                          child: Column(
                                            children: [
                                              LinearPercentIndicator(
                                                barRadius:
                                                    const Radius.circular(10),
                                                lineHeight: 8,
                                                percent: updatePatchCtl
                                                        .downloadProgressNotifier
                                                        .value /
                                                    100,
                                                backgroundColor: Colors.black
                                                    .withOpacity(0.2),
                                                progressColor:
                                                    const Color(0xFFFFB100),
                                              ),
                                              SizedBox(
                                                height: 0.01.sh,
                                              ),
                                              Text(
                                                "${updatePatchCtl.downloadProgressNotifier.value.round()}%",
                                                style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.white,
                                                    fontWeight:
                                                        FontWeight.w400),
                                              ),
                                            ],
                                          ),
                                        ),
                                )),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                      top: 0,
                      left: 40,
                      child: Container(
                        height: MediaQuery.of(context).size.width * 0.155,
                        // color: Colors.white,
                        child: Image.asset(
                          'assets/mascot/updatePatch.png',
                          fit: BoxFit.fill,
                        ),
                      )),
                  // Positioned(
                  //     top: 0,
                  //     left: 40,
                  //     child: Container(
                  //       height: MediaQuery.of(context).size.width * 0.155,
                  //       // color: Colors.white,
                  //       child: SvgPicture.asset(
                  //         'assets/images/mascot/updatePatch.svg',
                  //         fit: BoxFit.fill,
                  //       ),
                  //     )),
                ],
              ))),
    );
  }
}
