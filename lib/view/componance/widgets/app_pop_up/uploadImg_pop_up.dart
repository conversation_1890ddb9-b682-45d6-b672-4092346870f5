import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../controller/transalation/translation_key.dart';
import '../../themes/app_textstyle.dart';
import '../../themes/theme.dart';

class UploadimgPopUp {
  static buildUploadImage(context, VoidCallback onCamera,VoidCallback onGallery) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext bc) {
          return Container(
            height: 274.h,
            width: Get.width,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 14.h,
                ),
                Container(
                  height: 5.h,
                  width: 44.w,
                  decoration: BoxDecoration(
                    color: Color(0xFF1A1818).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                SizedBox(
                  height: 38.h,
                ),
                InkWell(
                  onTap: () {
                    onCamera();
                  },
                  child: Container(
                    height: 52.h,
                    margin: EdgeInsets.only(left: 24.w, right: 24.w),
                    // width: 44.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      color:
                          configTheme().colorScheme.onPrimary.withOpacity(0.05),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          height: 20.h,
                          width: 20.w,
                          child: SvgPicture.string(
                            AppSvgImage.camera_icon,
                            color: configTheme().colorScheme.onPrimary,
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Text(
                          mrCamera.tr,
                          style: TextStyle(
                            color: configTheme()
                                .colorScheme
                                .onPrimary
                                .withOpacity(1),
                            fontSize: configTheme().primaryTextTheme.bodyMedium!.fontSize,
                            fontFamily: configTheme().primaryTextTheme.bodyMedium!.fontFamily,
                            fontWeight:configTheme().primaryTextTheme.bodyMedium!.fontWeight,
                            // height: 0.12,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 10.h,),
                InkWell(
                  onTap: () {
                    onGallery();
                  },
                  child: Container(
                    height: 52.h,
                    margin: EdgeInsets.only(left: 24.w, right: 24.w),
                    // width: 44.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      color:
                          configTheme().colorScheme.onPrimary.withOpacity(0.05),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          height: 20.h,
                          width: 20.w,
                          child: SvgPicture.string(
                            AppSvgImage.gallary_icon,
                            color: configTheme().colorScheme.onPrimary,
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Text(
                          accountEditUploadDevice.tr,
                          style: TextStyle(
                            color: configTheme()
                                .colorScheme
                                .onPrimary
                                .withOpacity(1),
                            fontSize: configTheme().primaryTextTheme.bodyMedium!.fontSize,
                            fontFamily: configTheme().primaryTextTheme.bodyMedium!.fontFamily,
                            fontWeight:configTheme().primaryTextTheme.bodyMedium!.fontWeight,
                            // height: 0.12,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                TextButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: Text(
                      accountEditCancel.tr,
                      style: TextStyle(
                        color: configTheme().colorScheme.onPrimary,
                        fontSize: 14.sp,
                        fontFamily: TextStyleTheme.text_Regular.fontFamily,
                        fontWeight: FontWeight.w600,
                        // height: 0.12,
                      ),
                    ))
              ],
            ),
          );
        });
  }
}
