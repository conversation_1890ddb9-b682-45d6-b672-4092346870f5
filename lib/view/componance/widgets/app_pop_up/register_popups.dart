import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';

import '../../themes/app_textstyle.dart';
import '../../utils/AppSvgImage.dart';

class RegisterPopUp {
  static AlertOldUser(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 371.h,
            // padding: const EdgeInsets.only(bottom: 20),
            decoration: ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Column(
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  // height: 162.h,
                  margin: EdgeInsets.only(left: 24.w, right: 24.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 70.w,
                        height: 70.h,
                        child: Image.asset(AppImageAssets.verify_user),
                      ),
                      SizedBox(
                        height: 15.h,
                      ),
                      SizedBox(
                        // height: 20.h,
                        child: Text(
                          signUpPhoneAcc.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 16,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                       // SizedBox(height: 10.h),
                      SizedBox(
                        // height: 47.h,
                        child: Text(
                          signUpPhoneAccDes.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xBF1A1818),
                            fontSize: 14,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Spacer(),
                // SizedBox(
                //   height: 44.h,
                // ),
                GestureDetector(
                  onTap: () {
                    Get.find<RegisterController>().setPhoneDataLoginOldUser(context);
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    decoration: ShapeDecoration(
                      color: configTheme().colorScheme.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          backInContinue.tr,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8999999761581421),
                            fontSize: 14.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 48.h,
                )
              ],
            ),
          );
        });
  }
}