import 'package:AAMG/controller/likepoint/likepoint.controller.dart';
import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../../../../controller/home/<USER>';

class MigrationWidget {
  /// migration popup
  static migration() {
    final HomeController homeCtl = Get.find<HomeController>();
    final LikePointController likePointCtl = Get.find<LikePointController>();
    return AnimatedContainer(
      width: Get.width,
      duration: const Duration(milliseconds: 300),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF9D6EFF), Color(0xFF8B54FD)],
        ),
      ),
      padding: EdgeInsets.only(
        left: Get.width * 0.05,
        right: Get.width * 0.05,
      ),
      child: Obx(
            () => Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: Get.height * 0.055,
            ),
            GestureDetector(
              onTap: () {
                homeCtl.changeCloseHead(false);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Text(
                    'ปิดหน้านี้',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: 'Superspace_Bold',
                    ),
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  ClipOval(
                    child: Container(
                      width: 26,
                      height: 26,
                      color: Colors.white,
                      child: Icon(
                        Icons.close,
                        color: Colors.black,
                        size: 15,
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: Get.height * 0.01,
            ),

            ///  box
            Container(
              width: Get.width * 0.4,
              height: 70,
              decoration: ShapeDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF3C266F).withOpacity(0.4),
                    Color(0xFF47246A).withOpacity(0.3),
                  ],
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x0C000000),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                // crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 25,
                      ),
                      Container(
                        child: Image.asset(
                          'assets/images/icon/lock-2.png',
                          scale: 1.5,
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Obx(() => likePointCtl.statusLike!.value == true
                          ? GestureDetector(
                        onTap: () {
                          // if (likepointCtl
                          //     .isOpen!
                          //     .value) {
                          //   Get
                          //       .to(() =>
                          //       WebViewMinilike());
                          // }
                        },
                        child: Container(
                          color: Colors.transparent,
                          child: RichText(
                              text: TextSpan(children: [
                                TextSpan(
                                  text: likePointCtl.totalBalance!.value
                                      .toString(),
                                  style: GoogleFonts.kanit(
                                    fontSize: 25.sp,
                                    color: const Color(0xffffffff)
                                        .withOpacity(0.9),
                                    letterSpacing: 3.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                TextSpan(
                                  text: ' LIKE',
                                  style: GoogleFonts.kanit(
                                    fontSize: 25.sp,
                                    color: const Color(0xFFFEE975),
                                    letterSpacing: 1.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                )
                              ])),
                        ),
                      )
                          : Container()),
                    ],
                  ),
                  SizedBox(
                    height: 5,
                  ),
                  Container(
                    width: 136,
                    height: 25,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xff3C266F),
                            const Color(0xff1C1C1C)
                          ]),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(left: 4, right: 4),
                      child: Row(children: [
                        SizedBox(width: 8.w),
                        Text(
                          'อัปเกรด AAMPoint ',
                          style: TextStyle(
                              fontFamily: 'Superspace_Bold',
                              fontSize: 28.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.white.withOpacity(0.9),
                              letterSpacing: 0.2.sp),
                          textScaleFactor: 1.0,
                        ),
                        Container(
                          child: Image.asset(
                            "assets/images/icon/Coin.png",
                            scale: 1,
                          ),
                        )
                      ]),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: Get.height * 0.02,
            ),
            Get.find<WebViewPointController>().isSuccess.isFalse
                ? Column(
              children: [
                SizedBox(
                  width: Get.width,
                  child: const Text(
                    'อัปเกรดคะแนนเวอร์ชั่นใหม่!',
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      color: Color(0xFFFFEA74),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Superspace_Bold',
                    ),
                  ),
                ),
                SizedBox(
                  height: Get.height * 0.01,
                ),
                SizedBox(
                  width: Get.width,
                  child: const Text(
                    'สร้างประสบการณ์ใหม่กับ AAMPoint (เอเอเอ็มพอยท์) \nอัปเกรดคะแนนเดิมของคุณ เพื่อรับสิทธิประโยชน์ที่มากกว่า \nร่วมสนุกและทำกิจกรรมกับ เอเอเอ็ม ได้แล้ววันนี้',
                    maxLines: null,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Superspace_Bold',
                    ),
                  ),
                ),
                SizedBox(
                  height: Get.height * 0.01,
                ),
              ],
            )
                : Container(
                child: Row(
                  children: [
                    SvgPicture.asset(
                        'assets/images/svg/check_ring_round.svg'),
                    SizedBox(
                      width: 10,
                    ),
                    Text(
                      "อัปเกรดคะแนนสำเร็จ!",
                      style: TextStyle(
                        color: Color(0xFFFFEA74),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Superspace_Bold',
                      ),
                    ),
                  ],
                )),
            Get.find<WebViewPointController>().isUpgrading.isFalse
                ? Column(
              children: [
                SizedBox(
                  width: Get.width,
                  child: const Text(
                    'เงื่อนไข:',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Superspace_Bold',
                    ),
                  ),
                ),
                SizedBox(
                  width: Get.width,
                  child: const Text(
                    '  1.คะแนนจะเปลี่ยนชื่อใหม่เป็น AAMPoint AAMPoint \n  2.จะสามารถใช้ได้เฉพาะบนแอปเอเอเอ็ม เท่านั้น',
                    maxLines: null,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Superspace_Bold',
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: Get.find<WebViewPointController>().isChecked.isTrue,
                      activeColor: Color(0xFFFFEA74),
                      checkColor: Color(0xFF8B54FD),
                      side: const BorderSide(
                        color: Color(0xFFFFEA74),
                      ),
                      onChanged: (bool? value) {
                        Get.find<WebViewPointController>().checkAccept(value!);
                      },
                    ),
                    const Text(
                      'ยอมรับข้อกำหนด',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Superspace_Bold',
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    const Text(
                      'เงื่อนไขการให้บริการ>',
                      style: TextStyle(
                        decoration: TextDecoration.underline,
                        decorationColor: Color(0xFF30234E),
                        color: Color(0xFF30234E),
                        fontSize: 16,
                        fontFamily: 'Superspace_Bold',
                      ),
                    ),
                  ],
                ),
              ],
            )
                : const SizedBox(),
            Get.find<WebViewPointController>().isSuccess.isFalse
                ? Stack(
              children: [
                InkWell(
                  onTap: () async {
                    if (Get.find<WebViewPointController>().isChecked.isTrue) {
                      /// activate migration
                      await Get.find<WebViewPointController>().upgradeLikePoint(Get.find<ProfileController>()
                          .profile.value.phoneFirebase.obs.string);
                    }
                  },
                  child: Container(
                    width: Get.width * 0.9,
                    height: 48,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Get.find<WebViewPointController>().isChecked.isFalse ||
                            Get.find<WebViewPointController>().isUpgrading.isFalse
                            ? Color(0xFF30234e).withOpacity(0.2)
                            : Color(0xFF30234e)),
                    alignment: Alignment.center,
                    child: Get.find<WebViewPointController>().isUpgrading.isFalse
                        ? Text(
                      "อัปเกรดคะแนน",
                      style: TextStyle(
                        color: Get.find<WebViewPointController>().isChecked.isFalse
                            ? Color(0xFFFFEA74).withOpacity(0.5)
                            : Color(0xFFFFEA74),
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Superspace_Bold',
                      ),
                    )
                        : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "กำลังอัปเกรด",
                          style: TextStyle(
                            color: Color(0xFFFFEA74),
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Superspace_Bold',
                          ),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        LoadingAnimationWidget.waveDots(
                          color: Color(0xFFFFFFFF),
                          size: 30,
                        ),
                        // CircularProgressIndicator(
                        //   color: Color(0xFFFFB800),
                        // )
                      ],
                    ),
                  ),
                ),
              ],
            )
                : const SizedBox(),
            SizedBox(
              height: Get.height * 0.02,
            ),
          ],
        ),
      ),
    );
  }
}