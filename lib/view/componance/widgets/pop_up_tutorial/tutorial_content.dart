import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class TutorialItemContent extends StatefulWidget {
  const TutorialItemContent({
    super.key,
    required this.title,
    required this.content,
    required this.content1,
    required this.content2,
    required this.nameBu,
    required this.content3,
    required this.img,
    required this.nameUse
  });

  final String title;
  final String content;
  final String content1;
  final String content2;
  final String nameBu;
  final String content3;
  final String img;
  final String nameUse;

  @override
  State<TutorialItemContent> createState() => _TutorialItemContentState();
}

class _TutorialItemContentState extends State<TutorialItemContent> {
  final ProfileController profileController = Get.find<ProfileController>();
  final HomeController homeController = Get.find<HomeController>();
  final incrementKey = GlobalKey();
  final avatarKey = GlobalKey();
  final textKey = GlobalKey();
  final buttonKey = GlobalKey();
  @override
  void initState() {
    super.initState();
    profileController.getProfile();
    setState(() {
      print('profile: ${profileController.profile.value.firstname}');
    });
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GetBuilder<ProfileController>(
        init: profileController,
        builder: (profileController) {
          return  showTutorial();
        },
      ),
    );
  }

  showTutorial() {
    return showModalBottomSheet(
        context: context, builder: (builder)=>
        CarouselSlider.builder(
            itemCount: 15,
            itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) =>
                Container(
                  child: Text(itemIndex.toString()),
                ),
            options: CarouselOptions(
              height: 100.h,
              aspectRatio: 16 / 9,
              viewportFraction: 0.8,
              initialPage: 0,
              enableInfiniteScroll: true,
              reverse: false,
              // autoPlay: true,
              // autoPlayInterval: const Duration(seconds: 3),
              // autoPlayAnimationDuration: const Duration(milliseconds: 800),
              // autoPlayCurve: Curves.fastOutSlowIn,
              enlargeCenterPage: true,
              scrollDirection: Axis.horizontal,
            )
        )
    );
  }
}
