# Uncomment this line to define a global platform for your project
platform :ios, '15.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  # target 'RunnerTests' do
  #   inherit! :search_paths
  # end
end

# post_install do |installer|
#   installer.pods_project.targets.each do |target|
#     flutter_additional_ios_build_settings(target)

#     # Fix for Xcode 16.3 and BoringSSL-GRPC
#     if target.name == 'BoringSSL-GRPC'
#       target.build_configurations.each do |config|
#         # Remove the -G flag
#         config.build_settings['OTHER_CFLAGS'] = '$(inherited) -Wno-comma -Wno-unreachable-code'
#         config.build_settings['OTHER_CPLUSPLUSFLAGS'] = '$(inherited) -Wno-comma -Wno-unreachable-code'
#         config.build_settings['GCC_OPTIMIZATION_LEVEL'] = '0'
#       end
#     end

#     # Fix for WebKit
#     if target.name == 'flutter_inappwebview'
#       target.build_configurations.each do |config|
#         config.build_settings['SWIFT_VERSION'] = '5.0'
#         config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'YES'
#       end
#     end

#     # Add this block to fix Swift library loading issues
#     target.build_configurations.each do |config|
#       config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
      
#       # Ensure Swift standard libraries are included
#       config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'YES'
      
#       # Add Swift library search paths
#       config.build_settings['LD_RUNPATH_SEARCH_PATHS'] ||= [
#         '$(inherited)',
#         '@executable_path/Frameworks',
#         '@loader_path/Frameworks'
#       ]
#     end
#   end
# end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = "arm64"
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_LOCATION=1',
        'PERMISSION_CAMERA=1',
        'PERMISSION_NOTIFICATIONS=1',
        'PERMISSION_PHOTOS=1',
        'PERMISSION_MEDIA_LIBRARY=1',
      ]
      # Ensure Swift version is set
      config.build_settings['SWIFT_VERSION'] = '5.0'
    end
  end

  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
        # Ensure Swift version is set
        config.build_settings['SWIFT_VERSION'] = '5.0'
        xcconfig_path = config.base_configuration_reference.real_path
        xcconfig = File.read(xcconfig_path)
        xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
        File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
      end
    end
  end
end

