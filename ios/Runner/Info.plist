<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(APP_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>GOOGLE_MAPS_API_KEY</key>
		<string>$(API_KEY)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>mapp_aamg_ui</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false/>
		<key>LSApplicationCategoryType</key>
		<string></string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSCameraUsageDescription</key>
		<string>Need to access camera use the camera to upload a QR Code picture</string>
		<key>NSContactsUsageDescription</key>
		<string>We need access to your Contacts to help you easily invite friends to join the AAM app, and to personalize your experience by showing your contacts in the app.</string>
		<key>NSFaceIDUsageDescription</key>
		<string>$(PRODUCT_NAME) Authentication with TouchId or FaceID</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>This app needs access to location when open and in the background.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This app needs access to location when in the background.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to location in the background.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Need access to your photos to share</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Need to access photoLibrary use photoLibrary to upload a profile picture</string>
		<key>PermissionGroupNotification</key>
		<string>Allow $(PRODUCT_NAME) to send you notifications</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>$(LAUNCH_SCREEN)</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>NSLocalNetworkUsageDescription</key>
		<string>Looking for local tcp Bonjour service</string>
		<key>NSBonjourServices</key>
		<array>
			<string>_dartobservatory._tcp</string>
			<string>mqtt.tcp</string>
		</array>
		<key>FacebookAppID</key>
		<string>$(FacebookAppID)</string>
		<key>FacebookClientToken</key>
		<string>$(FacebookClientToken)</string>
		<key>FacebookDisplayName</key>
		<string>$(FacebookDisplayName)</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>$(FacebookLoginProtocol)</string>
				</array>
			</dict>
		</array>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fb</string>
		</array>
		<key>UIStatusBarHidden</key>
		<true/>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>NSAppleMusicUsageDescription</key>
		<string>$(PRODUCT_NAME) needs access to music</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>$(PRODUCT_NAME) needs access to microphone</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>$(PRODUCT_NAME) requests access to your photo library to allow you to select and upload photos to your profile. For example, you can choose a picture from your library to set as your profile picture.</string>
	</dict>
</plist>
