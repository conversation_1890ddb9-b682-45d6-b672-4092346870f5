import Flutter
import UIKit
import Firebase
import awesome_notifications
import GoogleMaps
import FBSDK<PERSON><PERSON><PERSON><PERSON>

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FirebaseApp.configure() //add this before the code below
      if let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
         let googleServiceInfo = NSDictionary(contentsOfFile: path) as? [String: AnyObject],
         let apiKey = googleServiceInfo["API_KEY"] as? String {
         GMSServices.provideAPIKey(apiKey)
    } else {
        // Handle the case where the API key is not set or is not a string
        print("Error: Google Maps API Key is missing or not valid.")
    }
  // ตั้งค่า Facebook SDK
    ApplicationDelegate.shared.application(
        application,
        didFinishLaunchingWithOptions: launchOptions
    )
//    GMSServices.provideAPIKey("AIzaSyDWl3v1tlItotlAi9Boft2Mcd2FnheNbis")
    GeneratedPluginRegistrant.register(with: self)
      // This function registers the desired plugins to be used within a notification background action
    SwiftAwesomeNotificationsPlugin.setPluginRegistrantCallback { registry in
      SwiftAwesomeNotificationsPlugin.register(
        with: registry.registrar(forPlugin: "io.flutter.plugins.awesomenotifications.AwesomeNotificationsPlugin")!)
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // รองรับการเรียก callback จาก Facebook Login
    override func application(
      _ app: UIApplication,
      open url: URL,
      options: [UIApplication.OpenURLOptionsKey : Any] = [:]
    ) -> Bool {
      ApplicationDelegate.shared.application(
          app,
          open: url,
          sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
          annotation: options[UIApplication.OpenURLOptionsKey.annotation]
      )
      return super.application(app, open: url, options: options)
    }

}
